node{

def tomcatWeb= 'E:\\Tomcat\\webapps'
	
	stage('SCM Checkout')
	{
		git(
			url: 'https://<EMAIL>/Optomatica/azimut-backend.git'
			)
	}
	stage('Compile-Package-create-war-file')
	{
		def mvnHome= tool name: 'maven-3', type: 'maven'
		bat "${mvnHome}/bin/mvn package"
	}
	stage ('Deploy to Tomcat')
	{
		bat "net stop Tomcat9"
		sleep(time:30,unit:"SECONDS")
		bat "copy target\\azimut-0.0.1-SNAPSHOT.war \"${tomcatWeb}\\azimut-0.0.1-SNAPSHOT.war\""
		bat "rmdir /s /q ${tomcatWeb}\\azimut-0.0.1-SNAPSHOT"
	}
	
	stage ('Start Tomcat Server')
	{
		bat "net start Tomcat9"
	}

}