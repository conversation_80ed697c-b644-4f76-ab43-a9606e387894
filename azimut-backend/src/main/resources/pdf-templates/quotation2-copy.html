<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
  <head>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/bootstrap@4.3.1/dist/css/bootstrap.min.css"
      integrity="sha384-ggOyR0iXCbMQv3Xipma34MD+dH/1fQ784/j6cY/iJTQUOhcWr7x9JvoRxT2MZw1T"
      crossorigin="anonymous"
    />
    <style>
      html,
      body,
      p,
      div,
      label,
      span,
      input {
        font-size: 11px;
      }

      .question_name {
        font-weight: bold;
      }
      .question_answers_holder {
        margin: 0 20px;
      }
      .question_answers_holder p {
        color: gray;
      }
      .related_answers_holder {
        margin: 0 30px;
      }
      .related_answers_holder p {
        color: gray;
      }
      .image_pdf{
      max-width:700px
      }
    </style>
  </head>

  <body>
  
    <div class="row">
    <div>
     <img class="image_pdf" th:src="@{classpath:static/img/Logo2.png}" />
    </div>
      <div th:each="question, iterStat: ${questions}">
        <div class="col-md-6" th:if="${question.answerType} == 'CHECK' ">
          <p
            class="question_name"
            th:text="${question.questionOrder} + '.' + ${question.questionText}"
          ></p>

          <div
            class="question_answers_holder"
            th:each="answer, iterStat: ${question.userAnswers}"
          >
            <input
              type="checkbox"
              th:id="'checkbox' + ${answer.answerId}"
              th:name="'checkbox' + ${answer.answerId}"
              th:checked="true"
              th:readonly="true"
            />
            <label th:for="'checked' + ${answer.answerId}"
              ><span th:text="${answer.answerValue}"> </span>
            </label>
            <div
              class="related_answers_holder"
              th:each="relatedUserAnswer, iterStat: ${answer.relatedAnswers}"
            >
              <p th:text="${relatedUserAnswer.relatedAnswerValue}"></p>
            </div>
          </div>

          <div th:if="${question.subQuestions} != 'undefined' ">
            <div class="row">
              <div th:each="questionSub, iterStat: ${question.subQuestions}">
                <div
                  class="col-md-6"
                  th:if="${questionSub.answerType} == 'CHECK' "
                >
                  <p
                    class="question_name"
                    th:text="${questionSub.questionOrder} + '.' + ${questionSub.questionText}"
                  ></p>

                  <div
                    class="question_answers_holder"
                    th:each="answer, iterStat: ${questionSub.userAnswers}"
                  >
                    <input
                      type="checkbox"
                      th:id="'checkbox' + ${answer.answerId}"
                      th:name="'checkbox' + ${answer.answerId}"
                      th:checked="true"
                      th:readonly="true"
                    />
                    <label th:for="'checked' + ${answer.answerId}"
                      ><span th:text="${answer.answerValue}"> </span>
                    </label>
                    <div
                      class="related_answers_holder"
                      th:each="relatedUserAnswer, iterStat: ${answer.relatedAnswers}"
                    >
                      <p th:text="${relatedUserAnswer.relatedAnswerValue}"></p>
                    </div>
                  </div>
                </div>

                <div
                  class="col-md-6"
                  th:if="${questionSub.answerType} == 'RADIO' or ${questionSub.answerType} == 'TEXT' or ${questionSub.answerType} == 'CALENDER' or ${questionSub.answerType} == 'RICH' or ${questionSub.answerType} == 'EMAIL' or ${questionSub.answerType} == 'DROP' or ${questionSub.answerType} == 'PHONE'"
                >
                  <p
                    class="question_name"
                    th:text="${questionSub.questionOrder} + '.' + ${questionSub.questionText}"
                  ></p>

                  <div
                    class="question_answers_holder"
                    th:each="answer, iterStat: ${questionSub.userAnswers}"
                  >
                    <p th:text="${answer.answerValue}"></p>
                    <div
                      class="related_answers_holder"
                      th:each="relatedUserAnswer, iterStat: ${answer.relatedAnswers}"
                    >
                      <p th:text="${relatedUserAnswer.relatedAnswerValue}"></p>
                    </div>
                  </div>
                </div>

                <div
                  class="col-md-6"
                  th:if="${questionSub.answerType} == 'DOCUMENT' "
                >
                  <p
                    class="question_name"
                    th:text="${questionSub.questionOrder} + '.' + ${questionSub.questionText}"
                  ></p>

                  <div
                    class="question_answers_holder"
                    th:each="answer, iterStat: ${questionSub.userAnswers}"
                  >
                    <p th:text="${answer.documentName}"></p>
                    <div
                      class="related_answers_holder"
                      th:each="relatedUserAnswer, iterStat: ${answer.relatedAnswers}"
                    >
                      <p th:text="${relatedUserAnswer.relatedAnswerValue}"></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
       
        </div>

        <div
          class="col-md-6"
          th:if="${question.answerType} == 'RADIO' or ${question.answerType} == 'TEXT' or ${question.answerType} == 'CALENDER' or ${question.answerType} == 'RICH' or ${question.answerType} == 'EMAIL' or ${question.answerType} == 'DROP' or ${question.answerType} == 'PHONE'"
        >
          <p
            class="question_name"
            th:text="${question.questionOrder} + '.' + ${question.questionText}"
          ></p>
          <div
            class="question_answers_holder"
            th:each="answer, iterStat: ${question.userAnswers}"
          >
            <p th:text="${answer.answerValue}"></p>
            <div
              class="related_answers_holder"
              th:each="relatedUserAnswer, iterStat: ${answer.relatedAnswers}"
            >
              <p th:text="${relatedUserAnswer.relatedAnswerValue}"></p>
            </div>
          </div>
          <div th:if="${question.subQuestions} != 'undefined' ">
            <div class="row">
              <div th:each="questionSub, iterStat: ${question.subQuestions}">
                <div
                  class="col-md-6"
                  th:if="${questionSub.answerType} == 'CHECK' "
                >
                  <p
                    class="question_name"
                    th:text="${questionSub.questionOrder} + '.' + ${questionSub.questionText}"
                  ></p>

                  <div
                    class="question_answers_holder"
                    th:each="answer, iterStat: ${questionSub.userAnswers}"
                  >
                    <input
                      type="checkbox"
                      th:id="'checkbox' + ${answer.answerId}"
                      th:name="'checkbox' + ${answer.answerId}"
                      th:checked="true"
                      th:readonly="true"
                    />
                    <label th:for="'checked' + ${answer.answerId}"
                      ><span th:text="${answer.answerValue}"> </span>
                    </label>
                    <div
                      class="related_answers_holder"
                      th:each="relatedUserAnswer, iterStat: ${answer.relatedAnswers}"
                    >
                      <p th:text="${relatedUserAnswer.relatedAnswerValue}"></p>
                    </div>
                  </div>
                </div>

                <div
                  class="col-md-6"
                  th:if="${questionSub.answerType} == 'RADIO' or ${questionSub.answerType} == 'TEXT' or ${questionSub.answerType} == 'CALENDER' or ${questionSub.answerType} == 'RICH' or ${questionSub.answerType} == 'EMAIL' or ${questionSub.answerType} == 'DROP' or ${questionSub.answerType} == 'PHONE'"
                >
                  <p
                    class="question_name"
                    th:text="${questionSub.questionOrder} + '.' + ${questionSub.questionText}"
                  ></p>

                  <div
                    class="question_answers_holder"
                    th:each="answer, iterStat: ${questionSub.userAnswers}"
                  >
                    <p th:text="${answer.answerValue}"></p>
                    <div
                      class="related_answers_holder"
                      th:each="relatedUserAnswer, iterStat: ${answer.relatedAnswers}"
                    >
                      <p th:text="${relatedUserAnswer.relatedAnswerValue}"></p>
                    </div>
                  </div>
                </div>

                <div
                  class="col-md-6"
                  th:if="${questionSub.answerType} == 'DOCUMENT' "
                >
                  <p
                    class="question_name"
                    th:text="${questionSub.questionOrder} + '.' + ${questionSub.questionText}"
                  ></p>

                  <div
                    class="question_answers_holder"
                    th:each="answer, iterStat: ${questionSub.userAnswers}"
                  >
                    <p th:text="${answer.documentName}"></p>
                    <div
                      class="related_answers_holder"
                      th:each="relatedUserAnswer, iterStat: ${answer.relatedAnswers}"
                    >
                      <p th:text="${relatedUserAnswer.relatedAnswerValue}"></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-md-6" th:if="${question.answerType} == 'DOCUMENT'">
          <p
            class="question_name"
            th:text="${question.questionOrder} + '.' + ${question.questionText}"
          ></p>
          <div
            class="question_answers_holder"
            th:each="answer, iterStat: ${question.userAnswers}"
          >
            <p th:text="${answer.documentName}"></p>
            <div
              class="related_answers_holder"
              th:each="relatedUserAnswer, iterStat: ${answer.relatedAnswers}"
            >
              <p th:text="${relatedUserAnswer.relatedAnswerValue}"></p>
            </div>
          </div>
          <div th:if="${question.subQuestions} != 'undefined' ">
            <div class="row">
              <div th:each="questionSub, iterStat: ${question.subQuestions}">
                <div
                  class="col-md-6"
                  th:if="${questionSub.answerType} == 'CHECK' "
                >
                  <p
                    class="question_name"
                    th:text="${questionSub.questionOrder} + '.' + ${questionSub.questionText}"
                  ></p>

                  <div
                    class="question_answers_holder"
                    th:each="answer, iterStat: ${questionSub.userAnswers}"
                  >
                    <input
                      type="checkbox"
                      th:id="'checkbox' + ${answer.answerId}"
                      th:name="'checkbox' + ${answer.answerId}"
                      th:checked="true"
                      th:readonly="true"
                    />
                    <label th:for="'checked' + ${answer.answerId}"
                      ><span th:text="${answer.answerValue}"> </span>
                    </label>
                    <div
                      class="related_answers_holder"
                      th:each="relatedUserAnswer, iterStat: ${answer.relatedAnswers}"
                    >
                      <p th:text="${relatedUserAnswer.relatedAnswerValue}"></p>
                    </div>
                  </div>
                </div>

                <div
                  class="col-md-6"
                  th:if="${questionSub.answerType} == 'RADIO' or ${questionSub.answerType} == 'TEXT' or ${questionSub.answerType} == 'CALENDER' or ${questionSub.answerType} == 'RICH' or ${questionSub.answerType} == 'EMAIL' or ${questionSub.answerType} == 'DROP' or ${questionSub.answerType} == 'PHONE'"
                >
                  <p
                    class="question_name"
                    th:text="${questionSub.questionOrder} + '.' + ${questionSub.questionText}"
                  ></p>

                  <div
                    class="question_answers_holder"
                    th:each="answer, iterStat: ${questionSub.userAnswers}"
                  >
                    <p th:text="${answer.answerValue}"></p>
                    <div
                      class="related_answers_holder"
                      th:each="relatedUserAnswer, iterStat: ${answer.relatedAnswers}"
                    >
                      <p th:text="${relatedUserAnswer.relatedAnswerValue}"></p>
                    </div>
                  </div>
                </div>

                <div
                  class="col-md-6"
                  th:if="${questionSub.answerType} == 'DOCUMENT' "
                >
                  <p
                    class="question_name"
                    th:text="${questionSub.questionOrder} + '.' + ${questionSub.questionText}"
                  ></p>

                  <div
                    class="question_answers_holder"
                    th:each="answer, iterStat: ${questionSub.userAnswers}"
                  >
                    <p th:text="${answer.documentName}"></p>
                    <div
                      class="related_answers_holder"
                      th:each="relatedUserAnswer, iterStat: ${answer.relatedAnswers}"
                    >
                      <p th:text="${relatedUserAnswer.relatedAnswerValue}"></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
