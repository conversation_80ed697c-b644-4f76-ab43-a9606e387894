spring.profiles.active=dev
spring.main.allow-circular-references=true
###############################################################################################################
#Database
###############################################################################################################
spring.jpa.hibernate.ddl-auto=update
spring.jpa.database=mysql
spring.datasource.initialization-mode=always
spring.datasource.url=***********************************************
spring.datasource.username=sandbox_azimut
spring.datasource.password=Innovitics@Azimut2021
#spring.datasource.url=****************************************************************
#spring.datasource.username=asmiolandingold@asmio-mysqlold
#spring.datasource.password=Innovitics@Demo2021
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.jpa.properties.hibernate.jdbc.time_zone=Africa/Cairo
spring.batch.initialize-schema=always
spring.jackson.default-property-inclusion = NON_NULL
spring.jackson.serialization.indent_output=true
###############################################################################################################
#Database batching
###############################################################################################################
spring.jpa.properties.hibernate.jdbc.batch_size=50
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.batch_versioned_data=true
spring.jpa.properties.hibernate.generate_statistics=true
spring.jpa.properties.hibernate.session.events.log.LOG_QUERIES_SLOWER_THAN_MS=25
###############################################################################################################
#Logging
###############################################################################################################
#log.file.path=e:\\Development\\Java_Projects\\SystemLogs\\logs
log.file.path=//home//site//wwwroot//webapps
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type=TRACE
###############################################################################################################
#System
###############################################################################################################
app.url=https://spring-boot-cicd-demo.scm.azurewebsites.net/wwwroot/webapps/
###############################################################################################################
#Blob
###############################################################################################################
blob.connection.string=DefaultEndpointsProtocol=https;AccountName=demostorageasmio;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
blob.account.name=demostorageasmio
blob.container.url=https://demostorageasmio.blob.core.windows.net/
blob.container.name.profile-pictures=azimutprofilepictures/Users/<USER>
blob.container.name.user-interactions=azimutprofilepictures/Users/<USER>
blob.container.name.signed-pdfs=azimutdocuments/Users/<USER>
blob.container.name.unsigned-pdf=azimutdocuments/unsignedPDF
blob.container.name.unsigned-pdf.subDirectory=PhoneNumberChange
blob.phoneNumberChangeDocument.name=SimpleAPIGuide(004)-Copy.pdf
blob.custodianDocument.name=Custodian.pdf
blob.container.public=public
blob.container.name.kyc.documents=azimut-kyc/Users
blob.container.name.kyc.documents.container=azimut-kyc
blob.container.name.kyc.documents.temp=azimut-kyc-temp/Users
blob.container.name.kyc.documents.temp.container=azimut-kyc-temp
blob.temp.deletion.hours=120000
blob.generate.sas.token=true
blob.sas.token.duration.minutes=2
blob.sas.document.token.duration.minutes=15
profile.picture.max.size.bytes=3000000
phone.number.pdf.max.size.bytes=15000000
temp.file.delete.delay.hours=4
################################################################################################################
#Security
################################################################################################################
admin.token.validity.minutes=1440
token.validity.minutes=1440
token.key=SDFGHJKLZXCV
token.encryption.key=KJRBGONENSGVCNDQ
token.encryption.init.vector=eio382h4tbiuboda
blockage.duration.minutes=60
blockage.number.of.trials=5
################################################################################################################
spring.mvc.dispatch-options-request=true
################################################################################################################
#Azimut
################################################################################################################
azimut.url=https://api.azimut.innsandbox.com/api
azimut.fund.images.url=http://api.azimut.innsandbox.com/storage
#azimut.bcc.mail=<EMAIL>
azimut.bcc.mail=<EMAIL>
################################################################################################################
#TeaComputers
################################################################################################################
tea.computers.url=http://41.187.88.188/FITSAPI/Api/fund
tea.computers.eport.url=http://41.187.88.188/EPortApi
tea.computers.key=TEABO
tea.computers.username=TEABO
tea.computers.password=TEABO
tea.computers.eportfolio.key=EPORTFOLIO
tea.computers.eportfolio.username=EPORTFOLIO
tea.computers.job.delay.seconds=180
################################################################################################################
#Opto Signer
################################################################################################################
opto.signer.url=https://opto-signer-5d42795d4031.herokuapp.com
opto.signer.username=<EMAIL>
opto.signer.password=azInvest@127GB
################################################################################################################
#FRA API
################################################################################################################
fra.url=https://api.di.fra.gov.eg/v1
fra.api.key=9ozLdZzy9IiXF2CxPO1zbChlzwRQaCUEq6SpLEqmXg
fra.company.name=Azimut
################################################################################################################
#Paytabs
################################################################################################################
paytabs.url=https://secure-egypt.paytabs.com
paytabs.profile.id=85273
paytabs.merchant.id=34325
paytabs.server.key=SKJNBJHJ62-J29Z29NMMG-ZDZ6GDLLNL
paytabs.client.key=CPKMP9-VH226M-P9MPQN-BKBGKB
paytabs.mobile.server.key=SKJNBJHJHN-J29Z29NMKH-9HRZRKGZ6N
paytabs.mobile.client.key=CTKMP9-VHQN6M-P9MPQN-GNQ2DH
paytabs.callback.url=https://spring-boot-cicd-demo.azurewebsites.net/azimut-0.0.1-SNAPSHOT/api/paytabs/callback
paytabs.return.url=https://azimutweb.innsandbox.com/
################################################################################################################
#Firebase
################################################################################################################
firebase.url=https://identitytoolkit.googleapis.com:443/v1/accounts
firebase.web.key=AIzaSyCYNtILg6bgkGE1OMDr4mUn6YV9UN4hDUk
################################################################################################################
#Mail
################################################################################################################
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=pzimxxkplwbzjhgj
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
################################################################################################################
#Configuring ehcache.xml
################################################################################################################
spring.cache.jcache.config=classpath:ehcache.xml
################################################################################################################
#Pagination
################################################################################################################
page.size=10
################################################################################################################
#Social Login
################################################################################################################
spring.security.oauth2.client.registration.google.clientId= 92069906716-9la3jom0uo82pe1r8a782utkpn44ogbm.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.clientSecret=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
spring.security.oauth2.client.registration.google.scope=email, profile
spring.security.oauth2.client.registration.facebook.clientId=959851142049147
spring.security.oauth2.client.registration.facebook.clientSecret=********************************
spring.security.oauth2.client.registration.facebook.scope=email, public_profile

real.estate.page.id=19

app.auth.tokenSecret=04ca023b39512e46d0c2cf4b48d5aac61d34302994c87ed4eff225dcf3b0a218739f3897051a057f9b846a69ea2927a587044164b7bae5e1306219d50b588cb1
app.auth.tokenExpirationMsec=864000000
app.cors.allowedOrigins=http://localhost:8090

app.version=1.6.4
