used.profile=Development
###############################################################################################################
#Database
###############################################################################################################
spring.jpa.hibernate.ddl-auto=update
spring.jpa.database=mysql
spring.datasource.initialization-mode=always
# spring.datasource.url=**********************************
# spring.datasource.username=root
# spring.datasource.password=root
spring.datasource.url=****************************************************************
spring.datasource.username=asmiolandingold@asmio-mysqlold
spring.datasource.password=Innovitics@Demo2021
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
#spring.jpa.properties.hibernate.jdbc.time_zone=Africa/Cairo
spring.batch.initialize-schema=always
spring.jackson.default-property-inclusion = NON_NULL
spring.jackson.serialization.indent_output=true
###############################################################################################################
#Database batching
###############################################################################################################
spring.jpa.properties.hibernate.jdbc.batch_size=50
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true
spring.jpa.properties.hibernate.batch_versioned_data=true
spring.jpa.properties.hibernate.generate_statistics=true
spring.jpa.properties.hibernate.session.events.log.LOG_QUERIES_SLOWER_THAN_MS=25

spring.datasource.hikari.data-source-properties.prepStmtCacheSize=250
spring.datasource.hikari.data-source-properties.prepStmtCacheSqlLimit=2048
spring.datasource.hikari.data-source-properties.cachePrepStmts=true
spring.datasource.hikari.data-source-properties.useServerPrepStmts=true
spring.datasource.hikari.data-source-properties.rewriteBatchedStatements=true
################################################################################################################
#Proxy
################################################################################################################
proxy.username=
proxy.password=
proxy.url=
proxy.port=
###############################################################################################################
#Logging
###############################################################################################################
log.file.path=E:\\Tomcat\\webapps\\Application\\SystemLogs\\
#log.file.path=//home//site//wwwroot//webapps
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type=TRACE
###############################################################################################################
#System
###############################################################################################################
app.url=http://localhost:8080
###############################################################################################################
#Blob
###############################################################################################################
blob.connection.string=DefaultEndpointsProtocol=https;AccountName=demostorageasmio;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net
blob.account.name=demostorageasmio
blob.container.url=https://demostorageasmio.blob.core.windows.net/
blob.container.name.profile-pictures=azimutprofilepictures/Users/<USER>
blob.container.name.user-interactions=azimutprofilepictures/Users/<USER>
blob.container.name.signed-pdfs=azimutdocuments/Users/<USER>
blob.container.name.digitally-signed-pdfs=azimut-contracts/Users
blob.container.name.fits-signed-pdfs=azimut-contracts/FitsUsers
blob.container.name.unsigned-pdf=azimutdocuments/unsignedPDF
blob.container.name.unsigned-pdf.subDirectory=PhoneNumberChange
blob.phoneNumberChangeDocument.name=SimpleAPIGuide(004)-Copy.pdf
blob.container.name.kyc.documents=azimut-kyc/Users
blob.container.name.kyc.documents.container=azimut-kyc
blob.container.name.kyc.documents.temp=azimut-kyc-temp/Users
blob.container.name.kyc.documents.temp.container=azimut-kyc-temp
blob.temp.deletion.hours=120000
blob.generate.sas.token=true
blob.sas.token.duration.minutes=2
blob.sas.document.token.duration.minutes=15
profile.picture.max.size.bytes=3000000
phone.number.pdf.max.size.bytes=15000000
temp.file.delete.delay.hours=4
is.production=false
################################################################################################################
#Security
################################################################################################################
admin.token.validity.minutes=1440
token.validity.minutes=1440
token.key=bqOYpUpLPypkDlXDUzbUFrzHR0zR43RNnYTGPI/sQYkXotDkTIgcS25wO52taS21rvWeYaKx1gw2hwveQdGuQQ==
token.encryption.key=KJRBGONENSGVCNDQ
token.encryption.init.vector=eio382h4tbiuboda
blockage.duration.minutes=60
blockage.number.of.trials=5
summer.time=true
################################################################################################################
spring.mvc.dispatch-options-request=true
################################################################################################################
#Azimut
################################################################################################################
azimut.url=https://api.azimut.innsandbox.com/api
azimut.fund.images.url=http://api.azimut.innsandbox.com/storage
#azimut.bcc.mail=<EMAIL>
azimut.bcc.mail=<EMAIL>
################################################################################################################
#TeaComputers
################################################################################################################
tea.computers.url=http://41.187.88.188/FITSAPI/Api/fund
tea.computers.eport.url=http://41.187.88.188/EPortApi
tea.computers.key=TEABO
tea.computers.username=TEABO
tea.computers.password=TEABO
tea.computers.eportfolio.key=EPORTFOLIO
tea.computers.eportfolio.username=EPORTFOLIO
tea.computers.eportfolio.password=EPORTFOLIO
tea.computers.job.delay.seconds=180
trading.path=/azimut-0.0.1-SNAPSHOT/api/azimut/trading/
otp.path=/azimut-0.0.1-SNAPSHOT/api/otp/
login.path=/azimut-0.0.1-SNAPSHOT/api/authenticate/
################################################################################################################
#eNROLL
################################################################################################################
enroll.url=https://enrollstg.nasps.org.eg:7400/OnBoarding
enroll.tenant.id=b3908e2a-298c-4a3f-a036-506918fda317
enroll.tenant.secret=bfe275e4-b081-46e9-94ab-eb97ea2903d1
enroll.client.id=8dd71abd-3e8e-4099-80c5-6b4635b8c4b0
enroll.client.secret=314935c8-d06e-44a1-a0ea-0656d78f6da1
################################################################################################################
#Gate ID
################################################################################################################
gateid.url=https://ekyc-backend.gateid.ai
gateid.username=<EMAIL>
gateid.password=Nv5ewFTYzBEqB7i
gateid.project.id=5
################################################################################################################
#Paymob
################################################################################################################
paymob.url=https://accept.paymob.com
paymob.order.url=https://accept.paymobsolutions.com
paymob.api.key=ZXlKaGJHY2lPaUpJVXpVeE1pSXNJblI1Y0NJNklrcFhWQ0o5LmV5SmpiR0Z6Y3lJNklrMWxjbU5vWVc1MElpd2ljSEp2Wm1sc1pWOXdheUk2TVRBME1EWXpNU3dpYm1GdFpTSTZJbWx1YVhScFlXd2lmUS5QRTVLTjlLLWtvNnF2N2I3WVZFT1VyNEp6THNkMEZOd2Y2TWdGclhWTWpwS1NLcDV6RS1tcVRaNWhJMkVPTHFXQzFZRm9rRVJTTTJmR2VRdlhzU1lmUQ==
paymob.public.key=egy_pk_test_FNt9T06D7x3hVAAP2cW9fyulAB9EdX6h
paymob.secret.key=egy_sk_test_f767ce91ce648ad8f81710831a19a8d2597fc5f228e0cc799d6ed2df008d2f5f
paymob.wallet.integration.id=5169068
paymob.hmac=11EB2F10CDCFDF6174B209A2BD2316B1
paymob.callback.url=https://azimut-test-be999a89e116.herokuapp.com/api/paytabs/paymobCallback
################################################################################################################
#Paytabs
################################################################################################################
paytabs.url=https://secure-egypt.paytabs.com
paytabs.profile.id=122103
paytabs.merchant.id=64951
paytabs.server.key=SLJNKTZ69D-J6KZGRL22B-9KZGDKZK2L
paytabs.client.key=C6KM7G-GKGV6G-K9TRBM-GH7VTM
paytabs.mobile.server.key=SDJNKTZ6KD-J6KZGRL2GR-KWHDZBJHRW
paytabs.mobile.client.key=CPKM7G-GKTG6G-K9TRBM-H9NBBQ
paytabs.callback.url=https://azimut-test-be999a89e116.herokuapp.com/api/paytabs/callback
paytabs.return.url=https://azinvest.azimut.eg/payment-status
################################################################################################################
#Firebase
################################################################################################################
firebase.url=https://identitytoolkit.googleapis.com:443/v1/accounts
firebase.web.key=AIzaSyCYNtILg6bgkGE1OMDr4mUn6YV9UN4hDUk
################################################################################################################
#Victory Link SMS (VL)
################################################################################################################
vl.url=https://umvas.vlserv.com
vl.sms.url=https://smsvas.vlserv.com/KannelSending/service.asmx/SendSMS
vl.sender=AzimutEgypt
vl.username=AzimutOTP
vl.password=e29Dg9JCfW
vl.service=AzimutEgyptOTP
################################################################################################################
#eZagel SMS 
################################################################################################################
ezagel.url=https://www.ezagel.com/portex_ws/service.asmx/Send_SMSWithoutMsg_ID
ezagel.sender=Azimut
ezagel.username=Azimut
ezagel.password=7tZEYS)%T2
ezagel.service=AzimutEgyptOTP
################################################################################################################
#Twilio SMS
################################################################################################################
twilio.account.sid=**********************************
twilio.auth.token=88dbf6ec9ee7a86257c9acaa8a7a8564
twilio.service.sid=VA6ff9893bbfeb3847915be4adc3b3b825
################################################################################################################
#Mail
################################################################################################################
spring.mail.host=email-smtp.eu-west-1.amazonaws.com
spring.mail.port=587
mail.from=<EMAIL>
spring.mail.username=AKIA2R3XKDECGHRYZI7Z
spring.mail.password=BMC9FjEfKXQRV7xtjo144bOUvbHMFqgswDgYhdwp+9S2
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
################################################################################################################
#Configuring ehcache.xml
################################################################################################################
spring.cache.jcache.config=classpath:ehcache.xml
################################################################################################################
#Pagination
################################################################################################################
page.size=10
################################################################################################################
#Social Login
################################################################################################################
spring.security.oauth2.client.registration.google.clientId= 92069906716-9la3jom0uo82pe1r8a782utkpn44ogbm.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.clientSecret=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
spring.security.oauth2.client.registration.google.scope=email, profile
spring.security.oauth2.client.registration.facebook.clientId=959851142049147
spring.security.oauth2.client.registration.facebook.clientSecret=********************************
spring.security.oauth2.client.registration.facebook.scope=email, public_profile



app.auth.tokenSecret=04ca023b39512e46d0c2cf4b48d5aac61d34302994c87ed4eff225dcf3b0a218739f3897051a057f9b846a69ea2927a587044164b7bae5e1306219d50b588cb1
app.auth.tokenExpirationMsec=864000000
app.cors.allowedOrigins=http://localhost:8090