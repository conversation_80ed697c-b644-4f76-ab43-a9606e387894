package innovitics.azimut.security;

import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import innovitics.azimut.security.AdminHeaderFilter;;

public class AdminHeaderFilterConfig {
      @Bean
    public FilterRegistrationBean<AdminHeaderFilter> adminHeaderFilter() {
        FilterRegistrationBean<AdminHeaderFilter> registrationBean = new FilterRegistrationBean<>();
        registrationBean.setFilter(new AdminHeaderFilter());
        // Define the URL patterns that this filter should apply to
        registrationBean.addUrlPatterns("/admin/*");
        return registrationBean;
    }
}
