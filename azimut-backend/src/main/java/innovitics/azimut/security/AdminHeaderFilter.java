package innovitics.azimut.security;

import org.springframework.stereotype.Component;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

public class AdminHeaderFilter extends HttpFilter {
  @Override
  protected void doFilter(HttpServletRequest request, HttpServletResponse response, FilterChain chain)
      throws ServletException {
    String isAdmin = request.getHeader("isadmin");

    if (isAdmin == null || !"1".equals(isAdmin)) {
      response.setStatus(403); // Forbidden
      return;
    }

    try {
      chain.doFilter(request, response);
    } catch (Exception e) {
      throw new ServletException(e);
    }
  }

  @Override
  public void init(FilterConfig filterConfig) throws ServletException {
    super.init(filterConfig);
  }
}
