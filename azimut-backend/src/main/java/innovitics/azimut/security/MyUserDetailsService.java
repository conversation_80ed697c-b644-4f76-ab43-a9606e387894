package innovitics.azimut.security;

import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessservices.BusinessUserService;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class MyUserDetailsService implements UserDetailsService {

  @Autowired
  private BusinessUserService businessUserService;

  @Override
  public BusinessUserHolder loadUserByUsername(String userPhone) throws UsernameNotFoundException {

    BusinessUser businessUser = new BusinessUser();
    try {
      businessUser = this.businessUserService.getByUserPhone(userPhone);
    } catch (BusinessException exception) {

      MyLogger.logStackTrace(exception);
    }

    return new BusinessUserHolder(businessUser.getUserPhone(), " ", new ArrayList<>(), businessUser);
  }

}
