package innovitics.azimut.repositories.kyc;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;

import innovitics.azimut.models.kyc.Review;

@Repository
public interface ReviewDynamicRepository
    extends JpaRepository<Review, Long>, EntityGraphJpaSpecificationExecutor<Review> {

  @Query(value = "select page_id from reviews where page_order in (select min(page_order) from reviews where user_id=? and result=? and deleted_at is null) and user_id =? LIMIT 1", nativeQuery = true)
  Long getIdOfThePageWithLeastOrder(Long userId, long resultId, Long secondUserId);

  @Query(value = "update reviews set deleted_at=sysdate() where page_id=? and user_id=? and deleted_at is null", nativeQuery = true)
  @Modifying
  @Transactional
  void deleteOldReviewsForThePage(Long pageId, Long userId);

  @Query(value = "update reviews set deleted_at=sysdate() where page_id is null and user_id=? and deleted_at is null", nativeQuery = true)
  @Modifying
  @Transactional
  void deleteOldReviewsForTheUser(Long userId);

  @Query(value = "update reviews JOIN kyc_reasons ON reviews.reason_id = kyc_reasons.id set reviews.deleted_at=sysdate() where reviews.page_id is null and reviews.account_id is null and reviews.user_id=? and reviews.deleted_at is null and kyc_reasons.reason_type=? ", nativeQuery = true)
  @Modifying
  @Transactional
  void deleteFraOldReviewsForTheUser(Long userId, String reasonType);

  int countByUserIdAndDeletedAtIsNull(Long userId);
}
