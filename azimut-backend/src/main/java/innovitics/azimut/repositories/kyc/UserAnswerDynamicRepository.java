package innovitics.azimut.repositories.kyc;

import java.util.List;
import java.util.Optional;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.EntityGraph.EntityGraphType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;

import innovitics.azimut.models.kyc.UserAnswer;

public interface UserAnswerDynamicRepository extends JpaRepository<UserAnswer, Long>,
    EntityGraphJpaSpecificationExecutor<UserAnswer> {

  @EntityGraph(value = "UserAnswer.details", attributePaths = { "answerId", "countryPhoneCode", "answerValue",
      "answerType", "documentName", "documentSize", "documentUrl", "documentSubDirectory",
      "countryCode" }, type = EntityGraphType.FETCH)
  Optional<UserAnswer> findById(Long id);

  @Query(value = "update user_answers set deleted_at=sysdate() where page_id=? and user_id=? and deleted_at is null", nativeQuery = true)
  @Modifying
  @Transactional
  void deleteOldUserAnswersForThePage(Long pageId, Long userId);

  @Query(value = "Select distinct question_id from user_answers where deleted_at is null and question_id is not null and user_id= ?", nativeQuery = true)
  List<Long> getDistinctQuestionIds(Long userId);

  @Query(value = "select * from ((select q.id from questions q inner join kyc_pages kp on "
      + "is_answer_mandatory=1 "
      + "and q.page_id =kp.id "
      + "and kp.user_id_type=? "
      + ")"
      + "UNION ALL "
      + "(select distinct question_id from user_answers ua "
      + "inner join  kyc_pages kp on "
      + "user_id=? and "
      + "kp.id =ua.page_id "
      + "and ua.question_id is not null "
      + "inner join questions q on q.page_id =kp.id "
      + "and ua.question_id =q.id "
      + "and q.is_answer_mandatory =1 "
      + "and ua.deleted_at is null "
      + "))a order by id ", nativeQuery = true)
  List<Long> getDistinctAnsweredQuestionIdsAndDefinitionQuestionIds(Long userIdType, Long userId);

  @Query(value = "update user_answers set deleted_at=sysdate() where user_id=? and deleted_at is null", nativeQuery = true)
  @Modifying
  @Transactional
  void deleteOldUserAnswers(Long userId);

}
