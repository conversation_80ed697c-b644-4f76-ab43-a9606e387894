package innovitics.azimut.repositories.fund;

import java.util.List;

import innovitics.azimut.models.Nav;
import innovitics.azimut.repositories.AbstractRepository;

public class NavRepositoryImpl extends AbstractRepository<Nav> implements NavRepositoryCustom {

  @Override
  public List<Nav> getByJoinedTeacomputerIds() {
    return (List<Nav>) this.generateQuery("select n.* from navs n  inner join"
        + "(select fund_id,teacomputer_id ,max(created_at) max_date from navs group by fund_id,teacomputer_id)x"
        + " on n.fund_id=x.fund_id"
        + " and n.teacomputer_id=x.teacomputer_id"
        + " and n.created_at = x.max_date"
        + " order by fund_id ,n.created_at", Nav.class, null).getResultList();
  }

  @Override
  public List<Nav> getByDatesAndTeacomputerIds(String datesAndTeacomputerIds) {
    return (List<Nav>) this.generateQuery(
        "select distinct * from navs where"
            + "("
            + datesAndTeacomputerIds
            + ")",
        Nav.class, null).getResultList();
  }

  @Override
  public List<Nav> getFirstBeforeDate(String date, Long fundId) {
    return (List<Nav>) this.generateQuery(
        " select * from navs p2 where p2.date = ( " + //
            "        SELECT MAX(date)  " + //
            "        FROM navs  " + //
            "        WHERE date <= '" + date + "' " + //
            "        and fund_id = " + fundId + " " + //
            "    ) " + //
            "    and p2.fund_id = " + fundId,
        Nav.class, null).getResultList();
  }

  @Override
  public List<Nav> getByDatesAndDatesBefore(List<String> dates, Long fundId) {
    StringBuffer datesTable = new StringBuffer();
    for (int i = 0; i < dates.size(); i++) {
      if (i == 0)
        datesTable.append(" (SELECT '" + dates.get(i) + "' As target_date ");
      else
        datesTable.append("UNION ALL SELECT '" + dates.get(i) + "'");
    }
    datesTable.append(") td ");
    return (List<Nav>) this.generateQuery(""
        + "SELECT p1.* " + //
        "FROM  " + datesTable + //
        "JOIN navs p1 " + //
        "ON p1.date = td.target_date and fund_id = " + fundId + " " + //

        "UNION ALL " + //

        "SELECT p2.* " + //
        "FROM  " + datesTable + //
        "JOIN navs p2 " + //
        "ON  p2.date = ( " + //
        "        SELECT MAX(date)  " + //
        "        FROM navs  " + //
        "        WHERE date < td.target_date " + //
        "        and fund_id = " + fundId + " " + //
        "    ) " + //
        "    and fund_id = " + fundId +
        " order by date", Nav.class, null).getResultList();
  }

}
