package innovitics.azimut.repositories.teacomputers;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;

import innovitics.azimut.models.teacomputers.Country;

@Repository
public interface CountryDynamicRepository
    extends JpaRepository<Country, Long>, EntityGraphJpaSpecificationExecutor<Country> {

  Country findByCountryId(Long id);

  Country findBySystemCountryCode(Long id);

  Country findByEnglishCountryName(String name);
}
