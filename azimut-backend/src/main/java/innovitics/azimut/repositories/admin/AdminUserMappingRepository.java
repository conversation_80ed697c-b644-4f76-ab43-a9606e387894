package innovitics.azimut.repositories.admin;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.admin.AdminUserMapping;

@Repository
public interface AdminUserMappingRepository extends JpaRepository<AdminUserMapping, Long> {

  List<AdminUserMapping> findByAdminUserId(Long adminUserId);

  List<AdminUserMapping> findByUserId(Long userId);

  List<AdminUserMapping> findByCreatedById(Long createdById);

  AdminUserMapping findByAdminUserIdAndUserId(Long adminUserId, Long userId);

  boolean existsByAdminUserIdAndUserId(Long adminUserId, Long userId);

  boolean existsByAdminUserId(Long adminUserId);

  void deleteByAdminUserIdAndUserId(Long adminUserId, Long userId);

  void deleteByAdminUserIdAndUserIdIn(Long adminUserId, List<Long> userIds);
}
