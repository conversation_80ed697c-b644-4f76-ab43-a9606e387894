package innovitics.azimut.repositories.popup;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;

import innovitics.azimut.models.Popup;

public interface PopupRepository
    extends JpaRepository<Popup, Long>, EntityGraphJpaSpecificationExecutor<Popup> {

  @Query(value = "update popups set is_read=1 where id=?", nativeQuery = true)
  @Modifying
  @Transactional
  void readPopup(Long id);

}
