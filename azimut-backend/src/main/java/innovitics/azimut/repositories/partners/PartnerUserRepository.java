package innovitics.azimut.repositories.partners;

import innovitics.azimut.models.partner.PartnerUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface PartnerUserRepository extends JpaRepository<PartnerUser, Long>, JpaSpecificationExecutor<PartnerUser> {
    PartnerUser findByPartnerIdAndExternalId(Long partnerId, String externalId);
}