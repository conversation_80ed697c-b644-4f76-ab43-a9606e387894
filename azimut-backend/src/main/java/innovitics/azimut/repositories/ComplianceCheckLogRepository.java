package innovitics.azimut.repositories;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.ComplianceCheckLog;

@Repository
public interface ComplianceCheckLogRepository extends JpaRepository<ComplianceCheckLog, Long> {
  List<ComplianceCheckLog> findByTargetUserIdOrderByCreatedAtDesc(Long targetUserId);
}
