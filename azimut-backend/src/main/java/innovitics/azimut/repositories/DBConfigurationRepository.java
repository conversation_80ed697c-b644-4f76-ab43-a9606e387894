package innovitics.azimut.repositories;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.DBConfiguration;

@Repository
public interface DBConfigurationRepository
    extends JpaRepository<DBConfiguration, Long>, JpaSpecificationExecutor<DBConfiguration> {

  @Query(value = "select value_string from db_configuration where key_string= ?", nativeQuery = true)
  String findValue(String key);

  @Query(value = "update db_configuration set value_string=? where key_string= ?", nativeQuery = true)
  @Modifying
  @Transactional
  void updateValue(String value, String key);

}
