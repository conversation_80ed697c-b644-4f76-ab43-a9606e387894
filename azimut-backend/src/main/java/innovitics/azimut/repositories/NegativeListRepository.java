package innovitics.azimut.repositories;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import com.cosium.spring.data.jpa.entity.graph.repository.EntityGraphJpaSpecificationExecutor;

import innovitics.azimut.models.NegativeList;

@Repository
public interface NegativeListRepository extends JpaRepository<NegativeList, Long>,
    EntityGraphJpaSpecificationExecutor<NegativeList> {

  List<NegativeList> findByListSource(String listSource);

  // Advanced fuzzy search methods
  @Query("SELECT n FROM NegativeList n WHERE " +
      "LOWER(n.fullName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
      "LOWER(n.alias) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
  List<NegativeList> findByNameFuzzySearch(@Param("searchTerm") String searchTerm);

  @Query("SELECT n FROM NegativeList n WHERE " +
      "LOWER(n.passportNumber) LIKE LOWER(CONCAT('%', :passportNumber, '%')) OR " +
      "LOWER(n.nationalId) LIKE LOWER(CONCAT('%', :nationalId, '%'))")
  List<NegativeList> findByIdentificationDocuments(@Param("passportNumber") String passportNumber,
      @Param("nationalId") String nationalId);

  @Query(value = "SELECT * FROM negative_list n WHERE n.national_id REGEXP :nationalId", nativeQuery = true)
  List<NegativeList> findByNationalIdLast4(@Param("nationalId") String nationalId);

  @Query("SELECT n FROM NegativeList n WHERE " +
      "LOWER(n.nationality) LIKE LOWER(CONCAT('%', :nationality, '%')) AND " +
      "n.dateOfBirth LIKE CONCAT('%', :dateOfBirth, '%')")
  List<NegativeList> findByNationalityAndDateOfBirth(@Param("nationality") String nationality,
      @Param("dateOfBirth") String dateOfBirth);

  // Method to find duplicates based on key fields
  @Query("SELECT n FROM NegativeList n WHERE " +
      "n.fullName = :fullName AND " +
      "(:nationality IS NULL OR n.nationality = :nationality) AND " +
      "(:passportNumber IS NULL OR n.passportNumber = :passportNumber) AND " +
      "(:nationalId IS NULL OR n.nationalId = :nationalId)")
  List<NegativeList> findByKeyFields(@Param("fullName") String fullName,
      @Param("nationality") String nationality,
      @Param("passportNumber") String passportNumber,
      @Param("nationalId") String nationalId);

  // Count methods for statistics
  @Query("SELECT COUNT(n) FROM NegativeList n WHERE n.listSource = :listSource")
  Long countByListSource(@Param("listSource") String listSource);

  @Query("SELECT n.listSource, COUNT(n) FROM NegativeList n GROUP BY n.listSource")
  List<Object[]> countByListSourceGrouped();

  @Query("SELECT n.listType, COUNT(n) FROM NegativeList n GROUP BY n.listType")
  List<Object[]> countByListTypeGrouped();
  
  @Query("SELECT n FROM NegativeList n WHERE DATE(n.createdAt) = CURRENT_DATE")
  List<NegativeList> findByCreatedAtToday();
}
