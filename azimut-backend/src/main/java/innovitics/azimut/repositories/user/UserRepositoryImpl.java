package innovitics.azimut.repositories.user;

import java.util.List;

import innovitics.azimut.models.user.User;
import innovitics.azimut.repositories.AbstractRepository;

public class UserRepositoryImpl extends AbstractRepository<User> implements UserRepositoryCustom {

  @Override
  public List<User> findByParam(String param) {
    return (List<User>) this.generateQuery("Select * from app_users where password= ?", User.class, param)
        .getResultList();
  }

  @Override
  public User findByUserPhone(String userPhone) {
    return (User) this.generateQuery("Select * from app_users where user_phone= ?", User.class, userPhone)
        .getSingleResult();
  }

  @Override
  public User findByUserPhoneAndPassword(String userPhone, String password) {
    return (User) this.generateQuery("Select * from app_users where user_phone= ? and password= binary ?", User.class,
        userPhone, password).getSingleResult();
  }

  @Override
  public User findByPhoneNumber(String phoneCode, String phoneNumber) {
    return (User) this.generateQuery("Select * from app_users where country_phone_code=? and phone_number=?",
        User.class, phoneCode, phoneNumber).getSingleResult();
  }

  @Override
  public User findByPhoneCodePhoneNumberPassword(String phoneCode, String phoneNumber, String password) {
    return (User) this
        .generateQuery("Select * from app_users where country_phone_code=? and phone_number=? and password= binary ?",
            User.class, phoneCode, phoneNumber, password)
        .getSingleResult();
  }

  @Override
  public List<User> findVerifiedUsersWithNoInvestment() {
    String query = "SELECT * from app_users u " +
    // "LEFT JOIN user_investments ui ON ui.user_id = u.id " +
        "WHERE (u.is_synchronized = 1 OR u.is_old = 1)" +
        // "AND (ui.id IS NULL or ui.updated_at < 20251002)" +
        "AND u.deleted_at IS NULL";

    return (List<User>) this.generateQuery(query, User.class).getResultList();
  }

}
