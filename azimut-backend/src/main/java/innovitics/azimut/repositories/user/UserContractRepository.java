package innovitics.azimut.repositories.user;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.user.User;
import innovitics.azimut.models.user.UserContract;
import innovitics.azimut.utilities.crosslayerenums.ContractType;

@Repository
public interface UserContractRepository extends JpaRepository<UserContract, Long> {

  List<UserContract> findByUser(User user);

  @Query("SELECT uc FROM UserContract uc WHERE uc.user.id = :userId")
  List<UserContract> findByUserId(@Param("userId") Long userId);

  long countByUserId(Long userId);

  long countByUserIdAndContractType(Long userId, ContractType contractType);

  @Query("SELECT uc FROM UserContract uc WHERE uc.user.id = :userId AND uc.contractType = :contractType")
  List<UserContract> findByUserIdAndContractType(@Param("userId") Long userId,
      @Param("contractType") ContractType contractType);
}
