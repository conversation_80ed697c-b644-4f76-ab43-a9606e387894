package innovitics.azimut.repositories.user;

import java.util.List;
import java.util.Optional;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.user.UserOldPhone;

@Repository
public interface UserOldPhoneRepository
    extends JpaRepository<UserOldPhone, Long>, JpaSpecificationExecutor<UserOldPhone> {

  int countByUserIdAndDeletedAtIsNull(Long userId);

  void deleteByUserId(Long userId);

  List<UserOldPhone> findByUserId(Long id);

  Optional<UserOldPhone> findByUserPhone(String oldUserPhone);

  Optional<UserOldPhone> findByUserPhoneAndDeletedAtIsNull(String oldUserPhone);

  @Query(value = "update user_old_phones set deleted_at=sysdate() where user_id=? and deleted_at is null", nativeQuery = true)
  @Modifying
  @Transactional
  void deleteOldUserPhones(Long userId);

}
