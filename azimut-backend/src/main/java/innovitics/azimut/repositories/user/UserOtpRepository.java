package innovitics.azimut.repositories.user;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.user.UserOTP;

@Repository
public interface UserOtpRepository extends JpaRepository<UserOTP, Long>, JpaSpecificationExecutor<UserOTP> {

  List<UserOTP> findTop1ByUserPhone(String userPhone);

  UserOTP findTopByUserPhoneOrderByIdDesc(String userPhone);

  List<UserOTP> findLastByUserIdOrderByIdDesc(Long userId);

  @Query(value = "update user_otp set deleted_at=sysdate() where user_id=? and deleted_at is null", nativeQuery = true)
  @Modifying
  @Transactional
  void deleteOldUserOTPs(Long userId);
}
