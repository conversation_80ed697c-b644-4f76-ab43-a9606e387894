package innovitics.azimut.repositories.user;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.user.UserInvestment;

@Repository
public interface UserInvestmentRepository
    extends JpaRepository<UserInvestment, Long>, JpaSpecificationExecutor<UserInvestment> {

  Optional<UserInvestment> findByUserId(Long id);

}
