package innovitics.azimut.repositories.user;

import java.util.List;

import javax.transaction.Transactional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.user.User;

@Repository
public interface UserRepository extends JpaRepository<User, Long>, UserRepositoryCustom {
  public User findByUserId(String userId);

  List<User> findByIdIn(List<Long> ids);

  List<User> findByUserIdIn(List<String> ids);

  @Query("SELECT u FROM User u WHERE u.userId IN :userIds AND u.deletedAt IS NULL")
  List<User> findNonDeletedUsersByUserIdIn(@Param("userIds") List<String> userIds);

  // Set azimutAmlMatches to zero for all users
  @Query(value = "update app_users set azimut_aml_matches=0", nativeQuery = true)
  @Modifying
  @Transactional
  public void updateAzimutAmlMatchesToZeroForAllUsers();

  @Query("SELECT u FROM User u WHERE u.deletedAt IS NULL")
  List<User> findNonDeletedUsers();

  @Query("SELECT u FROM User u WHERE u.deletedAt IS NULL and azimutAmlMatches IS NULL")
  List<User> findNonUpdatedAmlUsers();

}
