package innovitics.azimut.repositories.user;

import java.util.List;
import java.util.Optional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import innovitics.azimut.models.user.FitsUser;

@Repository
public interface FitsUserRepository extends JpaRepository<FitsUser, Long> {
  List<FitsUser> findByMobile(String mobile);

  Optional<FitsUser> findByIdTypeAndIdNumber(String idType, String idNumber);

  Optional<FitsUser> findByIdNumber(String idNumber);

  Optional<FitsUser> findByClientId(Long id);

  @Query("SELECT f FROM FitsUser f WHERE " +
      "(:firstName IS NULL OR f.firstName LIKE %:firstName%) AND " +
      "(:lastName IS NULL OR f.lastName LIKE %:lastName%) AND " +
      "(:mobile IS NULL OR f.mobile LIKE %:mobile%) AND " +
      "(:idNumber IS NULL OR f.idNumber LIKE %:idNumber%)")
  Page<FitsUser> findBySearchCriteria(
      @Param("firstName") String firstName,
      @Param("lastName") String lastName,
      @Param("mobile") String mobile,
      @Param("idNumber") String idNumber,
      Pageable pageable);
}