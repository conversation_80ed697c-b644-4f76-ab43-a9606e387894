package innovitics.azimut.service;

import java.util.Date;
import java.util.List;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.models.user.UserContract;
import innovitics.azimut.repositories.user.UserContractRepository;
import innovitics.azimut.utilities.crosslayerenums.ContractType;

@Service
public class UserContractService {

  @Autowired
  private UserContractRepository userContractRepository;

  public Optional<UserContract> getUserContractById(Long id) {
    return userContractRepository.findById(id);
  }

  public UserContract createUserContract(UserContract userContract) {
    userContract.setCreatedAt(new Date());
    userContract.setUpdatedAt(new Date());
    return userContractRepository.save(userContract);
  }

  public UserContract updateUserContract(UserContract userContractDetails) {
    UserContract userContract = userContractRepository.findById(userContractDetails.getId())
        .orElseThrow(() -> new RuntimeException("UserContract not found with id: " + userContractDetails.getId()));

    userContract.setUser(userContractDetails.getUser());
    userContract.setSignedPdf(userContractDetails.getSignedPdf());
    userContract.setPdfPath(userContractDetails.getPdfPath());
    userContract.setPdfSignedAt(userContractDetails.getPdfSignedAt());
    userContract.setUpdatedAt(new Date());

    return userContractRepository.save(userContract);
  }

  public void deleteUserContract(Long id) {
    UserContract userContract = userContractRepository.findById(id)
        .orElseThrow(() -> new RuntimeException("UserContract not found with id: " + id));
    userContractRepository.delete(userContract);
  }

  // Additional methods specific to UserContract can be added here
  public List<UserContract> getUserContractsByUserId(Long userId) {
    return userContractRepository.findByUserId(userId);
  }

  public long countByUserId(Long userId) {
    return userContractRepository.countByUserId(userId);
  }

  public long countByUserIdAndContractType(Long userId, ContractType contractType) {
    return userContractRepository.countByUserIdAndContractType(userId, contractType);
  }

  public List<UserContract> getByUserIdAndContractType(Long userId, ContractType contractType) {
    return userContractRepository.findByUserIdAndContractType(userId, contractType);
  }
}
