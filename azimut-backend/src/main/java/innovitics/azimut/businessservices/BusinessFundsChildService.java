package innovitics.azimut.businessservices;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.funds.BusinessFundPrice;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.Dividend;
import innovitics.azimut.models.Fund;
import innovitics.azimut.models.Nav;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersGetFundPricesApi;
import innovitics.azimut.services.DividendService;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class BusinessFundsChildService extends BusinessFundsService {

  @Autowired
  protected TeaComputersGetFundPricesApi fundPricesApi;

  private @Autowired DividendService dividendService;

  public List<BusinessFundPrice> updateFundPrices() throws IntegrationException, BusinessException {
    MyLogger.info("BusinessFund Child Service:::::");
    List<BusinessFundPrice> businessFundPrices = new ArrayList<BusinessFundPrice>();
    List<Nav> insertedNavs = new ArrayList<Nav>();
    List<Nav> availableNavs = new ArrayList<Nav>();
    try {
      var fundPricesRequest = this.fundPricesApi.generateRequest();
      var fundPricesResponse = this.fundPricesApi.getData(fundPricesRequest);
      businessFundPrices = this.fundPricesApi.getBusinessFundPrices(fundPricesResponse);

      availableNavs = this.getByDatesAndTeacomputerIds(businessFundPrices);
      insertedNavs.addAll(this.checkAndInsert(businessFundPrices, availableNavs));

      this.navService.batchInsert(insertedNavs);
    } catch (Exception exception) {
      if (exception instanceof IntegrationException)
        throw this.exceptionHandler.handleIntegrationExceptionAsBusinessException((IntegrationException) exception,
            ErrorCode.FAILED_TO_INTEGRATE);
      else
        throw this.handleBusinessException((Exception) exception, ErrorCode.OPERATION_NOT_PERFORMED);
    }
    return this.convertNavListToBusinessFundPricesList(insertedNavs);
  }

  private List<Nav> getByDatesAndTeacomputerIds(List<BusinessFundPrice> businessFundPrices) {
    List<Long> teacomputerIds = new ArrayList<Long>();
    List<String> dates = new ArrayList<String>();
    for (BusinessFundPrice businessFundPrice : businessFundPrices) {
      teacomputerIds.add(businessFundPrice.getFundId());
      dates.add(DateUtility.changeStringDateFormat(businessFundPrice.getPriceDate(), new SimpleDateFormat("dd-MM-yyyy"),
          new SimpleDateFormat("yyyy-MM-dd")));
    }
    return this.navService.getByDatesAndTeacomputerIds(teacomputerIds, dates);
  }

  public void updateFundReturns() {
    var funds = this.fundService.getAllFunds();
    var dividends = this.dividendService.getAllDividends();
    // Grouping by fundId
    Map<Long, List<Dividend>> dividendsByFundId = dividends.stream()
        .collect(Collectors.groupingBy(Dividend::getFundId));

    for (Fund fund : funds) {
      if (NumberUtility.areDoubleValuesMatching(fund.getSinceInception(), 0d))
        continue;
      var firstPrice = navService.getFirstByFundId(fund.getId());
      var curPrice = navService.getLastByFundId(fund.getId());
      var lastYearPrice = navService.getNavOneYearAgo(fund.getId());
      var ytdBasePrice = navService.getNavAtBeginningOfYear(fund.getId());
      MyLogger.info("Fund Id " + fund.getId());
      MyLogger.info(curPrice.toString());
      MyLogger.info(firstPrice.toString());
      MyLogger.info(lastYearPrice.toString());
      MyLogger.info(ytdBasePrice.toString());
      Double ytd = 0d;
      Double oneYear = 1d;
      Double sinceInception = 1d;
      if (dividendsByFundId.containsKey(fund.getId())) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        List<String> inceptionDates = new ArrayList<>();
        for (Dividend dividend : dividendsByFundId.get(fund.getId())) {
          inceptionDates.add(simpleDateFormat.format(dividend.getDividendDate()));
        }
        var dividendNavs = navService.getNavsWithDividendsDates(inceptionDates, fund.getId());
        ytd = getDividendFundReturn(dividendNavs, dividendsByFundId.get(fund.getId()), ytdBasePrice, curPrice);
        oneYear = getDividendFundReturn(dividendNavs, dividendsByFundId.get(fund.getId()), lastYearPrice, curPrice);
        sinceInception = getDividendFundReturn(dividendNavs, dividendsByFundId.get(fund.getId()), firstPrice, curPrice);
      } else {
        ytd = 100 * (curPrice.getNav() - ytdBasePrice.getNav()) / ytdBasePrice.getNav();
        oneYear = 100 * (curPrice.getNav() - lastYearPrice.getNav()) / lastYearPrice.getNav();
        sinceInception = 100 * (curPrice.getNav() - firstPrice.getNav()) / firstPrice.getNav();
      }

      fund.setYtd(ytd);
      fund.setOneYear(oneYear);
      fund.setSinceInception(sinceInception);
      fundService.save(fund);
    }
  }

  Double getDividendFundReturn(List<Nav> dividendNavs, List<Dividend> dividends, Nav startPrice, Nav curPrice) {
    Double fundReturn = 1d;
    Double lastPrice = startPrice.getNav();
    for (int i = 0; i < dividendNavs.size(); i += 2) {
      if (dividendNavs.get(i).getDate().after(startPrice.getDate())) {
        fundReturn *= 1d + (dividendNavs.get(i).getNav() - lastPrice) / lastPrice;
        var dividend = getDividendByDate(dividends, dividendNavs.get(i + 1).getDate());
        lastPrice = dividendNavs.get(i).getNav() - dividend.getAmount();
      } else if (NumberUtility.areLongValuesMatching(dividendNavs.get(i).getId(), startPrice.getId())) {
        var dividend = getDividendByDate(dividends, dividendNavs.get(i + 1).getDate());
        lastPrice = startPrice.getNav() - dividend.getAmount();
      }
    }
    fundReturn *= 1d + (curPrice.getNav() - lastPrice) / lastPrice;
    return 100 * (fundReturn - 1);
  }

  Dividend getDividendByDate(List<Dividend> dividends, Date dateIn) {
    return dividends.stream().filter(r -> DateUtility.isSameDay(r.getDividendDate(), dateIn)).findFirst().get();
  }

  Set<Nav> checkAndInsert(List<BusinessFundPrice> businessFundPrices, List<Nav> availableNavs) {
    MyLogger.info("Business Fund Prices:::" + businessFundPrices.toString());
    MyLogger.info("Available Navs:::" + availableNavs.toString());
    List<Fund> funds = this.fundService.getAllFunds();
    Set<Nav> insertedNavs = new HashSet<Nav>();
    for (BusinessFundPrice businessFundPrice : businessFundPrices) {
      List<Nav> filteredNavs = new ArrayList<Nav>();
      List<Fund> filteredFunds = new ArrayList<Fund>();
      try {
        Date newPriceDate = (new SimpleDateFormat("dd-MM-yyyy")).parse(businessFundPrice.getPriceDate());
        filteredNavs = availableNavs.stream().filter(availableNav -> NumberUtility
            .areLongValuesMatching(availableNav.getTeacomputerId(), businessFundPrice.getFundId()) &&
            !DateUtility.areDatesDifferent(availableNav.getDate(), newPriceDate))
            .collect(Collectors.toList());
        filteredFunds = funds.stream()
            .filter(fund -> NumberUtility.areLongValuesMatching(fund.getTeacomputerId(), businessFundPrice.getFundId()))
            .collect(Collectors.toList());
        if (navListUtility.sizeIsOne(filteredNavs)) {
          Nav updatedNav = filteredNavs.get(0);
          updatedNav.setNav(businessFundPrice.getTradePrice());
          MyLogger.info("One match found::");
          this.navService.updateNav(updatedNav);
        } else if (navListUtility.isListEmptyOrNull(filteredNavs)) {
          MyLogger.info("No matches found::");
          if (fundListUtility.sizeIsOne(filteredFunds)) {
            insertedNavs.add(this.generateNavFromNavAndBusinessFund(filteredFunds.get(0).getId(), businessFundPrice));
          }
        } else {
          MyLogger.info("More than one match found::");
        }
      } catch (ParseException e) {
        MyLogger.logStackTrace(e);
      }
    }
    return insertedNavs;
  }

}
