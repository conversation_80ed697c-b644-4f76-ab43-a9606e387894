package innovitics.azimut.businessservices;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.validation.Validator;

import innovitics.azimut.businessmodels.kyc.BusinessReview;
import innovitics.azimut.businessmodels.kyc.BusinessUserAnswerSubmission;
import innovitics.azimut.businessmodels.user.AzimutAccount;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessmodels.user.BusinessUserOTP;
import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.businessutilities.SearchFilter;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.OTPFunctionality;
import innovitics.azimut.models.kyc.Review;
import innovitics.azimut.pdfgenerator.PdfFillerChild;
import innovitics.azimut.security.AES;
import innovitics.azimut.security.JwtUtil;
import innovitics.azimut.security.RecaptchaUtility;
import innovitics.azimut.services.TwilioService;
import innovitics.azimut.services.admin.AdminUserService;
import innovitics.azimut.services.payment.PaymentService;
import innovitics.azimut.services.user.UserService;
import innovitics.azimut.utilities.businessutilities.AzimutDataLookupUtility;
import innovitics.azimut.utilities.businessutilities.BusinessSearchOperation;
import innovitics.azimut.utilities.businessutilities.PaymentTransactionUtility;
import innovitics.azimut.utilities.businessutilities.PhoneNumberBlockageUtility;
import innovitics.azimut.utilities.businessutilities.ReviewUtility;
import innovitics.azimut.utilities.businessutilities.UserBlockageUtility;
import innovitics.azimut.utilities.businessutilities.UserUtility;
import innovitics.azimut.utilities.crosslayerenums.ContractMap;
import innovitics.azimut.utilities.crosslayerenums.KycStatus;
import innovitics.azimut.utilities.crosslayerenums.LiveNotificationNavigations;
import innovitics.azimut.utilities.crosslayerenums.Messages;
import innovitics.azimut.utilities.crosslayerenums.Navigations;
import innovitics.azimut.utilities.crosslayerenums.UserStep;
import innovitics.azimut.utilities.datautilities.ArrayUtility;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.ListUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.dbutilities.DatabaseConditions;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.SearchOperation;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.exceptionhandling.ExceptionHandler;
import innovitics.azimut.utilities.logging.FileUtility;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.utilities.mapping.UserMapper;
import innovitics.azimut.utilities.messaging.MessagingService;
import innovitics.azimut.utilities.messaging.email.EmailTemplateUtility;
import innovitics.azimut.utilities.messaging.push.PushNotificationUtility;
import innovitics.azimut.validations.Validation;

@Service
public abstract class AbstractBusinessService<T> extends BaseBusinessService {
  protected static final Logger logger = LoggerFactory.getLogger(AbstractBusinessService.class);
  @Autowired
  protected ExceptionHandler exceptionHandler;

  @Autowired
  ListUtility<T> listUtility;
  @Autowired
  Validation validation;
  @Autowired
  protected UserMapper userMapper;
  @Autowired
  protected FileUtility fileUtility;
  @Autowired
  protected UserUtility userUtility;
  @Autowired
  protected PaymentService paymentService;
  @Autowired
  protected UserBlockageUtility userBlockageUtility;
  @Autowired
  protected PhoneNumberBlockageUtility phoneNumberBlockageUtility;
  @Autowired
  protected PaymentTransactionUtility paymentTransactionUtility;
  @Autowired
  protected UserService userService;
  @Autowired
  AdminUserService adminUserService;
  @Autowired
  public ReviewUtility reviewUtility;
  @Autowired
  protected ListUtility<BusinessReview> reviewListUtility;
  @Autowired
  protected ListUtility<Review> baseReviewListUtility;
  @Autowired
  protected AzimutDataLookupUtility azimutDataLookupUtility;
  @Autowired
  protected PdfFillerChild pdfFillerChild;
  @Autowired
  protected MessagingService messagingService;
  @Autowired
  protected PushNotificationUtility pushNotificationUtility;
  @Autowired
  protected EmailTemplateUtility emailUtility;
  @Autowired
  protected ArrayUtility arrayUtility;
  @Autowired
  protected AES aes;

  protected @Autowired TwilioService twilioService;
  protected @Autowired RecaptchaUtility recaptchaUtility;

  protected BusinessException handleBusinessException(Exception exception, ErrorCode errorCode) {
    if (this.exceptionHandler.isNonTechnicalException(exception, errorCode))
      return this.exceptionHandler.handleAsBusinessException(exception, errorCode);

    else
      return this.exceptionHandler.handleAsBusinessException(exception, ErrorCode.OPERATION_NOT_PERFORMED);
  }

  public DatabaseConditions generateDatabaseConditions(BusinessSearchCriteria businessSearchCriteria) {
    MyLogger.info("Entering the Generate DB Conditions in the Abstract::::");
    DatabaseConditions databaseConditions = new DatabaseConditions();
    if (businessSearchCriteria != null) {
      if (businessSearchCriteria.getPageNumber() != null && businessSearchCriteria.getPageSize() != null) {
        databaseConditions.setPageRequest(PageRequest.of(businessSearchCriteria.getPageNumber() - 1,
            businessSearchCriteria.getPageSize()));
      }
      if (businessSearchCriteria.getPageNumber() != null && businessSearchCriteria.getPageSize() != null
          &&
          StringUtility.isStringPopulated(businessSearchCriteria.getSortingParam())) {
        if (BooleanUtility.isTrue(businessSearchCriteria.getAsc())) {
          Sort sort = Sort.by(businessSearchCriteria.getSortingParam()).ascending();
          databaseConditions.setPageRequest(PageRequest.of(businessSearchCriteria.getPageNumber() - 1,
              businessSearchCriteria.getPageSize(), sort));
        } else {
          Sort sort = Sort.by(businessSearchCriteria.getSortingParam()).descending();
          databaseConditions.setPageRequest(PageRequest.of(businessSearchCriteria.getPageNumber() - 1,
              businessSearchCriteria.getPageSize(), sort));
        }
      }
      if (this.arrayUtility.isArrayPopulated(businessSearchCriteria.getSearchesAndFilters())) {
        MyLogger.info("businessSearchCriteria:::" + businessSearchCriteria.toString());
        List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();

        for (int i = 0; i < businessSearchCriteria.getSearchesAndFilters().length; i++) {
          MyLogger.info(
              "Search And Filter:::" + businessSearchCriteria.getSearchesAndFilters()[i].toString());
          SearchFilter searchFilter = businessSearchCriteria.getSearchesAndFilters()[i];

          if (!StringUtility.isStringPopulated(searchFilter.getParentColumn())) {
            if (searchFilter.getId() != null) {
              searchCriteriaList.add(new SearchCriteria("id", searchFilter.getId(),
                  SearchOperation.EQUAL, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.SEARCH.getOperation())
                && StringUtility.isStringPopulated(searchFilter.getValue())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(), searchFilter.getValue(),
                  SearchOperation.LIKE, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.FILTER.getOperation())
                && this.arrayUtility.isArrayPopulated(searchFilter.getValues())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(),
                  this.arrayUtility.generateObjectListFromObjectArray(
                      /* this.generateLongArray( */searchFilter.getValues()/* ) */),
                  SearchOperation.IN, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.NOT_IN.getOperation())
                && this.arrayUtility.isArrayPopulated(searchFilter.getValues())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(),
                  this.arrayUtility.generateObjectListFromObjectArray(
                      /* this.generateLongArray( */searchFilter.getValues()/* ) */),
                  SearchOperation.NOT_IN, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.GREATER_THAN.getOperation())
                && StringUtility.isStringPopulated(searchFilter.getValue())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(), Integer.valueOf(searchFilter.getValue()),
                  SearchOperation.GREATER_THAN, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.LESS_THAN.getOperation())
                && StringUtility.isStringPopulated(searchFilter.getValue())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(), Integer.valueOf(searchFilter.getValue()),
                  SearchOperation.LESS_THAN, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.GT.getOperation())
                && StringUtility.isStringPopulated(searchFilter.getValue())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(), searchFilter.getValue(),
                  SearchOperation.AFTER, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.LT.getOperation())
                && StringUtility.isStringPopulated(searchFilter.getValue())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(), searchFilter.getValue(),
                  SearchOperation.BEFORE, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.NULL.getOperation())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(), "",
                  SearchOperation.IS_NULL, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.NOT_NULL.getOperation())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(), "",
                  SearchOperation.IS_NOT_NULL, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.INJECTED.getOperation())) {
              searchCriteriaList.add(new SearchCriteria(
                  StringUtility.isStringPopulated(searchFilter.getKey()) ? searchFilter.getKey() : "totalTopup",
                  this.arrayUtility.generateObjectListFromObjectArray(searchFilter.getValues()),
                  SearchOperation.INJECTED, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.ADMIN_USER.getOperation())) {
              searchCriteriaList.add(new SearchCriteria(
                  StringUtility.isStringPopulated(searchFilter.getKey()) ? searchFilter.getKey() : "totalTopup",
                  searchFilter.getValue(),
                  SearchOperation.ADMIN_USER, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.REVIEW_NOT_EXIST.getOperation())) {
              searchCriteriaList.add(new SearchCriteria(null, "",
                  SearchOperation.REVIEW_DOES_NOT_EXIST, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.REVIEW_EXIST.getOperation())) {
              searchCriteriaList.add(new SearchCriteria(null, "",
                  SearchOperation.REVIEW_EXIST, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.QUESTION_ANSWER.getOperation())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(), searchFilter.getValue(),
                  SearchOperation.QUESTION_ANSWER, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.VALIDATION.getOperation())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(), "",
                  SearchOperation.VALIDATION, null));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.OFFLINE.getOperation())) {
              searchCriteriaList.add(new SearchCriteria(null, "",
                  SearchOperation.OFFLINE, null));
            }
          } else {
            if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.SEARCH.getOperation())
                && StringUtility.isStringPopulated(searchFilter.getValue())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(), searchFilter.getValue(),
                  SearchOperation.PARENT_LIKE, searchFilter.getParentColumn()));
            } else if (StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.FILTER.getOperation())
                && this.arrayUtility.isArrayPopulated(searchFilter.getValues())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(),
                  this.arrayUtility.generateObjectListFromObjectArray(
                      /* this.generateLongArray( */searchFilter.getValues()/* ) */),
                  SearchOperation.PARENT_IN, searchFilter.getParentColumn()));
            }
          }

        }
        databaseConditions.setSearchCriteria(searchCriteriaList);
        MyLogger.info("SearchCriteria List::::" + searchCriteriaList.toString());
      }
    }

    return databaseConditions;
  }

  protected void validate(T businessEntity, Validator validator, String objectName) throws BusinessException {
    this.validation.validate(businessEntity, validator, objectName);
  }

  protected Long getAzimutUserTypeId(BusinessUser businessUser) throws BusinessException {
    try {
      return businessUser.getAzimutIdTypeId();
    } catch (Exception exception) {
      this.exceptionHandler.getNullIfNonExistent(exception);

    }
    return null;
  }

  protected void execute(BusinessUser tokenizedBusinessUser, UserMapper userMapper, Object object, String methodName,
      Object[] parameters, Class<?>[] paramterTypes) throws BusinessException {
    {
      this.userUtility.getValueUsingReflection(object, methodName, parameters, paramterTypes);
    }
  }

  public BusinessUser findUserById(Long id, boolean throwException) throws BusinessException {
    try {
      BusinessUser businessUser = new BusinessUser();
      businessUser = this.userMapper.convertBasicUnitToBusinessUnit(this.userService.findById(id));
      return businessUser;
    } catch (Exception exception) {
      if (throwException) {
        throw this.exceptionHandler.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
      } else {
        this.exceptionHandler.getNullIfNonExistent(exception);
        return null;
      }

    }
  }

  /*
   * Long[] generateLongArray(Object[] values)
   * {
   * Long[] longValueArray=new Long[values.length];
   *
   * for(int i=0;i<values.length;i++)
   * {
   * longValueArray[i]=Long.valueOf((String)values[i]);
   * }
   *
   * return longValueArray;
   * }
   */

  List<BusinessReview> getNonKycReviews(Long userId, String language) {
    List<Review> reviews = this.reviewUtility.getReviews(userId, null);

    return this.reviewUtility.getFirstReview(reviews, language);
  }

  List<BusinessReview> getFraReviews(Long userId, String language) {
    List<Review> reviews = this.reviewUtility.getFraReviewsByUserId(userId);

    return this.reviewUtility.convertBasicListToBusinessList(reviews, language);
  }

  List<BusinessReview> getKycReviews(Long userId, String language) {
    List<Review> reviews = this.reviewUtility.getTotalKYCReviewsByUserId(userId);
    return this.reviewUtility.getFirstReview(reviews, language);
  }

  protected void setUserKycStatusToPendingAndUserStepToUnderReviewThenEdit(BusinessUser businessUser)
      throws BusinessException {
    businessUser.setUserStep(UserStep.UNDER_REVIEW.getStepId());
    this.editUser(businessUser);
  }

  boolean checkForRejectedReviewsAndUpdateUser(BusinessUser businessUser,
      BusinessUserAnswerSubmission businessUserAnswerSubmission) throws BusinessException {
    boolean userEdited = false;
    if (NumberUtility.areLongValuesMatching(businessUserAnswerSubmission.getNextPageId(),
        businessUserAnswerSubmission.getPageId())) {
      List<Review> reviews = this.reviewUtility.getAnyRejectedReviews(businessUser.getId());
      if (reviewUtility.reviewListUtility.isListPopulated(reviews)) {
        this.setUserKycStatusToPendingAndUserStepToUnderReviewThenEdit(businessUser);
        userEdited = true;
      }
    }
    return userEdited;
  }

  boolean checkForRejectedReviews(BusinessUser businessUser) throws BusinessException {
    boolean result = false;
    List<Review> reviews = this.reviewUtility.getAnyRejectedReviews(businessUser.getId());
    if (reviewUtility.reviewListUtility.isListPopulated(reviews)) {

      result = true;
    }
    return result;
  }

  void handleReviews(BusinessUser businessUser, Long pageId, String language) throws BusinessException {
    MyLogger.info("Handling Reviews:::");
    if (!NumberUtility.areIntegerValuesMatching(businessUser.getKycStatus(), KycStatus.FIRST_TIME.getStatusId())) {
      List<BusinessReview> reviewsToBeRemoved = this.reviewUtility.convertBasicListToBusinessList(
          this.reviewUtility.getReviews(businessUser.getId(), pageId), language);
      this.adjustUser(businessUser, reviewsToBeRemoved);
    }
  }

  void adjustUser(BusinessUser businessUser, List<BusinessReview> reviewsToBeRemoved) throws BusinessException {
    this.reviewUtility.softDeleteReviews(reviewsToBeRemoved);

    // List<Review>
    // rejectedReviews=this.reviewUtility.getAnyRejectedReviews(businessUser.getId());

    // if(this.reviewUtility.reviewListUtility.isListPopulated(rejectedReviews))
    // {
    businessUser.setKycStatus(KycStatus.UPDATED.getStatusId());
    // }
    /*
     * else
     * {
     * businessUser.setKycStatus(KycStatus.PENDING.getStatusId());
     * }
     */
    this.editUser(businessUser);
  }

  public void addAtTCAndSendPushNotification(BusinessUser businessUser,
      BusinessClientDetailsService businessClientDetailsService, BusinessKYCPageService businessKYCPageService,
      String language) throws BusinessException, IntegrationException, IOException {
    Messages appUserMessages = Messages.KYC_ACCEPTED_SIGNED_AT_AZIMUT;
    String navigation = Navigations.KYC_APPROVED.getNavigationId();
    String liveNavigation = LiveNotificationNavigations.KYC_APPROVED.getNavigationId();
    Integer nextUserStep = businessUser.getNextUserStep();

    Integer kycStatus = KycStatus.APPROVED.getStatusId();
    Long firstPageId = businessUser.getFirstPageId();
    if (NumberUtility.areIntegerValuesMatching(businessUser.getKycStatus(), KycStatus.APPROVED.getStatusId())
        || NumberUtility.areIntegerValuesMatching(businessUser.getKycStatus(),
            KycStatus.REJECTED.getStatusId())) {
      if (NumberUtility.areIntegerValuesMatching(businessUser.getKycStatus(), KycStatus.APPROVED.getStatusId())) {
        if (NumberUtility.areIntegerValuesMatching(businessUser.getContractMap(),
            ContractMap.AT_AZIMUT.getMapId())) {
          this.messagingService.send(pushNotificationUtility, businessUser, appUserMessages, language,
              navigation, liveNavigation,
              this.userUtility.generateUserMap(nextUserStep, firstPageId, null, null));
        }
      } else if (NumberUtility.areIntegerValuesMatching(businessUser.getKycStatus(),
          KycStatus.REJECTED.getStatusId())) {
        appUserMessages = Messages.KYC_REJECTED;
        navigation = Navigations.KYC_REJECTED.getNavigationId();
        liveNavigation = LiveNotificationNavigations.KYC_REJECTED.getNavigationId();
        this.userUtility.deleteOldUserOTPs(businessUser.getId());
        nextUserStep = this.reviewUtility.calculateUserStepUnderReview(businessUser);
        firstPageId = this.reviewUtility.reviewService.getIdOfThePageWithLeastOrder(businessUser.getId());
        kycStatus = KycStatus.REJECTED.getStatusId();
        this.messagingService.send(pushNotificationUtility, businessUser, appUserMessages, language, navigation,
            liveNavigation, this.userUtility.generateUserMap(nextUserStep, firstPageId, kycStatus, null));
      }
    }
  }

  void finishDocumentAndUserStepAndOTP(BusinessUser businessUser)
      throws BusinessException {
    businessUser.setUserStep(UserStep.FINISHED.getStepId());
    businessUser.setKycStatus(KycStatus.PENDING.getStatusId());
    this.editUser(businessUser);
  }

  BusinessUserOTP getUserOTP(BusinessUser businessUser, OTPFunctionality functionality) throws BusinessException {
    BusinessUserOTP businessUserOTP = this.userUtility.findOTPByUserId(businessUser.getId(), functionality);
    if (businessUserOTP == null) {
      throw new BusinessException(ErrorCode.OTP_NONE_EXISTING);
    }
    return businessUserOTP;
  }

  BusinessUserOTP getPhoneOTP(String phone) throws BusinessException {
    BusinessUserOTP businessUserOTP = this.userUtility.findLatestOTPByPhone(phone);
    if (businessUserOTP == null) {
      throw new BusinessException(ErrorCode.OTP_NONE_EXISTING);
    }
    return businessUserOTP;
  }

  public void verifyTwilioOtp(BusinessUserOTP userOtp, String otp) throws BusinessException {
    if (!twilioService.verify(userOtp.getUserPhone(), otp)) {
      recaptchaUtility.annotateOTPAssessment(userOtp, false);
      throw new BusinessException(ErrorCode.INVALID_OTP, HttpStatus.UNPROCESSABLE_ENTITY);
    }
    recaptchaUtility.annotateOTPAssessment(userOtp, true);
    userOtp.setOtp(otp);
    this.userUtility.upsertOTP(userOtp, ErrorCode.OTP_NOT_SAVED, true);
  }

  public Entry<MediaType, ByteArrayResource> showFile(BusinessUser tokenizedBusinessUser, JwtUtil jwtUtil,
      String documentId, String documentType, boolean tokenized, String token)
      throws ClassNotFoundException, IOException, BusinessException {

    if (tokenized) {
      if (storageService.validateFileToken(token, documentType, documentId)) {

        return this.getFileUsingIdAndType(tokenizedBusinessUser, token, documentId, documentType);
      } else {
        throw new BusinessException(ErrorCode.FAILED_TO_VALIDATE_TOKEN);
      }
    } else {
      return this.getFileUsingIdAndType(tokenizedBusinessUser, token, documentId, documentType);
    }

  }

  public Entry<MediaType, ByteArrayResource> showTempFile(BusinessUser tokenizedBusinessUser, String fileName,
      String path, String token) throws ClassNotFoundException, IOException, BusinessException {
    if (storageService.validateTempFileToken(token, path, fileName)) {
      Entry<MediaType, byte[]> fileBytesWithExtension = this.storageService.getFileWithAbsolutePath(path);
      return this.getSingleEntry(fileBytesWithExtension.getKey(),
          new ByteArrayResource(fileBytesWithExtension.getValue()));
    } else {
      throw new BusinessException(ErrorCode.FAILED_TO_VALIDATE_TOKEN);
    }
  }

  Entry<MediaType, ByteArrayResource> getFileUsingIdAndType(BusinessUser tokenizedBusinessUser, String token,
      String documentId, String documentType) throws ClassNotFoundException, BusinessException, IOException {
    Entry<MediaType, byte[]> fileBytesWithExtension = this.storageService
        .getFileUsingIdAndType(tokenizedBusinessUser, token, documentId, documentType);
    return this.getSingleEntry(fileBytesWithExtension.getKey(),
        new ByteArrayResource(fileBytesWithExtension.getValue()));
  }

  Entry<MediaType, ByteArrayResource> getSingleEntry(MediaType extension, ByteArrayResource resource) {
    Map<MediaType, ByteArrayResource> singleEntryMap = new HashMap<MediaType, ByteArrayResource>();
    singleEntryMap.put(extension, resource);
    return singleEntryMap.entrySet().iterator().next();
  }

  public void handleUserAdditionAtTc(BusinessClientDetailsService businessClientDetailsService,
      BusinessUser businessUser) throws BusinessException {
    AzimutAccount azimutAccount = new AzimutAccount();
    azimutAccount.setId(businessUser.getId());
    try {
      businessClientDetailsService.addAccountAtTeaComputers(azimutAccount.toAddAccountDto(), businessUser);
      if (!NumberUtility.areIntegerValuesMatching(businessUser.getKycStatus(),
          KycStatus.APPROVED.getStatusId())) {
        businessUser.setKycStatus(KycStatus.APPROVED.getStatusId());
      }
      businessUser.setIsSynchronized(true);
      this.editUser(businessUser);

    } catch (Exception exception) {
      // businessUser.setKycStatus(KycStatus.PENDING.getStatusId());
      businessUser.setIsSynchronized(false);
      this.editUser(businessUser);
      throw this.exceptionHandler.handleException(exception);
    }
  }

  public BusinessUser verifyUser(BusinessUser tokenizedBusinessUser) {
    MyLogger.info("Editing the user:::");
    tokenizedBusinessUser.setIsVerified(true);
    try {
      this.editUser(tokenizedBusinessUser);
      MyLogger.info("User Editted:::");
    } catch (Exception exception) {
      this.handleBusinessException(exception, ErrorCode.USER_NOT_UPDATED);
    }
    return tokenizedBusinessUser;
  }

  public void verifyUserSendEmailAndPushNotification(BusinessUser businessUser,
      String language) throws BusinessException, IOException {
    verifyUserSendEmailAndPushNotification(businessUser, language, true);
  }

  public void verifyUserSendEmailAndPushNotification(BusinessUser businessUser,
      String language, boolean force)
      throws BusinessException, IOException {
    String navigation = Navigations.KYC_APPROVED.getNavigationId();
    String liveNavigation = LiveNotificationNavigations.KYC_APPROVED.getNavigationId();

    this.verifyUser(businessUser);
    if (businessUser != null && (businessUser.getSignedPdf() == null || force)) {
      if (NumberUtility.areIntegerValuesMatching(businessUser.getKycStatus(), KycStatus.APPROVED.getStatusId())) {

        this.messagingService.send(emailUtility, businessUser, Messages.KYC_ATTACHMENT, language,
            this.userUtility.getSignedPdf(businessUser, StringUtility.CONTRACT_DOCUMENT_NAME),
            true);
        this.userUtility.deleteOldUserOTPs(businessUser.getId());
        try {
          this.messagingService.send(pushNotificationUtility, businessUser, Messages.KYC_ACCEPTED,
              StringUtility.ENGLISH, navigation, liveNavigation,
              this.userUtility.generateUserMap(null, null, KycStatus.APPROVED.getStatusId(), true));
        } catch (Exception exception) {
          MyLogger.logStackTrace(exception);
          MyLogger.info("Failed:: to push Notification");
        }

      } else {
        throw new BusinessException(ErrorCode.USER_UNDER_REVIEW);
      }
    }
  }
}
