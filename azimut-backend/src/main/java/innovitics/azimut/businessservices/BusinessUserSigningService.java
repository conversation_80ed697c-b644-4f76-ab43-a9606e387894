package innovitics.azimut.businessservices;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.services.kyc.ReviewService;
import innovitics.azimut.services.kyc.UserAnswerSubmissionService;
import innovitics.azimut.utilities.crosslayerenums.KycStatus;
import innovitics.azimut.utilities.crosslayerenums.UserStep;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class BusinessUserSigningService extends AbstractBusinessService<BusinessUser> {

  protected @Autowired ReviewService reviewService;
  protected @Autowired UserAnswerSubmissionService userAnswerSubmissionService;
  protected @Autowired BusinessClientDetailsService businessClientDetailsService;

  public void postFraActions(BusinessUser user) throws BusinessException {
    try {
      Thread.sleep(2000); // sleep 2 seconds for the data to be saved
    } catch (InterruptedException e) {
      MyLogger.logStackTrace(e);
    }

    var rejectedReviews = reviewService.getAnyRejectedReviewsByUserId(user.getId());
    MyLogger.info("Rejected Reviews Size In Controller:: " + rejectedReviews.size());
    if (rejectedReviews != null && rejectedReviews.size() == 0 &&
        NumberUtility.areLongValuesMatching(user.getAzimutAccount().getClientAML(), StringUtility.CLIENT_AML)
        && NumberUtility.areIntegerValuesMatching(user.getKycStatus(), KycStatus.REJECTED.getStatusId())) {
      MyLogger.info("Setting KYC status to PENDING for user: " + user.getId());
      user.setKycStatus(KycStatus.PENDING.getStatusId());
      this.editUser(user);
    }
  }

  public void autoAcceptActions(BusinessUser user, boolean throwError) throws BusinessException {
    var rejectedReviews = reviewService.getAnyRejectedReviewsByUserId(user.getId());
    MyLogger.info("FRA Reviews Size In Controller:: " + rejectedReviews.size() + ", kyc: " + user.getKycStatus()
        + ", autoApproved: " + userAnswerSubmissionService.isAutoApproved(user));
    if ((NumberUtility.areIntegerValuesMatching(user.getKycStatus(), KycStatus.PENDING.getStatusId())
        || NumberUtility.areIntegerValuesMatching(user.getKycStatus(), KycStatus.APPROVED.getStatusId()))
        && rejectedReviews.size() == 0
        && userAnswerSubmissionService.isAutoApproved(user)) {
      user.setKycStatus(KycStatus.APPROVED.getStatusId());
      user.setUserStep(UserStep.REVIEWED.getStepId());
      try {
        // this.handleUserAdditionAtTc(businessClientDetailsService, user);
        user.setIsVerified(true);
      } catch (Exception e) {
        MyLogger.error("Error while handling user addition at TC: " + e.getMessage());
        if (throwError)
          throw e;
      }
      this.editUser(user);
    }
  }

}
