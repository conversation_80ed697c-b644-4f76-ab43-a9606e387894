package innovitics.azimut.businessservices;

import java.io.BufferedInputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.AbstractMap.SimpleEntry;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.NoSuchElementException;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.BusinessTransaction;
import innovitics.azimut.businessmodels.funds.BusinessClientFund;
import innovitics.azimut.businessmodels.funds.BusinessFundPrice;
import innovitics.azimut.businessmodels.funds.BusinessFundTransaction;
import innovitics.azimut.businessmodels.kyc.BusinessReview;
import innovitics.azimut.businessmodels.user.AzimutAccount;
import innovitics.azimut.businessmodels.user.BusinessAzimutClient;
import innovitics.azimut.businessmodels.user.BusinessAzimutDataLookup;
import innovitics.azimut.businessmodels.user.BusinessClientBankAccountDetails;
import innovitics.azimut.businessmodels.user.BusinessClientCashBalance;
import innovitics.azimut.businessmodels.user.BusinessCompanyBankAccount;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessmodels.user.EportfolioDetail;
import innovitics.azimut.controllers.kyc.DTOs.SaveClientBankAccountDetailsTemporarilyDto;
import innovitics.azimut.controllers.users.DTOs.AddAccountDto;
import innovitics.azimut.controllers.users.DTOs.CheckAccountDto;
import innovitics.azimut.controllers.users.DTOs.GetAzimutClientBankAccountsDto;
import innovitics.azimut.controllers.users.DTOs.GetAzimutLookUpDataDto;
import innovitics.azimut.controllers.users.DTOs.GetBalanceAndFundOwnershipDto;
import innovitics.azimut.controllers.users.DTOs.GetRequestStatementDto;
import innovitics.azimut.controllers.users.DTOs.HoldClientBankAccountDto;
import innovitics.azimut.controllers.users.DTOs.RemoveClientBankAccountDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.partner.PartnerUser;
import innovitics.azimut.repositories.partners.PartnerRepository;
import innovitics.azimut.repositories.teacomputers.ClientBankAccountDynamicRepository;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersAddAccountApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersAddClientBankAccountApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersCheckAccountApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersGetClientBankAccountsApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersGetClientCashBalanceApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersGetClientFundsApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersGetClientTransactionListApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersGetCompanyBankAccountsApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersGetEportfolioApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersGetFundTransactionsApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersGetReportApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersHoldBankAccountApi;
import innovitics.azimut.rest.entities.teacomputers.AddAccountRequest;
import innovitics.azimut.rest.entities.teacomputers.AddClientBankAccountRequest;
import innovitics.azimut.services.FundService;
import innovitics.azimut.services.azimut.AzimutDataLookUpService;
import innovitics.azimut.services.teacomputer.TeaComputerService;
import innovitics.azimut.services.user.ReferralCodeService;
import innovitics.azimut.utilities.businessutilities.AzimutDataLookupUtility;
import innovitics.azimut.utilities.businessutilities.CashTransactionSortCompare;
import innovitics.azimut.utilities.businessutilities.FundTransactionSortCompare;
import innovitics.azimut.utilities.businessutilities.Sorting;
import innovitics.azimut.utilities.crosslayerenums.BankAccountStatus;
import innovitics.azimut.utilities.crosslayerenums.CurrencyType;
import innovitics.azimut.utilities.crosslayerenums.OrderStatus;
import innovitics.azimut.utilities.crosslayerenums.PaymentGateway;
import innovitics.azimut.utilities.crosslayerenums.ReviewResult;
import innovitics.azimut.utilities.crosslayerenums.UserStep;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.ListUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.utilities.mapping.FundPriceMapper;
import innovitics.azimut.validations.validators.azimutclient.GetAzimutEntityLookup;
import innovitics.azimut.validations.validators.azimutclient.GetBalanceAndTransactions;
import innovitics.azimut.validations.validators.azimutclient.SaveClientBankAccountTemporarily;

@SuppressWarnings("unchecked")
@Service
public class BusinessClientDetailsService extends AbstractBusinessService<BusinessAzimutClient> {

  @Autowired
  ListUtility<BusinessTransaction> businessTransactionListUtility;
  @Autowired
  ListUtility<BusinessFundPrice> fundPricesListUtility;
  @Autowired
  ListUtility<BusinessClientFund> clientFundListUtility;
  @Autowired
  ListUtility<BusinessClientCashBalance> clientCashBalanceListUtility;
  @Autowired
  ListUtility<BusinessClientBankAccountDetails> businessClientBankAccountDetailsListUtility;
  @Autowired
  CashTransactionSortCompare cashTransactionSortCompare;
  @Autowired
  FundTransactionSortCompare fundTransactionSortCompare;
  @Autowired
  GetBalanceAndTransactions getBalanceAndTransactions;
  @Autowired
  AzimutDataLookUpService azimutDataLookUpService;
  @Autowired
  AzimutDataLookupUtility azimutDataLookupUtility;
  @Autowired
  TeaComputerService teaComputerService;
  @Autowired
  SaveClientBankAccountTemporarily saveClientBankAccountTemporarily;
  @Autowired
  GetAzimutEntityLookup getAzimutEntityLookup;
  @Autowired
  FundService fundService;
  @Autowired
  ReferralCodeService referralCodeService;
  @Autowired
  FundPriceMapper fundPriceMapper;
  @Autowired
  ListUtility<EportfolioDetail> eportfolioDetailListUtility;
  @Autowired
  ListUtility<BusinessCompanyBankAccount> companyBankAccountListUtility;
  @Autowired
  ClientBankAccountDynamicRepository clientBankAccountDynamicRepository;
  @Autowired
  TeaComputersAddAccountApi addAccountApi;
  @Autowired
  TeaComputersAddClientBankAccountApi addClientBankAccountApi;
  @Autowired
  TeaComputersCheckAccountApi checkAccountApi;
  @Autowired
  TeaComputersGetClientCashBalanceApi cashBalanceApi;
  @Autowired
  TeaComputersGetClientBankAccountsApi bankAccountsApi;
  @Autowired
  TeaComputersGetClientFundsApi fundsApi;
  @Autowired
  TeaComputersGetClientTransactionListApi transactionListApi;
  @Autowired
  TeaComputersGetCompanyBankAccountsApi companyBankAccountsApi;
  @Autowired
  TeaComputersGetEportfolioApi eportfolioApi;
  @Autowired
  TeaComputersGetFundTransactionsApi fundTransactionsApi;
  @Autowired
  TeaComputersGetReportApi getReportApi;
  @Autowired
  TeaComputersHoldBankAccountApi holdBankAccountApi;
  @Autowired
  private PartnerRepository partnerRepository;

  public BusinessAzimutClient getBalanceAndFundOwnership(BusinessUser tokenizedBusinessUser, String language,
      GetBalanceAndFundOwnershipDto businessAzimutClient) throws BusinessException, IntegrationException {
    BusinessAzimutClient responseBusinessAzimutClient = new BusinessAzimutClient();
    this.validation.validate(businessAzimutClient, getBalanceAndTransactions, BusinessAzimutClient.class.getName());

    responseBusinessAzimutClient = this.getBalanceAndTransactions(businessAzimutClient, tokenizedBusinessUser,
        language);
    this.getFundOwnership(businessAzimutClient, tokenizedBusinessUser, responseBusinessAzimutClient, language);
    // this.getBalance(businessAzimutClient, tokenizedBusinessUser,
    // responseBusinessAzimutClient);

    return responseBusinessAzimutClient;

  }

  public BusinessAzimutClient getFundOwnership(GetBalanceAndFundOwnershipDto businessAzimutClient,
      BusinessUser tokenizedBusinessUser, BusinessAzimutClient responseBusinessAzimutClient, String language)
      throws BusinessException, IntegrationException {

    List<BusinessClientFund> businessClientFunds = new ArrayList<BusinessClientFund>();
    businessClientFunds = this.getClientFundsOrFund(tokenizedBusinessUser, businessAzimutClient, false, language)
        .getBusinessClientFunds();

    if (clientFundListUtility.sizeIsOne(businessClientFunds)) {
      responseBusinessAzimutClient.setOwned(true);
      responseBusinessAzimutClient.setBusinessClientFunds(businessClientFunds);
    } else {
      responseBusinessAzimutClient.setOwned(false);
    }

    return responseBusinessAzimutClient;
  }

  public BusinessAzimutClient getBalanceAndTransactions(GetBalanceAndFundOwnershipDto businessAzimutClient,
      BusinessUser tokenizedBusinessUser, String language) throws BusinessException, IntegrationException {

    BusinessAzimutClient responseBusinessAzimutClient = new BusinessAzimutClient();
    this.validation.validateUser(businessAzimutClient.getId(), tokenizedBusinessUser);
    this.validation.validate(businessAzimutClient, getBalanceAndTransactions, BusinessAzimutClient.class.getName());
    responseBusinessAzimutClient.setSorting(businessAzimutClient.getSorting());
    this.getBalance(businessAzimutClient, tokenizedBusinessUser, responseBusinessAzimutClient, language);
    this.getTransactions(tokenizedBusinessUser, responseBusinessAzimutClient, language);
    return this.beautifyBalanceAndTransactionsBusinessAzimutClient(responseBusinessAzimutClient);
  }

  public BusinessAzimutClient getBalance(GetBalanceAndFundOwnershipDto businessAzimutClient,
      BusinessUser tokenizedBusinessUser,
      BusinessAzimutClient responseBusinessAzimutClient, String language)
      throws BusinessException, IntegrationException {

    List<BusinessClientCashBalance> businessClientCashBalances = new ArrayList<BusinessClientCashBalance>();
    List<BusinessClientCashBalance> businessClientCashBalancesWithCurrencies = new ArrayList<BusinessClientCashBalance>();
    if (tokenizedBusinessUser != null && StringUtility.isStringPopulated(tokenizedBusinessUser.getUserId())) {

      try {
        String partnerUsername = null;
        if (businessAzimutClient.getPartnerId() != null) {
          var partner = partnerRepository.getById(businessAzimutClient.getPartnerId());
          partnerUsername = partner.getUserName();
        }
        var newCashBalanceRequest = this.cashBalanceApi.prepareRequest(tokenizedBusinessUser, partnerUsername);
        var cashBalanceResponse = this.cashBalanceApi.getData(newCashBalanceRequest);

        businessClientCashBalances = this.cashBalanceApi.createListBusinessEntityFromResponse(cashBalanceResponse,
            language);

        if (clientCashBalanceListUtility.isListEmptyOrNull(businessClientCashBalances)) {
          MyLogger.info("Empty List");
          businessClientCashBalances.add(new BusinessClientCashBalance(CurrencyType.EGYPTIAN_POUND, language));
          businessClientCashBalances.add(new BusinessClientCashBalance(CurrencyType.US_DOLLAR, language));
        } else if (clientCashBalanceListUtility.sizeIsOne(businessClientCashBalances)) {
          MyLogger.info("One Element List");
          BusinessClientCashBalance existingBusinessClientCashBalance = businessClientCashBalances.get(0);
          if (existingBusinessClientCashBalance != null && NumberUtility.areLongValuesMatching(
              existingBusinessClientCashBalance.getCurrencyID().longValue(), CurrencyType.EGYPTIAN_POUND.getTypeId())) {
            businessClientCashBalances.add(new BusinessClientCashBalance(CurrencyType.US_DOLLAR, language));
          } else if (existingBusinessClientCashBalance != null && NumberUtility.areLongValuesMatching(
              existingBusinessClientCashBalance.getCurrencyID().longValue(), CurrencyType.US_DOLLAR.getTypeId())) {
            businessClientCashBalances.add(new BusinessClientCashBalance(CurrencyType.EGYPTIAN_POUND, language));
          }
        }

      } catch (Exception exception) {
        businessClientCashBalances = clientCashBalanceListUtility.handleExceptionAndReturnEmptyList(exception);
        businessClientCashBalances.add(new BusinessClientCashBalance(CurrencyType.EGYPTIAN_POUND, language));
        businessClientCashBalances.add(new BusinessClientCashBalance(CurrencyType.US_DOLLAR, language));
      }

      if (clientCashBalanceListUtility.isListPopulated(businessClientCashBalances)
          && businessAzimutClient.getCurrencyId() != null) {
        for (BusinessClientCashBalance businessClientCashBalance : businessClientCashBalances) {
          if (businessClientCashBalance != null && NumberUtility
              .areLongValuesMatching(businessClientCashBalance.getCurrencyID(), businessAzimutClient.getCurrencyId())) {
            businessClientCashBalancesWithCurrencies.add(businessClientCashBalance);
          }
        }
        responseBusinessAzimutClient.setBusinessClientCashBalances(businessClientCashBalancesWithCurrencies);
      } else {
        responseBusinessAzimutClient.setBusinessClientCashBalances(businessClientCashBalances);
      }

    } else {
      businessClientCashBalances.add(new BusinessClientCashBalance(CurrencyType.EGYPTIAN_POUND, language));
      businessClientCashBalances.add(new BusinessClientCashBalance(CurrencyType.US_DOLLAR, language));
      if (clientCashBalanceListUtility.isListPopulated(businessClientCashBalances)
          && businessAzimutClient.getCurrencyId() != null) {
        for (BusinessClientCashBalance businessClientCashBalance : businessClientCashBalances) {
          if (businessClientCashBalance != null && NumberUtility
              .areLongValuesMatching(businessClientCashBalance.getCurrencyID(), businessAzimutClient.getCurrencyId())) {
            businessClientCashBalancesWithCurrencies.add(businessClientCashBalance);
          }
        }
        responseBusinessAzimutClient.setBusinessClientCashBalances(businessClientCashBalancesWithCurrencies);
      } else {
        responseBusinessAzimutClient.setBusinessClientCashBalances(businessClientCashBalances);
      }

    }

    return responseBusinessAzimutClient;

  }

  public BusinessAzimutClient getTransactions(BusinessUser tokenizedBusinessUser,
      BusinessAzimutClient responseBusinessAzimutClient, String language)
      throws BusinessException, IntegrationException {
    if (tokenizedBusinessUser != null && StringUtility.isStringPopulated(tokenizedBusinessUser.getUserId())) {
      try {
        var transactionListRequest = this.transactionListApi.prepareRequest(tokenizedBusinessUser);
        var transactionListResponses = this.transactionListApi.getData(transactionListRequest);
        List<BusinessTransaction> businessTransactions = this.transactionListApi
            .getBusinessTransactionsFromResponses(transactionListResponses, language);
        List<BusinessTransaction> businessPaymentTransactions = this.getPaymentTransactions(tokenizedBusinessUser,
            language);

        if (businessTransactionListUtility.isListPopulated(businessTransactions)
            && businessTransactionListUtility.isListPopulated(businessPaymentTransactions)) {
          businessTransactions.addAll(businessPaymentTransactions);
          responseBusinessAzimutClient.setTransactions(businessTransactions);
        } else if (businessTransactionListUtility.isListPopulated(businessTransactions)
            && businessTransactionListUtility.isListEmptyOrNull(businessPaymentTransactions)) {
          responseBusinessAzimutClient.setTransactions(businessTransactions);
        } else if (businessTransactionListUtility.isListEmptyOrNull(businessTransactions)
            && businessTransactionListUtility.isListPopulated(businessPaymentTransactions)) {
          responseBusinessAzimutClient.setTransactions(businessPaymentTransactions);
        } else if (businessTransactionListUtility.isListEmptyOrNull(businessTransactions)
            && businessTransactionListUtility.isListEmptyOrNull(businessPaymentTransactions)) {
          responseBusinessAzimutClient.setLastTransactionDate("No transactions yet.");
          responseBusinessAzimutClient.setTransactions(new ArrayList<BusinessTransaction>());
        }
      } catch (Exception exception) {
        responseBusinessAzimutClient.setLastTransactionDate("No transactions yet.");
        responseBusinessAzimutClient
            .setTransactions(businessTransactionListUtility.handleExceptionAndReturnEmptyList(exception));
      }
    } else {
      responseBusinessAzimutClient.setLastTransactionDate("No transactions yet.");
      responseBusinessAzimutClient.setTransactions(new ArrayList<BusinessTransaction>());

    }
    return responseBusinessAzimutClient;
  }

  public List<BusinessTransaction> getPaymentTransactions(BusinessUser tokenizedBusinessUser, String language)
      throws BusinessException, IntegrationException {
    List<BusinessTransaction> businessTransactions = new ArrayList<BusinessTransaction>();
    if (tokenizedBusinessUser != null && StringUtility.isStringPopulated(tokenizedBusinessUser.getUserId())) {
      businessTransactions = this.paymentTransactionUtility
          .getUserPaymentTransactionsAndConvertThemToBusinessTransactions(tokenizedBusinessUser,
              PaymentGateway.PAYTABS, language);
      businessTransactions.addAll(this.paymentTransactionUtility
          .getUserPaymentTransactionsAndConvertThemToBusinessTransactions(tokenizedBusinessUser,
              PaymentGateway.PAYMOB, language));
    }

    return businessTransactions;
  }

  public BusinessAzimutClient getBankAccountsWithDetails(GetAzimutClientBankAccountsDto businessAzimutClient,
      BusinessUser tokenizedBusinessUser, boolean isList, Long accountId)
      throws BusinessException, IntegrationException {
    BusinessAzimutClient responseBusinessAzimutClient = new BusinessAzimutClient();
    this.validation.validateUser(businessAzimutClient.getId(), tokenizedBusinessUser);
    if (tokenizedBusinessUser != null && StringUtility.isStringPopulated(tokenizedBusinessUser.getUserId())) {
      List<BusinessClientBankAccountDetails> totalBankAccountsWithCurrencies = new ArrayList<BusinessClientBankAccountDetails>();
      List<BusinessClientBankAccountDetails> totalBankAccounts = new ArrayList<BusinessClientBankAccountDetails>();

      try {
        var bankAccountsRequest = this.bankAccountsApi.prepareRequest(businessAzimutClient, tokenizedBusinessUser);
        var bankAccountResponse = this.bankAccountsApi.getData(bankAccountsRequest);
        List<BusinessClientBankAccountDetails> teacomputersBankAccounts = this.bankAccountsApi
            .getBanksAccountsFromResponse(bankAccountResponse);

        if (isList) {
          if (BooleanUtility.isFalse(businessAzimutClient.getIsActive())) {
            BusinessClientBankAccountDetails[] localClientTeacomputersBankAccounts = this.azimutDataLookupUtility
                .getClientBankAccountData(tokenizedBusinessUser);

            if (arrayUtility.isArrayPopulated(localClientTeacomputersBankAccounts)) {
              totalBankAccounts.addAll(Arrays.asList(localClientTeacomputersBankAccounts));
            }
            totalBankAccounts.addAll(teacomputersBankAccounts);
          } else {
            for (BusinessClientBankAccountDetails businessClientBankAccountDetails : teacomputersBankAccounts) {
              if (businessClientBankAccountDetails != null && NumberUtility.areLongValuesMatching(
                  (Long.valueOf(businessClientBankAccountDetails.getAccountStatus().intValue())),
                  BankAccountStatus.ACTIVE.getStatusId()))
                totalBankAccounts.add(businessClientBankAccountDetails);
            }

          }
          // teacomputersBankAccounts=this.restManager.getClientBankAccountsMapper.wrapBaseBusinessEntity(isList,
          // this.prepareClientBankAccountDetailsInputs(businessAzimutClient,tokenizedBusinessUser,isList),
          // null).getDataList();
        } else if (!isList) {
          if (BooleanUtility.isTrue(businessAzimutClient.getIsLocal())) {
            if (accountId != null) {
              responseBusinessAzimutClient.setBankAccountDetails(
                  this.azimutDataLookupUtility.getKYCClientBankAccountData(tokenizedBusinessUser, accountId));
            } else {
              responseBusinessAzimutClient.setBankAccountDetails(
                  this.azimutDataLookupUtility.getClientBankAccountData(tokenizedBusinessUser)[0]);
            }
          } else {
            // responseBusinessAzimutClient.setBankAccountDetails(this.restManager.getClientBankAccountsMapper.wrapBaseBusinessEntity(isList,
            // this.prepareClientBankAccountDetailsInputs(businessAzimutClient,tokenizedBusinessUser,isList),
            // null).getData());
            responseBusinessAzimutClient.setBankAccountDetails(teacomputersBankAccounts.get(0));
          }
        }
      } catch (Exception exception) {
        if (!this.exceptionHandler.checkIfIntegrationExceptinWithSpecificErrorCode(exception, ErrorCode.INVALID_CLIENT)
            && BooleanUtility.isFalse(tokenizedBusinessUser.getIsVerified())) {
          throw this.exceptionHandler.handleException(exception);
        }
      }

      if (businessClientBankAccountDetailsListUtility.isListPopulated(totalBankAccounts)
          && businessAzimutClient.getCurrencyId() != null) {
        for (BusinessClientBankAccountDetails businessClientBankAccountDetails : totalBankAccounts) {
          if (businessClientBankAccountDetails != null && NumberUtility.areLongValuesMatching(
              businessClientBankAccountDetails.getCurrencyId(), businessAzimutClient.getCurrencyId())) {
            totalBankAccountsWithCurrencies.add(businessClientBankAccountDetails);
          }
        }
        responseBusinessAzimutClient.setBankList(totalBankAccountsWithCurrencies);
      } else {
        responseBusinessAzimutClient.setBankList(totalBankAccounts);
      }
    } else {
      responseBusinessAzimutClient.setBankList(new ArrayList<BusinessClientBankAccountDetails>());
    }
    return responseBusinessAzimutClient;
  }

  public BusinessAzimutClient holdClientBankAccount(BusinessUser tokenizedBusinessUser,
      HoldClientBankAccountDto businessAzimutClient) throws BusinessException {
    BusinessAzimutClient responseBusinessAzimutClient = new BusinessAzimutClient();
    this.validation.validateUser(businessAzimutClient.getId(), tokenizedBusinessUser);
    try {
      if (BooleanUtility.isFalse(businessAzimutClient.getIsLocal())) {
        // responseBusinessAzimutClient=this.restManager.holdClientBankAccountMapper.wrapBaseBusinessEntity(false,
        // this.prepareAccountHoldingInputs(businessAzimutClient,tokenizedBusinessUser),
        // null).getData();
        var holdBankAccountRequest = this.holdBankAccountApi.prepareRequest(businessAzimutClient,
            tokenizedBusinessUser);
        this.holdBankAccountApi.getData(holdBankAccountRequest);
      } else {
        this.azimutDataLookupUtility.removeClientBankAccount(businessAzimutClient.getAccountId());
      }
    } catch (Exception exception) {

      throw this.exceptionHandler.handleException(exception);
    }

    return responseBusinessAzimutClient;
  }

  public BusinessAzimutClient checkAccountAtTeaComputers(CheckAccountDto businessAzimutClient,
      BusinessUser tokenizedBusinessUser) throws BusinessException, IntegrationException {
    BusinessAzimutClient responseBusinessAzimutClient = new BusinessAzimutClient();
    this.validation.validateUser(businessAzimutClient.getId(), tokenizedBusinessUser);
    try {
      // responseBusinessAzimutClient.setAzimutAccounts(this.restManager.checkAccountMapper.wrapBaseBusinessEntity(true,
      // this.prepareAccountRetrievalInputs(businessAzimutClient,tokenizedBusinessUser),
      // null).getDataList());

      var azimutAccount = this.prepareAccountRetrievalRequest(businessAzimutClient, tokenizedBusinessUser);
      var checkAccountRequest = this.checkAccountApi.createRequest(azimutAccount);
      var checkAccountResponse = this.checkAccountApi.getData(checkAccountRequest);
      var recievedAzimutAccounts = this.checkAccountApi.createAzimutAccountList(checkAccountResponse);
      responseBusinessAzimutClient.setAzimutAccounts(recievedAzimutAccounts);

    } catch (Exception exception) {

      throw this.exceptionHandler.handleException(exception);
    }

    return responseBusinessAzimutClient;
  }

  public BusinessAzimutClient saveTeaComputersAccountData(AzimutAccount azimutAccount,
      BusinessUser tokenizedBusinessUser) throws BusinessException, IntegrationException {
    BusinessAzimutClient responseBusinessAzimutClient = new BusinessAzimutClient();
    this.validation.validateUser(azimutAccount.getId(), tokenizedBusinessUser);
    try {
      tokenizedBusinessUser.setAzimutAccount(azimutAccount);
      this.editUser(tokenizedBusinessUser);
    } catch (Exception exception) {
      throw this.exceptionHandler.handleException(exception);
    }

    return responseBusinessAzimutClient;
  }

  public void addAccountAtTeaComputersApi(BusinessUser tokenizedBusinessUser)
      throws BusinessException, IntegrationException {
    var addAccountRequest = this.prepareAccountAdditionInputs(tokenizedBusinessUser);
    addAccountApi.getData(addAccountRequest);
    var user = userService.findById(tokenizedBusinessUser.getId());
    if (user.getPartnerUsers() != null)
      for (PartnerUser partnerUser : user.getPartnerUsers()) {
        addAccountRequest.setUserName(partnerUser.getPartner().getUserName());
        addAccountRequest.setClientTypeId(partnerUser.getPartner().getClientTypeId());
        addAccountRequest.setSignature(addAccountApi.generateSignature(addAccountRequest));
        addAccountApi.getData(addAccountRequest);
      }
  }

  public BusinessAzimutClient addAccountAtTeaComputers(AddAccountDto azimutAccount, BusinessUser tokenizedBusinessUser)
      throws BusinessException, IntegrationException {
    BusinessAzimutClient responseBusinessAzimutClient = new BusinessAzimutClient();
    this.validation.validateUser(azimutAccount.getId(), tokenizedBusinessUser);
    try {
      // add user account after sign contract
      addAccountAtTeaComputersApi(tokenizedBusinessUser);

      try {
        BusinessClientBankAccountDetails[] businessClientBankAccountDetailsArray = this.azimutDataLookupUtility
            .getKYCClientBankAccountData(tokenizedBusinessUser);

        if (arrayUtility.isArrayPopulated(businessClientBankAccountDetailsArray)) {
          List<AddClientBankAccountRequest> requests = new ArrayList<>();
          for (BusinessClientBankAccountDetails businessClientBankAccountDetails : businessClientBankAccountDetailsArray) {
            businessClientBankAccountDetails.setAzId(tokenizedBusinessUser.getUserId());
            businessClientBankAccountDetails.setAzIdType(this.getAzimutUserTypeId(tokenizedBusinessUser));
            requests.add(this.addClientBankAccountApi.prepareRequest(businessClientBankAccountDetails));
          }
          this.addClientBankAccountApi.loopConsumption(requests);
        }
        // TODO: add client bank account for partner users
      } catch (Exception exception) {
        this.exceptionHandler.logException(exception);
      }

      this.teaComputerService.deleteClientBankAccounts(tokenizedBusinessUser.getId());
    } catch (Exception exception) {
      throw this.exceptionHandler.handleException(exception);
    }

    return responseBusinessAzimutClient;
  }

  public BusinessAzimutClient getAzimutLookupData(GetAzimutLookUpDataDto businessAzimutDataLookup,
      BusinessUser tokenizedBusinessUser) throws BusinessException {
    this.validation.validate(businessAzimutDataLookup, getAzimutEntityLookup, BusinessAzimutDataLookup.class.getName());
    try {
      BusinessAzimutClient businessAzimutClient = new BusinessAzimutClient();
      businessAzimutClient.setLookupData(this.azimutDataLookupUtility.getLookups(businessAzimutDataLookup));
      return businessAzimutClient;
    } catch (Exception exception) {
      this.exceptionHandler.getNullIfNonExistent(exception);
    }
    return null;
  }

  public BusinessAzimutClient synchronizeTeaComputersLookupData(BusinessUser tokenizedBusinessUser)
      throws BusinessException, IntegrationException {
    try {
      this.azimutDataLookupUtility.syncTeaComputersData();
    } catch (Exception exception) {
      MyLogger.logStackTrace(exception);
    }
    return new BusinessAzimutClient();
  }

  public BusinessAzimutClient saveClientBankAccounts(SaveClientBankAccountDetailsTemporarilyDto businessAzimutClient,
      BusinessUser tokenizedBusinessUser, String language) throws BusinessException, IntegrationException {
    boolean isPersistentFalse = BooleanUtility.isFalse(businessAzimutClient.getPersist());
    MyLogger.info("isPersistentFalse:::" + isPersistentFalse);
    BusinessClientBankAccountDetails[] oldClientBankAccounts = this.azimutDataLookupUtility
        .getKYCClientBankAccountData(tokenizedBusinessUser);

    MyLogger.info("oldClientBankAccounts:::" + oldClientBankAccounts.toString());
    if (isPersistentFalse) {
      this.validation.validateUserKYCCompletion(tokenizedBusinessUser);
    }

    BusinessAzimutClient responseBusinessAzimutClient = new BusinessAzimutClient();

    for (BusinessClientBankAccountDetails businessClientBankAccountDetails : businessAzimutClient
        .getClientBankAccounts()) {
      this.validation.validate(businessClientBankAccountDetails, saveClientBankAccountTemporarily,
          BusinessClientBankAccountDetails.class.getName());
    }

    if (this.arrayUtility.isArrayPopulated(businessAzimutClient.getClientBankAccounts()) &&
        this.reviewListUtility.isListPopulated(businessAzimutClient.getClientBankAccounts()[0].getReviews()) &&
        NumberUtility.areLongValuesMatching(
            businessAzimutClient.getClientBankAccounts()[0].getReviews().get(0).getStatus(),
            ReviewResult.REJECTED.getResultId())) {
      this.validation.validateClientBankAccountsWhenUnderReview(tokenizedBusinessUser,
          businessAzimutClient.getClientBankAccounts(), oldClientBankAccounts);
    }
    try {
      MyLogger.info("Saving:::");
      this.azimutDataLookupUtility.saveAzimutClientBankAccountData(tokenizedBusinessUser, businessAzimutClient);
      BusinessUser editedUser = this.userUtility.isOldUserStepGreaterThanNewUserStep(tokenizedBusinessUser,
          UserStep.KYC.getStepId());
      this.editUser(editedUser);
      responseBusinessAzimutClient.setVerificationPercentage(editedUser.getVerificationPercentage());
      if (isPersistentFalse) {
        this.handleReviews(tokenizedBusinessUser, 0l, language);
        this.teaComputerService.deleteKycClientBankAccounts(oldClientBankAccounts);
      }

    }

    catch (Exception exception) {
      MyLogger.logStackTrace(exception);
      throw new BusinessException(ErrorCode.OPERATION_NOT_PERFORMED);
    }

    return responseBusinessAzimutClient;
  }

  public BusinessAzimutClient removeClientBankAccount(RemoveClientBankAccountDto businessClientBankAccountDetails,
      BusinessUser user)
      throws BusinessException {
    try {
      var currentClientBankAccountDetails = this.clientBankAccountDynamicRepository
          .findById(businessClientBankAccountDetails.getId());
      if (currentClientBankAccountDetails.isEmpty()
          || !NumberUtility.areLongValuesMatching(currentClientBankAccountDetails.get().getUserId(), user.getId())) {
        throw new BusinessException(ErrorCode.OPERATION_NOT_PERFORMED);
      }
      this.teaComputerService.removeClientBankAccount(businessClientBankAccountDetails.getId());
    } catch (Exception exception) {
      this.handleBusinessException(exception, ErrorCode.OPERATION_NOT_PERFORMED);
    }

    return new BusinessAzimutClient();
  }

  public BusinessAzimutClient getTemporaryClientBankAccountDetails(BusinessUser businessUser, String language)
      throws BusinessException {
    this.validation.validateUserKYCCompletion(businessUser);
    BusinessAzimutClient businessAzimutClient = new BusinessAzimutClient();
    try {
      businessAzimutClient
          .setClientBankAccounts(this.azimutDataLookupUtility.getKYCClientBankAccountData(businessUser));
      businessAzimutClient.setFirstPageId(businessUser.getFirstPageId());
    } catch (Exception exception) {
      this.handleBusinessException(exception, ErrorCode.OPERATION_NOT_PERFORMED);

    }
    if (this.arrayUtility.isArrayPopulated(businessAzimutClient.getClientBankAccounts())) {
      List<BusinessReview> totalReviews = this.reviewUtility
          .convertBasicListToBusinessList(this.reviewUtility.getReviews(businessUser.getId(), 0l), language);

      if (this.reviewListUtility.isListPopulated(totalReviews)) {
        for (BusinessClientBankAccountDetails businessClientBankAccountDetails : businessAzimutClient
            .getClientBankAccounts()) {
          List<BusinessReview> accountReviews = totalReviews.stream().filter(r -> NumberUtility
              .areLongValuesMatching(r.getAccountId(), businessClientBankAccountDetails.getAccountId()))
              .collect(Collectors.toList());

          businessClientBankAccountDetails.setReviews(this.reviewListUtility.getListWithNullIfEmpty(accountReviews));
        }
      }
    }

    return businessAzimutClient;
  }

  public BusinessAzimutClient getAzimutDetails() throws BusinessException {
    BusinessAzimutClient businessAzimutClient = new BusinessAzimutClient();
    try {
      businessAzimutClient.setAzimutDetails(this.azimutDataLookUpService.getAzimutDetails());
    } catch (Exception exception) {
      this.handleBusinessException(exception, ErrorCode.OPERATION_NOT_PERFORMED);

    }
    return businessAzimutClient;
  }

  public BusinessAzimutClient getTotalClientBankAccounts(BusinessUser tokenizedBusinessUser) throws BusinessException {
    BusinessAzimutClient businessAzimutClient = new BusinessAzimutClient();
    try {
      // List<BusinessClientBankAccountDetails>
      // clientTeacomputersBankAccounts=this.restManager.getClientBankAccountsMapper.wrapBaseBusinessEntity(true,this.prepareClientBankAccountDetailsInputs(businessAzimutClient,tokenizedBusinessUser,true),
      // null).getDataList();

      var bankAccountsRequest = this.bankAccountsApi.prepareRequest(new GetAzimutClientBankAccountsDto(),
          tokenizedBusinessUser);
      var bankAccountResponse = this.bankAccountsApi.getData(bankAccountsRequest);
      List<BusinessClientBankAccountDetails> clientTeacomputersBankAccounts = this.bankAccountsApi
          .getBanksAccountsFromResponse(bankAccountResponse);

      BusinessClientBankAccountDetails[] tempClientTeacomputersBankAccounts = this.azimutDataLookupUtility
          .getClientBankAccountData(tokenizedBusinessUser);
      if (arrayUtility.isArrayPopulated(tempClientTeacomputersBankAccounts)) {
        clientTeacomputersBankAccounts.addAll(Arrays.asList(tempClientTeacomputersBankAccounts));
      }
      businessAzimutClient.setBankList(clientTeacomputersBankAccounts);
    } catch (Exception exception) {
      this.handleBusinessException(exception, ErrorCode.OPERATION_NOT_PERFORMED);

    }
    return businessAzimutClient;
  }

  public BusinessAzimutClient getPaginatedClientFunds(BusinessUser tokenizedBusinessUser,
      GetBalanceAndFundOwnershipDto businessAzimutClient, String language)
      throws IntegrationException, BusinessException {
    MyLogger.info("Current thread" + Thread.currentThread().getId());
    BusinessAzimutClient responseBusinessAzimutClient = new BusinessAzimutClient();
    responseBusinessAzimutClient = this.getClientFundsOrFund(tokenizedBusinessUser, businessAzimutClient, true,
        language);
    if (responseBusinessAzimutClient != null && businessAzimutClient != null
        && businessAzimutClient.getPageNumber() != null) {
      List<BusinessClientFund> clientFunds = responseBusinessAzimutClient.getBusinessClientFunds();
      PaginatedEntity<BusinessClientFund> paginatedBusinessClientFunds = this.clientFundListUtility.getPaginatedList(
          clientFunds, businessAzimutClient.getPageNumber(), Integer.valueOf(this.configProperties.getPageSize()));
      responseBusinessAzimutClient.setPaginatedBusinessClientFunds(paginatedBusinessClientFunds);
      responseBusinessAzimutClient.setBusinessClientFunds(null);
    }
    return responseBusinessAzimutClient;
  }

  public BusinessAzimutClient getClientFundsOrFund(BusinessUser tokenizedBusinessUser,
      GetBalanceAndFundOwnershipDto businessAzimutClient, boolean getDetails, String language)
      throws IntegrationException, BusinessException {

    BusinessAzimutClient responseBusinessAzimutClient = new BusinessAzimutClient();
    if (tokenizedBusinessUser != null && StringUtility.isStringPopulated(tokenizedBusinessUser.getUserId())) {
      try {
        String partnerUsername = null;
        if (businessAzimutClient.getPartnerId() != null) {
          var partner = partnerRepository.getById(businessAzimutClient.getPartnerId());
          partnerUsername = partner.getUserName();
        }
        Map<String, Object> clientFundPriceMap = this.getClientFunds(tokenizedBusinessUser, businessAzimutClient,
            getDetails, partnerUsername, language);

        this.beautifyBusinessClientFunds(responseBusinessAzimutClient,
            (List<BusinessClientFund>) clientFundPriceMap.get(BusinessClientFund.class.getName()),
            (List<BusinessFundPrice>) clientFundPriceMap.get(BusinessFundPrice.class.getName()),
            (List<BusinessClientCashBalance>) clientFundPriceMap.get(BusinessClientCashBalance.class.getName()));

        var newCashBalanceRequest = this.cashBalanceApi.prepareRequest(tokenizedBusinessUser, partnerUsername);
        var cashBalanceResponse = this.cashBalanceApi.getData(newCashBalanceRequest);

        var businessClientCashBalances = this.cashBalanceApi.createListBusinessEntityFromResponse(cashBalanceResponse,
            language);
        Double onHold = 0.0;

        if (this.clientCashBalanceListUtility.isListPopulated(businessClientCashBalances)) {
          MyLogger.info("Cash Balances:::::::" + businessClientCashBalances.toString());

          for (BusinessClientCashBalance businessClientCashBalance : businessClientCashBalances) {
            if (businessClientCashBalance != null && businessClientCashBalance.getBalance() != null
                && businessClientCashBalance.getCurrencyRate() != null) {
              onHold = onHold + (businessClientCashBalance.getTotalBuyValue().doubleValue()
                  * businessClientCashBalance.getCurrencyRate().doubleValue());
            }
          }

          responseBusinessAzimutClient.setTotalPosition(
              NumberUtility.changeFormat(responseBusinessAzimutClient.getTotalPosition().add(new BigDecimal(onHold))));
        }
      }

      catch (Exception exception) {
        responseBusinessAzimutClient.setTotalPosition(new BigDecimal(0));
        responseBusinessAzimutClient.setBalance(0D);
        responseBusinessAzimutClient
            .setBusinessClientFunds(clientFundListUtility.handleExceptionAndReturnEmptyList(exception));
      }
    } else {
      responseBusinessAzimutClient.setTotalPosition(new BigDecimal(0));
      responseBusinessAzimutClient.setBalance(0D);
      responseBusinessAzimutClient.setBusinessClientFunds(new ArrayList<BusinessClientFund>());

    }
    return responseBusinessAzimutClient;
  }

  Map<String, Object> getClientFunds(BusinessUser tokenizedBusinessUser,
      GetBalanceAndFundOwnershipDto businessAzimutClient,
      boolean getDetails, String partnerUsername, String language) throws IntegrationException, BusinessException {

    Map<String, Object> clientFundPriceMap = new HashMap<String, Object>();
    List<BusinessFundPrice> businessFundPrices = new ArrayList<BusinessFundPrice>();
    List<BusinessClientFund> businessClientFunds = new ArrayList<BusinessClientFund>();
    // businessClientFunds=this.restManager.getClientFundsMapper.wrapBaseBusinessEntity(true,
    // this.prepareClientFundInputs(tokenizedBusinessUser,businessAzimutClient),
    // null).getDataList();

    var fundRequest = this.fundsApi.createRequest(tokenizedBusinessUser, businessAzimutClient, partnerUsername);
    var fundResponse = this.fundsApi.getData(fundRequest);

    businessClientFunds = this.fundsApi.getFundsFromResponse(fundResponse, language);

    if (businessAzimutClient != null && businessAzimutClient.getTeacomputerId() != null) {
      if (clientFundListUtility.isListPopulated(businessClientFunds) && getDetails) {
        this.getClientFundDetails(businessClientFunds.get(0), tokenizedBusinessUser, businessAzimutClient,
            partnerUsername);
      }
    } else {
      // List<BusinessClientCashBalance>
      // businessClientCashBalances=this.restManager.getClientBalanceMapper.wrapBaseBusinessEntity(true,this.preparClientCashBalanceInputs(businessAzimutClient,tokenizedBusinessUser),
      // null).getDataList();

      var newCashBalanceRequest = this.cashBalanceApi.prepareRequest(tokenizedBusinessUser, partnerUsername);
      var cashBalanceResponse = this.cashBalanceApi.getData(newCashBalanceRequest);

      List<BusinessClientCashBalance> businessClientCashBalances = this.cashBalanceApi
          .createListBusinessEntityFromResponse(cashBalanceResponse, language);
      clientFundPriceMap.put(BusinessClientCashBalance.class.getName(), businessClientCashBalances);
    }

    Long[] teacomputerFundIds = this.populateTheArrayOfFundIds(businessClientFunds);
    if (this.arrayUtility.isArrayPopulated(teacomputerFundIds)) {
      MyLogger.info("Funds Populated::::");
      businessFundPrices = this.fundPriceMapper
          .convertBasicListToBusinessList(this.fundService.getAllRelevantFundPrices(teacomputerFundIds));
    }

    clientFundPriceMap.put(BusinessClientFund.class.getName(), businessClientFunds);
    clientFundPriceMap.put(BusinessFundPrice.class.getName(), businessFundPrices);

    return clientFundPriceMap;
  }

  public void getClientFundDetails(BusinessClientFund businessClientFund, BusinessUser tokenizedBusinessUser,
      GetBalanceAndFundOwnershipDto businessAzimutClient, String partnerUsername)
      throws IntegrationException, BusinessException {
    // List<BusinessFundTransaction>
    // businessFundTransactions=this.restManager.getFundTransactionsMapper.wrapBaseBusinessEntity(true,
    // this.prepareBusinessBusinessFundTransactionRetrievalInputs(tokenizedBusinessUser,businessAzimutClient),
    // null).getDataList();
    List<BusinessFundTransaction> allTransactions = new ArrayList<BusinessFundTransaction>();
    String[] paths = { StringUtility.EXECUTED_ORDERS, StringUtility.PENDING_ORDERS, StringUtility.CANCELED_ORDERS };
    for (String path : paths) {
      var fundTransactionsRequest = this.fundTransactionsApi.prepareFundTransactionRequest(tokenizedBusinessUser,
          businessAzimutClient, partnerUsername);
      var fundTransactionsResponse = this.fundTransactionsApi.getData(fundTransactionsRequest, path);
      OrderStatus status = StringUtility.stringsMatch(path, StringUtility.EXECUTED_ORDERS) ? OrderStatus.EXECUTED
          : StringUtility.stringsMatch(path, StringUtility.PENDING_ORDERS) ? OrderStatus.PENDING : OrderStatus.CANCELED;
      var returnedTransactions = this.fundTransactionsApi
          .createListBusinessTransactionsFromResponses(fundTransactionsResponse, status);
      allTransactions.addAll(returnedTransactions);
    }

    this.beautifyBusinessClientFundTransactions(businessClientFund, allTransactions, businessAzimutClient);
  }

  public BusinessAzimutClient getEportfolio(BusinessUser tokenizedBusinessUser, String language)
      throws BusinessException, IntegrationException {
    BusinessAzimutClient responseBusinessAzimutClient = new BusinessAzimutClient();

    if (tokenizedBusinessUser != null && StringUtility.isStringPopulated(tokenizedBusinessUser.getUserId())) {
      try {
        var eportfolioRequest = this.eportfolioApi.generateRequest(tokenizedBusinessUser, language);
        var eportfolioResponse = this.eportfolioApi.getData(eportfolioRequest);

        responseBusinessAzimutClient
            .setEportfolioDetails(this.eportfolioApi.getEportfolioDetailsFromResponse(eportfolioResponse));
      } catch (Exception exception) {
        responseBusinessAzimutClient
            .setEportfolioDetails(eportfolioDetailListUtility.handleExceptionAndReturnEmptyList(exception));
      }
    } else {
      responseBusinessAzimutClient.setEportfolioDetails(new ArrayList<EportfolioDetail>());
    }

    return responseBusinessAzimutClient;
  }

  public BusinessAzimutClient getValuationReport(BusinessUser tokenizedBusinessUser, String language)
      throws BusinessException, IntegrationException {
    return this.getReport(tokenizedBusinessUser, language, StringUtility.VALUATION_REPORT, null);
  }

  public BusinessAzimutClient getRequestStatement(BusinessUser tokenizedBusinessUser, String language,
      GetRequestStatementDto businessAzimutClient) throws BusinessException, IntegrationException {
    return this.getReport(tokenizedBusinessUser, language, StringUtility.REQUEST_STATEMENT, businessAzimutClient);
  }

  public BusinessAzimutClient getReport(BusinessUser tokenizedBusinessUser, String language, String reportType,
      GetRequestStatementDto businessAzimutClient) throws BusinessException {
    BusinessAzimutClient responseBusinessAzimutClient = new BusinessAzimutClient();
    try {
      // responseBusinessAzimutClient=
      // this.restManager.getReportMapper.wrapBaseBusinessEntity(false,this.prepareGetReportInputs(tokenizedBusinessUser,language,reportType,businessAzimutClient),
      // reportType).getData();
      String partnerUsername = null;
      if (businessAzimutClient.getPartnerId() != null) {
        var partner = partnerRepository.getById(businessAzimutClient.getPartnerId());
        partnerUsername = partner.getUserName();
      }
      var getReportRequest = this.getReportApi.prepareRequest(tokenizedBusinessUser, language, reportType,
          businessAzimutClient, partnerUsername);
      var getReportResponse = this.getReportApi.getData(getReportRequest, reportType);

      responseBusinessAzimutClient = this.getReportApi.generateBusinessClientFromResponse(getReportResponse);
      String url = responseBusinessAzimutClient.getDocumentURL();
      String newUrl = this.storageService.uploadFileToBlob(
          StringUtility.stringsMatch(StringUtility.REQUEST_STATEMENT, reportType)
              ? reportType.split("/")[0] + "from" + businessAzimutClient.getSearchFromDate().substring(0, 10) + "to"
                  + businessAzimutClient.getSearchToDate().substring(0, 10)
              : reportType.split("/")[0],
          new BufferedInputStream(new URL(url).openStream()), true, configProperties.getBlobKYCDocumentsTemp(),
          DateUtility.getCurrentDayMonthYear() + "/" + tokenizedBusinessUser.getUserId(), StringUtility.PDF_EXTENSION)
          .getUrl();
      responseBusinessAzimutClient.setDocumentURL(newUrl);
    } catch (Exception exception) {
      throw this.exceptionHandler.handleException(exception);
    }
    return responseBusinessAzimutClient;
  }

  public BusinessAzimutClient getCompanyBankAccounts(BusinessUser user, Long currencyId, String language)
      throws BusinessException, IntegrationException {
    try {
      var userInvestment = this.userService.findUserInvestmentByUserId(user.getId());
      if (userInvestment != null) {
        user.setHasInjected(userInvestment.getTotalTopup() > 0.001);
      } else {
        user.setHasInjected(false);
      }

      BusinessAzimutClient businessAzimutClient = new BusinessAzimutClient();
      BusinessAzimutDataLookup businessAzimutDataLookup = new BusinessAzimutDataLookup();

      List<BusinessCompanyBankAccount> businessCompanyBankAccountsFiltered = new ArrayList<BusinessCompanyBankAccount>();

      // List<BusinessCompanyBankAccount> businessCompanyBankAccounts=
      // this.restManager.getCompanyBankAccountMapper.wrapBaseBusinessEntity(true,
      // null, null).getDataList();
      BusinessCompanyBankAccount businessCompanyBankAccountRequest = new BusinessCompanyBankAccount();
      businessCompanyBankAccountRequest.setLanguage(language);

      var bankAccountRequest = this.companyBankAccountsApi.generateRequest();
      var bankAccountResponses = this.companyBankAccountsApi.getData(bankAccountRequest);
      List<BusinessCompanyBankAccount> businessCompanyBankAccounts = this.companyBankAccountsApi
          .generateBankAccountsFromResponses(bankAccountResponses);

      if (companyBankAccountListUtility.isListPopulated(businessCompanyBankAccounts) && currencyId != null) {
        for (BusinessCompanyBankAccount businessCompanyBankAccount : businessCompanyBankAccounts) {
          if (businessCompanyBankAccount != null && businessCompanyBankAccount.getCurrencyId() != null) {
            if (NumberUtility.areLongValuesMatching(currencyId,
                Long.valueOf(businessCompanyBankAccount.getCurrencyId()))) {
              if (user.getHasInjected() || NumberUtility.areLongValuesMatching(currencyId,
                  CurrencyType.US_DOLLAR.getTypeId()) ? true
                      : NumberUtility.areLongValuesMatching(businessCompanyBankAccount.getAccountId(), 5L))
                businessCompanyBankAccountsFiltered.add(businessCompanyBankAccount);
            }
          }
        }
        businessAzimutDataLookup.setCompanyBankAccounts(businessCompanyBankAccountsFiltered);
      } else if (companyBankAccountListUtility.isListPopulated(businessCompanyBankAccounts) && currencyId == null) {
        for (BusinessCompanyBankAccount businessCompanyBankAccount : businessCompanyBankAccounts) {
          if (user.getHasInjected()
              || NumberUtility.areLongValuesMatching(businessCompanyBankAccount.getAccountId(), 5L))
            businessCompanyBankAccountsFiltered.add(businessCompanyBankAccount);
        }
        businessAzimutDataLookup.setCompanyBankAccounts(businessCompanyBankAccountsFiltered);
      }

      businessAzimutClient.setLookupData(businessAzimutDataLookup);

      return businessAzimutClient;
    } catch (Exception exception) {
      throw this.exceptionHandler.handleException(exception);
    }
  }

  public AddAccountRequest prepareAccountAdditionInputs(BusinessUser businessUser)
      throws BusinessException {
    var addAccountRequest = new AddAccountRequest();

    if (businessUser.getFirstName() != null && businessUser.getLastName() != null) {
      addAccountRequest
          .setCustomerNameEn(businessUser.getFirstName() + StringUtility.SPACE + businessUser.getLastName());
      addAccountRequest
          .setCustomerNameAr(businessUser.getFirstName() + StringUtility.SPACE + businessUser.getLastName());
    }

    addAccountRequest.setIdType(this.getAzimutUserTypeId(businessUser));
    addAccountRequest.setIdTypeId(this.getAzimutUserTypeId(businessUser));
    addAccountRequest.setIdNumber(businessUser.getUserId());
    addAccountRequest.setIdMaturityDate(businessUser.getDateOfIdExpiry());
    addAccountRequest.setBirthDate(businessUser.getDateOfBirth());
    addAccountRequest.setEmail(businessUser.getEmailAddress());
    addAccountRequest.setPhone(businessUser.getCountryPhoneCode().substring(1) + businessUser.getPhoneNumber());
    addAccountRequest.setMobile(businessUser.getCountryPhoneCode().substring(1) + businessUser.getPhoneNumber());
    addAccountRequest.setSexId(businessUser.getGenderId());
    addAccountRequest.setIdDate(businessUser.getDateOfRelease());

    Long clientTypeId = 65L;
    if (StringUtility.isStringPopulated(businessUser.getReferralCode())) {
      try {
        var referralCode = referralCodeService.findByCode(businessUser.getReferralCode().toLowerCase());
        clientTypeId = referralCode.getClientTypeId();
      } catch (NoSuchElementException e) {
        MyLogger.info("No Referral Code available for " + businessUser.getReferralCode());
      }
    }
    addAccountRequest.setClientTypeId(clientTypeId);

    AzimutAccount businessUserAzimutAccount = businessUser.getAzimutAccount();
    if (businessUserAzimutAccount != null) {
      addAccountRequest.setAddressAr(businessUserAzimutAccount.getAddressAr());
      addAccountRequest.setAddressEn(businessUserAzimutAccount.getAddressEn());
      addAccountRequest.setIDIssueDistrictId(businessUserAzimutAccount.getIDIssueDistrictId());
      addAccountRequest.setPostalNo(businessUserAzimutAccount.getPostalNo());

      addAccountRequest.setAddressEn(businessUserAzimutAccount.getAddressEn());

      addAccountRequest.setCityId(businessUserAzimutAccount.getCityId());

      addAccountRequest.setCountryId(businessUserAzimutAccount.getCountryId());

      addAccountRequest.setIDIssueCountryId(businessUserAzimutAccount.getCountryId());

      addAccountRequest.setIDIssueCityId(businessUserAzimutAccount.getCityId());

      addAccountRequest.setClientAML(businessUserAzimutAccount.getClientAML());

      addAccountRequest.setOccupation(businessUserAzimutAccount.getOccupation());
      // addAccountRequest.setOccupatio("l);
      addAccountRequest.setNationalityId(businessUserAzimutAccount.getNationalityId());
      addAccountRequest.setExternalcode(businessUserAzimutAccount.getExternalcode());
    }

    addAccountRequest.setSignature(addAccountApi.generateSignature(addAccountRequest));
    MyLogger.info("Azimut Add Account Request:::" + addAccountRequest.toString());

    return addAccountRequest;
  }

  BusinessFundPrice prepareFundPriceSearchInputs(BusinessAzimutClient businessAzimutClient) {
    BusinessFundPrice searchBusinessFundPrice = new BusinessFundPrice();
    searchBusinessFundPrice.setSearchFromDate(businessAzimutClient.getSearchFromDate());
    searchBusinessFundPrice.setSearchToDate(businessAzimutClient.getSearchToDate());
    MyLogger.info("SearchBusinessFundPrice:" + searchBusinessFundPrice.toString());
    return searchBusinessFundPrice;
  }

  public BusinessAzimutClient beautifyBalanceAndTransactionsBusinessAzimutClient(
      BusinessAzimutClient businessAzimutClient) {
    double pendingAmount = 0;
    if (businessAzimutClient != null
        && businessTransactionListUtility.isListPopulated(businessAzimutClient.getTransactions())) {
      List<BusinessTransaction> unsortedTransactions = businessAzimutClient.getTransactions();

      businessAzimutClient.setTotalPendingAmount(pendingAmount);

      boolean sorting = true;
      if (StringUtility.stringsMatch(businessAzimutClient.getSorting(), Sorting.ASC.getOrder()))
        sorting = true;

      else if (!StringUtility.isStringPopulated(businessAzimutClient.getSorting())
          || StringUtility.stringsMatch(businessAzimutClient.getSorting(), Sorting.DESC.getOrder()))
        sorting = false;

      MyLogger.info("Sorting:::" + sorting);

      Collections.sort(unsortedTransactions,
          sorting ? this.cashTransactionSortCompare : cashTransactionSortCompare.reversed());

      MyLogger.info("Sorted Transactions:::" + unsortedTransactions.toString());

      int lastIndex = unsortedTransactions.size();
      int index = sorting ? lastIndex : 0;

      MyLogger.info("Index:::" + index);
      businessAzimutClient.setTransactions(unsortedTransactions);

      businessAzimutClient.setLastTransactionDate(
          sorting ? unsortedTransactions.get(lastIndex - 1).getTrxDate() : unsortedTransactions.get(0).getTrxDate());
      String oldLastTransactionDate = businessAzimutClient.getLastTransactionDate();
      businessAzimutClient.setLastTransactionDate(DateUtility.changeStringDateFormat(oldLastTransactionDate,
          new SimpleDateFormat("dd-MM-yyyy HH:mm:ss"), new SimpleDateFormat("dd MMM, yyyy")));

      for (BusinessTransaction businessTransaction : unsortedTransactions) {
        if (businessTransaction != null) {
          MyLogger.info("business Transaction:::" + businessTransaction);

          String oldDate = businessTransaction.getTrxDate();
          businessTransaction
              .setTrxDate(DateUtility.changeStringDateFormat(oldDate, new SimpleDateFormat("dd-MM-yyyy HH:mm:ss"),
                  new SimpleDateFormat("dd MMM, yyyy h:mm a")));
          if (businessTransaction.getTrxDate().endsWith("12:00 AM"))
            businessTransaction.setTrxDate(businessTransaction.getTrxDate().substring(0, 12));
        }
      }

    }

    return businessAzimutClient;
  }

  AzimutAccount prepareAccountRetrievalRequest(CheckAccountDto businessAzimutClient,
      BusinessUser tokenizedBusinessUser) throws BusinessException {
    AzimutAccount azimutAccount = new AzimutAccount();
    azimutAccount.setPhoneNumber(businessAzimutClient.getUserPhone());
    azimutAccount.setUserId(businessAzimutClient.getUserId());
    azimutAccount.setIdType(this.getAzimutUserTypeId(tokenizedBusinessUser));
    return azimutAccount;
  }

  private void beautifyBusinessClientFunds(BusinessAzimutClient responseBusinessAzimutClient,
      List<BusinessClientFund> businessClientFunds, List<BusinessFundPrice> businessFundPrices,
      List<BusinessClientCashBalance> businessClientCashBalances) {

    double totalPosition = 0d;

    if (this.clientFundListUtility.isListPopulated(businessClientFunds)
        && this.fundPricesListUtility.isListPopulated(businessFundPrices)) {
      MyLogger.info("The two lists are populated::");
      double totalFundPosition = 0d;
      double totalCost = 0d;
      for (BusinessClientFund businessClientFund : businessClientFunds) {
        for (BusinessFundPrice businessFundPrice : businessFundPrices) {
          if (NumberUtility.areLongValuesMatching(businessClientFund.getTeacomputerId(),
              businessFundPrice.getTeacomputerId())) {

            if (StringUtility.isStringPopulated(businessFundPrice.getPriceDate()))
              businessClientFund
                  .setLastPriceUpdateDate(DateUtility.changeStringDateFormat(businessFundPrice.getPriceDate(),
                      new SimpleDateFormat("yyyy-MM-dd"), new SimpleDateFormat("dd MMM, yyyy")));

            businessClientFund.setLogo(businessFundPrice.getLogo());
          }
        }

        if (businessClientFund.getTotalAmount() != null && businessClientFund.getCurrencyRate() != null) {
          totalFundPosition = totalFundPosition + (businessClientFund.getTotalAmount().doubleValue()
              * businessClientFund.getCurrencyRate().doubleValue());
        }
        if (businessClientFund.getAvgcost() != null && businessClientFund.getQuantity() != null
            && businessClientFund.getAvgcost().doubleValue() > 0
            && businessClientFund.getQuantity().doubleValue() > 0) {
          totalCost = totalCost + (businessClientFund.getAvgcost().doubleValue()
              * businessClientFund.getQuantity().doubleValue() * businessClientFund.getCurrencyRate().doubleValue());
        }

      }
      totalPosition = totalFundPosition;
      MyLogger.info("Total position::::::" + totalPosition);
      MyLogger.info("Total cost::::::" + totalCost);
      BigDecimal currentRevenue = new BigDecimal(totalPosition).subtract(new BigDecimal(totalCost));
      BigDecimal currentRevenuePercent = Math.abs(totalCost) < 1e-9 ? new BigDecimal(0)
          : currentRevenue.multiply(new BigDecimal(100)).divide(new BigDecimal(totalCost), 2, RoundingMode.HALF_UP);

      responseBusinessAzimutClient.setCurrentRevenue(NumberUtility
          .changeFormat(currentRevenue).doubleValue());
      responseBusinessAzimutClient.setCurrentRevenuePercent(currentRevenuePercent.doubleValue());
    }

    if (this.clientCashBalanceListUtility.isListPopulated(businessClientCashBalances)) {
      MyLogger.info("Cash Balances:::::::" + businessClientCashBalances.toString());
      double clientCashBalanceInAllCurrencies = 0.0D;

      for (BusinessClientCashBalance businessClientCashBalance : businessClientCashBalances) {
        if (businessClientCashBalance != null && businessClientCashBalance.getBalance() != null
            && businessClientCashBalance.getCurrencyRate() != null) {
          clientCashBalanceInAllCurrencies = clientCashBalanceInAllCurrencies
              + (businessClientCashBalance.getBalance().doubleValue()
                  * businessClientCashBalance.getCurrencyRate().doubleValue());
        }
      }

      responseBusinessAzimutClient.setBalance(clientCashBalanceInAllCurrencies);
      totalPosition = totalPosition + clientCashBalanceInAllCurrencies;
    } else {
      responseBusinessAzimutClient.setBalance(0.0D);
    }

    // responseBusinessAzimutClient.setTotalPosition(totalPosition);
    responseBusinessAzimutClient.setTotalPosition(NumberUtility.changeFormat(new BigDecimal(totalPosition)));
    responseBusinessAzimutClient.setBusinessClientFunds(businessClientFunds.stream()
        // .filter(item -> item.getQuantity() > 0)
        .sorted(new Comparator<BusinessClientFund>() {
          @Override
          public int compare(BusinessClientFund s1, BusinessClientFund s2) {
            return s2.getQuantity().compareTo(s1.getQuantity());
          }
        })
        .collect(Collectors.toList()));
  }

  private void beautifyBusinessClientFundTransactions(BusinessClientFund businessClientFund,
      List<BusinessFundTransaction> businessFundTransactions,
      GetBalanceAndFundOwnershipDto searchBusinessAzimutClient) {
    MyLogger.info("Business Fund Transactions:::" + businessFundTransactions.toString());
    if (businessClientFund != null) {
      boolean sorting = true;
      if (StringUtility.stringsMatch(searchBusinessAzimutClient.getSorting(), Sorting.ASC.getOrder()))
        sorting = true;
      else if (!StringUtility.isStringPopulated(searchBusinessAzimutClient.getSorting())
          || StringUtility.stringsMatch(searchBusinessAzimutClient.getSorting(), Sorting.DESC.getOrder()))
        sorting = false;
      {
        MyLogger.info("Sorting:::" + sorting);
        Collections.sort(businessFundTransactions,
            sorting ? this.fundTransactionSortCompare : fundTransactionSortCompare.reversed());
        int index = sorting ? businessFundTransactions.size() : 0;
        MyLogger.info("Index:::" + index);
      }

      List<Entry<Integer, Double>> buyPricesList = new ArrayList<>();
      for (BusinessFundTransaction businessFundTransaction : businessFundTransactions) {
        String oldDate = businessFundTransaction.getOrderDate();
        MyLogger.info("Old date:::::" + oldDate);
        businessFundTransaction.setOrderDate(DateUtility.changeStringDateFormat(oldDate,
            new SimpleDateFormat("dd-MM-yyyy HH:mm:ss"), new SimpleDateFormat("dd MMM, yyyy h:mm a")));
      }

      for (int i = businessFundTransactions.size() - 1; i >= 0; i--) {
        BusinessFundTransaction businessFundTransaction = businessFundTransactions.get(i);
        if (NumberUtility.areIntegerValuesMatching(businessFundTransaction.getOrderStatusId(), 1)) {
          // executed
          if (NumberUtility.areIntegerValuesMatching(businessFundTransaction.getOrderTypeId(), 1)) {
            // buy
            Double price = businessFundTransaction.getOrderValue() / businessFundTransaction.getQuantity();
            var entry = new SimpleEntry<Integer, Double>(businessFundTransaction.getQuantity(), price);
            buyPricesList.add(entry);
          } else if (NumberUtility.areIntegerValuesMatching(businessFundTransaction.getOrderTypeId(), 2)) {
            // sell
            Integer quantity = businessFundTransaction.getQuantity();
            while (quantity > 0 && buyPricesList.size() > 0) {
              if (quantity >= buyPricesList.get(0).getKey()) {
                quantity -= buyPricesList.get(0).getKey();
                buyPricesList.remove(0);
              } else {
                var oldEntry = buyPricesList.get(0);
                var entry = new SimpleEntry<Integer, Double>(oldEntry.getKey() - quantity, oldEntry.getValue());
                buyPricesList.set(0, entry);
                quantity = 0;
              }
            }
          }
        }
      }

      Double sum = 0.0;
      // old method of calculating the cost (sum)
      // for (Entry<Integer, Double> entry : buyPricesList) {
      // if (entry.getKey() > 0 && entry.getValue() > 0)
      // sum += entry.getKey() * entry.getValue();
      // }
      sum = businessClientFund.getAvgcost() * businessClientFund.getQuantity();

      BigDecimal currentRevenue = businessClientFund.getTotalAmount().subtract(new BigDecimal(sum));
      MyLogger.info("Current Revenue: " + currentRevenue.toString() + " / sum: " + sum.toString());
      BigDecimal currentRevenuePercent = Math.abs(sum) < 1e-9 ? new BigDecimal(0)
          : currentRevenue.multiply(new BigDecimal(100)).divide(new BigDecimal(sum), 2, RoundingMode.HALF_UP);
      businessClientFund.setCurrentRevenue(NumberUtility
          .changeFormat(currentRevenue).doubleValue());
      businessClientFund.setCurrentRevenuePercent(currentRevenuePercent.doubleValue());
      businessClientFund.setFundTransactions(businessFundTransactions);

      businessClientFund.setBuyEnabled(false);
      businessClientFund.setSellEnabled(true);
      var fund = fundService.getFundsByTeacomputerId(businessClientFund.getTeacomputerId());
      if (fund.isPresent()) {
        businessClientFund.setBuyEnabled(fund.get().isBuyEnabled());
        businessClientFund.setSellEnabled(fund.get().isSellEnabled());
      }
    }

  }

  Long[] populateTheArrayOfFundIds(List<BusinessClientFund> businessClientFunds) {
    if (this.clientFundListUtility.isListPopulated(businessClientFunds)) {
      Long[] fundIds = new Long[businessClientFunds.size()];

      for (int i = 0; i < businessClientFunds.size(); i++) {
        if (businessClientFunds != null && businessClientFunds.get(i) != null
            && businessClientFunds.get(i).getTeacomputerId() != null) {
          fundIds[i] = businessClientFunds.get(i).getTeacomputerId().longValue();
        }
      }

      return fundIds;
    }
    return null;
  }

  public BusinessAzimutClient getDocuments(String documentName) throws BusinessException {

    BusinessAzimutClient businessAzimutClient = new BusinessAzimutClient();
    try {
      businessAzimutClient.setDocumentURL(

          this.storageService.generateFileRetrievalUrl(this.configProperties.getBlobUnsignedPdfPath(),
              documentName,
              this.configProperties.getBlobUnsignedPdfPathSubDirectory(), true));
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.OPERATION_NOT_PERFORMED);
    }
    return businessAzimutClient;
  }

}
