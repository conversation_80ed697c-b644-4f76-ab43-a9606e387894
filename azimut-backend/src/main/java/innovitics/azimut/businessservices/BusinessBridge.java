package innovitics.azimut.businessservices;

import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.kyc.BusinessKYCPage;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.kyc.SubmitAnswersDto;
import innovitics.azimut.controllers.kyc.DTOs.SaveClientBankAccountDetailsTemporarilyDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;

@Service
public class BusinessBridge {

  BusinessKYCPage submitAnswers(BusinessUser tokenizedBusinessUser,
      BusinessUserAnswerSubmissionService businessUserAnswerSubmissionService,
      BusinessKYCPageService businessKYCPageService, SubmitAnswersDto businessUserAnswerSubmission, String language)
      throws BusinessException {
    BusinessKYCPage businessKYCPage = new BusinessKYCPage();
    businessKYCPage = businessUserAnswerSubmissionService.submitAnswers(tokenizedBusinessUser,
        businessUserAnswerSubmission);

    if (!NumberUtility.areLongValuesMatching(businessUserAnswerSubmission.getNextPageId(),
        businessUserAnswerSubmission.getPageId())
        && BooleanUtility.isTrue(businessUserAnswerSubmission.getIsMobile())) {

      int verificationPercentage = businessKYCPage != null && businessKYCPage.getVerificationPercentage() != null
          ? businessKYCPage.getVerificationPercentage().intValue()
          : 0;
      businessKYCPage = businessKYCPageService.getKycPagebyId(tokenizedBusinessUser,
          businessUserAnswerSubmission.getNextPageId(), false, language);
      businessKYCPage.setVerificationPercentage(verificationPercentage);
    }
    if (NumberUtility.areLongValuesMatching(businessUserAnswerSubmission.getNextPageId(),
        businessUserAnswerSubmission.getPageId())
        && BooleanUtility.isTrue(businessUserAnswerSubmission.getIsMobile())) {
      businessKYCPage.setNextId(businessUserAnswerSubmission.getNextPageId());
      businessKYCPage.setId(businessUserAnswerSubmission.getNextPageId());
    }

    return businessKYCPage;
  }

  BusinessKYCPage saveclientBankAccountsTemporarily(BusinessUser businessUser,
      BusinessClientDetailsService businessClientDetailsService, BusinessKYCPageService businessKYCPageService,
      SaveClientBankAccountDetailsTemporarilyDto businessAzimutClient, String language)
      throws BusinessException, IntegrationException {
    BusinessKYCPage businessKYCPage = new BusinessKYCPage();

    businessClientDetailsService.saveClientBankAccounts(businessAzimutClient, businessUser, language);

    if (BooleanUtility.isTrue(businessAzimutClient.getIsMobile())) {
      businessKYCPage = businessKYCPageService.getKycPagebyId(businessUser, businessUser.getFirstPageId(), false,
          language);

    }
    businessKYCPage.setVerificationPercentage(businessKYCPageService.adjustProgress(null, businessUser));

    return businessKYCPage;
  }

}
