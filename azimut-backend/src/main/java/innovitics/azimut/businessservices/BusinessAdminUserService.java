package innovitics.azimut.businessservices;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.businessmodels.user.AuthenticationResponse;
import innovitics.azimut.controllers.admin.DTOs.AdminAuthenticateDto;
import innovitics.azimut.controllers.admin.DTOs.ChangeAdminDto;
import innovitics.azimut.controllers.admin.DTOs.ChangePasswordDto;
import innovitics.azimut.controllers.admin.DTOs.ChangeRoleDto;
import innovitics.azimut.controllers.admin.DTOs.ForgotPasswordDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.Role;
import innovitics.azimut.models.admin.AdminUser;
import innovitics.azimut.security.JwtUtil;
import innovitics.azimut.security.RandomPasswordGenerator;
import innovitics.azimut.services.user.RoleService;
import innovitics.azimut.utilities.crosslayerenums.Messages;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.utilities.mapping.AdminUserMapper;
import innovitics.azimut.validations.validators.adminuser.AddAdminUser;

@Service
public class BusinessAdminUserService extends AbstractBusinessService<BusinessAdminUser> {

  @Autowired
  AddAdminUser addAdminUser;
  @Autowired
  AdminUserMapper adminUserMapper;
  @Autowired
  RandomPasswordGenerator randomPasswordGenerator;

  @Autowired
  RoleService roleService;

  public AuthenticationResponse<BusinessAdminUser> authenticateAdminUser(AdminAuthenticateDto authenticationRequest,
      JwtUtil jwtUtil)
      throws IntegrationException, BusinessException {
    try {
      BusinessAdminUser businessAdminUser = new BusinessAdminUser(authenticationRequest.getEmail(),
          authenticationRequest.getPassword());
      AdminUser adminUser = this.adminUserService.findByUserNameAndPassword(authenticationRequest.getEmail(),
          authenticationRequest.getPassword());
      businessAdminUser.setEmailAddress(adminUser.getEmail());
      businessAdminUser.setPassword(null);
      businessAdminUser.setFullName(adminUser.getName());
      if (adminUser.getRoleId() != null) {
        var role = roleService.findById(adminUser.getRoleId().longValue());
        businessAdminUser.setPermissions(role.getPermissionsApp());
        businessAdminUser.setRoleId(adminUser.getRoleId());
      }

      return new AuthenticationResponse<BusinessAdminUser>(jwtUtil.generateTokenUsingUserDetails(businessAdminUser,
          this.configProperties.getAdminJwTokenDurationInMinutes()), businessAdminUser);
    } catch (Exception exception) {
      throw this.exceptionHandler.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
    }
  }

  public AuthenticationResponse<BusinessAdminUser> register(BusinessAdminUser businessAdminUser,
      AdminAuthenticateDto authenticationRequest) throws IntegrationException, BusinessException {
    this.validation.validate(businessAdminUser, addAdminUser, BusinessAdminUser.class.getName());
    BusinessAdminUser addedBusinessAdminUser = new BusinessAdminUser(authenticationRequest.getEmail(),
        authenticationRequest.getPassword());
    if (authenticationRequest.getFullName() != null)
      addedBusinessAdminUser.setFullName(authenticationRequest.getFullName());
    if (authenticationRequest.getRoleId() != null)
      addedBusinessAdminUser.setRoleId(authenticationRequest.getRoleId());
    if (authenticationRequest.getIsFA() != null)
      addedBusinessAdminUser.setIsFA(authenticationRequest.getIsFA());
    this.validation.validateAdminUserNoneExistence(authenticationRequest.getEmail(), this);

    try {
      this.adminUserService.addUser(this.adminUserMapper.convertBusinessUnitToBasicUnit(addedBusinessAdminUser, true));
      return new AuthenticationResponse<BusinessAdminUser>(null, null);
    } catch (Exception exception) {
      throw this.exceptionHandler.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
    }
  }

  public AuthenticationResponse<BusinessAdminUser> deleteUser(BusinessAdminUser businessAdminUser,
      ChangeAdminDto changeAdminDto) throws IntegrationException, BusinessException {
    this.validation.validate(businessAdminUser, addAdminUser, BusinessAdminUser.class.getName());
    try {
      this.adminUserService.deleteUser(changeAdminDto.getId());
      return new AuthenticationResponse<BusinessAdminUser>(null, null);
    } catch (Exception exception) {
      throw this.exceptionHandler.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
    }
  }

  public AuthenticationResponse<BusinessAdminUser> editUser(BusinessAdminUser businessAdminUser,
      ChangeAdminDto changeAdminDto) throws IntegrationException, BusinessException {
    this.validation.validate(businessAdminUser, addAdminUser, BusinessAdminUser.class.getName());
    var adminUser = this.adminUserService.findById(changeAdminDto.getId());
    adminUser.setName(changeAdminDto.getFullName());
    adminUser.setRoleId(Long.valueOf(changeAdminDto.getRoleId()));
    adminUser.setEmail(changeAdminDto.getEmail());
    adminUser.setIsFA(changeAdminDto.getIsFA());
    try {
      this.adminUserService.editAdminUser(adminUser);
      return new AuthenticationResponse<BusinessAdminUser>(null, null);
    } catch (Exception exception) {
      throw this.exceptionHandler.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
    }
  }

  public List<Role> listRoles() {
    return this.roleService.getAllRoles();
  }

  public List<BusinessAdminUser> listAdminUsers() {
    List<BusinessAdminUser> users = new ArrayList<>();
    for (var user : this.adminUserService.getAllAdminUsers()) {
      users.add(this.adminUserMapper.convertBasicUnitToBusinessUnit(user));
    }
    return users;
  }

  public Role addRole(BusinessAdminUser businessAdminUser,
      ChangeRoleDto changeRoleDto) throws IntegrationException, BusinessException {
    this.validation.validate(businessAdminUser, addAdminUser, BusinessAdminUser.class.getName());
    Role role = new Role();
    role.setName(changeRoleDto.getName());
    role.setPermissionsApp(changeRoleDto.getPermissions());
    return roleService.saveRole(role);
  }

  public AuthenticationResponse<BusinessAdminUser> deleteRole(BusinessAdminUser businessAdminUser,
      ChangeRoleDto changeRoleDto) throws IntegrationException, BusinessException {
    this.validation.validate(businessAdminUser, addAdminUser, BusinessAdminUser.class.getName());
    try {
      this.roleService.deleteRole(changeRoleDto.getId());
      return new AuthenticationResponse<BusinessAdminUser>(null, null);
    } catch (Exception exception) {
      throw this.exceptionHandler.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
    }
  }

  public Role editRole(BusinessAdminUser businessAdminUser,
      ChangeRoleDto changeRoleDto) throws IntegrationException, BusinessException {
    this.validation.validate(businessAdminUser, addAdminUser, BusinessAdminUser.class.getName());
    Role role = roleService.findById(changeRoleDto.getId());
    role.setName(changeRoleDto.getName());
    role.setPermissionsApp(changeRoleDto.getPermissions());
    return roleService.saveRole(role);
  }

  public AuthenticationResponse<BusinessAdminUser> sendAutoGeneratedPassword(ForgotPasswordDto authenticationRequest,
      String language)
      throws IntegrationException, BusinessException {

    BusinessAdminUser businessAdminUser = this.findAdminUserByUsername(authenticationRequest.getEmail(), true);
    try {
      String newPassword = randomPasswordGenerator.generate();
      String text = Messages.NEW_PASSWORD.getMessage() + newPassword;
      String textAr = Messages.NEW_PASSWORD.getMessageAr() + newPassword;
      this.messagingService.send(emailUtility, businessAdminUser, text + newPassword, textAr + newPassword,
          Messages.NEW_PASSWORD.getTitle(), Messages.NEW_PASSWORD.getTitleAr(), language);
      businessAdminUser.setPassword(newPassword);
      this.adminUserService
          .editAdminUser(this.adminUserMapper.convertBusinessUnitToBasicUnit(businessAdminUser, false));
    } catch (Exception exception) {
      throw this.exceptionHandler.handleException(exception);
    }

    return new AuthenticationResponse<BusinessAdminUser>(null, null);
  }

  public AuthenticationResponse<BusinessAdminUser> changePassword(BusinessAdminUser businessAdminUser,
      ChangePasswordDto changedBusinessAdminUser, JwtUtil jwtUtil) throws IntegrationException, BusinessException {
    this.findAdminUserByUsernameAndPassword(businessAdminUser.getEmailAddress(),
        changedBusinessAdminUser.getOldPassword(), true);
    try {
      businessAdminUser.setPassword(changedBusinessAdminUser.getPassword());
      this.adminUserService
          .editAdminUser(this.adminUserMapper.convertBusinessUnitToBasicUnit(businessAdminUser, false));

      return new AuthenticationResponse<BusinessAdminUser>(jwtUtil.generateTokenUsingUserDetails(businessAdminUser,
          this.configProperties.getAdminJwTokenDurationInMinutes()), null);
    } catch (Exception exception) {
      throw this.exceptionHandler.handleBusinessException(exception, ErrorCode.USER_NOT_UPDATED);
    }
  }

  public AuthenticationResponse<BusinessAdminUser> changeAdminUser(BusinessAdminUser businessAdminUser,
      ChangeAdminDto authenticationRequest) throws IntegrationException, BusinessException {
    try {
      if (authenticationRequest.getFullName() != null)
        businessAdminUser.setFullName(authenticationRequest.getFullName());
      if (authenticationRequest.getEmail() != null)
        businessAdminUser.setEmailAddress(authenticationRequest.getEmail());

      String decryptedPassword = this.aes.decrypt(businessAdminUser.getPassword());
      businessAdminUser.setPassword(decryptedPassword);
      this.adminUserService
          .editAdminUser(this.adminUserMapper.convertBusinessUnitToBasicUnit(businessAdminUser, false));

      return new AuthenticationResponse<BusinessAdminUser>(null, null);
    } catch (Exception exception) {
      throw this.exceptionHandler.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
    }
  }

  public BusinessAdminUser findAdminUserByUsername(String username, boolean throwException) throws BusinessException {
    try {
      BusinessAdminUser businessAdminUser = new BusinessAdminUser();
      businessAdminUser = this.adminUserMapper
          .convertBasicUnitToBusinessUnit(this.adminUserService.findByUserName(username));
      return businessAdminUser;
    } catch (Exception exception) {
      if (throwException) {
        throw this.exceptionHandler.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
      } else {
        this.exceptionHandler.getNullIfNonExistent(exception);
        return null;
      }

    }
  }

  public BusinessAdminUser findAdminUserByUsernameAndPassword(String username, String password, boolean throwException)
      throws BusinessException {
    MyLogger.info("Getting the Admin old account:::::::::::::::::::::::");
    try {
      BusinessAdminUser businessAdminUser = new BusinessAdminUser();
      businessAdminUser = this.adminUserMapper
          .convertBasicUnitToBusinessUnit(this.adminUserService.findByUserNameAndPassword(username, password));

      MyLogger.info("Found:::" + businessAdminUser.toString());
      return businessAdminUser;
    } catch (Exception exception) {
      if (throwException) {
        throw this.exceptionHandler.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
      } else {
        this.exceptionHandler.getNullIfNonExistent(exception);
        return null;
      }

    }
  }

}
