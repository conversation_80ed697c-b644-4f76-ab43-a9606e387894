package innovitics.azimut.businessservices;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.controllers.admin.DTOs.NotificationDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.NotificationTemplate;
import innovitics.azimut.services.NotificationTemplateService;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;
import innovitics.azimut.utilities.datautilities.StringUtility;

@Service
public class BusinessNotificationTemplateService extends AbstractBusinessService<NotificationTemplate> {

  private @Autowired NotificationTemplateService notificationTemplateService;

  private @Autowired NotificationExecuterService notificationExecuterService;

  public PaginatedEntity<NotificationTemplate> listNotificationTemplates(
      BusinessSearchCriteria businessSearchCriteria) {
    var templates = this.notificationTemplateService
        .getAllNotifications(generateDatabaseConditions(businessSearchCriteria));
    PaginatedEntity<NotificationTemplate> paginatedEntity = new PaginatedEntity<NotificationTemplate>();

    paginatedEntity.setCurrentPage(businessSearchCriteria.getPageNumber());
    paginatedEntity.setPageSize(businessSearchCriteria.getPageSize());
    paginatedEntity.setNumberOfPages(templates.getTotalPages());
    paginatedEntity.setNumberOfItems(templates.getTotalElements());
    paginatedEntity.setHasNext(!templates.isLast());
    paginatedEntity.setHasPrevious(!templates.isFirst());
    List<NotificationTemplate> list = new ArrayList<>();
    if (!templates.isEmpty()) {
      for (var template : templates.getContent()) {
        list.add(template);
      }
    } else {
      list.add(null);
    }
    paginatedEntity.setDataList(list);
    return paginatedEntity;
  }

  public void sendTemplate(Long templateId, String target)
      throws IntegrationException, BusinessException, IOException, Exception {
    var template = notificationTemplateService.getTemplate(templateId);
    if ((template.getSentGroups().contains(target) || template.getSentGroups().contains("allUsers"))
        && StringUtility.stringsDontMatch(target, "testUsers"))
      return;
    this.notificationExecuterService.sendTemplateToFirebase(template, target);

    if (!template.getSentGroups().contains(target))
      template.getSentGroups().add(target);
    template.setSent(true);
    notificationTemplateService.updateTemplate(template);
  }

  public void sendTemplateToList(Long templateId, List<Long> target)
      throws IntegrationException, BusinessException, IOException, Exception {
    var template = notificationTemplateService.getTemplate(templateId);
    this.notificationExecuterService.sendTemplateToFirebaseList(template, target);

    template.setSentCount(template.getSentCount() + target.size());
    template.setSent(true);
    notificationTemplateService.updateTemplate(template);
  }

  public void sendTemplateToUserIdList(Long templateId, List<String> target)
      throws IntegrationException, BusinessException, IOException, Exception {
    var template = notificationTemplateService.getTemplate(templateId);
    this.notificationExecuterService.sendTemplateToFirebaseUserIdsList(template, target);

    template.setSentCount(template.getSentCount() + target.size());
    template.setSent(true);
    notificationTemplateService.updateTemplate(template);
  }

  public NotificationTemplate addTemplate(NotificationDto businessReview)
      throws BusinessException {
    this.notificationTemplateService.addTemplate(businessReview);
    return new NotificationTemplate();
  }

  public NotificationTemplate editTemplate(NotificationDto businessReview)
      throws BusinessException {
    this.notificationTemplateService.editTemplate(businessReview);
    return new NotificationTemplate();
  }

  public NotificationTemplate deleteTemplate(NotificationDto businessReview) throws BusinessException {
    this.notificationTemplateService.deleteTemplate(businessReview);
    return new NotificationTemplate();
  }

}
