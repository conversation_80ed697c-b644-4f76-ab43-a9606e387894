package innovitics.azimut.businessservices;

import java.io.IOException;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;

import innovitics.azimut.businessmodels.kyc.BusinessReview;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.user.User;
import innovitics.azimut.services.user.UserService;
import innovitics.azimut.utilities.businessutilities.ReviewUtility;
import innovitics.azimut.utilities.crosslayerenums.DocumentType;
import innovitics.azimut.utilities.crosslayerenums.KycStatus;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.exceptionhandling.ExceptionHandler;
import innovitics.azimut.utilities.fileutilities.SecureStorageService;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.utilities.mapping.UserMapper;

public abstract class BaseBusinessService {
  @Autowired
  protected ReviewUtility reviewUtility;
  @Autowired
  protected ExceptionHandler exceptionHandler;
  @Autowired
  protected UserService userService;
  @Autowired
  protected UserMapper userMapper;
  @Autowired
  public SecureStorageService storageService;
  @Autowired
  protected ConfigProperties configProperties;

  protected void editUser(BusinessUser businessUser) throws BusinessException {
    User user = new User();
    try {
      user = userMapper.convertBusinessUnitToBasicUnit(businessUser, false);
      userService.save(user);
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.USER_NOT_UPDATED);
    }

  }

  void handleReviews(BusinessUser businessUser, Long pageId, String language) throws BusinessException {
    MyLogger.info("Handling Reviews:::");
    if (!NumberUtility.areIntegerValuesMatching(businessUser.getKycStatus(), KycStatus.FIRST_TIME.getStatusId())) {
      List<BusinessReview> reviewsToBeRemoved = this.reviewUtility
          .convertBasicListToBusinessList(this.reviewUtility.getReviews(businessUser.getId(), pageId), language);
      this.adjustUser(businessUser, reviewsToBeRemoved);
    }
  }

  void adjustUser(BusinessUser businessUser, List<BusinessReview> reviewsToBeRemoved) throws BusinessException {
    this.reviewUtility.softDeleteReviews(reviewsToBeRemoved);

    // List<Review>
    // rejectedReviews=this.reviewUtility.getAnyRejectedReviews(businessUser.getId());

    // if(this.reviewUtility.reviewListUtility.isListPopulated(rejectedReviews))
    // {
    businessUser.setKycStatus(KycStatus.UPDATED.getStatusId());
    // }
    /*
     * else
     * {
     * businessUser.setKycStatus(KycStatus.PENDING.getStatusId());
     * }
     */
    this.editUser(businessUser);
  }

  protected BusinessException handleBusinessException(Exception exception, ErrorCode errorCode) {
    if (this.exceptionHandler.isNonTechnicalException(exception, errorCode))
      return this.exceptionHandler.handleAsBusinessException(exception, errorCode);

    else
      return this.exceptionHandler.handleAsBusinessException(exception, ErrorCode.OPERATION_NOT_PERFORMED);
  }

  public BusinessUser getByUserId(Long userId) throws BusinessException {
    BusinessUser businessUser = new BusinessUser();
    try {
      businessUser = this.convertBasicToBusinessAndPrepareURLsInBusinessUser(businessUser,
          this.userService.findUserById(userId), false);
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
    }
    return businessUser;
  }

  protected BusinessUser convertBasicToBusinessAndPrepareURLsInBusinessUser(BusinessUser businessUser, User user,
      boolean getDocuments) throws IOException {
    businessUser = userMapper.convertBasicUnitToBusinessUnit(user);
    if (getDocuments) {
      String stringBusinessUserId = String.valueOf(businessUser.getId());
      if (user.getProfilePicture() != null && user.getPicturePath() != null)
        businessUser.setProfilePicture(
            this.storageService.generateFileRetrievalUrl(this.configProperties.getBlobProfilePicturePath(),
                user.getProfilePicture(), user.getPicturePath(), false, null, stringBusinessUserId,
                DocumentType.PROFILE_IMAGE));

      // if (user.getSignedPdf() != null && user.getPdfPath() != null)
      // businessUser.setSignedPdf(
      // this.storageService.generateFileRetrievalUrl(this.configProperties.getBlobDigitalPdfPath(),
      // user.getSignedPdf(), user.getPdfPath(), true, 2L, stringBusinessUserId,
      // DocumentType.PROFILE_IMAGE));
    }
    return businessUser;
  }
}
