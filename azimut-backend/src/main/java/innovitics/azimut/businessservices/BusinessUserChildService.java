package innovitics.azimut.businessservices;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.businessmodels.user.AzimutAccount;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.businessutilities.SearchFilter;
import innovitics.azimut.controllers.users.DTOs.EditUserAndSubmitReviewDto;
import innovitics.azimut.controllers.users.DTOs.GetByIdDto;
import innovitics.azimut.controllers.users.DTOs.VerifyUserByTCDto;
import innovitics.azimut.controllers.users.DTOs.VerifyUserDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.OTPFunctionality;
import innovitics.azimut.models.digitalregistry.DigitalRegistryAction;
import innovitics.azimut.models.user.User;
import innovitics.azimut.models.user.UserLocation;
import innovitics.azimut.repositories.user.UserOldPhoneRepository;
import innovitics.azimut.security.TeaComputersSignatureGenerator;
import innovitics.azimut.services.admin.AdminUserMappingService;
import innovitics.azimut.services.digitalregistry.DigitalRegistryService;
import innovitics.azimut.utilities.crosslayerenums.KycStatus;
import innovitics.azimut.utilities.crosslayerenums.LiveNotificationNavigations;
import innovitics.azimut.utilities.crosslayerenums.Messages;
import innovitics.azimut.utilities.crosslayerenums.Navigations;
import innovitics.azimut.utilities.crosslayerenums.UserImageType;
import innovitics.azimut.utilities.crosslayerenums.UserStep;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class BusinessUserChildService extends BusinessUserService {
  @Autowired
  BusinessClientDetailsService businessClientDetailsService;
  @Autowired
  BusinessKYCPageService businessKYCPageService;
  @Autowired
  TeaComputersSignatureGenerator teaComputersSignatureGenerator;
  private @Autowired UserOldPhoneRepository userOldPhoneRepository;
  private @Autowired DigitalRegistryService digitalRegistryService;
  private @Autowired AdminUserMappingService adminUserMappingService;

  public BusinessUser assignUserKycStatus(BusinessAdminUser businessAdminUser, GetByIdDto getByIdDto,
      String language) throws BusinessException {
    BusinessUser editedBusinessUser = this.findUserById(getByIdDto.getId(), true);
    MyLogger.info("Editing the user:::");

    try {
      this.reviewUtility.calculateUserKycStatus(editedBusinessUser, userAnswerSubmissionService, language);
      this.editUser(editedBusinessUser);
      MyLogger.info("User Editted:::");
    } catch (Exception exception) {
      this.handleBusinessException(exception, ErrorCode.USER_NOT_UPDATED);
    }
    return getByIdDto.toBusinessUser();
  }

  public List<BusinessUser> listUsers(BusinessAdminUser businessAdminUser) throws BusinessException {
    List<User> users = this.userService.findUsersForTheDashboard();
    List<BusinessUser> businessUsers = new ArrayList<BusinessUser>();
    if (this.userListUtility.isListPopulated(users)) {
      for (User user : users) {
        businessUsers.add(this.userMapper.convertBasicUnitToBusinessUnitCompacted(user));
      }
    }
    return businessUsers;
  }

  public PaginatedEntity<BusinessUser> listUsersFiltered(BusinessAdminUser businessAdminUser,
      BusinessSearchCriteria businessSearchCriteria) throws BusinessException {
    var isFA = adminUserMappingService.existsByAdminUserId(businessAdminUser.getId());
    if (isFA || BooleanUtility.isTrue(businessAdminUser.getIsFA())) { // Financial Advisor
      var addedFilter = new SearchFilter("", "ADMIN_USER", businessAdminUser.getId().toString(), null, null, null);
      // Handle adding the filter to searchesAndFilters array
      SearchFilter[] existingFilters = businessSearchCriteria.getSearchesAndFilters();
      if (existingFilters == null) {
        businessSearchCriteria.setSearchesAndFilters(new SearchFilter[] { addedFilter });
      } else {
        SearchFilter[] newFilters = new SearchFilter[existingFilters.length + 1];
        System.arraycopy(existingFilters, 0, newFilters, 0, existingFilters.length);
        newFilters[existingFilters.length] = addedFilter;
        businessSearchCriteria.setSearchesAndFilters(newFilters);
      }
    }
    Page<User> users = this.userService
        .findFilteredUsersForTheDashboard(generateDatabaseConditions(businessSearchCriteria));
    PaginatedEntity<BusinessUser> businessUsers = new PaginatedEntity<BusinessUser>();
    if (this.userListUtility.isListPopulated(users.getContent())) {
      businessUsers = userMapper.convertBasicPageToBusinessPage(users, businessSearchCriteria);
    } else {
      businessUsers.setDataList(this.listUtility.getListWithNull());
    }
    return businessUsers;
  }

  public BusinessUser getClientOCRDetailsAndImages(BusinessAdminUser businessAdminUser, Long searchBusinessUserId,
      String language) throws BusinessException {

    BusinessUser businessUser = new BusinessUser();
    businessUser = this.findUserById(searchBusinessUserId, true);
    // this.validation.validateWithCustomError(businessUser,
    // KycStatus.PENDING_CLIENT, ErrorCode.CLIENT_SOLVING);

    Integer imageTypes[] = { UserImageType.FRONT_IMAGE.getTypeId(), UserImageType.BACK_IMAGE.getTypeId(),
        UserImageType.PASSPORT_IMAGE.getTypeId(), UserImageType.STRAIGHT.getTypeId(),
        UserImageType.DOCUMENT.getTypeId() };
    businessUser.setUserImages(this.userUtility.getUserImages(businessUser, imageTypes, false));
    businessUser.setReviews(this.getNonKycReviews(businessUser.getId(), language));
    var locationDr = digitalRegistryService.getLastDigitalRegistry(businessUser.getId(),
        DigitalRegistryAction.UPDATE_LOCATION);
    if (locationDr != null) {
      var loc = new UserLocation();
      loc.setLat(locationDr.getLatitude().toString());
      loc.setLongt(locationDr.getLongitude().toString());
      businessUser.setUserLocation(loc);
    }
    businessUser.setBlockageCount(this.userBlockageUtility.countByUserId(businessUser.getId()));
    businessUser.setChangePhoneCount(this.userOldPhoneRepository.countByUserIdAndDeletedAtIsNull(businessUser.getId()));
    businessUser.setReviewCount(this.reviewService.countByUserIdAndDeletedAtIsNull(searchBusinessUserId));
    var userInvestment = this.userService.findUserInvestmentByUserId(businessUser.getId());
    if (userInvestment != null) {
      businessUser.setHasInjected(userInvestment.getTotalTopup() > 0.001);
    } else {
      businessUser.setHasInjected(false);
    }
    var signDr = digitalRegistryService.getLastDigitalRegistry(businessUser.getId(),
        DigitalRegistryAction.SIGN_CONTRACT);
    if (signDr != null) {
      businessUser.setContractSignedAt(signDr.getCreatedAt());
    } else {
      var userOtp = this.userUtility.findOTPByUserId(businessUser.getId(), OTPFunctionality.SIGN_CONTRACT);
      if (userOtp != null) {
        businessUser.setContractSignedAt(userOtp.getCreatedAt());
      }
    }
    // todo: add kyc for the FRA portal
    return businessUser;

  }

  public BusinessUser editUserOnly(EditUserAndSubmitReviewDto editBusinessUser) throws BusinessException {
    BusinessUser businessUser = editUserFromDto(editBusinessUser);
    this.editUser(businessUser);
    return new BusinessUser();
  }

  public BusinessUser editUserFromDto(EditUserAndSubmitReviewDto editBusinessUser) throws BusinessException {
    BusinessUser businessUser = new BusinessUser();
    businessUser = this.findUserById(editBusinessUser.getId(), true);
    // this.validation.validateWithCustomError(businessUser,
    // KycStatus.PENDING_CLIENT, ErrorCode.CLIENT_SOLVING);
    businessUser.setFirstName(editBusinessUser.getFirstName());
    businessUser.setLastName(editBusinessUser.getLastName());
    businessUser.setEmailAddress(editBusinessUser.getEmailAddress());
    businessUser.setCountryCode(editBusinessUser.getCountryCode());
    businessUser.setCountryPhoneCode(editBusinessUser.getCountryPhoneCode());
    businessUser.setPhoneNumber(editBusinessUser.getPhoneNumber());
    businessUser.setOtherIdType(editBusinessUser.getOtherIdType());
    businessUser.setOtherUserId(editBusinessUser.getOtherUserId());
    businessUser.setDateOfBirth(editBusinessUser.getDateOfBirth());
    businessUser.setCountry(editBusinessUser.getCountry());
    businessUser.setCity(editBusinessUser.getCity());
    if (editBusinessUser.getIdData() != null) {
      businessUser.setIdData(editBusinessUser.getIdData());
    }
    if (editBusinessUser.getUserId() != null) {
      businessUser.setUserId(editBusinessUser.getUserId());
    }
    if (editBusinessUser.getDateOfRelease() != null) {
      businessUser.setDateOfRelease(editBusinessUser.getDateOfRelease());
    }
    if (editBusinessUser.getDateOfIdExpiry() != null) {
      businessUser.setDateOfIdExpiry(editBusinessUser.getDateOfIdExpiry());
    }

    if (editBusinessUser.getAzimutAccount() != null) {
      AzimutAccount newAzimutAccount = editBusinessUser.getAzimutAccount();
      AzimutAccount azimutAccount = businessUser.getAzimutAccount();
      if (newAzimutAccount.getCityId() != null) {
        azimutAccount.setCityId(newAzimutAccount.getCityId());
      }
      if (newAzimutAccount.getCountryId() != null) {
        azimutAccount.setCountryId(newAzimutAccount.getCountryId());
      }
      if (newAzimutAccount.getAddressAr() != null) {
        azimutAccount.setAddressAr(newAzimutAccount.getAddressAr());
      }
      if (newAzimutAccount.getAddressEn() != null) {
        azimutAccount.setAddressEn(newAzimutAccount.getAddressEn());
      }
    }
    editBusinessUser.setAppUserId(editBusinessUser.getId());
    return businessUser;
  }

  public BusinessUser editUserAndSubmitReview(BusinessAdminUser businessAdminUser,
      EditUserAndSubmitReviewDto editBusinessUser,
      String language) throws BusinessException, IntegrationException, IOException {

    BusinessUser businessUser = editUserFromDto(editBusinessUser);
    if (reviewListUtility.isListPopulated(editBusinessUser.getReviews())) {
      validation.validateReviewSubmission(editBusinessUser.getReviews(), null, null);
    }
    this.reviewUtility.addReviews(businessAdminUser, editBusinessUser, language, businessUser);

    this.editUser(businessUser);
    this.addAtTCAndSendPushNotification(businessUser, businessClientDetailsService, businessKYCPageService, language);
    return new BusinessUser();

  }

  public void approveOfflineContract(BusinessUser businessUser)
      throws BusinessException, IOException {
    this.handleUserAdditionAtTc(businessClientDetailsService, businessUser);
    this.verifyUser(businessUser);
    try {
      String navigation = Navigations.KYC_APPROVED.getNavigationId();
      String liveNavigation = LiveNotificationNavigations.KYC_APPROVED.getNavigationId();
      this.messagingService.send(pushNotificationUtility, businessUser, Messages.KYC_ACCEPTED,
          StringUtility.ENGLISH, navigation, liveNavigation,
          this.userUtility.generateUserMap(null, null, KycStatus.APPROVED.getStatusId(), true));
    } catch (Exception exception) {
      MyLogger.logStackTrace(exception);
      MyLogger.info("Failed:: to push Notification");
    }
    businessUser.setIsOld(true);
    this.editUser(businessUser);
  }

  public void resyncUser(BusinessUser user) throws BusinessException {
    user.setUserStep(UserStep.POPUP.getStepId());
    user.setKycStatus(KycStatus.FIRST_TIME.getStatusId());
    user.setIsVerified(false);
    user.setEnrollRequestId(null);
    user.setEnrollApplicantId(null);
    user.setGateIdTask(null);
    user.setPdfSignedAt(null);
    user.setSignedPdf(null);
    user.setUserId(null);
    this.editUser(user);
  }

  public BusinessUser signUserPdf(Long userId, MultipartFile file, String language)
      throws BusinessException, IOException, IntegrationException {

    BusinessUser businessUser = new BusinessUser();
    businessUser = this.findUserById(userId, true);
    this.storageService.uploadFileToBlob(StringUtility.CONTRACT_DOCUMENT_NAME,
        file.getInputStream(), true, configProperties.getBlobDigitalPdfPath(),
        userId.toString(), StringUtility.PDF_EXTENSION);
    this.handleUserAdditionAtTc(businessClientDetailsService, businessUser);
    this.verifyUserSendEmailAndPushNotification(businessUser, language);
    businessUser.setPdfPath(userId.toString());
    businessUser.setSignedPdf(StringUtility.CONTRACT_DOCUMENT_NAME + "." + StringUtility.PDF_EXTENSION);
    businessUser.setPdfSignedAt(DateUtility.getCurrentDate());
    this.editUser(businessUser);
    return businessUser;
  }

  public BusinessUser verifyUser(BusinessAdminUser businessAdminUser, VerifyUserDto editBusinessUser, String language)
      throws BusinessException, IOException {
    BusinessUser businessUser = new BusinessUser();
    businessUser = this.findUserById(editBusinessUser.getId(), true);
    businessUser.setClientCode(editBusinessUser.getClientCode());
    this.verifyUserSendEmailAndPushNotification(businessUser, language);
    return new BusinessUser();
  }

  public BusinessUser verifyUserByTC(VerifyUserByTCDto editBusinessUser) throws BusinessException, IOException {
    BusinessUser businessUser = new BusinessUser();
    businessUser = this.userMapper.convertBasicUnitToBusinessUnit(this.userService
        .findUserForTC(editBusinessUser.getUserId(), editBusinessUser.getIdType(), editBusinessUser.getUserPhone()));
    if (StringUtility.stringsDontMatch(
        this.teaComputersSignatureGenerator.generateSignature(String.valueOf(editBusinessUser.getIdType()),
            editBusinessUser.getUserId(), editBusinessUser.getUserPhone()),
        editBusinessUser.getSignature())) {
      throw new BusinessException(ErrorCode.INVALID_SIGNATURE);
    }
    businessUser.setClientCode(editBusinessUser.getClientCode());
    this.verifyUserSendEmailAndPushNotification(businessUser, StringUtility.ENGLISH);
    return new BusinessUser();
  }

  public byte[] getDocument(BusinessAdminUser businessAdminUser, Long userId, String language)
      throws BusinessException, IOException {
    BusinessUser tokenizedBusinessUser = this.findUserById(userId, true);
    return this.pdfFillerChild
        .fillPdfFormInChild(this.userUtility.findLatestOTP(tokenizedBusinessUser.getId()),
            StringUtility.isStringPopulated(language) ? language : StringUtility.ARABIC,
            this.azimutDataLookupUtility, this.arrayUtility, tokenizedBusinessUser,
            businessKYCPageService.getUserKycPages(tokenizedBusinessUser, false, language), "", "", "", "", "", "")
        .toByteArray();
  }

  public void sendOfflineContractAndPushNotification(BusinessUser businessUser, String language)
      throws BusinessException, IOException {
    var contractPdf = this.pdfFillerChild.fillPdfFormInChild(
        this.getUserOTP(businessUser, OTPFunctionality.SIGN_CONTRACT),
        StringUtility.ENGLISH, this.azimutDataLookupUtility, this.arrayUtility, businessUser,
        businessKYCPageService.getUserKycPages(businessUser, false, StringUtility.ENGLISH), "", "", "", "",
        "", "");
    emailUtility.sendOfflineMailWithAttachment(businessUser.getFirstName(), businessUser.getLastName(),
        businessUser.getEmailAddress(), Messages.OFFLINE_CONTRACT_ATTACHMENT.getTitle(), contractPdf);

    try {
      var lang = StringUtility.isStringPopulated(businessUser.getLanguage()) ? businessUser.getLanguage()
          : StringUtility.ENGLISH;
      this.messagingService.send(pushNotificationUtility, businessUser, Messages.OFFLINE_CONTRACT_NOTIFICATION, lang);
    } catch (Exception exception) {
      MyLogger.logStackTrace(exception);
      MyLogger.info("Failed:: to push Notification");
    }
    businessUser.setSignedPdf("offline");
    businessUser.setPdfSignedAt(null);
    this.editUser(businessUser);
  }

  public ByteArrayOutputStream getCompanySignedPdf(BusinessUser businessUser) {
    try {
      return this.userUtility.getCompanySignedPdf(businessUser, "contract");
    } catch (IOException e) {
      MyLogger.info("no Company signed contract");
      return null;
    }
  }

  public ByteArrayOutputStream getSignedPdf(BusinessUser businessUser) {
    try {
      return this.userUtility.getSignedPdf(businessUser, "UserContract");
    } catch (IOException e) {
      MyLogger.info("no User signed contract");
      return null;
    }
  }
}
