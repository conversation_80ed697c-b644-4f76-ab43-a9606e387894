package innovitics.azimut.businessservices;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.kyc.BusinessReview;
import innovitics.azimut.businessmodels.user.BusinessAzimutClient;
import innovitics.azimut.businessmodels.user.BusinessClientBankAccountDetails;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.admin.DTOs.RecordIdDto;
import innovitics.azimut.controllers.users.DTOs.SaveClientBankAccountsDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.kyc.Review;
import innovitics.azimut.models.teacomputers.Branch;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersAddClientBankAccountApi;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class BusinessClientDetailsChildService extends BusinessClientDetailsService {

  @Autowired
  TeaComputersAddClientBankAccountApi addClientBankAccountApi;

  public BusinessAzimutClient getBankAccountsWithDetailsForReviewal(RecordIdDto businessAzimutClient,
      BusinessUser tokenizedBusinessUser, boolean isList, Long accountId, String language)
      throws BusinessException, IntegrationException {
    BusinessAzimutClient responseBusinessAzimutClient = new BusinessAzimutClient();
    this.validation.validateUser(businessAzimutClient.getId(), tokenizedBusinessUser);
    // this.validation.validateWithCustomError(tokenizedBusinessUser,
    // KycStatus.PENDING_CLIENT, ErrorCode.CLIENT_SOLVING);
    BusinessClientBankAccountDetails[] businessClientBankAccountDetails = this.azimutDataLookupUtility
        .getKYCClientBankAccountData(tokenizedBusinessUser);

    if (this.arrayUtility.isArrayPopulated(businessClientBankAccountDetails)) {
      MyLogger.info("Array Populated");
      List<Review> reviews = this.reviewUtility.getReviews(tokenizedBusinessUser.getId(), 0l);
      if (this.baseReviewListUtility.isListPopulated(reviews)) {
        MyLogger.info("List Populated");
        for (BusinessClientBankAccountDetails clientBankAccountDetail : businessClientBankAccountDetails) {
          List<BusinessReview> businessReviews = new ArrayList<BusinessReview>();
          for (Review review : reviews) {
            if (NumberUtility.areLongValuesMatching(review.getAccountId(), clientBankAccountDetail.getAccountId())) {
              businessReviews.add(reviewUtility.convertReviewToBusinessReview(review, language));
            }
          }
          clientBankAccountDetail.setReviews(businessReviews);
        }
      }
    }
    responseBusinessAzimutClient.setClientBankAccounts(businessClientBankAccountDetails);
    return responseBusinessAzimutClient;
  }

  public BusinessAzimutClient saveClientBankAccounts(SaveClientBankAccountsDto businessAzimutClient,
      BusinessUser tokenizedBusinessUser, String language) throws BusinessException, IntegrationException {
    if (tokenizedBusinessUser != null) {
      if (BooleanUtility.isTrue(tokenizedBusinessUser.getIsVerified())) {
        this.validation.validate(businessAzimutClient.getClientBankAccounts()[0], saveClientBankAccountTemporarily,
            BusinessAzimutClient.class.getName());
        try {
          BusinessClientBankAccountDetails accountDetails = businessAzimutClient.getClientBankAccounts()[0];
          accountDetails.setAzId(tokenizedBusinessUser.getUserId());
          accountDetails.setAzIdType(tokenizedBusinessUser.getIdType());

          if (accountDetails.getBranchId() != null) {
            Branch branch = this.teaComputerService.getBranch(accountDetails.getBranchId());
            if (branch == null) {
              branch = this.teaComputerService.getBranchById(accountDetails.getBranchId());
              if (branch != null) {
                accountDetails.setBranchId(branch.getBranchId());
              }
            }
          } else {
            var branches = this.teaComputerService.getAllBranchesByBankId(accountDetails.getBankId());
            if (branches.size() > 0) {
              accountDetails.setBranchId(branches.get(0).getBranchId());
            }
          }

          var request = this.addClientBankAccountApi.prepareRequest(accountDetails);
          this.addClientBankAccountApi.getData(request, null);
          // TODO: add partner bank account
        } catch (Exception exception) {
          throw this.exceptionHandler.handleException(exception);
        }
      } else {
        throw new BusinessException(ErrorCode.KYC_INCOMPLETE);
      }
    }

    return new BusinessAzimutClient();
  }

}
