package innovitics.azimut.businessservices;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessmodels.negativelist.BusinessNegativeListMatch;
import innovitics.azimut.businessmodels.negativelist.BusinessNegativeListScreeningResult;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.admin.DTOs.ManualNegativeListEntryDto;
import innovitics.azimut.controllers.admin.DTOs.NegativeListStatistics;
import innovitics.azimut.enums.ListType;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.NegativeList;
import innovitics.azimut.models.kyc.ReasonType;
import innovitics.azimut.models.user.User;
import innovitics.azimut.repositories.NegativeListRepository;
import innovitics.azimut.repositories.user.UserRepository;
import innovitics.azimut.services.kyc.ReasonService;
import innovitics.azimut.services.kyc.ReviewService;
import innovitics.azimut.utilities.crosslayerenums.KycStatus;
import innovitics.azimut.utilities.crosslayerenums.UserIdType;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.FuzzyMatchUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.validations.RegexPattern;

@Service
public class BusinessNegativeListService extends AbstractBusinessService<NegativeList> {

  @Autowired
  private NegativeListRepository negativeListRepository;

  @Autowired
  private UserRepository userRepository;

  private @Autowired ReasonService reasonService;
  private @Autowired ReviewService reviewService;

  public void scrapNegativeList() throws IOException, BusinessException, IntegrationException {
    String unUrl = "https://mlcu.org.eg/ar/3118/%D9%82%D9%88%D8%A7%D8%A6%D9%85-%D9%85%D8%AC%D9%84%D8%B3-%D8%A7%D9%84%D8%A7%D9%85%D9%86-%D8%B0%D8%A7%D8%AA-%D8%A7%D9%84%D8%B5%D9%84%D8%A9";
    String localTerrorismUrl = "https://mlcu.org.eg/ar/3125/%D9%82%D9%88%D8%A7%D8%A6%D9%85-%D8%A7%D8%AF%D8%B1%D8%A7%D8%AC-%D8%A7%D9%84%D9%83%D9%8A%D8%A7%D9%86%D8%A7%D8%AA-%D8%A7%D9%84%D8%A7%D8%B1%D9%87%D8%A7%D8%A8%D9%8A%D8%A9-%D9%88%D8%A7%D9%84%D8%A7%D8%B1%D9%87%D8%A7%D8%A8%D9%8A%D9%8A%D9%86-%D8%A7%D9%84%D9%85%D8%AD%D9%84%D9%8A%D8%A9-%D8%B9%D9%85%D9%84%D8%A7-%D8%A8%D9%82%D8%B1%D8%A7%D8%B1-%D9%85%D8%AC%D9%84%D8%B3-%D8%A7%D9%84%D8%A7%D9%85%D9%86-1373";

    List<String> urls = scrapeXlsxLinksFromUrl(localTerrorismUrl);
    this.addNegativeList(urls.get(0), ListType.TERRORISM_LOCAL, 1);
    urls = scrapeXlsxLinksFromUrl(unUrl);
    this.addNegativeList(urls.get(0), ListType.TERRORISM_UN, 2);
    this.addNegativeList(urls.get(1), ListType.TERRORISM_UN, 3);
    this.addNegativeList(urls.get(2), ListType.TERRORISM_UN, 4);
    this.flagExistingUsers(false, true);
  }

  public List<NegativeList> addNegativeList(String fileUrl, ListType listType, Integer fileType)
      throws BusinessException, IntegrationException {
    try {
      String decodedUrl = URLDecoder.decode(fileUrl, StandardCharsets.UTF_8);
      String fileName = extractFileNameFromUrl(decodedUrl);
      List<NegativeList> entities = new ArrayList<>();

      var recordCount = negativeListRepository.countByListSource(fileName); // Use filename as list source
      if (recordCount > 0)
        return entities;

      URL url = new URL(fileUrl);
      HttpURLConnection conn = (HttpURLConnection) url.openConnection();

      // Pretend to be a browser
      conn.setRequestProperty("User-Agent",
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 " +
              "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");

      conn.connect();

      // Download file from URL
      try (InputStream inputStream = conn.getInputStream()) {
        if (fileName != null && fileName.toLowerCase().endsWith(".xlsx")) {
          entities = parseExcelFile(inputStream, fileName, listType, fileType);
        } else {
          throw new BusinessException(ErrorCode.INVALID_NEGATIVE_LIST_FORMAT);
        }
      }

      // Check for duplicate records and delete old ones before saving new ones
      for (NegativeList entity : entities) {
        List<NegativeList> existingRecords = negativeListRepository.findByKeyFields(
            entity.getFullName(),
            entity.getNationality(),
            entity.getPassportNumber(),
            entity.getNationalId());
        if (!existingRecords.isEmpty()) {
          MyLogger.info(
              "Deleting " + existingRecords.size() + " existing duplicate records for entity: " + entity.getFullName());
          negativeListRepository.deleteAll(existingRecords);
          entity.setCreatedAt(existingRecords.get(0).getCreatedAt());
        }
      }

      negativeListRepository.saveAll(entities);

      MyLogger.info("Successfully uploaded " + entities.size() + " entries for list: " + fileUrl);

      return entities;

    } catch (Exception e) {
      MyLogger.logStackTrace(e);
      throw new BusinessException(ErrorCode.OPERATION_NOT_PERFORMED);
    }
  }

  public List<NegativeList> uploadHtmlFile(MultipartFile file)
      throws BusinessException, IntegrationException {
    try {
      String fileName = file.getOriginalFilename();

      var recordCount = negativeListRepository.countByListSource(fileName); // Use filename as list source
      if (recordCount > 0)
        throw new BusinessException(ErrorCode.NEGATIVE_LIST_ALREADY_EXISTS);

      // Parse HTML file from uploaded content
      List<NegativeList> entities = parseHtmlFile(file.getInputStream(), fileName, ListType.SANCTION);

      // Check for duplicate records and delete old ones before saving new ones
      for (NegativeList entity : entities) {
        List<NegativeList> existingRecords = negativeListRepository.findByKeyFields(
            entity.getFullName(),
            entity.getNationality(),
            entity.getPassportNumber(),
            entity.getNationalId());
        if (!existingRecords.isEmpty()) {
          MyLogger.info(
              "Deleting " + existingRecords.size() + " existing duplicate records for entity: " + entity.getFullName());
          negativeListRepository.deleteAll(existingRecords);
        }
      }

      // Save all entities
      negativeListRepository.saveAll(entities);

      MyLogger.info("Successfully uploaded HTML file: " + fileName +
          " with " + entities.size() + " entries");
      this.flagExistingUsers(false, true);

      return entities;

    } catch (Exception e) {
      MyLogger.logStackTrace(e);
      throw new BusinessException(ErrorCode.OPERATION_NOT_PERFORMED);
    }
  }

  private String extractFileNameFromUrl(String fileUrl) {
    try {
      String path = new URL(fileUrl).getPath();
      return path.substring(path.lastIndexOf('/') + 1);
    } catch (Exception e) {
      return "unknown";
    }
  }

  private List<NegativeList> parseExcelFile(InputStream inputStream, String listSource, ListType listType,
      Integer fileType)
      throws Exception {
    List<NegativeList> entities = new ArrayList<>();
    Workbook workbook = new XSSFWorkbook(inputStream);
    Sheet sheet = workbook.getSheetAt(0);

    for (Row row : sheet) {
      if (row.getRowNum() == 0 || (row.getRowNum() == 1 && fileType > 1))
        continue; // Skip header row

      NegativeList entity = new NegativeList();
      switch (fileType) {
        case 1: // Local Terrorism
          entity.setFullName(getCellValue(row.getCell(0)));
          entity.setAlias(getCellValue(row.getCell(1)));
          entity.setNationalId(StringUtility.convertArabicToEnglish(getCellValue(row.getCell(2))));
          entity.setPassportNumber(getCellValue(row.getCell(3)));
          entity.setNationality(getCellValue(row.getCell(4)));
          entity.setAdditionalInfo(getCellValue(row.getCell(5)) + "," + getCellValue(row.getCell(6)) + "," +
              getCellValue(row.getCell(7)) + "," + getCellValue(row.getCell(8)));
          break;

        case 2: // UN 1
        case 4: // UN 3
          if (!StringUtility.stringsMatch(getCellValue(row.getCell(0)), "شخص"))
            continue; // Skip non-person entries
          var fullName = getCellValue(row.getCell(6));
          var concatName = getCellValue(row.getCell(2)) + " " + getCellValue(row.getCell(3)) +
              " " + getCellValue(row.getCell(4)) + " " + getCellValue(row.getCell(5));
          entity.setFullName(StringUtility.stringsMatch(fullName.toLowerCase(), "na") ? concatName : fullName);
          entity.setDateOfBirth(getCellValue(row.getCell(7)));
          entity.setPlaceOfBirth(getCellValue(row.getCell(8)));
          entity.setAlias(getCellValue(row.getCell(9)));
          entity.setNationality(getCellValue(row.getCell(11)));
          entity.setPassportNumber(StringUtility.extractPassportNumbers(getCellValue(row.getCell(12))));
          entity.setNationalId(StringUtility.extractPassportNumbers(getCellValue(row.getCell(13))));
          entity.setAddress(getCellValue(row.getCell(14)));
          entity.setAdditionalInfo(getCellValue(row.getCell(1)) + "," + getCellValue(row.getCell(15)));
          break;

        case 3: // UN 2
          var fullName2 = getCellValue(row.getCell(5));
          var concatName2 = getCellValue(row.getCell(1)) + " " + getCellValue(row.getCell(2)) +
              " " + getCellValue(row.getCell(3)) + " " + getCellValue(row.getCell(4));
          entity.setFullName(StringUtility.stringsMatch(fullName2.toLowerCase(), "na") ? concatName2 : fullName2);
          entity.setDateOfBirth(getCellValue(row.getCell(6)));
          entity.setPlaceOfBirth(getCellValue(row.getCell(7)));
          entity.setAlias(getCellValue(row.getCell(8)));
          entity.setNationality(getCellValue(row.getCell(10)));
          entity.setPassportNumber(StringUtility.extractPassportNumbers(getCellValue(row.getCell(11))));
          entity.setNationalId(StringUtility.extractPassportNumbers(getCellValue(row.getCell(12))));
          entity.setAddress(getCellValue(row.getCell(13)));
          entity.setAdditionalInfo(getCellValue(row.getCell(1)) + "," + getCellValue(row.getCell(14)));
          break;

        default:
          break;
      }

      entity.setListSource(listSource);
      entity.setListType(listType);
      entity.setCreatedAt(DateUtility.getCurrentDate());
      entity.setUpdatedAt(DateUtility.getCurrentDate());

      // Skip empty rows
      if (entity.getFullName() != null && !entity.getFullName().trim().isEmpty()) {
        entities.add(entity);
      }
    }

    workbook.close();
    return entities;
  }

  private List<NegativeList> parseHtmlFile(InputStream inputStream, String listSource, ListType listType)
      throws Exception {
    List<NegativeList> entities = new ArrayList<>();

    // Read HTML content
    StringBuilder htmlContent = new StringBuilder();
    Charset windows1256 = Charset.forName("windows-1256");
    try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, windows1256))) {
      String line;
      while ((line = reader.readLine()) != null) {
        htmlContent.append(line).append("\n");
      }
    }

    String html = htmlContent.toString();

    // Parse HTML tables using simple regex patterns
    // Look for table rows containing personal data
    Pattern tableRowPattern = Pattern.compile("<tr[^>]*>(.*?)</tr>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);
    Pattern cellPattern = Pattern.compile("<td[^>]*>(.*?)</td>", Pattern.CASE_INSENSITIVE | Pattern.DOTALL);

    Matcher rowMatcher = tableRowPattern.matcher(html);

    while (rowMatcher.find()) {
      String rowContent = rowMatcher.group(1);

      // Skip header rows (check for Arabic headers)
      if (rowContent.toLowerCase().contains("<th") ||
          rowContent.contains("رقم الحالة") || rowContent.contains("الاسم") ||
          rowContent.contains("البطاقة") || rowContent.contains("العنوان") ||
          rowContent.contains("المهنه")) {
        continue;
      }

      Matcher cellMatcher = cellPattern.matcher(rowContent);
      List<String> cells = new ArrayList<>();

      while (cellMatcher.find()) {
        String cellContent = cellMatcher.group(1);
        // Clean HTML tags and decode entities
        cellContent = cellContent.replaceAll("<[^>]+>", "").trim();
        cellContent = cellContent.replace("&nbsp;", " ").replace("&amp;", "&")
            .replace("&lt;", "<").replace("&gt;", ">")
            .replace("&quot;", "\"");
        cells.add(cellContent);
      }

      // Process cells if we have enough data (at least case number and name)
      if (cells.size() >= 2 && !cells.get(0).trim().isEmpty() && !cells.get(1).trim().isEmpty()) {
        NegativeList entity = new NegativeList();

        // Map fields according to Arabic structure:
        // 0: رقم الحالة (Case Number)
        // 1: الاسم (Name)
        // 2: البطاقة (ID Card/National ID)
        // 3: العنوان (Address)
        // 4: المهنه (Profession)

        String caseNumber = cells.get(0).trim();
        String fullName = cells.get(1).trim();

        if (fullName.isEmpty())
          continue;

        entity.setFullName(fullName);

        // Map other fields according to Arabic structure
        if (cells.size() > 2 && !cells.get(2).trim().isEmpty()) {
          entity.setNationalId(cells.get(2).trim()); // البطاقة (ID Card)
        }
        if (cells.size() > 3 && !cells.get(3).trim().isEmpty()) {
          entity.setAddress(cells.get(3).trim()); // العنوان (Address)
        }
        if (cells.size() > 4 && !cells.get(4).trim().isEmpty()) {
          entity.setProfession(cells.get(4).trim()); // المهنه (Profession)
        }

        String additionalInfo = "Case Number: " + caseNumber;
        entity.setAdditionalInfo(additionalInfo);

        entity.setListSource(listSource);
        entity.setListType(listType);
        entity.setCreatedAt(DateUtility.getCurrentDate());
        entity.setUpdatedAt(DateUtility.getCurrentDate());

        entities.add(entity);
      }
    }

    return entities;
  }

  public Boolean nationalIdMatch(String passportNumber, String nationalId, NegativeList match) {
    if (StringUtility.isStringPopulated(nationalId) && StringUtility.isStringPopulated(match.getNationalId())) {
      String last4 = nationalId.substring(nationalId.length() - 4);
      for (String idToCheck : match.getNationalId().split(" - ")) {
        if (nationalId.length() == idToCheck.length()
            && StringUtility.stringsMatch(last4, idToCheck.substring(idToCheck.length() - 4)))
          return true;
      }
    } else if (StringUtility.isStringPopulated(passportNumber)
        && StringUtility.isStringPopulated(match.getPassportNumber())) {
      String last4 = passportNumber.substring(passportNumber.length() - 4);
      for (String idToCheck : match.getPassportNumber().split(" - ")) {
        if (passportNumber.length() == idToCheck.length()
            && StringUtility.stringsMatch(last4, idToCheck.substring(idToCheck.length() - 4)))
          return true;
      }
    } else
      return true;
    return false;
  }

  // only used in endpoint search
  public BusinessNegativeListScreeningResult performScreening(String userFullName, String passportNumber,
      String nationalId, String nationality, String dateOfBirth) {

    BusinessNegativeListScreeningResult result = new BusinessNegativeListScreeningResult();
    result.setUserName(userFullName);
    result.setIsMatch(false);
    result.setMatchCount(0);
    result.setMatches(new ArrayList<>());
    result.setScreeningStatus("CLEAR");
    result.setRiskLevel("LOW");

    List<BusinessNegativeListMatch> allMatches = new ArrayList<>();

    // 1. Fuzzy name search
    List<NegativeList> fuzzyMatches = negativeListRepository.findByNameFuzzySearch(userFullName);
    for (NegativeList match : fuzzyMatches) {
      if (nationalIdMatch(passportNumber, nationalId, match)) {
        BusinessNegativeListMatch businessMatch = createBusinessMatch(match, "EXACT", 1.0, "FULL_NAME");
        allMatches.add(businessMatch);
      }
    }

    // 2. Fuzzy search for every 2 consecutive words in userFullName
    if (StringUtility.isStringPopulated(userFullName)) {
      String[] words = userFullName.split("\\s+");
      for (int i = 0; i < words.length - 1; i++) {
        String twoWordPhrase = words[i] + " " + words[i + 1];
        List<NegativeList> phraseMatches = negativeListRepository.findByNameFuzzySearch(twoWordPhrase);
        for (NegativeList match : phraseMatches) {
          var score = FuzzyMatchUtility.similarity(userFullName, match.getFullName());
          if (score >= 0.75 && nationalIdMatch(passportNumber, nationalId, match)) {
            MyLogger.info(userFullName + "-" + match.getFullName() + " ---- " + score);
            BusinessNegativeListMatch businessMatch = createBusinessMatch(match, "PARTIAL", score,
                "FULL_NAME_PHRASE");
            allMatches.add(businessMatch);
          }
        }
      }
    }

    // 3. Document-based matching
    if (passportNumber != null && !passportNumber.trim().isEmpty() ||
        nationalId != null && !nationalId.trim().isEmpty()) {
      List<NegativeList> docMatches = negativeListRepository
          .findByIdentificationDocuments(passportNumber, nationalId);
      for (NegativeList match : docMatches) {
        String matchedField = passportNumber != null && passportNumber.equals(match.getPassportNumber())
            ? "PASSPORT"
            : "NATIONAL_ID";
        BusinessNegativeListMatch businessMatch = createBusinessMatch(match, "EXACT", 1.0, matchedField);
        allMatches.add(businessMatch);
      }
    }

    // 4. last 4 digits in id
    if (StringUtility.isStringPopulated(nationalId)) {
      String searchTerm = "(^|[^A-Za-z0-9])[A-Za-z0-9]*" + nationalId.substring(nationalId.length() - 4)
          + "([^A-Za-z0-9]|$)";
      List<NegativeList> docMatches = negativeListRepository.findByNationalIdLast4(searchTerm);
      for (NegativeList match : docMatches) {
        String matchedField = "NATIONAL_ID";
        var score = FuzzyMatchUtility.similarity(userFullName, match.getFullName());
        if (score >= 0.75 && nationalIdMatch(passportNumber, nationalId, match)) { // Fixed threshold
          BusinessNegativeListMatch businessMatch = createBusinessMatch(match, "PARTIAL", score, matchedField);
          allMatches.add(businessMatch);
        }

      }
    }

    // 5. Nationality and DOB matching
    if (nationality != null && dateOfBirth != null) {
      List<NegativeList> bioMatches = negativeListRepository
          .findByNationalityAndDateOfBirth(nationality, dateOfBirth);
      for (NegativeList match : bioMatches) {
        var score = FuzzyMatchUtility.similarity(userFullName, match.getFullName());
        if (score >= 0.75 && nationalIdMatch(passportNumber, nationalId, match)) { // Fixed threshold
          BusinessNegativeListMatch businessMatch = createBusinessMatch(match, "PARTIAL", score, "NATIONALITY_DOB");
          allMatches.add(businessMatch);
        }
      }
    }

    // Remove duplicates and set results
    allMatches = allMatches.stream()
        .collect(Collectors.toMap(
            m -> m.getNegativeList().getId(),
            m -> m,
            (existing, replacement) -> existing.getConfidenceScore() > replacement.getConfidenceScore()
                ? existing
                : replacement))
        .values()
        .stream()
        .collect(Collectors.toList());

    result.setMatches(allMatches);
    result.setMatchCount(allMatches.size());
    result.setIsMatch(!allMatches.isEmpty());

    if (!allMatches.isEmpty()) {
      double maxConfidence = allMatches.stream()
          .mapToDouble(BusinessNegativeListMatch::getConfidenceScore)
          .max().orElse(0.0);

      result.setConfidenceScore(maxConfidence);

      if (maxConfidence >= 0.95) {
        result.setScreeningStatus("BLOCKED");
        result.setRiskLevel("CRITICAL");
      } else if (maxConfidence >= 0.75) {
        result.setScreeningStatus("POTENTIAL_MATCH");
        result.setRiskLevel("HIGH");
      } else {
        result.setScreeningStatus("POTENTIAL_MATCH");
        result.setRiskLevel("MEDIUM");
      }
    }

    return result;
  }

  private BusinessNegativeListMatch createBusinessMatch(NegativeList negativeList, String matchType,
      double confidence, String matchedField) {
    BusinessNegativeListMatch match = new BusinessNegativeListMatch();
    match.setNegativeList(negativeList);
    match.setMatchedName(negativeList.getFullName());
    match.setListSource(negativeList.getListSource());
    match.setMatchType(matchType);
    match.setConfidenceScore(confidence);
    match.setMatchedField(matchedField);
    match.setAdditionalInfo(negativeList.getAdditionalInfo());
    return match;
  }

  public void deleteNegativeListEntry(Long id) throws BusinessException {
    try {
      if (!negativeListRepository.existsById(id)) {
        throw new BusinessException(ErrorCode.NO_DATA_FOUND);
      }
      negativeListRepository.deleteById(id);
      MyLogger.info("Successfully deleted negative list entry with ID: " + id);
    } catch (Exception e) {
      MyLogger.logStackTrace(e);
      throw new BusinessException(ErrorCode.OPERATION_NOT_PERFORMED);
    }
  }

  public NegativeListStatistics getNegativeListStatistics() throws BusinessException {
    try {
      NegativeListStatistics statistics = new NegativeListStatistics();

      // Total number of records
      Long totalRecords = negativeListRepository.count();
      statistics.setTotalRecords(totalRecords);

      // Number of unique files/sources
      List<Object[]> sourceStats = negativeListRepository.countByListSourceGrouped();
      statistics.setTotalFiles(sourceStats.size());

      // Records by source
      Map<String, Long> recordsBySource = new HashMap<>();
      for (Object[] stat : sourceStats) {
        recordsBySource.put((String) stat[0], (Long) stat[1]);
      }
      statistics.setRecordsBySource(recordsBySource);

      // Records by list type
      Map<String, Long> recordsByType = new HashMap<>();
      List<Object[]> typeStats = negativeListRepository.countByListTypeGrouped();
      for (Object[] stat : typeStats) {
        String type = (stat[0] != null) ? stat[0].toString() : "UNKNOWN";
        recordsByType.put(type, (Long) stat[1]);
      }
      statistics.setRecordsByType(recordsByType);

      return statistics;

    } catch (Exception e) {
      MyLogger.logStackTrace(e);
      throw new BusinessException(ErrorCode.OPERATION_NOT_PERFORMED);
    }
  }

  private Map<String, List<NegativeList>> buildNameIndex(List<NegativeList> negativeRecords) {
    Map<String, List<NegativeList>> nameIndex = new HashMap<>();

    for (NegativeList record : negativeRecords) {
      if (StringUtility.isStringPopulated(record.getFullName())) {
        // Index by full name
        String normalizedName = record.getFullName().toLowerCase().trim();
        nameIndex.computeIfAbsent(normalizedName, k -> new ArrayList<>()).add(record);

        // Index by individual words for partial matching
        String[] words = normalizedName.split("\\s+");
        for (String word : words) {
          if (word.length() > 2) { // Skip very short words
            nameIndex.computeIfAbsent(word, k -> new ArrayList<>()).add(record);
          }
        }
      }

      // Index aliases too
      if (StringUtility.isStringPopulated(record.getAlias())) {
        String normalizedAlias = record.getAlias().toLowerCase().trim();
        nameIndex.computeIfAbsent(normalizedAlias, k -> new ArrayList<>()).add(record);
      }
    }

    return nameIndex;
  }

  private Map<String, List<NegativeList>> buildDocumentIndex(List<NegativeList> negativeRecords) {
    Map<String, List<NegativeList>> documentIndex = new HashMap<>();

    for (NegativeList record : negativeRecords) {
      // Index passport numbers
      if (StringUtility.isStringPopulated(record.getPassportNumber())) {
        for (String passport : record.getPassportNumber().split(" - ")) {
          documentIndex.computeIfAbsent(passport.trim(), k -> new ArrayList<>()).add(record);
        }
      }

      // Index national IDs
      if (StringUtility.isStringPopulated(record.getNationalId())) {
        for (String nationalId : record.getNationalId().split(" - ")) {
          documentIndex.computeIfAbsent(nationalId.trim(), k -> new ArrayList<>()).add(record);

          // Index last 4 digits for partial matching
          if (nationalId.length() >= 4) {
            String last4 = nationalId.substring(nationalId.length() - 4);
            documentIndex.computeIfAbsent("LAST4_" + last4, k -> new ArrayList<>()).add(record);
          }
        }
      }
    }

    return documentIndex;
  }

  private int checkUserAgainstIndexes(User user, Map<String, List<NegativeList>> nameIndex,
      Map<String, List<NegativeList>> documentIndex) {
    Set<Long> matchedRecords = new HashSet<>();

    // Prepare user data
    String userName = user.getFirstName() != null
        ? (user.getFirstName() + " " + (user.getLastName() != null ? user.getLastName() : "")).trim()
        : user.getNickName();

    String passportNumber = NumberUtility.areLongValuesMatching(user.getUserType().getId(),
        UserIdType.PASSPORT.getTypeId()) ? user.getUserId() : null;
    String nationalId = NumberUtility.areLongValuesMatching(user.getUserType().getId(),
        UserIdType.NATIONAL_ID.getTypeId()) ? user.getUserId() : null;

    // 1. Check exact name matches
    if (StringUtility.isStringPopulated(userName)) {
      String normalizedUserName = userName.toLowerCase().trim();
      List<NegativeList> exactMatches = nameIndex.get(normalizedUserName);
      if (exactMatches != null) {
        for (NegativeList match : exactMatches) {
          if (nationalIdMatch(passportNumber, nationalId, match)) {
            matchedRecords.add(match.getId());
          }
        }
      }

      // Check word-based partial matches
      String[] userWords = normalizedUserName.split("\\s+");
      for (String word : userWords) {
        if (word.length() > 2) {
          List<NegativeList> wordMatches = nameIndex.get(word);
          if (wordMatches != null) {
            for (NegativeList match : wordMatches) {
              double similarity = FuzzyMatchUtility.similarity(userName, match.getFullName());
              if (similarity >= 0.90 && nationalIdMatch(passportNumber, nationalId, match)) {
                MyLogger.info(userName + "-" + match.getFullName() + " ---- " + similarity);
                matchedRecords.add(match.getId());
              }
            }
          }
        }
      }
    }

    // 2. Check exact document matches
    if (StringUtility.isStringPopulated(passportNumber)) {
      List<NegativeList> passportMatches = documentIndex.get(passportNumber);
      if (passportMatches != null) {
        matchedRecords.addAll(passportMatches.stream().map(NegativeList::getId).collect(Collectors.toSet()));
      }
    }

    if (StringUtility.isStringPopulated(nationalId)) {
      List<NegativeList> nationalIdMatches = documentIndex.get(nationalId);
      if (nationalIdMatches != null) {
        matchedRecords.addAll(nationalIdMatches.stream().map(NegativeList::getId).collect(Collectors.toSet()));
      }

      // Check last 4 digits
      if (nationalId.length() >= 4) {
        String last4 = nationalId.substring(nationalId.length() - 4);
        List<NegativeList> last4Matches = documentIndex.get("LAST4_" + last4);
        if (last4Matches != null) {
          for (NegativeList match : last4Matches) {
            double similarity = FuzzyMatchUtility.similarity(userName, match.getFullName());
            if (similarity >= 0.75) {
              MyLogger.info(userName + "-" + match.getFullName() + " ---- " + similarity);
              matchedRecords.add(match.getId());
            }
          }
        }
      }
    }

    return matchedRecords.size();
  }

  // Index-based approach using HashMap lookups
  public void flagExistingUsers(Boolean recheckAll, Boolean old) throws BusinessException, IntegrationException {
    try {
      MyLogger.info("Starting indexed screening process", true);

      // Build indexes for fast lookups
      List<NegativeList> allNegativeRecords = BooleanUtility.isTrue(recheckAll) ? negativeListRepository.findAll()
          : negativeListRepository.findByCreatedAtToday();
      Map<String, List<NegativeList>> nameIndex = buildNameIndex(allNegativeRecords);
      Map<String, List<NegativeList>> documentIndex = buildDocumentIndex(allNegativeRecords);

      int flaggedUserCount = 0;
      if (BooleanUtility.isTrue(recheckAll))
        userRepository.updateAzimutAmlMatchesToZeroForAllUsers();
      List<User> allUsers = BooleanUtility.isTrue(old) ? userRepository.findNonDeletedUsers()
          : userRepository.findNonUpdatedAmlUsers();

      MyLogger.info("Starting screening process for " + allUsers.size() + " users in " + allNegativeRecords.size()
          + " negative lists", true);

      for (User user : allUsers) {
        if (user.getUserType() == null || !StringUtility.isStringPopulated(user.getUserId())
            || NumberUtility.areIntegerValuesMatching(user.getKycStatus(), KycStatus.FIRST_TIME.getStatusId())) {
          continue;
        }

        int userMatches = checkUserAgainstIndexes(user, nameIndex, documentIndex);
        if (userMatches > 0) {
          user.setAzimutAmlMatches(userMatches);
          user.setTeacomputersClientaml(StringUtility.CLIENT_AML_BLACKLIST);
          userService.save(user);
          flaggedUserCount++;
          MyLogger.info("User flagged in negative list screening: " + user.getId() +
              " - Matched with: " + userMatches +
              " - Updated match count: " + user.getAzimutAmlMatches(), true);
        }
        // in update all user we don't reset azimutAmlMatches to matches if not matching
        // partial users

        // Log progress every 1000 users
        if ((allUsers.indexOf(user) + 1) % 1000 == 0) {
          MyLogger.info("Processed " + (allUsers.indexOf(user) + 1) + " users", true);
        }
      }

      MyLogger.info("Completed indexed screening. " + flaggedUserCount + " matches found.", true);

    } catch (Exception e) {
      MyLogger.logStackTrace(e);
      throw new BusinessException(ErrorCode.OPERATION_NOT_PERFORMED);
    }
  }

  public void checkNegativeListMatch(BusinessUser user) throws BusinessException {

    MyLogger.info("Starting screening for user: " + user.getId());

    // Prepare user data for screening
    String userName = user.getFirstName() != null
        ? (user.getFirstName() + " " + (user.getLastName() != null ? user.getLastName() : "")).trim()
        : user.getNickName();

    // Use userId as both passport and national ID for screening
    String passportNumber = NumberUtility.areLongValuesMatching(user.getAzimutIdTypeId(),
        UserIdType.PASSPORT.getTypeId()) ? user.getUserId() : null;
    String nationalId = NumberUtility.areLongValuesMatching(user.getAzimutIdTypeId(),
        UserIdType.NATIONAL_ID.getTypeId()) ? user.getUserId() : null;

    var res = performScreening(userName, passportNumber, nationalId, null, null);
    if (res.getMatchCount() > 0) {
      // Set the match count for this user
      user.setAzimutAmlMatches(res.getMatchCount());
      user.setKycStatus(KycStatus.REJECTED.getStatusId());
      user.getAzimutAccount().setClientAML(StringUtility.CLIENT_AML_BLACKLIST);
      var reason = reasonService.findByReasonType(ReasonType.AML).get(0);
      reviewService.addRejectReview(user.getId(), "User is blacklisted in AML", reason);
      this.editUser(user);

      // Log the flagged user
      MyLogger.info("User flagged in negative list screening: " + user.getId() +
          " - Matched with: " + res.getMatchCount() +
          " - Updated match count: " + user.getAzimutAmlMatches(), true);
    } else {
      user.setAzimutAmlMatches(0);
      this.editUser(user);
    }

  }

  private String getCellValue(Cell cell) {
    if (cell == null)
      return "";
    switch (cell.getCellType()) {
      case STRING:
        return cell.getStringCellValue().trim();
      case NUMERIC:
        return String.valueOf((long) cell.getNumericCellValue());
      default:
        return "";
    }
  }

  /**
   * Scrapes the specified URL and returns any links that end with .xlsx
   * 
   * @param url The URL to scrape
   * @return Array of URLs ending with .xlsx
   * @throws IOException if there's an error connecting to the URL
   */
  public List<String> scrapeXlsxLinksFromUrl(String url) throws IOException {
    URL website = new URL(url);
    HttpURLConnection connection = (HttpURLConnection) website.openConnection();

    // Set user agent to simulate a browser request
    connection.setRequestProperty("User-Agent",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36");

    connection.connect();

    // Read the response
    StringBuilder response = new StringBuilder();
    try (BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
      String line;
      while ((line = reader.readLine()) != null) {
        response.append(line);
      }
    }

    // Find all links ending with .xlsx
    Pattern pattern = Pattern.compile("href\\s*=\\s*[\\\"']([^\\\"']*\\.xlsx[^\\\"']*)[\\\"']",
        Pattern.CASE_INSENSITIVE);
    Matcher matcher = pattern.matcher(response.toString());

    List<String> xlsxLinks = new ArrayList<>();
    while (matcher.find()) {
      String link = matcher.group(1);
      // Handle relative URLs
      if (link.startsWith("/")) {
        // Convert relative URL to absolute
        URL baseUrl = new URL(url);
        link = baseUrl.getProtocol() + "://" + baseUrl.getHost() + link;
      } else if (!link.startsWith("http")) {
        // Handle relative paths
        URL baseUrl = new URL(url);
        String base = baseUrl.toString();
        int lastSlash = base.lastIndexOf('/');
        if (lastSlash > 0) {
          base = base.substring(0, lastSlash + 1);
        }
        link = base + link;
      }
      xlsxLinks.add(link);
    }

    MyLogger.info("Found " + xlsxLinks.size() + " .xlsx links on page: " + url);
    MyLogger.info(xlsxLinks.toString());

    return xlsxLinks;
  }

  public NegativeList addManualEntry(ManualNegativeListEntryDto dto) throws BusinessException {
    // Validation
    if (!StringUtility.isStringPopulated(dto.getFullName())) {
      throw new BusinessException(ErrorCode.INVALID_INPUT);
    }

    // Document validation
    if (StringUtility.isStringPopulated(dto.getPassportNumber()) &&
        !dto.getPassportNumber().matches(RegexPattern.Constants.PASSPORT_REGEX)) {
      throw new BusinessException(ErrorCode.INVALID_PASSPORT_FORMAT);
    }

    if (StringUtility.isStringPopulated(dto.getNationalId()) &&
        !dto.getNationalId().matches(RegexPattern.Constants.NATIONAL_ID_REGEX)) {
      throw new BusinessException(ErrorCode.INVALID_NATIONAL_ID_FORMAT);
    }

    // Existing check and save
    List<NegativeList> existing = negativeListRepository.findByKeyFields(
        dto.getFullName(), dto.getNationality(),
        dto.getPassportNumber(), dto.getNationalId());

    if (!existing.isEmpty()) {
      throw new BusinessException(ErrorCode.DUPLICATE_ENTRY);
    }

    NegativeList entry = new NegativeList();
    entry.setFullName(dto.getFullName());
    entry.setAlias(dto.getAlias());
    entry.setNationality(dto.getNationality());
    entry.setDateOfBirth(dto.getDateOfBirth());
    entry.setPassportNumber(dto.getPassportNumber());
    entry.setNationalId(dto.getNationalId());
    entry.setListSource(dto.getListSource());
    entry.setListType(dto.getListType());
    entry.setAdditionalInfo(dto.getAdditionalInfo());
    entry.setCreatedAt(new Date());
    entry.setUpdatedAt(new Date());

    return negativeListRepository.save(entry);
  }
}
