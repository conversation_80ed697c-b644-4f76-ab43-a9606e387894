package innovitics.azimut.businessservices;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.xml.bind.DatatypeConverter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessmodels.user.AzimutAccount;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.kyc.ReasonType;
import innovitics.azimut.models.kyc.Review;
import innovitics.azimut.models.user.UserImage;
import innovitics.azimut.rest.apis.gateid.GateIdCreateTask;
import innovitics.azimut.rest.apis.gateid.GateIdGetTask;
import innovitics.azimut.rest.apis.gateid.GateIdNidOcr;
import innovitics.azimut.rest.apis.gateid.GateIdUploadBack;
import innovitics.azimut.rest.apis.gateid.GateIdUploadFront;
import innovitics.azimut.rest.apis.gateid.GateIdUploadPassport;
import innovitics.azimut.rest.apis.gateid.GateIdUploadSelfie;
import innovitics.azimut.rest.entities.gateid.GateIdImageInput;
import innovitics.azimut.rest.entities.gateid.GateIdTask;
import innovitics.azimut.services.kyc.UserImageService;
import innovitics.azimut.services.user.GenderService;
import innovitics.azimut.utilities.businessutilities.UserUtility;
import innovitics.azimut.utilities.crosslayerenums.UserIdType;
import innovitics.azimut.utilities.crosslayerenums.UserImageType;
import innovitics.azimut.utilities.crosslayerenums.UserStep;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.CustomMultipartFile;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.JsonUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.validations.Validation;

@Service
public class GateIdService extends BusinessUserSigningService {

  private @Autowired Validation validation;
  private @Autowired UserUtility userUtility;
  private @Autowired GenderService genderService;
  private @Autowired UserImageService userImageService;
  private @Autowired GateIdCreateTask gateIdCreateTask;
  private @Autowired GateIdUploadFront gateIdUploadFront;
  private @Autowired GateIdUploadBack gateIdUploadBack;
  private @Autowired GateIdUploadPassport gateIdUploadPassport;
  private @Autowired GateIdUploadSelfie gateIdUploadSelfie;
  private @Autowired GateIdNidOcr gateIdNidOcr;
  private @Autowired GateIdGetTask gateIdGetTask;
  private @Autowired FraService fraService;

  public void saveNid(BusinessUser businessUser, MultipartFile frontImageIn, MultipartFile backImageIn)
      throws IntegrationException, BusinessException, IOException {
    this.validation.validateUserKYCCompletion(businessUser);

    List<UserImage> userImages = new ArrayList<UserImage>();

    var createTaskRes = gateIdCreateTask.getData(null);
    Long taskId = createTaskRes.getData().getAddTask().getId();
    businessUser.setGateIdTask(taskId);
    this.editUser(businessUser); // update user gateId task so that get status work on new task
    GateIdImageInput imageInput = new GateIdImageInput();

    imageInput.setImage(frontImageIn);
    var frontRes = gateIdUploadFront.getData(imageInput, taskId.toString());
    byte[] base64 = DatatypeConverter.parseBase64Binary(frontRes.getFront());
    MultipartFile frontImage = new CustomMultipartFile(base64, "idFront.jpeg");
    UserImage frontUserImage = this.userUtility.createUserImageRecord(businessUser, frontImage,
        UserImageType.FRONT_IMAGE);
    userImages.add(frontUserImage);

    imageInput.setImage(backImageIn);
    var backRes = gateIdUploadBack.getData(imageInput, taskId.toString());
    base64 = DatatypeConverter.parseBase64Binary(backRes.getBack());
    MultipartFile backImage = new CustomMultipartFile(base64, "idBack.jpeg");
    UserImage backUserImage = this.userUtility.createUserImageRecord(businessUser, backImage,
        UserImageType.BACK_IMAGE);
    userImages.add(backUserImage);

    var ocr = gateIdNidOcr.getData(null, taskId.toString());

    String state = ocr.getArea();
    var splitState = state.split("-");
    Map<String, Object> idData = new HashMap<>();
    idData.put("religion", ocr.getReligion());
    idData.put("maritalStatus", ocr.getMarital());
    idData.put("fcn", ocr.getFcn());
    idData.put("similarity", ocr.getSimilarity());
    List<Double> percentages = new ArrayList<>();
    percentages.add(ocr.getFrontColorScore());
    percentages.add(ocr.getBackColorScore());
    percentages.add(ocr.getFontManipulationPercentage());
    idData.put("percentages", percentages);
    businessUser.setIdData(JsonUtility.toJson(idData));
    businessUser.setFirstName(ocr.getFirstName());
    businessUser.setLastName(ocr.getLastName());
    businessUser.setCountry(StringUtility.EGYPT);
    businessUser.setCity(splitState[splitState.length - 1].trim());
    businessUser.setDateOfBirth(DateUtility.getDateForGateId(ocr.getBirthDateEn()));
    businessUser.setDateOfIdExpiry(DateUtility.getDateForGateId(ocr.getExpirationDateEn()));
    businessUser.setUserId(ocr.getNidEn());
    businessUser.setIdType(UserIdType.NATIONAL_ID.getTypeId());
    businessUser.setGenderId(this.determineUserGender(ocr.getGender()));
    businessUser.setDateOfRelease(DateUtility.getDateForGateId(ocr.getCreationDateEn() + "/01"));
    AzimutAccount azimutAccount = businessUser.getAzimutAccount();
    if (StringUtility.isStringPopulated(ocr.getArea()) && StringUtility.isStringPopulated(ocr.getAddress())) {
      var address = StringUtility.convertArabicToEnglish(ocr.getAddress());
      azimutAccount.setAddressAr(address + "," + splitState[0].trim());
      azimutAccount.setAddressEn(address + "," + splitState[0].trim());
    }
    if (StringUtility.isStringPopulated(ocr.getProfession()))
      azimutAccount.setOccupation(ocr.getProfession() + " " + ocr.getWorkplace());
    var oldIdData = StringUtility.isStringPopulated(businessUser.getIdData())
        ? JsonUtility.fromJson(businessUser.getIdData())
        : null;
    this.editUser(businessUser);

    this.userUtility.uploadUserImages(userImages, businessUser);
    userImageService.saveImages(userImages);
    if (businessUser.getUserStep() >= UserStep.CONTRACT_MAP.getStepId()
        && !BooleanUtility.isTrue(businessUser.getIsVerified()) && oldIdData != null) {
      // if the user is finished and not verified, we need to check the CSO
      var oldFcn = (String) oldIdData.get("fcn");
      if (StringUtility.isStringPopulated(oldFcn) && !StringUtility.stringsMatch(oldFcn, ocr.getFcn())) {
        var fraReviews = this.reviewUtility.getFraReviewsByUserId(businessUser.getId());
        for (Review review : fraReviews) {
          if (NumberUtility.areIntegerValuesMatching(review.getReason().getReasonType().getTypeId(),
              ReasonType.CSO.getTypeId())) {
            fraService.checkCso(businessUser);
            postFraActions(businessUser);
            autoAcceptActions(businessUser, false);
          }
        }
      }
    }
  }

  public void savePassport(BusinessUser businessUser, MultipartFile passportImage)
      throws BusinessException, IntegrationException, IOException {
    this.validation.validateUserKYCCompletion(businessUser);

    List<UserImage> userImages = new ArrayList<UserImage>();

    var createTaskRes = gateIdCreateTask.getData(null);
    Long taskId = createTaskRes.getData().getAddTask().getId();
    businessUser.setGateIdTask(taskId);
    this.editUser(businessUser); // update user gateId task so that get status work on new task
    GateIdImageInput imageInput = new GateIdImageInput();

    imageInput.setImage(passportImage);
    var res = gateIdUploadPassport.getData(imageInput, taskId.toString());
    byte[] base64 = DatatypeConverter.parseBase64Binary(res.getFront());
    MultipartFile frontImage = new CustomMultipartFile(base64, "idFront.jpeg");
    UserImage frontUserImage = this.userUtility.createUserImageRecord(businessUser, frontImage,
        UserImageType.PASSPORT_IMAGE);
    userImages.add(frontUserImage);

    Map<String, Object> idData = new HashMap<>();
    idData.put("similarity", res.getSimilarity());
    List<Double> percentages = new ArrayList<>();
    percentages.add(res.getFrontColorScore());
    percentages.add(res.getValidScore());
    idData.put("percentages", percentages);
    businessUser.setIdData(JsonUtility.toJson(idData));
    businessUser.setFirstName(res.getFirstName());
    businessUser.setLastName(res.getLastName());
    businessUser.setCountry(res.getCountry());
    businessUser.setDateOfBirth(DateUtility.getDateForGateId(res.getBirthDateEn()));
    businessUser.setDateOfIdExpiry(DateUtility.getDateForGateId(res.getExpirationDateEn()));
    businessUser.setUserId(res.getPassportNumber());
    businessUser.setIdType(UserIdType.PASSPORT.getTypeId());
    businessUser.setGenderId(this.determineUserGender(res.getGender()));
    this.editUser(businessUser);

    this.userUtility.uploadUserImages(userImages, businessUser);
    userImageService.saveImages(userImages);

  }

  public void saveSelfie(BusinessUser businessUser, MultipartFile straightFaceIn, MultipartFile leftSideIn,
      MultipartFile rightSideIn)
      throws IntegrationException, BusinessException, IOException {
    businessUser = this.userUtility.isOldUserStepGreaterThanNewUserStep(businessUser, UserStep.CLIENT_DATA.getStepId());
    List<UserImage> userImages = new ArrayList<UserImage>();

    GateIdImageInput imageInput = new GateIdImageInput();
    imageInput.setImage(straightFaceIn);
    var faceMatch = gateIdUploadSelfie.getData(imageInput, businessUser.getGateIdTask().toString());
    MyLogger.info("faceMatch: " + faceMatch.getSimilarity());
    imageInput.setImage(leftSideIn);
    var leftMatch = gateIdUploadSelfie.getData(imageInput, businessUser.getGateIdTask().toString());
    MyLogger.info("leftMatch: " + leftMatch.getSimilarity());
    imageInput.setImage(rightSideIn);
    var rightMatch = gateIdUploadSelfie.getData(imageInput, businessUser.getGateIdTask().toString());
    MyLogger.info("rightMatch: " + rightMatch.getSimilarity());
    if (faceMatch.getSimilarity() < 85 || leftMatch.getSimilarity() < 70 || rightMatch.getSimilarity() < 70) {
      throw new BusinessException(ErrorCode.FACE_NOT_MATCHING);
    }
    UserImage straightImage = this.userUtility.createUserImageRecord(businessUser, straightFaceIn,
        UserImageType.STRAIGHT);
    userImages.add(straightImage);

    UserImage leftImage = this.userUtility.createUserImageRecord(businessUser, leftSideIn,
        UserImageType.LEFT);
    userImages.add(leftImage);

    UserImage rightImage = this.userUtility.createUserImageRecord(businessUser, rightSideIn,
        UserImageType.RIGHT);
    userImages.add(rightImage);

    businessUser.setLivenessChecked(true);
    this.editUser(businessUser);
    this.userUtility.uploadUserImages(userImages, businessUser);
    userImageService.saveImages(userImages);
  }

  public GateIdTask getStatus(BusinessUser businessUser) throws IntegrationException, BusinessException {
    if (businessUser.getGateIdTask() == null)
      throw new BusinessException(ErrorCode.NO_DATA_FOUND);
    return gateIdGetTask.getData(null, businessUser.getGateIdTask().toString());
  }

  private Long determineUserGender(String genderType) {
    if (StringUtility.isStringPopulated(genderType)) {
      return this.genderService.determineGender(
          (StringUtility.stringsMatch(genderType, "ذكر") || StringUtility.stringsMatch(genderType, "Male")) ? "M"
              : "F");
    } else
      return null;
  }

}
