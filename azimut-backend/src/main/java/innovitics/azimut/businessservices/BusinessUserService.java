package innovitics.azimut.businessservices;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.NoSuchElementException;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.google.firebase.auth.FirebaseAuthException;

import innovitics.azimut.businessmodels.funds.BusinessClientFund;
import innovitics.azimut.businessmodels.user.AuthenticationResponse;
import innovitics.azimut.businessmodels.user.AzimutAccount;
import innovitics.azimut.businessmodels.user.BusinessAzimutClient;
import innovitics.azimut.businessmodels.user.BusinessClientCashBalance;
import innovitics.azimut.businessmodels.user.BusinessFlow;
import innovitics.azimut.businessmodels.user.BusinessNotification;
import innovitics.azimut.businessmodels.user.BusinessPopup;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessmodels.user.BusinessUserOTP;
import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.businessutilities.FireBaseUtility;
import innovitics.azimut.businessutilities.SearchFilter;
import innovitics.azimut.controllers.users.DTOs.ChangePhoneDto;
import innovitics.azimut.controllers.users.DTOs.CheckIfUserDeletedDto;
import innovitics.azimut.controllers.users.DTOs.CheckSocialIdExistenceDto;
import innovitics.azimut.controllers.users.DTOs.DirectUserDto;
import innovitics.azimut.controllers.users.DTOs.ForgotPasswordDto;
import innovitics.azimut.controllers.users.DTOs.GetBalanceAndFundOwnershipDto;
import innovitics.azimut.controllers.users.DTOs.GetByIdDto;
import innovitics.azimut.controllers.users.DTOs.GetByUserPhoneDto;
import innovitics.azimut.controllers.users.DTOs.GetUserImagesDto;
import innovitics.azimut.controllers.users.DTOs.GetUserNotificationsDto;
import innovitics.azimut.controllers.users.DTOs.ReadNotificationDto;
import innovitics.azimut.controllers.users.DTOs.SaveClientDetailsDto;
import innovitics.azimut.controllers.users.DTOs.SaveContractMapChoiceDto;
import innovitics.azimut.controllers.users.DTOs.SaveUserLocationDto;
import innovitics.azimut.controllers.users.DTOs.SetUserIdAndIdTypeDto;
import innovitics.azimut.controllers.users.DTOs.SetUserMessagingTokenDto;
import innovitics.azimut.controllers.users.DTOs.TeaComputersCallbackDto;
import innovitics.azimut.controllers.users.DTOs.UpdatePasswordDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.Fund;
import innovitics.azimut.models.OTPFunctionality;
import innovitics.azimut.models.OTPMethod;
import innovitics.azimut.models.digitalregistry.DigitalRegistryAction;
import innovitics.azimut.models.kyc.ReasonType;
import innovitics.azimut.models.kyc.Review;
import innovitics.azimut.models.user.FitsUser;
import innovitics.azimut.models.user.User;
import innovitics.azimut.models.user.UserImage;
import innovitics.azimut.models.user.UserInvestment;
import innovitics.azimut.models.user.UserOldPhone;
import innovitics.azimut.models.user.UserSecurityQuestion;
import innovitics.azimut.repositories.user.FitsUserRepository;
import innovitics.azimut.repositories.user.UserInvestmentRepository;
import innovitics.azimut.repositories.user.UserOldPhoneRepository;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersCheckAccountApi;
import innovitics.azimut.security.JwtUtil;
import innovitics.azimut.service.UserContractService;
import innovitics.azimut.services.FundService;
import innovitics.azimut.services.PopupService;
import innovitics.azimut.services.digitalregistry.DigitalRegistryService;
import innovitics.azimut.services.kyc.KYCPageService;
import innovitics.azimut.services.kyc.ReviewService;
import innovitics.azimut.services.kyc.UserImageService;
import innovitics.azimut.services.user.FitsUserService;
import innovitics.azimut.services.user.ReferralCodeService;
import innovitics.azimut.utilities.businessutilities.ChangePhoneNumberRequestUtility;
import innovitics.azimut.utilities.businessutilities.PhoneNumberBlockageUtility;
import innovitics.azimut.utilities.businessutilities.Sorting;
import innovitics.azimut.utilities.crosslayerenums.ClientStatus;
import innovitics.azimut.utilities.crosslayerenums.ContractType;
import innovitics.azimut.utilities.crosslayerenums.CurrencyType;
import innovitics.azimut.utilities.crosslayerenums.DeletionReason;
import innovitics.azimut.utilities.crosslayerenums.DocumentName;
import innovitics.azimut.utilities.crosslayerenums.DocumentType;
import innovitics.azimut.utilities.crosslayerenums.KycStatus;
import innovitics.azimut.utilities.crosslayerenums.LiveNotificationNavigations;
import innovitics.azimut.utilities.crosslayerenums.Messages;
import innovitics.azimut.utilities.crosslayerenums.Navigations;
import innovitics.azimut.utilities.crosslayerenums.OrderStatus;
import innovitics.azimut.utilities.crosslayerenums.OrderType;
import innovitics.azimut.utilities.crosslayerenums.ReviewResult;
import innovitics.azimut.utilities.crosslayerenums.TransactionOrderType;
import innovitics.azimut.utilities.crosslayerenums.TransactionStatus;
import innovitics.azimut.utilities.crosslayerenums.UserIdType;
import innovitics.azimut.utilities.crosslayerenums.UserImageType;
import innovitics.azimut.utilities.crosslayerenums.UserStep;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.JsonUtility;
import innovitics.azimut.utilities.datautilities.ListUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.fileutilities.BlobData;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.utilities.mapping.PopupMapper;
import innovitics.azimut.validations.validators.user.AddBusinessUserValidator;
import innovitics.azimut.validations.validators.user.ChangePhoneNumber;
import innovitics.azimut.validations.validators.user.ChangeUserPassword;
import innovitics.azimut.validations.validators.user.EditUserProfile;
import innovitics.azimut.validations.validators.user.FindUserByUserPhone;
import innovitics.azimut.validations.validators.user.ForgottenUserPassword;

@Service
public class BusinessUserService extends BusinessUserSigningService {
  private @Autowired EditUserProfile editUserProfile;
  private @Autowired ChangeUserPassword changeUserPassword;
  private @Autowired ForgottenUserPassword forgottenUserPassword;
  private @Autowired AddBusinessUserValidator addBusinessUserValidator;
  private @Autowired FindUserByUserPhone findUserByUserPhone;
  private @Autowired ChangePhoneNumber changePhoneNumber;
  private @Autowired ChangePhoneNumberRequestUtility changePhoneNumberRequestUtility;
  private @Autowired KYCPageService kycPageService;
  private @Autowired ListUtility<AzimutAccount> azimutAccountListUtility;
  private @Autowired PhoneNumberBlockageUtility phoneNumberBlockageUtility;
  protected @Autowired ListUtility<User> userListUtility;
  private @Autowired BusinessKYCPageService businessKYCPageService;
  private @Autowired FireBaseUtility fireBaseUtility;
  private @Autowired TeaComputersCheckAccountApi checkAccountApi;
  private @Autowired EnrollService enrollService;
  private @Autowired FundService fundService;
  private @Autowired ReferralCodeService referralCodeService;
  private @Autowired FraService fraService;
  protected @Autowired ReviewService reviewService;
  private @Autowired UserOldPhoneRepository userOldPhoneRepository;
  private @Autowired DigitalRegistryService digitalRegistryService;
  private @Autowired PopupService popupService;
  private @Autowired PopupMapper popupMapper;
  private @Autowired UserInvestmentRepository userInvestmentRepository;
  private @Autowired FitsUserRepository fitsUserRepository;
  private @Autowired UserImageService userImageService;
  private @Autowired FitsUserService fitsUserService;
  private @Autowired BusinessNegativeListService businessNegativeListService;
  private @Autowired UserContractService userContractService;

  public static final String PROFILE_PICTURE_PARAMETER = "profilePicture";
  public static final String SIGNED_PDF_PARAMETER = "signedPdf";

  public BusinessUser saveUser(BusinessUser businessUser, boolean checkBlockage) throws BusinessException {
    businessUser.concatinate();
    return (BusinessUser) this.phoneNumberBlockageUtility.checkUserBlockage(
        this.configProperties.getBlockageNumberOfTrialsInt(),
        this.configProperties.getBlockageDurationInMinutes(), businessUser.getUserPhone(),
        this, "saveUser",
        new Object[] { businessUser },
        new Class<?>[] { BusinessUser.class },
        ErrorCode.OPERATION_FAILURE);

  }

  public BusinessUser saveUser(BusinessUser businessUser)
      throws BusinessException, FirebaseAuthException, IOException {
    businessUser.concatinate();
    var deviceId = businessUser.getDeviceId();
    this.authenticateFirebaseToken(businessUser);
    this.validation.validateNewPhoneNumberAvailability(businessUser);
    this.validate(businessUser, addBusinessUserValidator, BusinessUser.class.getName());
    User user = new User();
    BusinessUser savedBusinessUser = new BusinessUser();
    try {
      if (businessUser.getAzimutAccount() != null) {
        AzimutAccount businessAzimutAccount = businessUser.getAzimutAccount();
        long countryId = businessAzimutAccount.getCountryId();
        long cityId = businessAzimutAccount.getCityId();

        businessAzimutAccount.setNationalityId(countryId);
        businessAzimutAccount.setCountryId(countryId);
        businessAzimutAccount.setCityId(cityId);
        businessAzimutAccount.setOccupation(StringUtility.OCCUPATION);
        businessAzimutAccount.setClientAML(StringUtility.CLIENT_AML);
      }

      user = userMapper.convertBusinessUnitToBasicUnit(businessUser, true);
      user.setCreatedAt(DateUtility.getCurrentDate());
      user.setKycStatus(KycStatus.FIRST_TIME.getStatusId());
      user.setProvider(businessUser.getProvider());
      user.setProviderId(businessUser.getProviderId());
      user.setReferralCode(businessUser.getReferralCode());
      userService.save(user);
      savedBusinessUser = userMapper.convertBasicUnitToBusinessUnit(user);
      userUtility.setOldUserData(user);
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.USER_NOT_SAVED);
    }
    this.userUtility.handleMutipleLoginsAndDeviceLogging(savedBusinessUser.getId(), savedBusinessUser, deviceId,
        null, null, false);
    return savedBusinessUser;

  }

  public void saveOldUserData(BusinessUser businessUser) {
    var user = userMapper.convertBusinessUnitToBasicUnit(businessUser, true);
    userUtility.setOldUserData(user);
  }

  // legacy code with upload pdf to change phone number
  // todo should be removed along with changePhoneNumberRequest related
  public BusinessUser uploadSignedPdf(Long id, String newCountryPhoneCode, String newPhoneNumber, MultipartFile file,
      BusinessUser tokenizedBusinessUser) throws IOException, BusinessException {
    User user = new User();
    BusinessUser updatedbusinessUser = new BusinessUser();
    BusinessUser newBusinessUser = new BusinessUser();
    newBusinessUser.setUserPhone(newCountryPhoneCode + newPhoneNumber);
    newBusinessUser.setFile(file);
    this.validation.validateUser(id == null ? null : Long.valueOf(id), tokenizedBusinessUser);
    this.validation.validateFilePresence(newBusinessUser);
    this.validation.validateFileSize(file, Long.valueOf(this.configProperties.getPhoneNumberMaximumSizeInBytes()));
    this.validation.validate(newBusinessUser, changePhoneNumber, BusinessUser.class.getName());
    this.validation.validateNewPhoneNumberAvailability(newBusinessUser);
    this.validation.checkForOpenChangeRequests(tokenizedBusinessUser);
    try {
      user = userMapper.convertBusinessUnitToBasicUnit(tokenizedBusinessUser, false);
      this.changePhoneNumberRequestUtility.addNewChangePhoneNumberRequest(user, newBusinessUser.getUserPhone());
      user = this.storeFileBlobNameAndGenerateTokenInBusinessUser(tokenizedBusinessUser, user, file,
          this.configProperties.getBlobSignedPdfPath(), SIGNED_PDF_PARAMETER, true, DocumentType.SIGNED_PDF);

      updatedbusinessUser = userMapper.convertBasicUnitToBusinessUnit(user);
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.USER_NOT_UPDATED);
    }
    return updatedbusinessUser;

  }

  public BusinessUser editUserProfile(Long id, MultipartFile file, String nickName, String emailAddress,
      BusinessUser tokenizedBusinessUser) throws BusinessException, IOException {

    this.validation.validateUser(id, tokenizedBusinessUser);
    if (StringUtility.isStringPopulated(nickName))
      tokenizedBusinessUser.setNickName(nickName);

    if (!StringUtility.isStringPopulated(tokenizedBusinessUser.getEmailAddress())
        || StringUtility.stringsDontMatch(tokenizedBusinessUser.getEmailAddress(), emailAddress))
      tokenizedBusinessUser.setIsEmailVerified(false);

    if (StringUtility.isStringPopulated(emailAddress))
      tokenizedBusinessUser.setEmailAddress(emailAddress);

    this.validate(tokenizedBusinessUser, editUserProfile, BusinessUser.class.getName());

    if (this.fileUtility.isFilePopulated(file)) {
      this.validation.validateFileSize(file, Long.valueOf(this.configProperties.getProfilePictureMaximumSizeInBytes()));
      try {
        tokenizedBusinessUser = this.uploadProfilePicture(file, tokenizedBusinessUser);

      } catch (Exception exception) {
        throw this.handleBusinessException(exception, ErrorCode.USER_NOT_UPDATED);
      }
    } else {
      this.userService.update(this.userMapper.convertBusinessUnitToBasicUnit(tokenizedBusinessUser, false));
      if (tokenizedBusinessUser != null && tokenizedBusinessUser.getProfilePicture() != null
          && tokenizedBusinessUser.getPicturePath() != null) {
        tokenizedBusinessUser.setProfilePicture(
            this.storageService.generateFileRetrievalUrl(this.configProperties.getBlobProfilePicturePath(),
                tokenizedBusinessUser.getProfilePicture(),
                tokenizedBusinessUser.getPicturePath(), false, String.valueOf(id), DocumentType.PROFILE_IMAGE));
      }
    }
    return tokenizedBusinessUser;

  }

  public BusinessUser uploadProfilePicture(MultipartFile file, BusinessUser businessUser)
      throws IOException, BusinessException {
    User user = new User();
    BusinessUser updatedbusinessUser = new BusinessUser();
    try {
      user = this.storeFileBlobNameAndGenerateTokenInBusinessUser(businessUser, user, file,
          this.configProperties.getBlobProfilePicturePath(), PROFILE_PICTURE_PARAMETER, false,
          DocumentType.PROFILE_IMAGE);
      updatedbusinessUser = userMapper.convertBasicUnitToBusinessUnit(user);
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.USER_NOT_UPDATED);
    }
    return updatedbusinessUser;

  }

  public BusinessUser editUserPassword(UpdatePasswordDto updatePasswordDto, BusinessUser tokenizedBusinessUser,
      boolean checkBlockage) throws BusinessException {
    return (BusinessUser) this.userBlockageUtility.checkUserBlockage(
        this.configProperties.getBlockageNumberOfTrialsInt(),
        this.configProperties.getBlockageDurationInMinutes(),
        tokenizedBusinessUser,
        userMapper, this, "editUserPassword",
        new Object[] { updatePasswordDto, tokenizedBusinessUser },
        new Class<?>[] { UpdatePasswordDto.class, BusinessUser.class },
        ErrorCode.OPERATION_FAILURE);
  }

  public BusinessUser editUserPassword(UpdatePasswordDto updatePasswordDto, BusinessUser tokenizedBusinessUser)
      throws BusinessException {
    BusinessUser businessUser = new BusinessUser();
    businessUser.setPassword(updatePasswordDto.getPassword());
    businessUser.setNewPassword(updatePasswordDto.getNewPassword());
    businessUser.setId(updatePasswordDto.getId());
    this.validate(businessUser, changeUserPassword, BusinessUser.class.getName());
    this.validation.validateUser(updatePasswordDto.getId(), tokenizedBusinessUser);
    businessUser.setCountryPhoneCode(tokenizedBusinessUser.getCountryPhoneCode());
    businessUser.setPhoneNumber(tokenizedBusinessUser.getPhoneNumber());
    BusinessUser oldUser = this.getByUserPhoneAndPassword(businessUser);
    BusinessUser updatedBusinessUser = new BusinessUser();
    try {
      oldUser.setPassword(updatePasswordDto.getNewPassword());
      updatedBusinessUser = this.returnUpdatedEntity(oldUser, false);

      if (this.checkForDifferencesInTheBusinessUser(businessUser, tokenizedBusinessUser)) {
        this.updateUserAtTeaComputers(updatedBusinessUser);
      }
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.USER_NOT_UPDATED);
    }
    return updatedBusinessUser;

  }

  public BusinessUser forgotUserPassword(ForgotPasswordDto authenticationRequest, boolean checkBlockage)
      throws BusinessException {
    return (BusinessUser) this.phoneNumberBlockageUtility.checkUserBlockage(
        this.configProperties.getBlockageNumberOfTrialsInt(),
        this.configProperties.getBlockageDurationInMinutes(),
        authenticationRequest.getCountryPhoneCode() + authenticationRequest.getPhoneNumber(),
        this, "forgotUserPassword",
        new Object[] { authenticationRequest },
        new Class<?>[] { ForgotPasswordDto.class },
        ErrorCode.OPERATION_FAILURE);
  }

  public BusinessUser forgotUserPassword(ForgotPasswordDto authenticationRequest) throws BusinessException {

    BusinessUser oldUser = this
        .getByUserPhone(authenticationRequest.getCountryPhoneCode() + authenticationRequest.getPhoneNumber());

    if (authenticationRequest != null)
      oldUser.setNewPassword(authenticationRequest.getNewPassword());

    this.validate(oldUser, forgottenUserPassword, BusinessUser.class.getName());
    BusinessUser updatedBusinessUser = new BusinessUser();
    this.userUtility.handleMutipleLoginsAndDeviceLogging(oldUser.getId(), oldUser, authenticationRequest.getDeviceId(),
        null, null, true);
    try {
      oldUser.setPassword(authenticationRequest.getNewPassword());
      oldUser.setLoggedIn(true);
      oldUser.setLastLogin(new Date());
      updatedBusinessUser = this.returnUpdatedEntity(oldUser, true);
      this.getMultipleTcAccounts(updatedBusinessUser);

    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.USER_NOT_UPDATED);
    }
    return updatedBusinessUser;

  }

  public BusinessUser getById(Long id, BusinessUser tokenizedBusinessUser, boolean validate, String language)
      throws BusinessException {
    this.validation.validateUser(id, tokenizedBusinessUser);
    if (validate) {
      this.validation.validateUserKYCCompletion(tokenizedBusinessUser);
    }
    if (StringUtility.isStringPopulated(tokenizedBusinessUser.getEnrollRequestId()) &&
        (tokenizedBusinessUser.getUserStep() == null
            || tokenizedBusinessUser.getUserStep() < UserStep.CLIENT_DATA.getStepId())) {
      enrollService.checkRequestId(tokenizedBusinessUser);
    }
    BusinessUser businessUser = new BusinessUser();
    try {
      businessUser = this.convertBasicToBusinessAndPrepareURLsInBusinessUser(businessUser,
          this.userService.findById(id), validate ? false : true);
      businessUser.setReviews(this.getFraReviews(businessUser.getId(), language));

    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
    }
    return businessUser;

  }

  public void bypassAml(BusinessUser businessUser) throws BusinessException {
    try {
      businessUser.getAzimutAccount().setClientAML(StringUtility.CLIENT_AML);
      this.reviewService.deleteFraOldReviews(businessUser.getId(), ReasonType.AML.getType());
      this.editUser(businessUser);
    } catch (Exception exception) {
      throw this.exceptionHandler.handleBusinessException(exception, ErrorCode.USER_NOT_UPDATED);
    }
  }

  public BusinessUser getPopup(BusinessUser businessUser, String language)
      throws BusinessException {
    List<BusinessPopup> popups = popupMapper
        .convertBasicListToBusinessList(this.popupService.findAllByUserId(businessUser.getId()), language);
    MyLogger.info("BusinessPopups::" + popups.toString());
    for (var popup : popups) {
      popupService.readPopup(popup.getId());
    }
    businessUser.setPopups(popups);

    List<BusinessNotification> notifications = this.userUtility.getUnreadNotifications(businessUser, language);
    for (var notification : notifications) {
      userUtility.readNotification(notification.getId());
    }
    businessUser.setNotificationsList(notifications);

    businessUser.setReviews(this.getFraReviews(businessUser.getId(), language));

    businessUser.setRealEstateSigned(
        userContractService.countByUserIdAndContractType(businessUser.getId(), ContractType.REAL_ESTATE) > 0);

    return businessUser;
  }

  public BusinessUser getByUserPhone(String username) throws BusinessException {
    BusinessUser businessUser = new BusinessUser();
    try {
      businessUser = this.convertBasicToBusinessAndPrepareURLsInBusinessUser(businessUser,
          this.userService.findByUserPhone(username), false);
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
    }
    return businessUser;

  }

  public BusinessUser verifyUserExistence(GetByUserPhoneDto getByUserPhoneDto)
      throws BusinessException, IntegrationException {
    var businessUser = getByUserPhoneDto.toBusinessUser();
    this.userUtility.checkIfUserIsDeleted(new CheckIfUserDeletedDto(
        getByUserPhoneDto));

    this.validate(businessUser, findUserByUserPhone, BusinessUser.class.getName());
    BusinessUser searchedForBusinessUser = new BusinessUser();
    User user = this.userUtility.checkIfUserIsBlockedAndProceed(
        businessUser.getCountryPhoneCode() + businessUser.getPhoneNumber(), null, false);

    if (user == null) {
      MyLogger.info("Look for user at TC:::::");
      searchedForBusinessUser = this.lookForuUserAtTC(searchedForBusinessUser, businessUser);
    } else {
      try {

        searchedForBusinessUser = this.convertBasicToBusinessAndPrepareURLsInBusinessUser(searchedForBusinessUser,
            /*
             * this.userService.findByUserPhone(businessUser.getCountryPhoneCode()+
             * businessUser.getPhoneNumber()),
             */
            user,
            false);

        if (searchedForBusinessUser != null) {

          if (BooleanUtility.isTrue(searchedForBusinessUser.getIsOld())
              && !StringUtility.isStringPopulated(searchedForBusinessUser.getPassword())) {
            searchedForBusinessUser.setBusinessFlow(BusinessFlow.SET_PASSWORD);
          }

          if (StringUtility.isStringPopulated(searchedForBusinessUser.getPassword())
              || ((StringUtility.isStringPopulated(searchedForBusinessUser.getProvider()))
                  && StringUtility.isStringPopulated(searchedForBusinessUser.getProviderId()))) {
            searchedForBusinessUser.setBusinessFlow(BusinessFlow.VERIFY_PASSWORD);
          } else if (BooleanUtility.isTrue(searchedForBusinessUser.getIsVerified())) {
            searchedForBusinessUser.setBusinessFlow(BusinessFlow.SET_PASSWORD);
          }

        }

      } catch (Exception exception) {
        MyLogger.info("Enter Excepton Handling");
        BusinessException businessException = this.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
        if (NumberUtility.areIntegerValuesMatching(businessException.getErrorCode(),
            ErrorCode.USER_NOT_FOUND.getCode())) {
          searchedForBusinessUser = this.lookForuUserAtTC(searchedForBusinessUser, businessUser);
        } else {
          throw businessException;
        }
      }
    }
    searchedForBusinessUser.setCountryPhoneCode(businessUser.getCountryPhoneCode());
    searchedForBusinessUser.setPhoneNumber(businessUser.getPhoneNumber());
    return searchedForBusinessUser;
  }

  public BusinessUser getByUserPhoneAndPassword(String username, String password, String deviceId, String deviceName,
      String language)
      throws BusinessException {
    BusinessUser businessUser = new BusinessUser();
    User user = new User();
    String profilePictureFile = "";
    String profilePictureUrl = "";
    user = this.userUtility.checkIfUserIsBlockedAndProceed(username, password, true);
    if (user != null) {
      try {
        businessUser = this.convertBasicToBusinessAndPrepareURLsInBusinessUser(businessUser, user, true);
        profilePictureFile = user != null ? user.getProfilePicture() : "";
        profilePictureUrl = businessUser != null ? businessUser.getProfilePicture() : "";

        if (NumberUtility.areIntegerValuesMatching(businessUser.getKycStatus(), KycStatus.FIRST_TIME.getStatusId())) {
          MyLogger.info("Condition Met:::");
          if (StringUtility.isStringPopulated(deviceId) && BooleanUtility.isFalse(businessUser.getLivenessChecked())
              && businessUser.getUserStep() != null
              && businessUser.getUserStep().intValue() > UserStep.LEFT_AND_RIGHT.getStepId()) {
            businessUser.setNextUserStep(UserStep.LEFT_AND_RIGHT.getStepId());
          }
        }
        MyLogger.info("Condition Not Met:::");
        businessUser.setProfilePicture(profilePictureFile);
        this.getMultipleTcAccounts(businessUser);
        businessUser.setProfilePicture(profilePictureUrl);
      } catch (Exception exception) {
        throw this.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
      }
    } else {
      throw new BusinessException(ErrorCode.USER_NOT_FOUND);
    }
    this.userUtility.handleMutipleLoginsAndDeviceLogging(businessUser.getId(), businessUser, deviceId, deviceName,
        language, true);
    return businessUser;

  }

  public BusinessUser getByUserPhoneAndPassword(BusinessUser insertedBusinessUser) throws BusinessException {
    BusinessUser businessUser = new BusinessUser();
    try {
      businessUser = userMapper.convertBasicUnitToBusinessUnit(
          this.userService.findByUserPhoneAndPassword(insertedBusinessUser.getCountryPhoneCode(),
              insertedBusinessUser.getPhoneNumber(), aes.encrypt(insertedBusinessUser.getPassword())));
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
    }
    return businessUser;

  }

  public BusinessUser getUnsignedPDF(GetByIdDto getByIdDto, BusinessUser tokenizedBusinessUser)
      throws IOException, BusinessException {
    this.validation.validateUser(getByIdDto.getId(), tokenizedBusinessUser);
    try {
      tokenizedBusinessUser.setDocumentURL(
          this.storageService.generateFileRetrievalUrl(this.configProperties.getBlobUnsignedPdfPath(),
              this.configProperties.getPhoneNumberChangeDocumentName(),
              this.configProperties.getBlobUnsignedPdfPathSubDirectory(), true));
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.OPERATION_NOT_PERFORMED);
    }
    return tokenizedBusinessUser;
  }

  public int userStepCheck(Integer oldUserStep, Integer newUserStep) {
    return 0;
  }

  public BusinessUser addUserLocation(SaveUserLocationDto userLocation, BusinessUser tokenizedBusinessUser)
      throws BusinessException {
    try {
      this.userUtility.addUserLocation(tokenizedBusinessUser, userLocation);

      if (userLocation.getUserStep() != null && NumberUtility.areIntegerValuesMatching(userLocation.getUserStep(),
          UserStep.CHOOSE_CONTRACT_MAP.getStepId())) {
        tokenizedBusinessUser.setUserStep(UserStep.CHOOSE_CONTRACT_MAP.getStepId());
        this.editUser(tokenizedBusinessUser);
      }

    } catch (Exception exception) {
      return (BusinessUser) this.exceptionHandler.getNullIfNonExistent(exception);
    }
    return new BusinessUser();
  }

  public BusinessUser getUserLocation(BusinessUser tokenizedBusinessUser) throws BusinessException {
    BusinessUser businessUser = new BusinessUser();

    try {
      businessUser.setUserLocation(this.userUtility.getUserLocation(tokenizedBusinessUser));
    } catch (Exception exception) {
      return (BusinessUser) this.exceptionHandler.getNullIfNonExistent(exception);
    }
    return businessUser;
  }

  public BusinessUser updateUserDetails(BusinessUser tokenizedBusinessUser, SaveClientDetailsDto saveClientDetailsDto,
      boolean isContractSigning) throws BusinessException {
    if (!isContractSigning) {
      this.validation.validateUserKYCCompletion(tokenizedBusinessUser);
    }
    BusinessUser businessUser = saveClientDetailsDto.toBusinessUser();

    try {

      tokenizedBusinessUser = this.userUtility.isOldUserStepGreaterThanNewUserStep(tokenizedBusinessUser,
          businessUser.getUserStep());

      if (businessUser.getFirstName() != null)
        tokenizedBusinessUser.setFirstName(businessUser.getFirstName());
      if (businessUser.getLastName() != null)
        tokenizedBusinessUser.setLastName(businessUser.getLastName());
      // tokenizedBusinessUser.setEmailAddress(businessUser.getEmailAddress());
      if (businessUser.getCountryCode() != null)
        tokenizedBusinessUser.setCountryCode(businessUser.getCountryCode());
      if (businessUser.getCountryPhoneCode() != null)
        tokenizedBusinessUser.setCountryPhoneCode(businessUser.getCountryPhoneCode());
      if (businessUser.getPhoneNumber() != null)
        tokenizedBusinessUser.setPhoneNumber(businessUser.getPhoneNumber());
      if (businessUser.getDateOfBirth() != null)
        tokenizedBusinessUser.setDateOfBirth(businessUser.getDateOfBirth());
      if (businessUser.getCountry() != null)
        tokenizedBusinessUser.setCountry(businessUser.getCountry());
      if (businessUser.getCity() != null)
        tokenizedBusinessUser.setCity(businessUser.getCity());

      if (businessUser.getAzimutAccount() != null) {
        AzimutAccount newAzimutAccount = businessUser.getAzimutAccount();
        AzimutAccount azimutAccount = tokenizedBusinessUser.getAzimutAccount();
        if (newAzimutAccount.getCityId() != null) {
          azimutAccount.setCityId(newAzimutAccount.getCityId());
        }
        if (newAzimutAccount.getCountryId() != null) {
          azimutAccount.setCountryId(newAzimutAccount.getCountryId());
        }
        if (newAzimutAccount.getAddressEn() != null && !StringUtility.isStringPopulated(azimutAccount.getAddressEn())) {
          azimutAccount.setAddressEn(newAzimutAccount.getAddressEn());
        }
        if (newAzimutAccount.getAddressAr() != null && !StringUtility.isStringPopulated(azimutAccount.getAddressAr())) {
          azimutAccount.setAddressAr(newAzimutAccount.getAddressAr());
        }
      }

      if (businessUser.getContractMap() != null)
        tokenizedBusinessUser.setContractMap(businessUser.getContractMap());
      if (businessUser.getOtherIdType() != null)
        tokenizedBusinessUser.setOtherIdType(businessUser.getOtherIdType());
      if (businessUser.getOtherUserId() != null)
        tokenizedBusinessUser.setOtherUserId(businessUser.getOtherUserId());
      if (businessUser.getOtherNationality() != null)
        tokenizedBusinessUser.setOtherNationality(businessUser.getOtherNationality());
      if (businessUser.getMailingAddress() != null)
        tokenizedBusinessUser.setMailingAddress(businessUser.getMailingAddress());

      var nextUserStep = NumberUtility.areLongValuesMatching(tokenizedBusinessUser.getIdType(),
          UserIdType.PASSPORT.getTypeId())
              ? UserStep.GET_SIGNATURE_IMG.getStepId()
              : UserStep.BANK_REFERENCES_SHOW.getStepId();
      tokenizedBusinessUser = this.userUtility.isOldUserStepGreaterThanNewUserStep(tokenizedBusinessUser, nextUserStep);
      this.editUser(tokenizedBusinessUser);

      if (BooleanUtility.isTrue(businessUser.getIsMobile())) {
        businessUser
            .setClientBankAccounts(this.azimutDataLookupUtility.getKYCClientBankAccountData(tokenizedBusinessUser));

      }
      businessUser.setVerificationPercentage(tokenizedBusinessUser.getVerificationPercentage());
      businessUser.setFirstPageId(tokenizedBusinessUser.getFirstPageId());
      businessUser.setNextUserStep(nextUserStep);
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.USER_NOT_UPDATED);
    }
    businessUser.setNextUserStep(this.reviewUtility.calculateUserStepUnderReview(tokenizedBusinessUser));
    businessUser.setLivenessChecked(BooleanUtility.getValue(tokenizedBusinessUser.getLivenessChecked()));
    return businessUser;
  }

  public BusinessUser saveSignatureImage(BusinessUser user, MultipartFile signatureImage)
      throws BusinessException, IOException {
    List<UserImage> userImages = new ArrayList<UserImage>();

    UserImage straightImage = this.userUtility.createUserImageRecord(user, signatureImage, UserImageType.DOCUMENT);
    userImages.add(straightImage);

    this.userUtility.uploadUserImages(userImages, user);
    userImageService.saveImages(userImages);

    user = this.userUtility.isOldUserStepGreaterThanNewUserStep(user,
        UserStep.BANK_REFERENCES_SHOW.getStepId());
    this.editUser(user);

    user.setNextUserStep(UserStep.BANK_REFERENCES_SHOW.getStepId());
    return user;
  }

  public BusinessUser getUserImages(BusinessUser tokenizedBusinessUser, GetUserImagesDto businessUser, String language)
      throws BusinessException {
    tokenizedBusinessUser
        .setUserImages(this.userUtility.getUserImages(tokenizedBusinessUser, businessUser.getImageTypes(), false));
    tokenizedBusinessUser.setReviews(this.reviewUtility
        .getFirstReview(this.reviewUtility.getReviews(tokenizedBusinessUser.getId(), null), language));
    return tokenizedBusinessUser;
  }

  public BusinessUser returnUpdatedEntity(BusinessUser oldUser, boolean getDocuments) throws IOException {
    User user = this.userMapper.convertBusinessUnitToBasicUnit(oldUser, false);
    if (getDocuments)
      return this.convertBasicToBusinessAndPrepareURLsInBusinessUser(oldUser, this.userService.update(user),
          getDocuments);
    else
      return userMapper.convertBasicUnitToBusinessUnit(this.userService.update(user));
  }

  boolean checkForDifferencesInTheBusinessUser(BusinessUser businessUser, BusinessUser oldBusinessUser) {
    return (businessUser != null && oldBusinessUser != null) &&
        (StringUtility.stringsDontMatch(businessUser.getCountryPhoneCode(), oldBusinessUser.getCountryPhoneCode()) ||
            StringUtility.stringsDontMatch(businessUser.getPhoneNumber(), oldBusinessUser.getPhoneNumber()));
  }

  public boolean verifyUserExistenceAtTeaComputers(BusinessUser businessUser) {
    return true;
  }

  public void updateUserAtTeaComputers(BusinessUser businessUser) {
    MyLogger.info("Update User at Tea Computers");
  }

  public byte[] getSignedContract(Long userId) throws BusinessException, IOException {
    var businessUser = this.findUserById(userId, false);
    if (businessUser == null) {
      MyLogger.error("User not found with id: " + userId);
      return null;
    }
    ByteArrayOutputStream signedContractFile = null;
    try {
      signedContractFile = this.userUtility.getSignedPdf(businessUser, "UserContract");
    } catch (IOException e) {
      MyLogger.info("no User signed contract");
    }

    if (signedContractFile == null)
      signedContractFile = this.downloadUserContract(businessUser, StringUtility.ARABIC);
    return signedContractFile.toByteArray();
  }

  public byte[] getDigitallySignedContract(Long userId) throws BusinessException, IOException {
    var businessUser = this.findUserById(userId, false);
    if (businessUser == null) {
      MyLogger.error("User not found with id: " + userId);
      return null;
    }

    return this.userUtility.getSignedPdfBytes(businessUser, StringUtility.CONTRACT_DOCUMENT_NAME);
  }

  public ByteArrayOutputStream downloadUserContract(BusinessUser tokenizedBusinessUser, String language)
      throws BusinessException, IOException {
    this.validation.validateKYCFormCompletion(tokenizedBusinessUser,
        this.kycPageService.countPagesByUserType(tokenizedBusinessUser.getIdType()));
    return this.pdfFillerChild.fillPdfFormInChild(
        this.getUserOTP(tokenizedBusinessUser, OTPFunctionality.SIGN_CONTRACT), StringUtility.ENGLISH,
        this.azimutDataLookupUtility, this.arrayUtility, tokenizedBusinessUser,
        businessKYCPageService.getUserKycPages(tokenizedBusinessUser, false, StringUtility.ENGLISH), "", "", "", "",
        "", "");
  }

  public ByteArrayOutputStream downloadUnsignedUserContract(BusinessUser tokenizedBusinessUser, String language)
      throws BusinessException, IOException {
    this.validation.validateKYCFormCompletion(tokenizedBusinessUser,
        this.kycPageService.countPagesByUserType(tokenizedBusinessUser.getIdType()));
    return this.pdfFillerChild.fillPdfFormInChild(null, StringUtility.ENGLISH,
        this.azimutDataLookupUtility, this.arrayUtility, tokenizedBusinessUser,
        businessKYCPageService.getUserKycPages(tokenizedBusinessUser, false, StringUtility.ENGLISH), "", "", "", "",
        "", "");
  }

  public byte[] downloadUnsignedCustodianPDF(BusinessUser tokenizedBusinessUser)
      throws IOException, BusinessException {
    var path = storageService.generateLocalPath(configProperties.getBlobUnsignedPdfPath(),
        configProperties.getBlobUnsignedPdfPathSubDirectory(), configProperties.getCustodianDocumentName(),
        false);
    MyLogger.info("Custodian PDF File path: " + path);
    Entry<MediaType, byte[]> fileBytesWithExtension = this.storageService.getFileWithAbsolutePath(aes.encrypt(path));
    return fileBytesWithExtension.getValue();
  }

  AzimutAccount prepareAccountRetrievalInputs(BusinessAzimutClient businessAzimutClient,
      BusinessUser searchBusinessUser) {
    AzimutAccount azimutAccount = new AzimutAccount();
    if (searchBusinessUser != null) {
      String withoutPlus = StringUtility.isStringPopulated((searchBusinessUser.getCountryPhoneCode()))
          ? (searchBusinessUser.getCountryPhoneCode()).substring(1)
          : null;
      azimutAccount.setPhoneNumber(withoutPlus + searchBusinessUser.getPhoneNumber());
    }
    return azimutAccount;
  }

  private User storeFileBlobNameAndGenerateTokenInBusinessUser(BusinessUser businessUser, User user, MultipartFile file,
      String containerName, String parameter, boolean generateSasToken, DocumentType documentType)
      throws IOException, BusinessException {
    BlobData blobData = new BlobData();
    blobData = this.storageService.uploadFile(file, generateSasToken, containerName, null, false,
        String.valueOf(businessUser.getId()), documentType);
    String fileName = blobData.getFileName();
    String filePath = blobData.getSubDirectory();
    user = this.userMapper.convertBusinessUnitToBasicUnit(businessUser, false);
    MyLogger.info("Parameter::" + parameter);
    if (StringUtility.stringsMatch(PROFILE_PICTURE_PARAMETER, parameter)) {
      MyLogger.info("updating Profile Picture");
      user.setProfilePicture(fileName);
      user.setPicturePath(filePath);
    } else if (StringUtility.stringsMatch(SIGNED_PDF_PARAMETER, parameter)) {
      MyLogger.info("updating signed PDF");
      user.setSignedPdf(fileName);
      user.setPdfPath(filePath);
      user.setIsChangeNoApproved(false);
    }
    user = userService.update(user);
    if (user.getProfilePicture() != null && user.getPicturePath() != null
        && StringUtility.stringsMatch(PROFILE_PICTURE_PARAMETER, parameter)) {
      MyLogger.info("Generating Profile Picture URL");
      user.setProfilePicture(blobData.getUrl());
    }

    if (user.getSignedPdf() != null && user.getPdfPath() != null
        && StringUtility.stringsMatch(SIGNED_PDF_PARAMETER, parameter)) {
      MyLogger.info("Generating Signed PDF URL");
      user.setSignedPdf(blobData.getUrl());
    }

    return user;
  }

  public BusinessUser hideUserPassword(BusinessUser businessUser) {
    if (businessUser != null) {
      businessUser.setPassword(null);
    }
    return businessUser;
  }

  public BusinessUser beautifyUser(BusinessUser businessUser) {
    if (businessUser != null) {
      businessUser.setPassword(null);
      if (businessUser.getIsVerified() == null)
        businessUser.setIsVerified(false);
      if (businessUser.getIsEmailVerified() == null)
        businessUser.setIsEmailVerified(false);
      if (businessUser.getHasSecurityQuestions() == null)
        businessUser.setHasSecurityQuestions(false);
      if (businessUser.isChangeNoApproved() == null)
        businessUser.setChangeNoApproved(true);
      if (businessUser.getVerificationPercentage() == null)
        businessUser.setVerificationPercentage(0);
      if (businessUser.getMigrated() == null)
        businessUser.setMigrated(false);
      if (businessUser.getIsOld() == null)
        businessUser.setIsOld(false);
    }

    return businessUser;
  }

  public BusinessUser hideUserDetails(BusinessUser businessUser) {
    BusinessUser businessUserWithHiddenDetails = new BusinessUser();
    if (businessUser != null) {
      businessUserWithHiddenDetails.setPhoneNumber(businessUser.getPhoneNumber());
      businessUserWithHiddenDetails.setCountryPhoneCode(businessUser.getCountryPhoneCode());
      // businessUserWithHiddenDetails.setEmailAddress(businessUser.getEmailAddress());
      businessUserWithHiddenDetails.setFlowId(businessUser.getFlowId());
      // businessUserWithHiddenDetails.setBusinessFlow(businessUser.getBusinessFlow());
    }

    return businessUserWithHiddenDetails;
  }

  public BusinessUser setUserIdAndUserIdType(BusinessUser tokenizedBusinessUser,
      SetUserIdAndIdTypeDto inputBusinessUser) {
    MyLogger.info("Editing the user:::");
    if (inputBusinessUser != null) {
      tokenizedBusinessUser.setIdType(inputBusinessUser.getIdType());
      tokenizedBusinessUser.setUserId(inputBusinessUser.getUserId());
      // don't override nickname for old users
      if (tokenizedBusinessUser.getNickName() == null) {
        tokenizedBusinessUser.setNickName(inputBusinessUser.getFullName());
      }
      try {
        this.editUser(tokenizedBusinessUser);
        MyLogger.info("User Editted:::");
      } catch (Exception exception) {
        this.handleBusinessException(exception, ErrorCode.USER_NOT_UPDATED);
      }
    }

    return tokenizedBusinessUser;
  }

  public BusinessUser saveUserQuestions(BusinessUser businessUser, List<UserSecurityQuestion> answers)
      throws BusinessException {
    if (BooleanUtility.isTrue(businessUser.getHasSecurityQuestions())) {
      throw new BusinessException(ErrorCode.SECURITY_QUESTIONS_EXISTS);
    }
    this.userService.addSecurityQuestions(businessUser.getId(), answers);
    businessUser.setHasSecurityQuestions(true);
    return businessUser;
  }

  public BusinessUser verifyUserEmail(BusinessUser tokenizedBusinessUser, String otp) throws BusinessException {

    MyLogger.info("Editing the user Email Verified:::");

    BusinessUserOTP businessUserOTP = this.getUserOTP(tokenizedBusinessUser, OTPFunctionality.VERIFY_EMAIL);
    if (StringUtility.stringsMatch(businessUserOTP.getOtp(), otp)) {
      tokenizedBusinessUser.setIsEmailVerified(true);
      this.editUser(tokenizedBusinessUser);
      this.userUtility.deleteOldUserOTPs(tokenizedBusinessUser.getId());
      if (tokenizedBusinessUser.isOnFits()) {
        AzimutAccount azimutAccount = new AzimutAccount();
        azimutAccount.setId(tokenizedBusinessUser.getId());
        try {
          businessClientDetailsService.addAccountAtTeaComputers(azimutAccount.toAddAccountDto(), tokenizedBusinessUser);
        } catch (Exception e) {
          MyLogger.info("Could not add the account at Tea Computers");
          MyLogger.logStackTrace(e);
        }
      }
    } else {
      throw new BusinessException(ErrorCode.INVALID_OTP);
    }
    MyLogger.info("User Editted Email Verified:::");
    return tokenizedBusinessUser;
  }

  public void getMultipleTcAccounts(BusinessUser businessUser) throws BusinessException {
    if (businessUser != null && businessUser.isOnFits()) {
      try {
        // List<AzimutAccount>
        // azimutAccounts=this.restManager.checkAccountMapper.wrapBaseBusinessEntity(true,
        // this.prepareAccountRetrievalInputs(null, businessUser),null).getDataList();
        var azimutAccount = this.prepareAccountRetrievalInputs(null, businessUser);
        var checkAccountRequest = this.checkAccountApi.createRequest(azimutAccount);
        var checkAccountResponse = this.checkAccountApi.getData(checkAccountRequest);
        var azimutAccounts = this.checkAccountApi.createAzimutAccountList(checkAccountResponse);
        boolean isListPopulated = azimutAccountListUtility.isListPopulated(azimutAccounts);

        if (isListPopulated && azimutAccounts.size() == 1) {
          MyLogger.info("Single old account::::");
          SetUserIdAndIdTypeDto inputBusinessUser = new SetUserIdAndIdTypeDto();

          inputBusinessUser.setIdType(azimutAccounts.get(0).getAzIdType());
          inputBusinessUser.setUserId(azimutAccounts.get(0).getAzId());
          inputBusinessUser.setFullName(azimutAccounts.get(0).getFullName());
          this.setUserIdAndUserIdType(businessUser, inputBusinessUser);
        } else if (isListPopulated && azimutAccounts.size() > 1) {
          businessUser.setAzimutAccounts(azimutAccounts);
        } else {
          businessUser.setAzimutAccounts(null);
        }
      } catch (Exception exception) {
        MyLogger.info("Could not retrieve the azimut bank accounts");
        if (this.exceptionHandler.checkIfIntegrationExceptinWithSpecificErrorCode(exception,
            ErrorCode.NO_MATCHED_CLIENT_NUMBER_EXIST)) {
          throw this.exceptionHandler.handleIntegrationExceptionAsBusinessException((IntegrationException) exception,
              null);
        } else {
          throw this.exceptionHandler.handleException(exception);
        }
      }
    }

  }

  private BusinessUser lookForuUserAtTC(BusinessUser searchedForBusinessUser, BusinessUser businessUser)
      throws BusinessException {
    searchedForBusinessUser = new BusinessUser();
    try {
      // List<AzimutAccount>
      // azimutAccounts=this.restManager.checkAccountMapper.wrapBaseBusinessEntity(true,
      // this.prepareAccountRetrievalInputs(null, businessUser), null).getDataList();
      var azimutAccount = this.prepareAccountRetrievalInputs(null, businessUser);
      Boolean useFits = false;
      List<AzimutAccount> azimutAccounts = new ArrayList<>();
      if (BooleanUtility.isTrue(useFits)) {
        var checkAccountRequest = this.checkAccountApi.createRequest(azimutAccount);
        var checkAccountResponse = this.checkAccountApi.getData(checkAccountRequest);
        azimutAccounts = this.checkAccountApi.createAzimutAccountList(checkAccountResponse);
      } else {
        var fitsUsers = fitsUserService.findByMobile(azimutAccount.getPhoneNumber());
        azimutAccounts = fitsUserService.createAzimutAccountList(fitsUsers);
      }
      if (this.azimutAccountListUtility.isListPopulated(azimutAccounts)) {
        searchedForBusinessUser = userMapper.convertBasicUnitToBusinessUnit(this.userUtility
            .saveOldUser(businessUser.getCountryPhoneCode(), businessUser.getPhoneNumber(), azimutAccounts));
        searchedForBusinessUser.setBusinessFlow(BusinessFlow.SET_PASSWORD);
        searchedForBusinessUser.setCountryPhoneCode(businessUser.getCountryPhoneCode());
        searchedForBusinessUser.setPhoneNumber(businessUser.getPhoneNumber());
      }

      else {
        searchedForBusinessUser.setBusinessFlow(BusinessFlow.GO_TO_REGISTRATION);
      }
    } catch (Exception teacomputerException) {

      MyLogger.info("Enter TC exception Handling");
      if (exceptionHandler.checkIfIntegrationExceptinWithSpecificErrorCode(teacomputerException,
          ErrorCode.NO_MATCHED_CLIENT_NUMBER_EXIST)) {
        MyLogger.info("Go to reg");
        searchedForBusinessUser.setBusinessFlow(BusinessFlow.GO_TO_REGISTRATION);
      } else {
        MyLogger.info("Throw exception");
        throw this.exceptionHandler.handleException(teacomputerException);
      }
    }
    return searchedForBusinessUser;
  }

  public BusinessUser addUserInteraction(String countryCode, String countryPhoneCode, String phoneNumber, String email,
      String body, Integer type, MultipartFile file) throws BusinessException {
    this.userUtility.addUserInteraction(countryCode, countryPhoneCode, phoneNumber, email, body, type, file);
    return new BusinessUser();
  }

  public BusinessUser getUserInteractions(Integer typeId) throws BusinessException {
    this.userUtility.getUserInteractions(typeId);
    return new BusinessUser();
  }

  public Boolean checkReferralCode(String code) {
    try {
      referralCodeService.findByCode(code.toLowerCase());
      return true;
    } catch (NoSuchElementException e) {
      MyLogger.info("No Referral Code available for " + code);
      return false;
    }
  }

  public BusinessUser setUserMessagingToken(BusinessUser tokenizedBusinessUser,
      SetUserMessagingTokenDto setUserMessagingTokenDto)
      throws BusinessException {
    tokenizedBusinessUser.setMessagingToken(setUserMessagingTokenDto.getMessagingToken());
    this.editUser(tokenizedBusinessUser);
    return new BusinessUser();
  }

  public BusinessUser getUserNotifications(BusinessUser tokenizedBusinessUser,
      GetUserNotificationsDto getUserNotificationsDto,
      String language) throws BusinessException {
    PaginatedEntity<BusinessNotification> notifications = this.userUtility.getUserNotifications(tokenizedBusinessUser,
        getUserNotificationsDto, language);
    MyLogger.info("BusinessNotifications::" + notifications.toString());

    BusinessUser businessUser = new BusinessUser();

    businessUser.setNotifications(notifications);

    if (!StringUtility.isStringPopulated(tokenizedBusinessUser.getLanguage())) {
      var newBusinessUser = this.getByUserId(tokenizedBusinessUser.getId());
      newBusinessUser.setLanguage(language);
      this.editUser(newBusinessUser);
    }

    if (NumberUtility.areIntegerValuesMatching(tokenizedBusinessUser.getKycStatus(),
        KycStatus.REJECTED.getStatusId())) {
      businessUser.setFirstPageId(tokenizedBusinessUser.getFirstPageId());
      var nextStep = this.reviewUtility.calculateUserStepUnderReview(tokenizedBusinessUser);
      businessUser.setNextUserStep(nextStep);
      businessUser.setUserStep(nextStep);
    }
    businessUser.setKycStatus(tokenizedBusinessUser.getKycStatus());
    businessUser.setIsVerified(tokenizedBusinessUser.getIsVerified());
    businessUser.setIsEmailVerified(tokenizedBusinessUser.getIsEmailVerified());
    businessUser.setHasSecurityQuestions(tokenizedBusinessUser.getHasSecurityQuestions());
    businessUser.setPageNumber(getUserNotificationsDto.getPageNumber());
    return businessUser;
  }

  public BusinessUser readNotification(BusinessUser tokenizedBusinessUser, ReadNotificationDto businessNotification)
      throws BusinessException {
    this.userUtility.readNotification(businessNotification.getId());
    return new BusinessUser();
  }

  public BusinessUser saveContractMapChoice(BusinessUser tokenizedBusinessUser,
      SaveContractMapChoiceDto saveContractMapChoiceDto,
      boolean isContractSigning) throws BusinessException {
    var businessUser = saveContractMapChoiceDto.toBusinessUser();
    tokenizedBusinessUser.setContractMap(saveContractMapChoiceDto.getContractMap());
    tokenizedBusinessUser.setUserStep(saveContractMapChoiceDto.getUserStep());
    this.editUser(tokenizedBusinessUser);
    businessUser.setNextUserStep(saveContractMapChoiceDto.getUserStep().intValue() + 1);
    return businessUser;
  }

  public AuthenticationResponse<BusinessUser> checkSocialIdExistence(JwtUtil jwtUtil,
      CheckSocialIdExistenceDto checkSocialIdExistenceDto)
      throws FirebaseAuthException, IOException, BusinessException {
    this.userUtility.checkIfUserIsDeleted(new CheckIfUserDeletedDto(checkSocialIdExistenceDto));
    this.authenticateFirebaseToken(checkSocialIdExistenceDto.toBusinessUser());
    BusinessUser tokenizedBusinessUser = new BusinessUser();
    User user = this.userService.findByUserSocialId(checkSocialIdExistenceDto.getProvider(),
        checkSocialIdExistenceDto.getProviderId());

    if (user != null) {
      this.fireBaseUtility.verify(checkSocialIdExistenceDto.getSocialToken(),
          checkSocialIdExistenceDto.getProviderId());
      MyLogger.info("User::" + user.toString());
      tokenizedBusinessUser = this.convertBasicToBusinessAndPrepareURLsInBusinessUser(tokenizedBusinessUser, user,
          true);
      this.getMultipleTcAccounts(tokenizedBusinessUser);
      tokenizedBusinessUser.setUserPhone(user.getUserPhone());
      AuthenticationResponse<BusinessUser> authenticationResponse = new AuthenticationResponse<BusinessUser>(
          jwtUtil.generateTokenUsingUserDetails(tokenizedBusinessUser), this.beautifyUser(tokenizedBusinessUser));
      authenticationResponse.setFlowId(BusinessFlow.SET_PASSWORD.getFlowId());
      return authenticationResponse;
    }

    else {
      AuthenticationResponse<BusinessUser> authenticationResponse = new AuthenticationResponse<BusinessUser>();
      authenticationResponse.setFlowId(BusinessFlow.DIRECT_USER.getFlowId());
      return authenticationResponse;
    }
  }

  public AuthenticationResponse<BusinessUser> directUser(JwtUtil jwtUtil, DirectUserDto businessUser)
      throws BusinessException, IntegrationException, FirebaseAuthException, IOException {
    BusinessUser searchedForBusinessUser = new BusinessUser();
    searchedForBusinessUser = this.verifyUserExistence(businessUser);
    MyLogger.info("After checking");
    try {
      var phone = businessUser.getCountryPhoneCode() + businessUser.getPhoneNumber();
      BusinessUserOTP businessUserOTP = this.getPhoneOTP(phone);
      if (!phone.startsWith("+20") || businessUserOTP.getOtp() == null)
        this.verifyTwilioOtp(businessUserOTP, businessUser.getOtp());
      else {
        if (StringUtility.stringsDontMatch(businessUserOTP.getOtp(), businessUser.getOtp())) {
          if (businessUserOTP.getAssessmentId() != null)
            recaptchaUtility.annotateOTPAssessment(businessUserOTP, false);
          throw new BusinessException(ErrorCode.INVALID_OTP, HttpStatus.UNPROCESSABLE_ENTITY);
        }
        if (businessUserOTP.getAssessmentId() != null)
          recaptchaUtility.annotateOTPAssessment(businessUserOTP, true);
      }
      if (searchedForBusinessUser != null && NumberUtility
          .areIntegerValuesMatching(BusinessFlow.SET_PASSWORD.getFlowId(), searchedForBusinessUser.getFlowId())) {
        this.fireBaseUtility.verify(businessUser.getSocialToken(), businessUser.getProviderId());
        searchedForBusinessUser.setProviderId(businessUser.getProviderId());
        searchedForBusinessUser.setProvider(businessUser.getProvider());
        searchedForBusinessUser.setKycStatus(KycStatus.APPROVED.getStatusId());
        searchedForBusinessUser.setReferralCode(businessUser.getReferralCode());
        searchedForBusinessUser.concatinate();
        this.editUser(searchedForBusinessUser);
        this.getMultipleTcAccounts(searchedForBusinessUser);
        AuthenticationResponse<BusinessUser> authenticationResponse = new AuthenticationResponse<BusinessUser>(
            jwtUtil.generateTokenUsingUserDetails(searchedForBusinessUser), this.beautifyUser(searchedForBusinessUser));
        authenticationResponse.setBusinessFlow(BusinessFlow.SET_PASSWORD);
        MyLogger.info("After authentication response");
        return authenticationResponse;
      } else if (searchedForBusinessUser != null && !NumberUtility
          .areIntegerValuesMatching(BusinessFlow.SET_PASSWORD.getFlowId(), searchedForBusinessUser.getFlowId())) {
        MyLogger.info("After authentication response inside else if");
        AuthenticationResponse<BusinessUser> authenticationResponse = new AuthenticationResponse<BusinessUser>();
        authenticationResponse.setFlowId(searchedForBusinessUser.getFlowId());
        return authenticationResponse;
      }
    } catch (Exception exception) {
      throw this.exceptionHandler.handleException(exception);
    }

    return null;
  }

  private void authenticateFirebaseToken(BusinessUser businessUser) throws FirebaseAuthException, IOException {
    if (businessUser != null && StringUtility.isStringPopulated(businessUser.getSocialToken())
        && StringUtility.isStringPopulated(businessUser.getProvider())
        && StringUtility.isStringPopulated(businessUser.getProviderId()))
      fireBaseUtility.verify(businessUser.getSocialToken(), businessUser.getProviderId());
  }

  public BusinessUser pushNotification(BusinessUser tokenizedBusinessUser) throws BusinessException {
    // this.messagingService.send(emailUtility,tokenizedBusinessUser,"Test","Test","Test","Test",StringUtility.ENGLISH);
    /*
     * Messages appUserMessage= Messages.KYC_ACCEPTED;
     * String navigation=Navigations.KYC_APPROVED.getNavigationId();
     * String
     * liveNavigation=LiveNotificationNavigations.KYC_APPROVED.getNavigationId();
     */
    Messages appUserMessage = Messages.KYC_REJECTED;
    String navigation = Navigations.KYC_REJECTED.getNavigationId();
    String liveNavigation = LiveNotificationNavigations.KYC_REJECTED.getNavigationId();
    Integer nextStep = 2;
    Integer kycStatus = KycStatus.REJECTED.getStatusId();

    this.messagingService.send(pushNotificationUtility, tokenizedBusinessUser, appUserMessage, StringUtility.ENGLISH,
        navigation, liveNavigation, this.userUtility.generateUserMap(nextStep, 16l, kycStatus, null));
    return new BusinessUser();
  }

  public void sendCallbackNotification(TeaComputersCallbackDto data) throws BusinessException {
    try {
      var user = this.userService.findUserByUserId(data.getIdNumber());
      if (user == null) {
        MyLogger.info("User not found with id: " + data.getIdNumber());
        return;
      }
      BusinessUser businessUser = this.userMapper.convertBasicUnitToBusinessUnit(user);
      Messages message = null;
      Fund fund = null;
      if (StringUtility.stringsMatch(data.getNotificationType(), "User")) {
        message = NumberUtility.areIntegerValuesMatching(data.getUser().getStatus(), ClientStatus.ACTIVE.getTypeId())
            ? Messages.ACCOUNT_ACTIVATED
            : Messages.ACCOUNT_DISABLED;
      } else if (StringUtility.stringsMatch(data.getNotificationType(), "Order")) {
        int statusTypeId = data.getOrder().getOrderStatus();
        TransactionStatus transactionStatus = TransactionStatus.getById(statusTypeId);
        fund = fundService.getFundsByTeacomputerId(data.getOrder().getFundId()).get();
        if (NumberUtility.areIntegerValuesMatching(data.getOrder().getOrderTypeId(), OrderType.BUY.getTypeId())) {
          if (NumberUtility.areIntegerValuesMatching(transactionStatus.getStatusId(),
              TransactionStatus.POSTED.getStatusId()))
            message = Messages.BUY_ORDER_EXECUTED;
          else if (NumberUtility.areIntegerValuesMatching(transactionStatus.getStatusId(),
              TransactionStatus.CANCELED.getStatusId()))
            message = Messages.BUY_ORDER_CANCELED;
        }
        if (NumberUtility.areIntegerValuesMatching(data.getOrder().getOrderTypeId(), OrderType.SELL.getTypeId())) {
          if (NumberUtility.areIntegerValuesMatching(transactionStatus.getStatusId(),
              TransactionStatus.POSTED.getStatusId()))
            message = Messages.SELL_ORDER_EXECUTED;
          else if (NumberUtility.areIntegerValuesMatching(transactionStatus.getStatusId(),
              TransactionStatus.CANCELED.getStatusId()))
            message = Messages.SELL_ORDER_CANCELED;
        }
      } else { // notification type = "Transaction"
        int statusTypeId = Integer.parseInt(data.getTransaction().getOrderStatus());
        TransactionStatus transactionStatus = TransactionStatus.getById(statusTypeId);

        int orderTypeId = Integer.parseInt(data.getTransaction().getOrderType());
        TransactionOrderType orderType = TransactionOrderType.getById(orderTypeId);
        if (NumberUtility.areIntegerValuesMatching(orderType.getTypeId(), TransactionOrderType.WITHDRAW.getTypeId())) {
          if (NumberUtility.areIntegerValuesMatching(transactionStatus.getStatusId(),
              TransactionStatus.POSTED.getStatusId()))
            message = Messages.CASHOUT_EXECUTED;
          else if (NumberUtility.areIntegerValuesMatching(transactionStatus.getStatusId(),
              TransactionStatus.CANCELED.getStatusId()))
            message = Messages.CASHOUT_CANCELED;
        }
        if (NumberUtility.areIntegerValuesMatching(orderType.getTypeId(), TransactionOrderType.INJECT.getTypeId())) {
          if (NumberUtility.areIntegerValuesMatching(transactionStatus.getStatusId(),
              TransactionStatus.POSTED.getStatusId()))
            message = Messages.CASHIN_EXECUTED;
          else if (NumberUtility.areIntegerValuesMatching(transactionStatus.getStatusId(),
              TransactionStatus.CANCELED.getStatusId()))
            message = Messages.CASHIN_CANCELED;
        }
      }
      // sending notification message
      if (message != null) {
        MyLogger.info("sending notification " + message.getTitle() + " to " + businessUser.getId());
        this.pushNotificationUtility.sendCallbackNotification(businessUser, message,
            fund != null ? fund.getTeacomputerId() : null,
            data.getNotificationType(), fund != null ? fund.getName() : null);
      }
    } catch (Exception exception) {
      this.exceptionHandler.getNullIfNonExistent(exception);
    }
  }

  public BusinessUser sendEmail(BusinessUser tokenizedBusinessUser) throws BusinessException, IOException {
    this.messagingService.send(emailUtility, tokenizedBusinessUser, Messages.KYC_ATTACHMENT, StringUtility.ENGLISH,
        this.pdfFillerChild.fillPdfFormInChild(this.getUserOTP(tokenizedBusinessUser, OTPFunctionality.SIGN_CONTRACT),
            StringUtility.ENGLISH, this.azimutDataLookupUtility, this.arrayUtility, tokenizedBusinessUser,
            businessKYCPageService.getUserKycPages(tokenizedBusinessUser, false, StringUtility.ENGLISH), "", "", "", "",
            "", ""),
        true);
    return new BusinessUser();
  }

  public BusinessUser sendOtpEmail(BusinessUser tokenizedBusinessUser, String otpInput)
      throws BusinessException, IOException {
    int randomPin = (int) (Math.random() * 900000) + 100000;
    String otp = otpInput == null ? String.valueOf(randomPin) : otpInput;
    BusinessUserOTP businessUserOTP = new BusinessUserOTP();
    businessUserOTP.setUserId(tokenizedBusinessUser.getId());
    businessUserOTP.setUserPhone(tokenizedBusinessUser.getUserPhone());
    businessUserOTP.setContractType(1);
    businessUserOTP.setOtpMethod(OTPMethod.MAIL);
    businessUserOTP.setOtp(otp);
    businessUserOTP.setSessionInfo(tokenizedBusinessUser.getSessionInfo());
    businessUserOTP.setFunctionality(OTPFunctionality.VERIFY_EMAIL.name());

    this.userUtility.upsertOTP(businessUserOTP, ErrorCode.OTP_NOT_SAVED, true);
    String text = String.format(Messages.OTP_EMAIL.getMessage(), otp);
    String textAr = String.format(Messages.OTP_EMAIL.getMessageAr(), otp);
    this.messagingService.send(emailUtility, tokenizedBusinessUser, text, textAr, Messages.OTP_EMAIL.getTitle(),
        Messages.OTP_EMAIL.getTitleAr(), StringUtility.ENGLISH);
    return new BusinessUser();
  }

  // deleteUser
  @Transactional
  public BusinessUser deleteAccount(BusinessUser tokenizedBusinessUser, Integer reasonId)
      throws BusinessException, IOException {
    String oldUserPhone = tokenizedBusinessUser.getUserPhone();
    String oldPhoneNumber = tokenizedBusinessUser.getPhoneNumber();
    tokenizedBusinessUser.setDeletedAt(new Date());
    tokenizedBusinessUser.setUserPhone("deleted" + oldUserPhone);
    tokenizedBusinessUser.setPhoneNumber("deleted" + oldPhoneNumber);
    tokenizedBusinessUser.setDeletionReasonId(reasonId);
    this.editUser(tokenizedBusinessUser);
    // this.userUtility.removeImagesFromBlobAndDb(tokenizedBusinessUser, true);
    this.userUtility.deleteOldUserOTPs(tokenizedBusinessUser.getId());
    this.userOldPhoneRepository.deleteOldUserPhones(tokenizedBusinessUser.getId());
    this.userAnswerSubmissionService.deleteOldUserAnswers(null, tokenizedBusinessUser.getId());
    return new BusinessUser();
  }

  public BusinessUser getDeletionReasons(BusinessUser tokenizedBusinessUser) throws BusinessException, IOException {
    BusinessUser businessUser = new BusinessUser();
    businessUser.setDeletionReasons(DeletionReason.getDeletionReasons());
    businessUser
        .setDocumentURL(this.storageService.generateFileRetrievalUrl(this.configProperties.getBlobUnsignedPdfPath(),
            DocumentName.DELETION.getName(),
            this.configProperties.getBlobUnsignedPdfPathSubDirectory(), true));
    return businessUser;
  }

  public AuthenticationResponse<BusinessUser> logUserOut(BusinessUser businessUser) throws BusinessException {
    businessUser.setLoggedIn(false);
    this.editUser(businessUser);
    return new AuthenticationResponse<BusinessUser>();
  }

  // for now, it just stores the file waiting to be signed
  public void signPdf(BusinessUser user, MultipartFile file)
      throws IOException, BusinessException, IntegrationException {
    this.validation.validateKYCSigning(user);
    user.setUserStep(UserStep.FINISHED.getStepId());
    user.setKycStatus(KycStatus.PENDING.getStatusId());
    this.editUser(user);
    this.storageService.uploadFileToBlob("UserContract",
        file.getInputStream(), true, configProperties.getBlobDigitalPdfPath(),
        user.getId().toString(), StringUtility.PDF_EXTENSION);
    this.autoAccept(user);
  }

  public void autoAccept(BusinessUser user) throws BusinessException, IOException, IntegrationException {
    this.fraService.checkAml(user);
    this.fraService.checkCso(user);
    this.fraService.checkNtra(user);
    this.businessNegativeListService.checkNegativeListMatch(user);
    postFraActions(user);
    autoAcceptActions(user, false);
  }

  public void addUserToFits(BusinessUser user) throws BusinessException {
    this.handleUserAdditionAtTc(businessClientDetailsService, user);
    user.setIsVerified(true);
    this.editUser(user);
  }

  public void addReviewIfNotExist(BusinessUser user) throws BusinessException {
    var reviewsCount = this.reviewService.countByUserIdAndDeletedAtIsNull(user.getId());
    if (reviewsCount == 0) {
      MyLogger.info("Adding review for user: " + user.getId());
      List<Review> reviews = new ArrayList<Review>();
      Review review = new Review();
      review.setUserId(user.getId());
      review.setResult(ReviewResult.APPROVED.getResultId());
      review.setCreatedAt(new Date());
      review.setComment("Auto Accept");
      reviews.add(review);
      this.reviewService.submitReviews(reviews);
    }
  }

  public UserInvestment updateInvestments(BusinessUser user) throws BusinessException {
    try {
      BusinessAzimutClient responseBusinessAzimutClient = new BusinessAzimutClient();
      responseBusinessAzimutClient.setSorting(Sorting.ASC.getOrder());
      this.businessClientDetailsService.getBalance(new GetBalanceAndFundOwnershipDto(), user,
          responseBusinessAzimutClient, "en");
      this.businessClientDetailsService.getTransactions(user, responseBusinessAzimutClient, "en");
      var azimutClient = this.businessClientDetailsService
          .beautifyBalanceAndTransactionsBusinessAzimutClient(responseBusinessAzimutClient);
      UserInvestment userInvestment = new UserInvestment();
      Double balance = 0.0;
      for (BusinessClientCashBalance businessClientCashBalance : azimutClient.getBusinessClientCashBalances()) {
        if (businessClientCashBalance != null && businessClientCashBalance.getBalance() != null
            && businessClientCashBalance.getCurrencyRate() != null) {
          balance = balance + (businessClientCashBalance.getBalance().doubleValue()
              * businessClientCashBalance.getCurrencyRate().doubleValue());
        }
      }
      userInvestment.setBalance(balance);
      Double totalTopUp = 0.0;
      Double totalWithdraw = 0.0;
      var transactions = azimutClient.getTransactions();
      for (var transaction : transactions) {
        if (NumberUtility.areIntegerValuesMatching(transaction.getStatus(), TransactionStatus.POSTED.getStatusId())) {
          if (NumberUtility.areIntegerValuesMatching(transaction.getType(), TransactionOrderType.INJECT.getTypeId())) {
            totalTopUp += transaction.getAmount().doubleValue();
            if (!StringUtility.isStringPopulated(userInvestment.getFirstTransactionReference())) {
              userInvestment.setFirstTransactionReference(transaction.getReferenceNumber());
              userInvestment.setFirstTransactionValue(transaction.getAmount().doubleValue());
              userInvestment.setFirstTransactionCurrency(transaction.getCurrency());
            }
          } else if (NumberUtility.areIntegerValuesMatching(transaction.getType(),
              TransactionOrderType.WITHDRAW.getTypeId())) {
            totalWithdraw += transaction.getAmount().doubleValue();
          }
        }
      }
      userInvestment.setTotalTopup(totalTopUp);
      userInvestment.setTotalWithdraw(totalWithdraw);

      Double totalBuyValue = 0.0;
      Double totalSellValue = 0.0;
      Map<String, Integer> fundBalances = new HashMap<>();
      var fundsDto = new GetBalanceAndFundOwnershipDto();
      responseBusinessAzimutClient = businessClientDetailsService.getClientFundsOrFund(user, fundsDto, true, "en");
      Double totalPosition = responseBusinessAzimutClient.getTotalPosition().doubleValue();

      for (BusinessClientFund fund : responseBusinessAzimutClient.getBusinessClientFunds()) {
        if (fund.getQuantity() > 0) {
          fundsDto.setTeacomputerId(fund.getTeacomputerId());
          var fundTransactions = businessClientDetailsService.getClientFundsOrFund(user, fundsDto, true, "en");
          for (var transaction : fundTransactions.getBusinessClientFunds().get(0).getFundTransactions()) {
            if (NumberUtility.areIntegerValuesMatching(transaction.getOrderStatusId(),
                OrderStatus.EXECUTED.getTypeId())) {
              if (NumberUtility.areIntegerValuesMatching(transaction.getOrderTypeId(),
                  OrderType.BUY.getTypeId())) {
                totalBuyValue += transaction.getOrderValue().doubleValue();
              } else if (NumberUtility.areIntegerValuesMatching(transaction.getOrderTypeId(),
                  OrderType.SELL.getTypeId())) {
                totalSellValue += transaction.getOrderValue().doubleValue();
              }
            }
          }

          fundBalances.put(fund.getTeacomputerId().toString(), fund.getQuantity().intValue());
        }
      }

      // json string of fund ids and their respective balances
      userInvestment.setFunds(JsonUtility.toJson(fundBalances));
      userInvestment.setTotalPosition(totalPosition);
      userInvestment.setTotalBuyValue(totalBuyValue);
      userInvestment.setTotalSellValue(totalSellValue);

      userService.addInvestment(user.getId(), userInvestment);
      return userInvestment;
    } catch (Exception e) {
      MyLogger.logStackTrace(e);
      MyLogger.error("Failed to update investments for user: " + user.getId());
      return null;
    }
  }

  public void updateAllUserInvestments() {
    var users = this.userService.findAllUsersWithNoInvestment();
    int i = 1;
    for (User user : users) {
      try {
        MyLogger.info("Updating investments for user: " + user.getId() + " --- " + i + "/" + users.size());
        i++;
        BusinessUser businessUser = this.userMapper.convertBasicUnitToBusinessUnit(user);
        this.updateInvestments(businessUser);
      } catch (Exception e) {
        MyLogger.logStackTrace(e);
        MyLogger.error("Failed to update investments for user: " + user.getId());
      }
    }
    MyLogger.info("Finished updating investments for all users.");
  }

  public void updateUsersFromFits() {
    // Get all users where isOld is true and firstName is null
    List<User> users = this.userService.findOldUsers().stream()
        .filter(user -> user.getFirstName() == null || user.getFirstName().isEmpty())
        .collect(Collectors.toList());

    for (User user : users) {
      try {
        MyLogger.info("Updating user data from FITS for user: " + user.getId());
        // Find matching FitsUser by userId and idNumber
        if (!StringUtility.isStringPopulated(user.getUserId())) {
          MyLogger.error("User ID is null or empty for user: " + user.getId());
          continue;
        }
        Optional<FitsUser> fitsUserOptional = this.fitsUserRepository.findByIdNumber(user.getUserId());
        if (fitsUserOptional.isPresent()) {
          FitsUser fitsUser = fitsUserOptional.get();
          this.userService.updateUserFromFits(user, fitsUser);
          MyLogger.info("Successfully updated user data from FITS for user: " + user.getId());
        } else {
          MyLogger
              .error("No matching FITS user found for user ID: " + user.getId() + " with userId: " + user.getUserId());
        }
      } catch (Exception e) {
        MyLogger.logStackTrace(e);
        MyLogger.error("Failed to update user data from FITS for user: " + user.getId());
      }
    }
    MyLogger.info("Finished updating all users from FITS data.");
  }

  public List<BusinessUser> getSignedUsers(String afterDate) {
    BusinessSearchCriteria businessSearchCriteria = new BusinessSearchCriteria();
    SearchFilter[] searchFilters = new SearchFilter[1];
    searchFilters[0] = new SearchFilter("pdfSignedAt", "GT", afterDate, null, null, null);
    businessSearchCriteria.setSearchesAndFilters(searchFilters);
    var users = this.userService.findFilteredUsersForScript(generateDatabaseConditions(businessSearchCriteria));
    return userMapper.convertBasicListToBusinessList(users.stream().limit(100).collect(Collectors.toList()));
  }

  public void addReviewsForAutoAccept() {
    try {
      BusinessSearchCriteria businessSearchCriteria = new BusinessSearchCriteria();
      SearchFilter[] searchFilters = new SearchFilter[4];
      searchFilters[0] = new SearchFilter("kycStatus", "FILTER", null, new String[] { "1" }, null, null);
      searchFilters[1] = new SearchFilter("", "REVIEW_NOT_EXIST", null, null, null, null);
      searchFilters[2] = new SearchFilter("isOld", "IS_NULL", null, null, null, null);
      searchFilters[3] = new SearchFilter("signedPdf", "IS_NOT_NULL", null, null, null, null);
      businessSearchCriteria.setSearchesAndFilters(searchFilters);
      var users = this.userService.findFilteredUsersForScript(generateDatabaseConditions(businessSearchCriteria));
      var businessUsers = userMapper.convertBasicListToBusinessList(users);
      for (BusinessUser user : businessUsers) {
        try {
          MyLogger.info("Adding review for user: " + user.getId());
          List<Review> reviews = new ArrayList<Review>();
          Review review = new Review();
          review.setUserId(user.getId());
          review.setResult(ReviewResult.APPROVED.getResultId());
          review.setCreatedAt(new Date());
          review.setComment("Auto Accept");

          reviews.add(review);
          this.reviewService.submitReviews(reviews);
        } catch (Exception e) {
          MyLogger.logStackTrace(e);
          MyLogger.error("Failed to add review for user: " + user.getId());
        }
      }
    } catch (Exception e) {
      MyLogger.logStackTrace(e);
      MyLogger.error("Failed to add reviews");
    }
  }

  public BusinessUser changePhoneNumber(BusinessUser user, ChangePhoneDto changePhoneDto)
      throws BusinessException, FirebaseAuthException, IOException, IntegrationException {
    BusinessUser newBusinessUser = new BusinessUser();
    String oldPhone = user.getUserPhone();
    newBusinessUser.setUserPhone(changePhoneDto.getCountryPhoneCode() + changePhoneDto.getPhoneNumber());
    if (user.getPassword() != null && user.getProvider() == null) {
      boolean passwordVerified = StringUtility.stringsMatch(user.getPassword(), changePhoneDto.getPassword());
      if (!passwordVerified) {
        throw new BusinessException(ErrorCode.INCORRECT_PASSWORD);
      }
    }
    this.validation.validate(newBusinessUser, changePhoneNumber, BusinessUser.class.getName());
    this.validation.validateNewPhoneNumberAvailability(newBusinessUser);
    user.setCountryPhoneCode(changePhoneDto.getCountryPhoneCode());
    user.setPhoneNumber(changePhoneDto.getPhoneNumber());
    user.setUserPhone(changePhoneDto.getCountryPhoneCode() + changePhoneDto.getPhoneNumber());
    if (user.isOnFits()) {
      if (!StringUtility.stringsMatch(changePhoneDto.getCountryPhoneCode(), "+20")) {
        MyLogger.error("Phone change is only allowed for Egyptian numbers");
        throw new BusinessException(ErrorCode.INVALID_NTRA);
      }
      if (userOldPhoneRepository.countByUserIdAndDeletedAtIsNull(user.getId()) == 0) {
        var ntraRes = this.fraService.checkNtra(user);
        if (!ntraRes) {
          MyLogger.error("NTRA is not valid for user: " + user.getId());
          UserOldPhone userOldPhone = new UserOldPhone();
          userOldPhone.setCreatedAt(new Date());
          userOldPhone.setUser(userMapper.convertBusinessUnitToBasicUnit(user, false));
          userOldPhone.setUserPhone(oldPhone);
          userOldPhoneRepository.save(userOldPhone);
          reviewService.deleteFraOldReviews(user.getId(), ReasonType.NTRA.getType());
          throw new BusinessException(ErrorCode.INVALID_NTRA);
        }
      } else {
        MyLogger.info("User Old Phone already exists for user: " + user.getId());
        throw new BusinessException(ErrorCode.PHONE_CHANGE_LIMIT);
      }
      AzimutAccount azimutAccount = new AzimutAccount();
      azimutAccount.setId(user.getId());
      try {
        businessClientDetailsService.addAccountAtTeaComputers(azimutAccount.toAddAccountDto(), user);
      } catch (Exception exception) {
        throw this.exceptionHandler.handleException(exception);
      }
    }

    this.editUser(user);
    var signDr = digitalRegistryService.getLastDigitalRegistry(user.getId(),
        DigitalRegistryAction.SIGN_CONTRACT);
    if (userOldPhoneRepository.countByUserIdAndDeletedAtIsNull(user.getId()) == 0
        && user.getUserStep() >= UserStep.CONTRACT_MAP.getStepId()
        && signDr != null
        && !BooleanUtility.isTrue(user.getIsVerified())) {
      this.autoAccept(user);
    }
    UserOldPhone userOldPhone = new UserOldPhone();
    userOldPhone.setCreatedAt(new Date());
    userOldPhone.setUser(userMapper.convertBusinessUnitToBasicUnit(user, false));
    userOldPhone.setUserPhone(oldPhone);
    userOldPhoneRepository.save(userOldPhone);
    return user;
  }

  public void unblockUser(BusinessUser user) throws BusinessException {
    user.setFailedLogins(0);
    this.editUser(user);
    userBlockageUtility.removeBlockage(user.getId());
    userBlockageUtility.removePhoneBlockage(user.getUserPhone());
  }

  public void resetChangePhoneAttempts(BusinessUser user) {
    List<UserOldPhone> oldPhones = getOldPhones(user);
    oldPhones.forEach(phone -> {
      phone.setDeletedAt(new Date());
      userOldPhoneRepository.save(phone);
    });
  }

  public List<UserOldPhone> getOldPhones(BusinessUser user) {
    return userOldPhoneRepository.findByUserId(user.getId());
  }

  public BusinessUser getByOldUserPhone(String oldUserPhone) throws BusinessException {
    MyLogger.info("Getting user by old phone: " + oldUserPhone);
    var oldUserRecord = userOldPhoneRepository.findByUserPhoneAndDeletedAtIsNull(oldUserPhone)
        .orElseThrow(() -> new BusinessException(ErrorCode.USER_NOT_FOUND));
    MyLogger.info("Old user record found: " + oldUserRecord.toString());
    ;
    BusinessUser businessUser = new BusinessUser();
    try {
      businessUser = this.convertBasicToBusinessAndPrepareURLsInBusinessUser(businessUser,
          oldUserRecord.getUser(), false);
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.USER_NOT_FOUND);
    }
    return businessUser;
  }

  public void updateInvestmentsFromCallback(TeaComputersCallbackDto data)
      throws BusinessException, IntegrationException {
    var user = this.userService.findUserByUserId(data.getIdNumber());
    if (user == null) {
      MyLogger.info("User not found with id: " + data.getIdNumber());
      return;
    }
    BusinessUser businessUser = this.userMapper.convertBasicUnitToBusinessUnit(user);
    UserInvestment userInvestment = this.userInvestmentRepository.findByUserId(user.getId())
        .orElse(null);
    if (StringUtility.stringsMatch(data.getNotificationType(), "Order")) {
      int statusTypeId = data.getOrder().getOrderStatus();
      TransactionStatus transactionStatus = TransactionStatus.getById(statusTypeId);
      var fund = fundService.getFundsByTeacomputerId(data.getOrder().getFundId()).get();

      if (NumberUtility.areIntegerValuesMatching(transactionStatus.getStatusId(),
          TransactionStatus.POSTED.getStatusId())) {

        if (userInvestment == null || !NumberUtility.areLongValuesMatching(Long.valueOf(fund.getCurrencyId()),
            CurrencyType.EGYPTIAN_POUND.getTypeId())) {
          this.updateInvestments(businessUser);
          return;
        }

        if (NumberUtility.areIntegerValuesMatching(data.getOrder().getOrderTypeId(), OrderType.BUY.getTypeId())) {
          var buyAmount = data.getOrder().getExecValue();
          userInvestment.setTotalBuyValue(buyAmount.doubleValue() + (userInvestment.getTotalBuyValue() == null
              ? 0
              : userInvestment.getTotalBuyValue()));
          var userFunds = userInvestment.getFunds() != null
              ? JsonUtility.fromJson(userInvestment.getFunds())
              : new HashMap<String, Object>();
          userFunds.put(data.getOrder().getFundId().toString(),
              (Integer) userFunds.getOrDefault(data.getOrder().getFundId().toString(), 0)
                  + data.getOrder().getQuantity().intValue());
          userInvestment.setFunds(JsonUtility.toJson(userFunds));
        } else if (NumberUtility.areIntegerValuesMatching(data.getOrder().getOrderTypeId(),
            OrderType.SELL.getTypeId())) {
          var sellAmount = data.getOrder().getExecValue();
          userInvestment.setTotalSellValue(sellAmount.doubleValue()
              + (userInvestment.getTotalSellValue() == null ? 0 : userInvestment.getTotalSellValue()));
          var userFunds = userInvestment.getFunds() != null
              ? JsonUtility.fromJson(userInvestment.getFunds())
              : new HashMap<String, Object>();
          userFunds.put(data.getOrder().getFundId().toString(),
              (Integer) userFunds.getOrDefault(data.getOrder().getFundId().toString(), 0)
                  - data.getOrder().getQuantity().intValue());
          if ((Integer) userFunds.get(data.getOrder().getFundId().toString()) <= 0)
            userFunds.remove(data.getOrder().getFundId().toString());
          userInvestment.setFunds(JsonUtility.toJson(userFunds));
        }

      }
    } else if (StringUtility.stringsMatch(data.getNotificationType(), "Transaction")) {
      int statusTypeId = Integer.parseInt(data.getTransaction().getOrderStatus());
      TransactionStatus transactionStatus = TransactionStatus.getById(statusTypeId);

      int orderTypeId = Integer.parseInt(data.getTransaction().getOrderType());
      TransactionOrderType orderType = TransactionOrderType.getById(orderTypeId);

      if (NumberUtility.areIntegerValuesMatching(transactionStatus.getStatusId(),
          TransactionStatus.POSTED.getStatusId())) {

        if (userInvestment == null || !NumberUtility.areLongValuesMatching(data.getTransaction().getCurrencyId(),
            CurrencyType.EGYPTIAN_POUND.getTypeId())) {
          this.updateInvestments(businessUser);
          return;
        }

        if (NumberUtility.areIntegerValuesMatching(orderType.getTypeId(),
            TransactionOrderType.INJECT.getTypeId())) {
          var cashInAmount = Double.valueOf(data.getTransaction().getNetValue());
          userInvestment.setTotalTopup(cashInAmount.doubleValue() + (userInvestment.getTotalTopup() == null
              ? 0
              : userInvestment.getTotalTopup()));
          if (userInvestment.getBalance() != null) {
            userInvestment.setBalance(userInvestment.getBalance() + cashInAmount.doubleValue());
          } else {
            userInvestment.setBalance(cashInAmount.doubleValue());
          }
          if (userInvestment.getFirstTransactionReference() == null) {
            userInvestment.setFirstTransactionReference(data.getTransaction().getReferenceNumber());
            userInvestment.setFirstTransactionValue(cashInAmount);
            userInvestment
                .setFirstTransactionCurrency(CurrencyType.getById(data.getTransaction().getCurrencyId()).getType());
          }
        } else if (NumberUtility.areIntegerValuesMatching(orderType.getTypeId(),
            TransactionOrderType.WITHDRAW.getTypeId())) {
          var cashOutAmount = Double.valueOf(data.getTransaction().getNetValue());
          userInvestment.setTotalWithdraw(cashOutAmount.doubleValue() + (userInvestment.getTotalWithdraw() == null
              ? 0
              : userInvestment.getTotalWithdraw()));
          if (userInvestment.getBalance() != null) {
            userInvestment.setBalance(userInvestment.getBalance() - cashOutAmount.doubleValue());
          }
        }

      }
    }
    userInvestment.setUpdatedAt(new Date());
    var fundsDto = new GetBalanceAndFundOwnershipDto();
    var responseBusinessAzimutClient = businessClientDetailsService.getClientFundsOrFund(businessUser, fundsDto, true,
        "en");
    Double totalPosition = responseBusinessAzimutClient.getTotalPosition().doubleValue();
    userInvestment.setTotalPosition(totalPosition);
    this.userInvestmentRepository.save(userInvestment);
    MyLogger.info("Updated investments for user: " + user.getId());

  }

  public void setKycUpdated(BusinessUser user) throws BusinessException {
    user.setKycStatus(KycStatus.UPDATED.getStatusId());
    this.editUser(user);
  }
}
