package innovitics.azimut.businessservices;

import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.kyc.BusinessKYCPage;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;

@Service
public class BusinessKYCPageChildService extends BusinessKYCPageService {

  public BusinessKYCPage getKycPagebyId(BusinessUser businessUser, Long id, Boolean draw, String language)
      throws BusinessException {
    // this.validation.validateWithCustomError(businessUser,
    // KycStatus.PENDING_CLIENT, ErrorCode.CLIENT_SOLVING);
    try {
      return this.getPageWithDetails(businessUser, id, draw, language);
    } catch (Exception exception) {
      throw this.handleBusinessException(exception, ErrorCode.PAGE_NOT_FOUND);
    }
  }
}
