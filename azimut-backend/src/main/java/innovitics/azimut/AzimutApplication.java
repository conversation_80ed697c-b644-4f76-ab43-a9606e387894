package innovitics.azimut;

import java.util.concurrent.Executor;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.multipart.MultipartResolver;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.cosium.spring.data.jpa.entity.graph.repository.support.EntityGraphJpaRepositoryFactoryBean;

import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.utilities.datautilities.StringUtility;
import io.swagger.v3.oas.annotations.OpenAPIDefinition;

@SpringBootApplication
@OpenAPIDefinition
@ComponentScan(basePackages = { "innovitics.azimut.utilities" })
@ComponentScan(basePackages = { "innovitics.azimut.models" })
@ComponentScan(basePackages = { "innovitics.azimut.businessmodels" })
@ComponentScan(basePackages = { "innovitics.azimut.businessutilities" })
@ComponentScan(basePackages = { "innovitics.azimut.exceptions" })
@ComponentScan(basePackages = { "innovitics.azimut.controllers" })
@ComponentScan(basePackages = { "innovitics.azimut.rest" })
@ComponentScan(basePackages = { "innovitics.azimut.repositories" })
@ComponentScan(basePackages = { "innovitics.azimut.services" })
@ComponentScan(basePackages = { "innovitics.azimut.pdfgenerator" })
@ComponentScan(basePackages = { "innovitics.azimut.jobs" })
@EntityScan("innovitics.azimut.models")

@EnableJpaRepositories(basePackages = {
    "innovitics.azimut.repositories" }, repositoryFactoryBeanClass = EntityGraphJpaRepositoryFactoryBean.class)

@ConfigurationPropertiesScan(basePackages = { "innovitics.azimut.configproperties" })

@EnableConfigurationProperties(ConfigProperties.class)

@EnableJpaAuditing
@EnableScheduling
@EnableAsync
@EnableTransactionManagement
@EnableCaching
public class AzimutApplication {

  protected static final Logger logger = LoggerFactory.getLogger(AzimutApplication.class);

  public static void main(String[] args) {
    SpringApplication.run(AzimutApplication.class, args);
  }

  @Bean(name = "taskExecutor")
  public Executor taskExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(5);
    executor.setMaxPoolSize(10);
    executor.setQueueCapacity(100);
    executor.setThreadNamePrefix("AsyncThread-");
    executor.initialize();
    return executor;
  }

  @Bean
  public MultipartResolver multipartResolver() {
    CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver();
    this.logger.info("File size:::");
    multipartResolver.setResolveLazily(true);
    multipartResolver.setMaxUploadSize(20000000);
    return multipartResolver;
  }

  @Configuration
  public class WebConfig extends WebMvcAutoConfiguration implements WebMvcConfigurer {
    String[] allowedHeaders = { "Content-Type",
        "X-Requested-With",
        "accept",
        "Origin",
        "Access-Control-Request-Method",
        "Access-Control-Request-Headers",
        "X-CUSTOM1",
        "X-CUSOM2",
        "X-CUSTOM3", StringUtility.LANGUAGE, StringUtility.SIGNATURE_HEADER, "Authorization", "X-CSRF-TOKEN" };

    @Override
    public void addCorsMappings(CorsRegistry registry) {
      registry.addMapping("/**")
          .allowedOrigins("https://azinvest.azimut.eg", "https://portal.azimut.eg")
          .allowedMethods("GET", "POST", "OPTIONS", "PATCH", "DELETE", "PUT", "HEAD", "CONNECT")
          .allowedHeaders(allowedHeaders)
          .exposedHeaders(StringUtility.LANGUAGE, StringUtility.SIGNATURE_HEADER)
          .allowCredentials(true);

    }
  }

}
