package innovitics.azimut.validations.validators.adminuser;

import org.springframework.stereotype.Component;
import org.springframework.validation.Errors;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.validations.validators.BaseValidator;

@Component
public class AddAdminUser extends BaseValidator {
  @Override
  public boolean supports(Class<?> clazz)

  {
    return BusinessAdminUser.class.equals(clazz);

  }

  @Override
  public void validate(Object target, Errors errors) {
    BusinessAdminUser businessAdminUser = (BusinessAdminUser) target;

    if (businessAdminUser != null) {
      if (businessAdminUser.getRoleId() == null
          || !NumberUtility.areLongValuesMatching(businessAdminUser.getRoleId(), 1l))
        errors.rejectValue("roleId", "Invalid role");
    }
  }
}
