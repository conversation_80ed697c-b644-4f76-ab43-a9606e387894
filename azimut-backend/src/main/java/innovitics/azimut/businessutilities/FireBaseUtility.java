package innovitics.azimut.businessutilities;

import java.io.IOException;

import org.springframework.stereotype.Component;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.FirebaseToken;

import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class FireBaseUtility {
	public boolean verify(String idToken,String uuid) throws FirebaseAuthException, IOException
	{
		
		FirebaseToken decodedToken = FirebaseAuth.getInstance().verifyIdToken(idToken);
		String tokenId = decodedToken.getUid();
		String details=decodedToken.getName();
		MyLogger.info("tokenId"+tokenId);
		MyLogger.info("details:::"+details);
		return StringUtility.stringsMatch(tokenId, uuid);
		
	}
	
	
	
}


