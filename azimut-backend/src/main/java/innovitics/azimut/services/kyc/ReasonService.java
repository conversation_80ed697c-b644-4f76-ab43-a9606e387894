package innovitics.azimut.services.kyc;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.models.kyc.Reason;
import innovitics.azimut.models.kyc.ReasonType;
import innovitics.azimut.repositories.kyc.ReasonDynamicRepository;
import innovitics.azimut.services.AbstractService;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.SearchOperation;
import innovitics.azimut.utilities.dbutilities.specifications.childparent.ReasonSpecification;

@Service
public class ReasonService extends AbstractService<Reason, String> {

  @Autowired
  ReasonDynamicRepository reasonDynamicRepository;
  @Autowired
  ReasonSpecification reasonSpecification;

  public List<Reason> findAll() {
    return reasonDynamicRepository.findAllByOrderByIdDesc();
  }

  public List<Reason> findByReasonType(ReasonType reasonType) {
    return reasonDynamicRepository.findAllByReasonType(reasonType);
  }

  public List<Reason> findByKeyword(String keyword, String language) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();

    if (StringUtility.stringsMatch(StringUtility.ENGLISH, language)) {
      searchCriteriaList.add(new SearchCriteria("reason", keyword, SearchOperation.LIKE, null));
    } else if (StringUtility.stringsMatch(StringUtility.ARABIC, language)) {
      searchCriteriaList.add(new SearchCriteria("reasonAr", keyword, SearchOperation.LIKE, null));
    }
    return reasonDynamicRepository.findAll(this.reasonSpecification.findByCriteria(searchCriteriaList));
  }

  public void saveBulk(List<Reason> reasons) {
    reasonDynamicRepository.saveAll(reasons);
  }

  public void save(Reason reason) {
    if (reason.getId() != null) {
      var oldReason = reasonDynamicRepository.getById(reason.getId());
      reason.setCreatedAt(oldReason.getCreatedAt());
      reason.setReasonType(oldReason.getReasonType());
    }
    reasonDynamicRepository.save(reason);
  }

  public void delete(Reason reason) {
    reasonDynamicRepository.delete(reason);
  }
}
