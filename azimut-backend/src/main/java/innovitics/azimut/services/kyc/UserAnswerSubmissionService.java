package innovitics.azimut.services.kyc;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.transaction.Transactional;
import javax.transaction.Transactional.TxType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import com.cosium.spring.data.jpa.entity.graph.domain.EntityGraphType;
import com.cosium.spring.data.jpa.entity.graph.domain.NamedEntityGraph;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.models.kyc.UserAnswer;
import innovitics.azimut.repositories.kyc.UserAnswerDynamicRepository;
import innovitics.azimut.services.AbstractService;
import innovitics.azimut.utilities.crosslayerenums.UserIdType;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.JsonUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.SearchOperation;
import innovitics.azimut.utilities.dbutilities.specifications.child.UserAnswerSpecification;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class UserAnswerSubmissionService extends AbstractService<UserAnswer, String> {

  @Autowired
  UserAnswerDynamicRepository userAnswerDynamicRepository;
  @Autowired
  UserAnswerSpecification userAnwerSpecification;

  public UserAnswer submitAnswer(UserAnswer userAnswer) {
    return this.userAnswerDynamicRepository.save(userAnswer);
  }

  public List<UserAnswer> submitAnswers(List<UserAnswer> userAnswers) {
    StopWatch timer = new StopWatch();
    timer.start();
    List<UserAnswer> inserted = this.userAnswerDynamicRepository.saveAll(userAnswers);
    timer.stop();
    MyLogger.info("batchInsert -> Total time in seconds: " + timer.getTotalTimeSeconds());
    return inserted;

  }

  public UserAnswer getPreviousAnswerIfExisting(Long userId, Long answerId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId.toString(), SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("answerId", answerId.toString(), SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.userAnswerDynamicRepository.findOne(this.userAnwerSpecification.findByCriteria(searchCriteriaList),
        new NamedEntityGraph(EntityGraphType.FETCH, "UserAnswer.details")).get();
  }

  public List<UserAnswer> getUserAnswersByUserIdandPageId(Long pageId, Long userId) {
    List<UserAnswer> userAnswers = new ArrayList<UserAnswer>();
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("pageId", pageId.toString(), SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("userId", userId.toString(), SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    userAnswers = this.userAnswerDynamicRepository.findAll(
        this.userAnwerSpecification.findByCriteria(searchCriteriaList),
        new NamedEntityGraph(EntityGraphType.FETCH, "UserAnswer.details"));
    return userAnswers;
  }

  public UserAnswer updateAnswer(UserAnswer userAnswer) {
    return this.userAnswerDynamicRepository.save(userAnswer);
  }

  public void deleteOldUserAnswers(Long pageId, Long userId) {
    if (pageId != null)
      this.userAnswerDynamicRepository.deleteOldUserAnswersForThePage(pageId, userId);
    else
      this.userAnswerDynamicRepository.deleteOldUserAnswers(userId);

  }

  public boolean checkOldAnswerExistence(Long pageId, Long userId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("pageId", pageId.toString(), SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("userId", userId.toString(), SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    long count = this.userAnswerDynamicRepository.count(this.userAnwerSpecification.findByCriteria(searchCriteriaList));
    return count > 0l;
  }

  public List<UserAnswer> getOldUserAnswers(Long pageId, Long userId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("pageId", pageId.toString(), SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("userId", userId.toString(), SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.userAnswerDynamicRepository.findAll(this.userAnwerSpecification.findByCriteria(searchCriteriaList));

  }

  public List<UserAnswer> getUserAnswersByUserId(Long userId) {
    List<UserAnswer> userAnswers = new ArrayList<UserAnswer>();
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId.toString(), SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    userAnswers = this.userAnswerDynamicRepository.findAll(
        this.userAnwerSpecification.findByCriteria(searchCriteriaList),
        new NamedEntityGraph(EntityGraphType.FETCH, "UserAnswer.details"));
    return userAnswers;
  }

  public List<UserAnswer> getDeletedAnswersByUserId(Long userId) {
    List<UserAnswer> userAnswers = new ArrayList<UserAnswer>();
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId.toString(), SearchOperation.EQUAL, null));
    userAnswers = this.userAnswerDynamicRepository.findAll(
        this.userAnwerSpecification.findByCriteria(searchCriteriaList),
        new NamedEntityGraph(EntityGraphType.FETCH, "UserAnswer.details"));
    if (!userAnswers.isEmpty()) {
      // Find the most recent deletedAt date
      Optional<Date> lastDeletedAt = userAnswers.stream()
          .map(UserAnswer::getDeletedAt)
          .max(Date::compareTo);
      if (lastDeletedAt.isPresent()) {
        Date mostRecentDate = lastDeletedAt.get();

        // Filter the list to include only those UserAnswer objects with the most recent
        // deletedAt date
        List<UserAnswer> filteredUserAnswers = userAnswers.stream()
            .filter(userAnswer -> userAnswer.getDeletedAt().equals(mostRecentDate))
            .collect(Collectors.toList());
        userAnswers = filteredUserAnswers;
      }
    }
    return userAnswers;
  }

  public List<Long> getDistinctQuestionIdsByUserId(Long userId) {
    return this.userAnswerDynamicRepository.getDistinctQuestionIds(userId);
  }

  @Transactional(value = TxType.REQUIRES_NEW)
  public void removeOldUserAnswers(List<UserAnswer> userAnswers) {
    StopWatch timer = new StopWatch();
    String sql = "UPDATE user_answers SET deleted_at=sysdate() WHERE id=:id";

    List<MapSqlParameterSource> params = new ArrayList<MapSqlParameterSource>();

    for (UserAnswer userAnswer : userAnswers) {
      MapSqlParameterSource source = new MapSqlParameterSource();
      source.addValue("id", userAnswer.getId());
      params.add(source);
    }

    timer.start();
    namedJdbcTemplate.batchUpdate(sql, params.toArray(MapSqlParameterSource[]::new));

    timer.stop();
    MyLogger.info("batchUpdate -> Total time in seconds: " + timer.getTotalTimeSeconds());
  }

  private List<Long> getDistinctAnsweredQuestionIdsAndDefinitionQuestionIds(Long userIdType, Long userId) {
    return this.userAnswerDynamicRepository.getDistinctAnsweredQuestionIdsAndDefinitionQuestionIds(userIdType, userId);
  }

  public boolean areUserAnswersComplete(Long userIdType, Long userId) {
    List<Long> ids = this.getDistinctAnsweredQuestionIdsAndDefinitionQuestionIds(userIdType, userId);

    for (int i = 0; i <= ids.size() - 2; i = i + 2) {
      if (!NumberUtility.areLongValuesMatching(ids.get(i), ids.get(i + 1))) {
        MyLogger.info("Question Ids " + ids.get(i) + " and " + ids.get(i + 1) + " are different");
        return false;
      }
    }

    return NumberUtility
        .isNumberEven(this.getDistinctAnsweredQuestionIdsAndDefinitionQuestionIds(userIdType, userId).size());
  }

  public UserAnswer getUserAnswersById(Long id) {

    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("id", id.toString(), SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("deletedAt", "", SearchOperation.IS_NULL, null));
    return this.getSingleElement(
        this.userAnswerDynamicRepository.findAll(this.userAnwerSpecification.findByCriteria(searchCriteriaList),
            new NamedEntityGraph(EntityGraphType.FETCH, "UserAnswer.details")));

  }

  public boolean isAutoApproved(BusinessUser user) {
    if (user.getPdfSignedAt() != null || BooleanUtility.isTrue(user.getIsOld()))
      return true;
    if (!NumberUtility.areLongValuesMatching(user.getIdType(), UserIdType.NATIONAL_ID.getTypeId())
        || !StringUtility.stringsMatch(user.getCountryPhoneCode(), "+20"))
      return false;
    if (user.getCso() == null) {
      return false;
    } else {
      var cso = JsonUtility.fromJson(user.getCso());
      if (BooleanUtility.isFalse((Boolean) cso.get("isValid"))) {
        return false;
      }
    }
    if (user.getAml() == null) {
      return false;
    }
    if (user.getNtra() == null) {
      return false;
    } else {
      var ntra = JsonUtility.fromJson(user.getNtra());
      if (BooleanUtility.isFalse((Boolean) ntra.get("isMatched"))) {
        return false;
      }
    }

    return true;
    // Long userId = user.getId();
    // try {
    // List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    // searchCriteriaList.add(new SearchCriteria("userId", userId.toString(),
    // SearchOperation.EQUAL, null));
    // searchCriteriaList.add(new SearchCriteria("deletedAt", "",
    // SearchOperation.IS_NULL, null));
    // searchCriteriaList.add(new SearchCriteria("questionId", 38,
    // SearchOperation.EQUAL, null));
    // var investmentSize = this.getSingleElement(
    // this.userAnswerDynamicRepository.findAll(this.userAnwerSpecification.findByCriteria(searchCriteriaList)));

    // var isLessThan100k =
    // NumberUtility.areLongValuesMatching(investmentSize.getAnswerId(), 73L);
    // // 74: 100-250k, 75: 250k-500k; 76: > 500K
    // return isLessThan100k;
    // } catch (Exception e) {
    // MyLogger.logStackTrace(e);
    // return false;
    // }

  }

}
