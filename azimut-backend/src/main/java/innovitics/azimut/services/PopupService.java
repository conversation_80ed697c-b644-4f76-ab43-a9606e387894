package innovitics.azimut.services;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.models.Popup;
import innovitics.azimut.models.PopupTemplate;
import innovitics.azimut.repositories.popup.PopupRepository;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.SearchOperation;
import innovitics.azimut.utilities.dbutilities.specifications.child.PopupSpecification;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class PopupService extends AbstractService<Popup, String> {

  @Autowired
  PopupRepository popupRepository;
  @Autowired
  PopupSpecification popupSpecification;

  public List<Popup> findAll() {
    List<Popup> popups = new ArrayList<Popup>();
    popups = this.popupRepository.findAll();
    MyLogger.info("popups::" + popups.toString());

    return popups;
  }

  public List<Popup> findAllByUserId(Long userId) {
    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    searchCriteriaList.add(new SearchCriteria("userId", userId, SearchOperation.EQUAL, null));
    searchCriteriaList.add(new SearchCriteria("isRead", null, SearchOperation.IS_NULL, null));
    return this.popupRepository.findAll(popupSpecification.findByCriteria(searchCriteriaList));
  }

  public Popup addPopup(Long userId, PopupTemplate template) {
    Popup popup = new Popup();
    popup.setCreatedAt(new Date());
    popup.setUserId(userId);
    popup.setPopupTemplate(template);
    return this.popupRepository.save(popup);
  }

  public void readPopup(Long id) {
    this.popupRepository.readPopup(id);
  }
}
