package innovitics.azimut.services.user;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.models.user.SecurityQuestion;
import innovitics.azimut.repositories.user.SecurityQuestionRepository;

@Service
public class SecurityQuestionService {
  @Autowired SecurityQuestionRepository securityQuestionRepository;

  public List<SecurityQuestion> getAllQuestion()
	{
		return this.securityQuestionRepository.findAll();
	}
}