package innovitics.azimut.services.user;

import java.io.IOException;
import java.util.List;
import java.util.Map.Entry;
import java.util.NoSuchElementException;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessmodels.user.AzimutAccount;
import innovitics.azimut.businessmodels.user.BusinessClientBankAccountDetails;
import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.controllers.admin.DTOs.AddBankAccountDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.user.FitsUser;
import innovitics.azimut.repositories.teacomputers.CityDynamicRepository;
import innovitics.azimut.repositories.teacomputers.CountryDynamicRepository;
import innovitics.azimut.repositories.teacomputers.NationalityDynamicRepository;
import innovitics.azimut.repositories.user.FitsUserRepository;
import innovitics.azimut.repositories.user.GenderDynamicRepository;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersAddAccountApi;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersAddClientBankAccountApi;
import innovitics.azimut.rest.entities.teacomputers.AddAccountRequest;
import innovitics.azimut.security.AES;
import innovitics.azimut.services.teacomputer.TeaComputerService;
import innovitics.azimut.utilities.crosslayerenums.UserIdType;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.fileutilities.SecureStorageService;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class FitsUserService {

  @Autowired
  private FitsUserRepository fitsUserRepository;
  @Autowired
  private SecureStorageService storageService;
  @Autowired
  private ConfigProperties configProperties;
  @Autowired
  private AES aes;

  private @Autowired ReferralCodeService referralCodeService;
  private @Autowired TeaComputersAddAccountApi addAccountApi;
  private @Autowired TeaComputersAddClientBankAccountApi addClientBankAccountApi;
  private @Autowired CountryDynamicRepository countryDynamicRepository;
  private @Autowired CityDynamicRepository cityDynamicRepository;
  private @Autowired NationalityDynamicRepository nationalityDynamicRepository;
  private @Autowired GenderDynamicRepository genderDynamicRepository;

  private @Autowired TeaComputerService teaComputerService;

  public Optional<FitsUser> findById(Long id) {
    return fitsUserRepository.findById(id);
  }

  public List<FitsUser> findByMobile(String mobile) {
    return fitsUserRepository.findByMobile(mobile);
  }

  public Optional<FitsUser> findByIdNumber(String idNumber) {
    return fitsUserRepository.findByIdNumber(idNumber);
  }

  public Optional<FitsUser> findByIdTypeAndIdNumber(String idType, String idNumber) {
    return fitsUserRepository.findByIdTypeAndIdNumber(idType, idNumber);
  }

  public List<AzimutAccount> createAzimutAccountList(List<FitsUser> fitsUsers) {
    List<AzimutAccount> azimutAccounts = new java.util.ArrayList<>();
    for (FitsUser fitsUser : fitsUsers) {
      azimutAccounts.add(convertResponseToAzimutAccount(fitsUser));
    }
    return azimutAccounts;
  }

  private AzimutAccount convertResponseToAzimutAccount(FitsUser fitsUser) {

    AzimutAccount azimutAccount = new AzimutAccount();
    Long responseTypeId = UserIdType.getByIdTypeName(fitsUser.getIdType()).getTypeId();

    azimutAccount.setFullName(fitsUser.getFirstName() + " " + fitsUser.getLastName());
    azimutAccount.setPhoneNumber(fitsUser.getMobile());
    azimutAccount.setAzId(fitsUser.getIdNumber());
    azimutAccount.setAzIdType(responseTypeId);

    azimutAccount.setUserIdType(UserIdType.getById(responseTypeId).getType());
    azimutAccount.setUserIdTypeAr(UserIdType.getById(responseTypeId).getTypeAr());

    return azimutAccount;
  }

  public FitsUser uploadContract(Long userId, MultipartFile file) throws IOException, BusinessException {
    FitsUser user = fitsUserRepository.findById(userId)
        .orElseThrow(() -> new BusinessException(ErrorCode.USER_NOT_FOUND));

    this.storageService.uploadFileToBlob(StringUtility.PHYSICAL_CONTRACT_DOCUMENT_NAME,
        file.getInputStream(), true, configProperties.getBlobFitsDigitalPdfPath(),
        userId.toString(), StringUtility.PDF_EXTENSION);

    // update entity
    user.setContractFilePath(userId.toString());
    return fitsUserRepository.save(user);
  }

  public byte[] getContractFileBytes(Long userId) throws IOException, BusinessException {
    FitsUser user = fitsUserRepository.findByClientId(userId)
        .orElseThrow(() -> new BusinessException(ErrorCode.USER_NOT_FOUND));

    if (user.getContractFilePath() == null || user.getContractFilePath().isEmpty()) {
      throw new BusinessException(ErrorCode.NO_DATA_FOUND);
    }

    String path = storageService.generateLocalPath(
        configProperties.getBlobFitsDigitalPdfPath(),
        user.getContractFilePath(),
        StringUtility.PHYSICAL_CONTRACT_DOCUMENT_NAME + "." + StringUtility.PDF_EXTENSION,
        false);

    MyLogger.info("Contract File path: " + path);
    Entry<MediaType, byte[]> fileBytesWithExtension = this.storageService.getFileWithAbsolutePath(aes.encrypt(path));
    return fileBytesWithExtension.getValue();
  }

  public FitsUser createUser(FitsUser user) throws IntegrationException {
    var addAccountRequest = this.prepareAccountAdditionInputs(user);
    addAccountApi.getData(addAccountRequest);
    return fitsUserRepository.save(user);
  }

  public AddAccountRequest prepareAccountAdditionInputs(FitsUser user) {
    var addAccountRequest = new AddAccountRequest();

    if (user.getFirstName() != null && user.getLastName() != null) {
      addAccountRequest
          .setCustomerNameEn(user.getFirstName() + StringUtility.SPACE + user.getLastName());
      addAccountRequest
          .setCustomerNameAr(user.getFirstName() + StringUtility.SPACE + user.getLastName());
    }

    Long idTypeId = UserIdType.getByIdTypeName(user.getIdType()).getTypeId();
    addAccountRequest.setIdType(idTypeId);
    addAccountRequest.setIdTypeId(idTypeId);
    addAccountRequest.setIdNumber(user.getIdNumber());
    addAccountRequest.setIdMaturityDate(user.getIdMaturityDate());
    addAccountRequest.setBirthDate(user.getBirthDate());
    addAccountRequest.setEmail(user.getEmail());
    addAccountRequest.setPhone(user.getMobile());
    addAccountRequest.setMobile(user.getMobile());
    addAccountRequest.setIdDate(user.getIdIssueDate());

    Long clientTypeId = 65L;
    if (StringUtility.isStringPopulated(user.getContactName())) {
      try {
        var referralCode = referralCodeService.findByCode(user.getContactName().toLowerCase());
        clientTypeId = referralCode.getClientTypeId();
      } catch (NoSuchElementException e) {
        MyLogger.info("No Referral Code available for " + user.getContactName());
      }
    }
    addAccountRequest.setClientTypeId(clientTypeId);

    addAccountRequest.setAddressAr(user.getAddress());
    addAccountRequest.setAddressEn(user.getAddress());
    // addAccountRequest.setIDIssueDistrictId(user.getIDIssueDistrictId());
    // addAccountRequest.setPostalNo(user.getPostalNo());

    var country = this.countryDynamicRepository.findByEnglishCountryName(user.getCountry());
    addAccountRequest.setCountryId(country.getSystemCountryCode());
    addAccountRequest.setIDIssueCountryId(country.getSystemCountryCode());

    var city = this.cityDynamicRepository.findByEnglishCityNameAndSystemCountryCode(user.getCity(),
        country.getSystemCountryCode());
    addAccountRequest.setCityId(city.getSystemCityCode());
    addAccountRequest.setIDIssueCityId(city.getSystemCityCode());

    var nationality = this.nationalityDynamicRepository.findByEnglishNationalityName(user.getNationality());
    addAccountRequest.setNationalityId(nationality.getSystemNationalityCode());

    var gender = this.genderDynamicRepository.findByGenderType(user.getGender());
    addAccountRequest.setSexId(gender.getId());

    addAccountRequest.setClientAML(1L);

    addAccountRequest.setOccupation(user.getOccupation());
    // addAccountRequest.setExternalcode(user.getExternalcode());

    addAccountRequest.setSignature(addAccountApi.generateSignature(addAccountRequest));
    MyLogger.info("Azimut Add Account Request:::" + addAccountRequest.toString());

    return addAccountRequest;
  }

  public Page<FitsUser> getFitsUsersWithPaginationAndSearch(String firstName, String lastName, String mobile,
      String idNumber, Pageable pageable) {
    return fitsUserRepository.findBySearchCriteria(firstName, lastName, mobile, idNumber, pageable);
  }

  public void addBankAccountToUser(Long userId, AddBankAccountDto request)
      throws BusinessException, IntegrationException {

    // Get the user to retrieve ID details
    FitsUser user = fitsUserRepository.findById(userId)
        .orElseThrow(() -> new BusinessException(ErrorCode.USER_NOT_FOUND));

    // Construct AddClientBankAccountRequest
    BusinessClientBankAccountDetails accountDetails = new BusinessClientBankAccountDetails();
    accountDetails.setBankId(request.getBankId());
    accountDetails.setCurrencyId(request.getCurrencyId());
    accountDetails.setAccountNumber(request.getAccountNo());
    accountDetails.setIban(request.getIban());
    var branches = this.teaComputerService.getAllBranchesByBankId(request.getBankId());
    if (branches.size() > 0) {
      accountDetails.setBranchId(branches.get(0).getBranchId());
    }
    // Set ID details from the user
    accountDetails.setAzIdType(1L); // Assuming default ID type, adjust as needed
    accountDetails.setAzId(user.getIdNumber());

    var addBankAccountRequest = this.addClientBankAccountApi.prepareRequest(accountDetails);

    addClientBankAccountApi.getData(addBankAccountRequest);
  }

}