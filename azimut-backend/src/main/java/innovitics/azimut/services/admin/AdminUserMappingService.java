package innovitics.azimut.services.admin;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.admin.BusinessAdminUserMapping;
import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.models.admin.AdminUser;
import innovitics.azimut.models.admin.AdminUserMapping;
import innovitics.azimut.models.user.User;
import innovitics.azimut.repositories.admin.AdminUserMappingDynamicRepository;
import innovitics.azimut.repositories.admin.AdminUserMappingRepository;
import innovitics.azimut.repositories.user.UserRepository;
import innovitics.azimut.services.AbstractService;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;
import innovitics.azimut.utilities.dbutilities.DatabaseConditions;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.SearchOperation;
import innovitics.azimut.utilities.dbutilities.specifications.child.AdminUserMappingSpecification;
import innovitics.azimut.utilities.mapping.AdminUserMappingMapper;

@Service
public class AdminUserMappingService extends AbstractService<AdminUserMapping, String> {

  @Autowired
  AdminUserMappingRepository adminUserMappingRepository;

  @Autowired
  AdminUserMappingDynamicRepository adminUserMappingDynamicRepository;

  @Autowired
  AdminUserMappingSpecification adminUserMappingSpecification;

  @Autowired
  UserRepository userRepository;

  @Autowired
  AdminUserMappingMapper adminUserMappingMapper;

  public AdminUserMapping findById(Long id) {
    return adminUserMappingRepository.findById(id).orElse(null);
  }

  public List<AdminUserMapping> findAll() {
    return adminUserMappingRepository.findAll();
  }

  public List<BusinessAdminUserMapping> findByAdminUserId(Long adminUserId) {
    List<AdminUserMapping> mappings = adminUserMappingRepository.findByAdminUserId(adminUserId);
    return mappings.stream()
        .map(adminUserMappingMapper::convertBasicUnitToBusinessUnit)
        .collect(Collectors.toList());
  }

  public List<BusinessAdminUserMapping> findByUserId(Long userId) {
    List<AdminUserMapping> mappings = adminUserMappingRepository.findByUserId(userId);
    return mappings.stream()
        .map(adminUserMappingMapper::convertBasicUnitToBusinessUnit)
        .collect(Collectors.toList());
  }

  public List<BusinessAdminUserMapping> findByCreatedById(Long createdById) {
    List<AdminUserMapping> mappings = adminUserMappingRepository.findByCreatedById(createdById);
    return mappings.stream()
        .map(adminUserMappingMapper::convertBasicUnitToBusinessUnit)
        .collect(Collectors.toList());
  }

  public AdminUserMapping findByAdminUserIdAndUserId(Long adminUserId, Long userId) {
    return adminUserMappingRepository.findByAdminUserIdAndUserId(adminUserId, userId);
  }

  public boolean existsByAdminUserIdAndUserId(Long adminUserId, Long userId) {
    return adminUserMappingRepository.existsByAdminUserIdAndUserId(adminUserId, userId);
  }

  public boolean existsByAdminUserId(Long adminUserId) {
    return adminUserMappingRepository.existsByAdminUserId(adminUserId);
  }

  public AdminUserMapping save(AdminUserMapping adminUserMapping) {
    return adminUserMappingRepository.save(adminUserMapping);
  }

  public List<AdminUserMapping> saveAll(List<AdminUserMapping> adminUserMappings) {
    return adminUserMappingRepository.saveAll(adminUserMappings);
  }

  @Transactional
  public void deleteById(Long id) {
    adminUserMappingRepository.deleteById(id);
  }

  @Transactional
  public void deleteByAdminUserIdAndUserId(Long adminUserId, Long userId) {
    adminUserMappingRepository.deleteByAdminUserIdAndUserId(adminUserId, userId);
  }

  public List<AdminUserMapping> bulkCreateMappings(Long adminUserId, List<String> userIds, Long createdByAdminId) {
    // Find non-deleted users by userId
    List<User> users = userRepository.findNonDeletedUsersByUserIdIn(userIds);

    List<AdminUserMapping> mappings = new ArrayList<>();

    for (User user : users) {
      // Skip if mapping already exists
      if (existsByAdminUserIdAndUserId(adminUserId, user.getId())) {
        continue;
      }

      AdminUserMapping mapping = new AdminUserMapping();

      AdminUser adminUser = new AdminUser();
      adminUser.setId(adminUserId);
      mapping.setAdminUser(adminUser);

      mapping.setUser(user);

      AdminUser createdBy = new AdminUser();
      createdBy.setId(createdByAdminId);
      mapping.setCreatedBy(createdBy);

      mapping.setCreatedAt(new Date());
      mapping.setUpdatedAt(new Date());

      mappings.add(mapping);
    }

    return saveAll(mappings);
  }

  @Transactional
  public void bulkDeleteMappings(Long adminUserId, List<String> userIds, Long deletedByAdminId) {
    // Find non-deleted users by userId and extract their IDs
    List<User> users = userRepository.findNonDeletedUsersByUserIdIn(userIds);
    List<Long> userIdsLong = users.stream()
        .map(User::getId)
        .collect(Collectors.toList());

    // Delete mappings in a single SQL request
    if (!userIdsLong.isEmpty()) {
      adminUserMappingRepository.deleteByAdminUserIdAndUserIdIn(adminUserId, userIdsLong);
    }
  }

  public PaginatedEntity<BusinessAdminUserMapping> listMappingsByAdminUserIdFiltered(Long adminUserId,
      BusinessSearchCriteria businessSearchCriteria) {

    DatabaseConditions databaseConditions = this.generateDatabaseConditions(businessSearchCriteria);
    List<SearchCriteria> searchCriteriaList = databaseConditions.getSearchCriteria() != null
        ? databaseConditions.getSearchCriteria()
        : new ArrayList<SearchCriteria>();

    // Add filter for specific adminUserId
    searchCriteriaList.add(new SearchCriteria("adminUser", adminUserId, SearchOperation.EQUAL, null));

    Page<AdminUserMapping> mappingsPage = this.adminUserMappingDynamicRepository.findAll(
        this.adminUserMappingSpecification.findByCriteria(searchCriteriaList),
        databaseConditions.getPageRequest());

    PaginatedEntity<BusinessAdminUserMapping> paginatedEntity = new PaginatedEntity<BusinessAdminUserMapping>();
    paginatedEntity.setCurrentPage(businessSearchCriteria.getPageNumber());
    paginatedEntity.setPageSize(businessSearchCriteria.getPageSize());
    paginatedEntity.setNumberOfPages(mappingsPage.getTotalPages());
    paginatedEntity.setNumberOfItems(mappingsPage.getTotalElements());
    paginatedEntity.setHasNext(!mappingsPage.isLast());
    paginatedEntity.setHasPrevious(!mappingsPage.isFirst());

    List<BusinessAdminUserMapping> businessMappings = mappingsPage.getContent().stream()
        .map(adminUserMappingMapper::convertBasicUnitToBusinessUnit)
        .collect(Collectors.toList());
    paginatedEntity.setDataList(businessMappings);

    return paginatedEntity;
  }
}
