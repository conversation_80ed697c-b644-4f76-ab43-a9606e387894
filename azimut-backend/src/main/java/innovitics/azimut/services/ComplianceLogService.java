package innovitics.azimut.services;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.models.ComplianceCheckLog;
import innovitics.azimut.repositories.ComplianceCheckLogRepository;

@Service
public class ComplianceLogService {
  @Autowired
  private ComplianceCheckLogRepository logRepository;

  public void logComplianceCheck(BusinessAdminUser admin, String checkType, Long targetUserId, String fraResponse) {
    ComplianceCheckLog log = new ComplianceCheckLog();
    log.setAdminUserId(admin.getId());
    log.setCheckType(checkType);
    log.setTargetUserId(targetUserId);

    if (fraResponse != null && fraResponse.length() > 2000) {
      fraResponse = fraResponse.substring(0, 2000) + "...[truncated]";
    }

    log.setFraResponse(fraResponse);
    log.setCreatedAt(new Date());
    logRepository.save(log);
  }
}
