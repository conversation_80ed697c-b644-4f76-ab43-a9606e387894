package innovitics.azimut.services;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.twilio.Twilio;
import com.twilio.rest.verify.v2.service.Verification;
import com.twilio.rest.verify.v2.service.VerificationCheck;

import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class TwilioService {

  @Autowired
  ConfigProperties configProperties;

  public void send(String phone) {
    Twilio.init(configProperties.getTwilioAccountSid(), configProperties.getTwilioAuthToken());
    Verification verification = Verification.creator(
        configProperties.getTwilioServiceSid(),
        phone,
        "sms")
        .create();

    MyLogger.info("Twilio: Sent SMS to " + phone + " with Sid " + verification.getSid());
  }

  public boolean verify(String phone, String otp) {
    Twilio.init(configProperties.getTwilioAccountSid(), configProperties.getTwilioAuthToken());
    VerificationCheck verificationCheck = VerificationCheck.creator(configProperties.getTwilioServiceSid())
        .setTo(phone).setCode(otp).create();
    return StringUtility.stringsMatch(verificationCheck.getStatus(), "approved");
  }
}
