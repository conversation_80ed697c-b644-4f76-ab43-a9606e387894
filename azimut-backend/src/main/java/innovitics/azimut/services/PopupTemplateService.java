package innovitics.azimut.services;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import innovitics.azimut.controllers.admin.DTOs.PopupDto;
import innovitics.azimut.models.PopupTemplate;
import innovitics.azimut.repositories.popup.PopupTemplateRepository;
import innovitics.azimut.utilities.dbutilities.DatabaseConditions;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.specifications.child.PopupTemplateSpecification;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class PopupTemplateService extends AbstractService<PopupTemplate, String> {

  @Autowired
  PopupTemplateRepository popupTemplateRepository;

  @Autowired
  PopupTemplateSpecification popupTemplateSpecification;

  public Page<PopupTemplate> getAllPopups(DatabaseConditions databaseConditions) {

    List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();
    if (this.searchCriteriaListUtility.isListPopulated(databaseConditions.getSearchCriteria())) {
      searchCriteriaList = databaseConditions.getSearchCriteria();

    }
    this.changeDateFormat(searchCriteriaList, "createdAt");
    MyLogger.info("Search Criteria:::" + searchCriteriaList.toString());

    Page<PopupTemplate> popupTemplates = this.popupTemplateRepository.findAll(
        this.popupTemplateSpecification.findByCriteria(searchCriteriaList),
        databaseConditions.getPageRequest());

    return popupTemplates;
  }

  public void addTemplate(PopupDto businessPopup) {
    if (businessPopup != null && businessPopup.getPopup() != null) {
      PopupTemplate template = businessPopup.getPopup();
      template.setCreatedAt(new Date());
      this.popupTemplateRepository.save(template);
    }
  }

  public void editTemplate(PopupDto businessPopup) {
    if (businessPopup != null && businessPopup.getPopup() != null) {
      PopupTemplate template = businessPopup.getPopup();
      var dbTemplate = getTemplate(template.getId());
      template.setCreatedAt(dbTemplate.getCreatedAt());
      template.setSent(dbTemplate.getSent());
      template.setSentGroups(dbTemplate.getSentGroups());
      this.popupTemplateRepository.save(template);
    }
  }

  public void deleteTemplate(PopupDto businessPopup) {
    if (businessPopup != null && businessPopup.getPopup() != null) {
      PopupTemplate template = businessPopup.getPopup();
      this.popupTemplateRepository.delete(template);
    }
  }

  public void updateTemplate(PopupTemplate template) {
    this.popupTemplateRepository.save(template);
  }

  public PopupTemplate getTemplate(Long id) {
    return this.popupTemplateRepository.getById(id);
  }

}
