package innovitics.azimut.services;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.support.PagedListHolder;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.businessutilities.SearchFilter;
import innovitics.azimut.utilities.businessutilities.BusinessSearchOperation;
import innovitics.azimut.utilities.datautilities.ArrayUtility;
import innovitics.azimut.utilities.datautilities.ListUtility;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.dbutilities.DatabaseConditions;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.dbutilities.SearchOperation;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public abstract class AbstractService<T, S> {
  @Autowired
  protected ListUtility<SearchCriteria> searchCriteriaListUtility;
  @Autowired
  protected ListUtility<Object> objectListUtility;
  @Autowired
  protected ListUtility<T> listUtility;
  @Autowired
  protected ArrayUtility arrayUtility;

  @PersistenceContext
  public EntityManager entityManager;
  @Autowired
  protected NamedParameterJdbcTemplate namedJdbcTemplate;

  protected static final Logger logger = LogManager.getLogger(AbstractService.class);

  PaginatedEntity<T> createPaginatedEntity(List<T> dataList, Integer pageNumber, Integer pageSize) {
    PagedListHolder<T> pages = new PagedListHolder<T>(dataList);
    pages.setPage(pageNumber); // set current page number
    pages.setPageSize(pageSize); // set the size of page
    pages.getPageList();
    PaginatedEntity<T> paginatedEntity = new PaginatedEntity<T>();

    paginatedEntity.setCurrentPage(pageNumber);
    paginatedEntity.setPageSize(pageSize);
    paginatedEntity.setNumberOfPages(pages.getPageCount());
    paginatedEntity.setHasNext(!pages.isLastPage());
    paginatedEntity.setHasPrevious(!pages.isFirstPage());
    paginatedEntity.setDataList(dataList);

    return paginatedEntity;

  }

  public DatabaseConditions generateDatabaseConditions(BusinessSearchCriteria businessSearchCriteria) {
    DatabaseConditions databaseConditions = new DatabaseConditions();
    if (businessSearchCriteria != null) {
      if (businessSearchCriteria.getPageNumber() != null && businessSearchCriteria.getPageSize() != null) {
        databaseConditions.setPageRequest(
            PageRequest.of(businessSearchCriteria.getPageNumber() - 1, businessSearchCriteria.getPageSize()));
      }
      if (businessSearchCriteria.getPageNumber() != null && businessSearchCriteria.getPageSize() != null
          && businessSearchCriteria.getAsc() != null &&
          StringUtility.isStringPopulated(businessSearchCriteria.getSortingParam())) {
        if (businessSearchCriteria.getAsc()) {
          Sort sort = Sort.by(businessSearchCriteria.getSortingParam()).ascending();
          databaseConditions.setPageRequest(
              PageRequest.of(businessSearchCriteria.getPageNumber() - 1, businessSearchCriteria.getPageSize(), sort));
        } else {
          Sort sort = Sort.by(businessSearchCriteria.getSortingParam()).descending();
          databaseConditions.setPageRequest(
              PageRequest.of(businessSearchCriteria.getPageNumber() - 1, businessSearchCriteria.getPageSize(), sort));
        }
      }
      if (this.arrayUtility.isArrayPopulated(businessSearchCriteria.getSearchesAndFilters())) {
        List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();

        for (int i = 0; i < businessSearchCriteria.getSearchesAndFilters().length; i++) {
          SearchFilter searchFilter = businessSearchCriteria.getSearchesAndFilters()[i];

          if (!StringUtility.isStringPopulated(searchFilter.getParentColumn())) {

            if (searchFilter.getOperation() != null && StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.SEARCH.getOperation())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(), searchFilter.getValue(),
                  SearchOperation.LIKE, null));
            } else if (searchFilter.getOperation() != null && StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.FILTER.getOperation())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(),
                  this.arrayUtility.generateObjectListFromObjectArray(searchFilter.getValues()),
                  SearchOperation.IN, null));
            }
          } else {

            if (searchFilter.getOperation() != null && StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.SEARCH.getOperation())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(), searchFilter.getValue(),
                  SearchOperation.PARENT_LIKE, searchFilter.getParentColumn()));
            } else if (searchFilter.getOperation() != null && StringUtility.stringsMatch(searchFilter.getOperation(),
                BusinessSearchOperation.FILTER.getOperation())) {
              searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(),
                  this.arrayUtility.generateObjectListFromObjectArray(searchFilter.getValues()),
                  SearchOperation.PARENT_IN, searchFilter.getParentColumn()));
            }
          }

        }
        databaseConditions.setSearchCriteria(searchCriteriaList);
      }
    }

    return databaseConditions;
  }

  public DatabaseConditions generateDatabaseConditions(SearchFilter[] searchesAndFilters) {
    DatabaseConditions databaseConditions = new DatabaseConditions();

    if (this.arrayUtility.isArrayPopulated(searchesAndFilters)) {
      List<SearchCriteria> searchCriteriaList = new ArrayList<SearchCriteria>();

      for (int i = 0; i < searchesAndFilters.length; i++) {
        SearchFilter searchFilter = searchesAndFilters[i];

        if (!StringUtility.isStringPopulated(searchFilter.getParentColumn())) {

          if (searchFilter.getOperation() != null && StringUtility.stringsMatch(searchFilter.getOperation(),
              BusinessSearchOperation.SEARCH.getOperation())) {
            searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(), searchFilter.getValue(),
                SearchOperation.LIKE, null));
          } else if (searchFilter.getOperation() != null && StringUtility.stringsMatch(searchFilter.getOperation(),
              BusinessSearchOperation.FILTER.getOperation())) {
            searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(),
                this.arrayUtility.generateObjectListFromObjectArray(searchFilter.getValues()),
                SearchOperation.IN, null));
          }
        } else {

          if (searchFilter.getOperation() != null && StringUtility.stringsMatch(searchFilter.getOperation(),
              BusinessSearchOperation.SEARCH.getOperation())) {
            searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(), searchFilter.getValue(),
                SearchOperation.PARENT_LIKE, searchFilter.getParentColumn()));
          } else if (searchFilter.getOperation() != null && StringUtility.stringsMatch(searchFilter.getOperation(),
              BusinessSearchOperation.FILTER.getOperation())) {
            searchCriteriaList.add(new SearchCriteria(searchFilter.getKey(),
                this.arrayUtility.generateObjectListFromObjectArray(searchFilter.getValues()),
                SearchOperation.PARENT_IN, searchFilter.getParentColumn()));
          }
        }

      }
      databaseConditions.setSearchCriteria(searchCriteriaList);
    }

    return databaseConditions;
  }

  void changeDataTypeToInteger(List<SearchCriteria> searchCriteriaList, String key) {
    for (SearchCriteria searchCriteria : searchCriteriaList) {
      if (StringUtility.stringsMatch(searchCriteria.getKey(), key)
          && (new ListUtility<Object>()).isListPopulated(searchCriteria.getValueList())) {
        List<Object> modifiedTypeObjects = new ArrayList<Object>();
        for (Object object : searchCriteria.getValueList()) {
          modifiedTypeObjects.add(Integer.valueOf((String) object));
        }
        searchCriteria.setValueList(modifiedTypeObjects);
      }
    }
  }

  protected void changeDateFormat(List<SearchCriteria> searchCriteriaList, String key) {
    for (SearchCriteria searchCriteria : searchCriteriaList) {
      if (StringUtility.stringsMatch(searchCriteria.getKey(), key))
        try {
          Date date = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX").parse((String) searchCriteria.getValue());
          DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");

          // LocalDate startDate =
          // date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
          searchCriteria.setValue(df.format(date));
        } catch (ParseException e) {
          MyLogger.logStackTrace(e);
        }
    }
  }

  protected <D> void changeDataType(List<SearchCriteria> searchCriteriaList, String key, Class<D> clazz) {
    for (SearchCriteria searchCriteria : searchCriteriaList) {
      if (StringUtility.stringsMatch(searchCriteria.getKey(), key)
          && objectListUtility.isListPopulated(searchCriteria.getValueList())) {
        List<Object> modifiedTypeObjects = new ArrayList<Object>();
        for (Object object : searchCriteria.getValueList()) {
          if (clazz.isInstance(0)) {
            modifiedTypeObjects.add(Integer.valueOf((String) object));
          } else if (clazz.isInstance(0l)) {
            modifiedTypeObjects.add(Long.valueOf((String) object));
          }

        }
        searchCriteria.setValueList(modifiedTypeObjects);
      }
    }
  }

  protected T getSingleElement(List<T> dataList) {
    if (this.listUtility.isListPopulated(dataList))
      return dataList.get(0);
    else
      return null;
  }

}
