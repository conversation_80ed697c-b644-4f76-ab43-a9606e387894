package innovitics.azimut.services.partner;

import java.util.Date;
import java.util.NoSuchElementException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessmodels.user.BusinessUserOTP;
import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.OTPFunctionality;
import innovitics.azimut.models.partner.Partner;
import innovitics.azimut.models.partner.PartnerUser;
import innovitics.azimut.models.user.User;
import innovitics.azimut.repositories.partners.PartnerRepository;
import innovitics.azimut.repositories.partners.PartnerUserRepository;
import innovitics.azimut.utilities.businessutilities.UserUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtException;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

@Service
public class PartnerService {
  @Autowired
  private PartnerRepository partnerRepository;
  @Autowired
  private PartnerUserRepository partnerUserRepository;
  @Autowired
  private ConfigProperties configProperties;
  @Autowired
  private UserUtility userUtility;

  public PartnerUser initiatePartnerUser(String apiKey, String externalId) throws NoSuchElementException {
    Partner currentPartner = partnerRepository.findByApiKey(apiKey).orElseThrow();
    PartnerUser partnerUser = partnerUserRepository.findByPartnerIdAndExternalId(currentPartner.getId(), externalId);
    if (partnerUser != null)
      return partnerUser;

    partnerUser = new PartnerUser();
    partnerUser.setPartner(currentPartner);
    partnerUser.setExternalId(externalId);
    return this.partnerUserRepository.save(partnerUser);
  }

  public String createTokenForPartner(PartnerUser partnerUser) {
    Partner currentPartner = partnerUser.getPartner();

    long currentTime = System.currentTimeMillis();
    long minutesToExpire = 60;
    long expirationTime = currentTime + (1000 * 60) * minutesToExpire;

    Claims claims = Jwts.claims()
        .setSubject(partnerUser.getId().toString())
        .setIssuer(currentPartner.getId().toString())
        .setIssuedAt(new Date(currentTime))
        .setExpiration(new Date(expirationTime))
        .setAudience(partnerUser.getExternalId());

    return Jwts.builder().setClaims(claims).signWith(SignatureAlgorithm.HS512, this.configProperties.getJwTokenKey())
        .compact();
  }

  public PartnerUser validatePartnerUserToken(String token) throws JwtException, NoSuchElementException {
    Claims claims = Jwts.parser().setSigningKey(this.configProperties.getJwTokenKey()).parseClaimsJws(token).getBody();

    if (claims == null || claims.getIssuer() == null || claims.getAudience() == null)
      throw new JwtException("Invalid token");

    long partnerId = Long.parseLong(claims.getIssuer());
    String externalId = claims.getAudience();
    PartnerUser partnerUser = this.partnerUserRepository.findByPartnerIdAndExternalId(partnerId, externalId);

    if (partnerUser == null || !claims.getSubject().equals(partnerUser.getId().toString()))
      throw new NoSuchElementException();

    return partnerUser;
  }

  public PartnerUser joinPartnerUserToNativeUser(PartnerUser partnerUser, Long userId) {
    User realizedUser = new User();
    realizedUser.setId(userId);

    partnerUser.setUser(realizedUser);
    return this.partnerUserRepository.save(partnerUser);
  }

  public PartnerUser getPartnerUserFromToken(String token) throws BusinessException {
    PartnerUser partnerUser;
    try {
      partnerUser = this.validatePartnerUserToken(token);
    } catch (JwtException e) {
      throw new BusinessException(ErrorCode.UNAUTHORIZED_USER, HttpStatus.UNAUTHORIZED);
    } catch (NoSuchElementException e) {
      throw new BusinessException(ErrorCode.NO_DATA_FOUND, HttpStatus.NOT_FOUND);
    }

    return partnerUser;
  }

  public boolean verifyOtpForRealizedUser(
      BusinessUser businessUser, String currentOtp,
      OTPFunctionality otpFunctionality) throws BusinessException {

    BusinessUserOTP businessUserOTP = otpFunctionality == OTPFunctionality.VERIFY_PHONE
        ? this.userUtility.findLatestOTPByPhone(businessUser.getUserPhone())
        : this.userUtility.findOTPByUserId(businessUser.getId(), otpFunctionality);
    if (businessUserOTP == null)
      throw new BusinessException(ErrorCode.OTP_NONE_EXISTING, HttpStatus.NOT_FOUND);

    return businessUserOTP.getOtp().equals(currentOtp);
  }
}
