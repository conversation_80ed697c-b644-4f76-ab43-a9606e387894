package innovitics.azimut.models;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "assets")
@Setter
@Getter
@ToString
public class Asset extends DbBaseEntity {
  private String name;
  private String description;
}
