package innovitics.azimut.models;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "dividends")
@CustomJsonRootName(plural = "response", singular = "response")
@Data
public class Dividend {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private Long id;
  private Long fundId;
  private Long teacomputerId;
  private Date dividendDate;
  private Double amount;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date createdAt;

}
