package innovitics.azimut.models;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.enums.ListType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "negative_list")
@Getter
@Setter
@ToString
public class NegativeList extends DbBaseEntity {

  @Column(name = "full_name", nullable = false)
  private String fullName;

  @Column(name = "alias", columnDefinition = "TEXT")
  private String alias;
  private String nationality;
  private String dateOfBirth;
  @Column(name = "place_of_birth", columnDefinition = "TEXT")
  private String placeOfBirth;
  private String passportNumber;
  private String nationalId;

  @Column(name = "list_source", nullable = false)
  private String listSource;

  @Enumerated(EnumType.STRING)
  private ListType listType;
  private String referenceNumber;
  @Column(name = "address", columnDefinition = "TEXT")
  private String address;
  private String profession;

  @Column(name = "additional_info", columnDefinition = "TEXT")
  private String additionalInfo;
}
