package innovitics.azimut.models;

import javax.persistence.Entity;
import javax.persistence.Table;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Entity
@Table(name = "compliance_check_logs")
@Data
@CustomJsonRootName(plural = "logs", singular = "log")
public class ComplianceCheckLog extends DbBaseEntity {
  private Long adminUserId;
  private String checkType;
  private Long targetUserId;
  private String fraResponse;
}
