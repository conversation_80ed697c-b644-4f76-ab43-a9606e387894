package innovitics.azimut.models.partner;

import innovitics.azimut.models.DbBaseEntity;
import innovitics.azimut.models.user.User;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;

@Entity
@Table(name = "partner_user", uniqueConstraints =
        {@UniqueConstraint(name = "UniquePartnerAndExternalID", columnNames = { "partner_id", "external_id" })})
@Setter
@Getter
@ToString
public class PartnerUser extends DbBaseEntity {

    @ManyToOne
    @JoinColumn(name = "partner_id", nullable = false)
    private Partner partner;

    @ManyToOne
    @JoinColumn(name = "user_id")
    private User user;

    @Column(name="external_id", nullable = false)
    private String externalId;
}
