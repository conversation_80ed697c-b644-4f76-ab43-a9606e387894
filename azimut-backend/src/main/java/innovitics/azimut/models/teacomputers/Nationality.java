package innovitics.azimut.models.teacomputers;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "azimut_nationalities")
@Data
public class Nationality {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private Long id;
  private Long systemNationalityCode;
  private Long nationalityId;
  private String arabicNationalityName;
  private String englishNationalityName;

}
