package innovitics.azimut.models.teacomputers;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "azimut_branches")
@Data
public class Branch {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private Long id;
  private Long bankId;
  private Long systemBankCode;
  private Long branchId;
  private Long systemBranchCode;
  private String englishBranchName;
  private String arabicBranchName;

}
