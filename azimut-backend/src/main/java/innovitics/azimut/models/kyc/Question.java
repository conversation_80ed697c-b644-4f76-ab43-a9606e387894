package innovitics.azimut.models.kyc;

import java.util.Date;
import java.util.Set;

import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.NamedSubgraph;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.models.DbBaseEntity;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler", "appUserId", "pageId" })
@NamedEntityGraph(name = "Question.details", attributeNodes = { @NamedAttributeNode("answers"),
    @NamedAttributeNode(value = "subQuestions") }, subgraphs = {
        @NamedSubgraph(name = "Answer.details", attributeNodes = { @NamedAttributeNode(value = "relatedAnswers") })
    })

@Entity
@Table(name = "questions")
@Getter
@Setter
public class Question extends DbBaseEntity {
  protected String questionText;
  protected String questionTextAr;
  protected String answerType;
  protected Integer questionOrder;
  protected Boolean isAnswerMandatory;
  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "PAGE_ID")
  protected KYCPage kycPage;
  protected Date createdAt;
  protected Date updatedAt;
  protected Date deletedAt;
  @OneToMany(mappedBy = "question")
  @Fetch(FetchMode.JOIN)
  @OrderBy("answerOrder ASC")
  protected Set<Answer> answers;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "parent_question_id")
  protected Question parentQuestion;

  @OneToMany(mappedBy = "parentQuestion")
  @Fetch(FetchMode.JOIN)
  @OrderBy("questionOrder ASC")
  protected Set<Question> subQuestions;

  protected String questionPlaceHolder;
  protected String questionPlaceHolderAr;
  protected String pdfField;

  @Transient
  protected Long appUserId;

  @Transient
  protected Long pageId;

  private Long modelAnswerId;

  @Override
  public String toString() {
    return "Question{" +
        "id=" + getId() +
        ", questionText='" + questionText + '\'' +
        ", answerType='" + answerType + '\'' +
        ", questionOrder=" + questionOrder +
        ", isAnswerMandatory=" + isAnswerMandatory +
        ", kycPageId=" + (kycPage != null ? kycPage.getId() : null) +
        '}';
  }

}
