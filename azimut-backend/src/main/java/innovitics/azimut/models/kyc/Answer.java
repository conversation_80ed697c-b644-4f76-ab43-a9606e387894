package innovitics.azimut.models.kyc;

import java.util.Date;
import java.util.Set;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Transient;

import org.hibernate.annotations.Fetch;
import org.hibernate.annotations.FetchMode;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.models.DbBaseEntity;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler", "appUserId", "pageId" })
@Entity
@NamedEntityGraph(name = "Answer.details", attributeNodes = { @NamedAttributeNode(value = "relatedAnswers") })
@Table(name = "answers")
@Setter
@Getter
public class Answer extends DbBaseEntity {
  @ManyToOne(cascade = { CascadeType.ALL })
  @JoinColumn(name = "question_id")
  private Question question;
  private String answerType;
  private String answerOrder;
  private String answerOption;
  private String answerOptionAr;
  private String answerPlaceHolder;
  private Boolean isRelatedAnswerMandatory;
  private String relatedQuestionText;
  private String relatedQuestionTextAr;

  @Transient
  protected Long appUserId;

  @Transient
  protected Long pageId;

  @ManyToOne(fetch = FetchType.EAGER)
  @JoinColumn(name = "parent_answer_id")
  private Answer parentAnswer;

  @OneToMany(mappedBy = "parentAnswer")
  @Fetch(FetchMode.JOIN)
  @OrderBy("answerOrder ASC")
  private Set<Answer> relatedAnswers;

  private Date deletedAt;

  private Boolean isAnswerMandatory;
  private String answerPlaceHolderAr;
  private String pdfField;

  @Override
  public String toString() {
    return "Answer{" +
        "id=" + getId() +
        ", questionId=" + (question != null ? question.getId() : null) +
        ", answerType='" + answerType + '\'' +
        ", answerOrder='" + answerOrder + '\'' +
        ", answerOption='" + answerOption + '\'' +
        '}';
  }

}
