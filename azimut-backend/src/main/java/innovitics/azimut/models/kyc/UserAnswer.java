package innovitics.azimut.models.kyc;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.NamedEntityGraph;
import javax.persistence.SequenceGenerator;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.models.DbBaseEntity;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "user_answers")
@NamedEntityGraph(name = "UserAnswer.details")
@Setter
@Getter
public class UserAnswer extends DbBaseEntity {
  @Id
  // @GeneratedValue(strategy=GenerationType.AUTO)

  @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "userAnswers_jpa_sequence_generator")
  @SequenceGenerator(name = "userAnswers_jpa_sequence_generator", sequenceName = "user_answer_sequence", initialValue = 23088)

  private Long id;
  private Long userId;
  private Long questionId;
  private Long answerId;
  private Long relatedAnswerId;
  private String countryPhoneCode;
  private String answerValue;
  private String answerType;
  private String documentName;
  private String documentSize;
  private String documentUrl;
  private String documentSubDirectory;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date createdAt;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date updatedAt;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date deletedAt;
  private String countryCode;
  private Long pageId;

  @Override
  public String toString() {
    return "UserAnswer [id=" + id + ", userId=" + userId + ", questionId=" + questionId + ", relatedAnswerId="
        + relatedAnswerId + ", countryPhoneCode=" + countryPhoneCode + ", answerValue=" + answerValue
        + ", answerType=" + answerType + ", documentName=" + documentName + ", documentSize=" + documentSize
        + ", documentUrl=" + documentUrl + ", documentSubDirectory=" + documentSubDirectory + ", createdAt="
        + createdAt + ", updatedAt=" + updatedAt + ", deletedAt=" + deletedAt + ", countryCode=" + countryCode
        + ", pageId=" + pageId + "]";
  }

}
