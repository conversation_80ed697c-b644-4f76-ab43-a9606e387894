package innovitics.azimut.models;

import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.utilities.CustomJsonRootName;
import innovitics.azimut.utilities.dbutilities.StringListConverter;
import lombok.Data;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "notification_templates")
@CustomJsonRootName(plural = "response", singular = "response")
@Data
public class NotificationTemplate {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private Long id;
  private String title;
  private String body;
  private String titleAr;
  private String bodyAr;
  private String link;
  private Boolean sent;

  private int sentCount = 0;

  @Column(columnDefinition = "TEXT")
  @Convert(converter = StringListConverter.class)
  private List<String> sentGroups;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date createdAt;

}
