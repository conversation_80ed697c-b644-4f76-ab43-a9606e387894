package innovitics.azimut.models;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "funds")
@Setter
@Getter
@ToString
@CustomJsonRootName(plural = "response", singular = "response")
public class Fund extends DbBaseEntity {
  private String name;
  @Column(name = "currency_id")
  private int currencyId;
  @Column(name = "asset_id")
  private int assetId;
  private String files;
  private String objective;
  private String RIC;
  private String bbg;
  private String subscription;
  private String redemption;
  private Date date;
  private String logo;
  private String entitiesForm;
  @Column(name = "`order`")
  private int order;
  private String filesAr;
  private String slug;
  private String pricingFreq;
  private boolean buyEnabled;
  private boolean sellEnabled;
  private Double ytd;
  private Double oneYear;
  private Double sinceInception;
  private boolean visible;
  private boolean inFocus;
  private Long teacomputerId;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date deletedAt;

}
