package innovitics.azimut.models.user;

import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.NamedAttributeNode;
import javax.persistence.NamedEntityGraph;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "azimut_data_lookup")
@NamedEntityGraph(name = "AzimutDataLookup.details", attributeNodes = { @NamedAttributeNode("entity"),
    @NamedAttributeNode("entityKey"), @NamedAttributeNode("entityValue") })
@Data
public class AzimutDataLookup {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private Long id;
  private Long entity;
  private String entityKey;
  private String entityValue;
  @ManyToOne(cascade = { CascadeType.ALL })
  @JoinColumn(name = "entity_id")
  private AzimutEntity parent;

}
