package innovitics.azimut.models.user;

import java.util.Date;
import java.util.List;
import java.util.UUID;

import javax.persistence.Column;
import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonProperty.Access;

import innovitics.azimut.models.DbBaseEntity;
import innovitics.azimut.models.admin.AdminUserMapping;
import innovitics.azimut.models.digitalregistry.DigitalRegistry;
import innovitics.azimut.models.partner.PartnerUser;
import innovitics.azimut.utilities.crosslayerenums.RiskLevel;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "app_users")
@Getter
@Setter
public class User extends DbBaseEntity {
  private String userPhone;
  @JsonProperty(access = Access.WRITE_ONLY)
  private String password;
  private String deviceId;
  private String deviceName;
  private String language;
  private String userId;
  @ManyToOne
  @JoinColumn(name = "user_id_type", foreignKey = @javax.persistence.ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
  private UserType userType;
  private String nickName;
  private String emailAddress;
  private String countryPhoneCode;
  private String phoneNumber;
  private String profilePicture;
  private String signedPdf;
  private String picturePath;
  private String pdfPath;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date pdfSignedAt;
  private Boolean isChangeNoApproved;
  private Integer verificationPercentage;
  private Boolean isVerified;
  private Boolean isEmailVerified;
  private Boolean migrated;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date createdAt;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date updatedAt;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date deletedAt;
  private String countryCode;
  private Long lastSolvedPageId;
  private Long nextPageId;
  private Integer userStep;
  private Integer contractMap;

  @Column(name = "id_data", columnDefinition = "json")
  private String idData; // Stores JSON as a string

  @Column(columnDefinition = "json")
  private String cso; // Stores JSON as a string
  @Column(columnDefinition = "json")
  private String ntra; // Stores JSON as a string
  @Column(columnDefinition = "json")
  private String aml; // Stores JSON as a string

  private UUID uuid;
  private String fraStoreId;
  private String fraCompanyStoreId;

  private String country;
  private String city;
  private String firstName;
  private String lastName;
  private String dateOfBirth;
  private String dateOfIdExpiry;

  private String otherUserIdType;
  private Long otherIdType;
  private String otherUserId;
  private String otherNationality;
  private Long genderId;

  private String teacomputersAddressAr;
  private String teacomputersAddressEn;

  private Long teacomputersCityId;
  private Long teacomputersCountryId;

  private Long teacomputersIssueCityId;
  private Long teacomputersIssueCountryId;

  private Long teacomputersNationalityId;

  private Long teacomputersClientaml;

  private Integer azimutAmlMatches;
  private RiskLevel risk;

  private String teacomputersOccupation;

  private Integer failureNumber;

  private Boolean isInstitutional;
  private Boolean livenessChecked;

  private String solvedPages;

  private Boolean isOld;
  private String mailingAddress;

  private Boolean isReviewed;

  private Integer kycStatus;

  private String messagingToken;

  private String dateOfRelease;

  private String provider;

  private String providerId;

  private String clientCode;

  private Integer deletionReasonId;

  private Boolean isSynchronized;

  private Integer failedLogins;

  private Boolean loggedIn;

  private String referralCode;

  private String enrollApplicantId;

  private String enrollRequestId;

  private Long gateIdTask;

  // removed for performance as one to one is always fetched by JPA
  // @OneToOne(mappedBy = "user", fetch = FetchType.LAZY)
  // private UserInvestment userInvestment;

  @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
  private List<UserSecurityQuestion> userSecurityQuestions;

  @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
  private List<UserContract> userContracts;

  @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
  private List<PartnerUser> partnerUsers;

  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date lastLogin;

  public void concatinate() {
    this.setUserPhone(this.getCountryPhoneCode() + this.getPhoneNumber());
  }

  @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
  private List<AdminUserMapping> adminUserMappings;

  @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
  private List<DigitalRegistry> digitalRegistry;

  @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
  private List<UserOldPhone> oldPhones;

  // @OneToMany(mappedBy = "user", fetch = FetchType.LAZY)
  // private List<UserEnrollRequest> enrollRequests;

  @Override
  public String toString() {
    return "User [id=" + this.getId() + ", userPhone=" + userPhone + ", deviceId=" + deviceId + ", userId=" + userId
        + ", userType=" + userType + ", nickName=" + nickName + ", emailAddress=" + emailAddress
        + ", countryPhoneCode=" + countryPhoneCode + ", phoneNumber=" + phoneNumber + ", profilePicture="
        + profilePicture + ", signedPdf=" + signedPdf + ", picturePath=" + picturePath + ", pdfPath=" + pdfPath
        + ", isChangeNoApproved=" + isChangeNoApproved + ", verificationPercentage=" + verificationPercentage
        + ", isVerified=" + isVerified + ", migrated=" + migrated + ", createdAt=" + getCreatedAt() + ", updatedAt="
        + getUpdatedAt() + ", deletedAt=" + getDeletedAt() + ", countryCode=" + countryCode + ", lastSolvedPageId="
        + lastSolvedPageId + ", nextPageId=" + nextPageId + ", userStep=" + userStep + ", contractMap="
        + contractMap + ", country=" + country + ", city=" + city + ", firstName=" + firstName + ", lastName="
        + lastName + ", dateOfBirth=" + dateOfBirth + ", dateOfIdExpiry=" + dateOfIdExpiry
        + ", otherUserIdType=" + otherUserIdType + ", otherIdType=" + otherIdType + ", otherUserId="
        + otherUserId + ", otherNationality=" + otherNationality + ", genderId=" + genderId
        + ", teacomputersAddressAr=" + teacomputersAddressAr + ", teacomputersAddressEn="
        + teacomputersAddressEn + ", teacomputersCityId=" + teacomputersCityId + ", teacomputersCountryId="
        + teacomputersCountryId + ", teacomputersIssueCityId=" + teacomputersIssueCityId
        + ", teacomputersIssueCountryId=" + teacomputersIssueCountryId + ", teacomputersNationalityId="
        + teacomputersNationalityId + ", teacomputersClientaml=" + teacomputersClientaml
        + ", teacomputersOccupation=" + teacomputersOccupation
        + ", isInstitutional=" + isInstitutional + ", livenessChecked=" + livenessChecked + ", solvedPages="
        + solvedPages + ", isOld=" + isOld + ", kycStatus" + kycStatus + "]";
  }

}
