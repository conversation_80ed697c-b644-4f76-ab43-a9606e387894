package innovitics.azimut.models.user;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Entity
@Table(name = "fits_users", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"idType", "idNumber"}, 
                                           name = "unique_id_type_number"))
@Data
@CustomJsonRootName(plural = "fitsUsers", singular = "fitsUser")
public class FitsUser {

  @Id
  @GeneratedValue(strategy = GenerationType.SEQUENCE)
  private Long clientId;

  private String firstName;
  private String lastName;
  private String address;
  private String birthDate;
  private String email;
  private String contactName;
  private String nationality;
  private String idType;
  private String idNumber;
  private String gender;
  private String mobile;
  private String occupation;
  private String clientStatus;
  private String fundName;
  private String unicode;
  private String ageRange;
  private String geographicalScope;
  private String country;
  private String city;
  private String idIssueDate;
  private String idMaturityDate;
  private String contractFilePath;

}
