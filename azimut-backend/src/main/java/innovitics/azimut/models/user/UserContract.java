package innovitics.azimut.models.user;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonFormat;

import innovitics.azimut.models.DbBaseEntity;
import innovitics.azimut.utilities.crosslayerenums.ContractType;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "user_contracts")
@Setter
@Getter
@ToString
public class UserContract extends DbBaseEntity {

  @ManyToOne
  @JoinColumn(name = "user_id", nullable = false)
  private User user;

  private String signedPdf;
  private String pdfPath;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date pdfSignedAt;

  @Enumerated(EnumType.STRING)
  private ContractType contractType;
}
