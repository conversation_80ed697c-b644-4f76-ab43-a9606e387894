package innovitics.azimut.models.user;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import innovitics.azimut.models.DbBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "user_investments")
@Setter
@Getter
@ToString
public class UserInvestment extends DbBaseEntity {

  @OneToOne
  @JoinColumn(name = "user_id", nullable = false, unique = true)
  private User user;

  private Double balance; // in EGP equivalent for both currencies
  private Double totalTopup;
  private Double totalWithdraw;
  private Double totalPosition;
  private Double totalBuyValue;
  private Double totalSellValue;
  @Column(columnDefinition = "json")
  private String funds; // json string of fund ids and their respective balances
  private Double firstTransactionValue;
  private String firstTransactionCurrency; // EGP or USD
  private String firstTransactionReference;
}
