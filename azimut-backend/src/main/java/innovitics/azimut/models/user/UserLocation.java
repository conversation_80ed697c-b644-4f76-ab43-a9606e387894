package innovitics.azimut.models.user;

import java.util.Date;

import javax.persistence.Entity;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.models.DbBaseEntity;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "user_locations")
@Setter
@Getter
@ToString
public class UserLocation extends DbBaseEntity {
  private Long userId;
  private String fullName;
  private String street;
  private String buildingNo;
  private String floorNo;
  private String apartment;
  private String countryCode;
  private String countryPhoneCode;
  private String phoneNumber;
  private String longt;
  private String lat;
  private Date deletedAt;
}
