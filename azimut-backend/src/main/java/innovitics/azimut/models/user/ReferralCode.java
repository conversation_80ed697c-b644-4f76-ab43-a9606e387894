package innovitics.azimut.models.user;

import javax.persistence.Entity;
import javax.persistence.Table;

import innovitics.azimut.models.DbBaseEntity;
import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Entity
@Table(name = "referral_codes")
@Setter
@Getter
@ToString
@CustomJsonRootName(plural = "referralCodes", singular = "referralCode")
public class ReferralCode extends DbBaseEntity {

  private String code;
  private Long clientTypeId;
}
