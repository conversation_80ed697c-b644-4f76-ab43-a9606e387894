package innovitics.azimut.models.digitalregistry;

import java.util.Date;

import org.springframework.data.jpa.domain.Specification;

public class ApiRegistrySpecification {

  public static Specification<ApiRegistry> createdAfter(Date date) {
    return (root, query, cb) -> date != null ? cb.greaterThanOrEqualTo(root.get("createdAt"), date) : null;
  }

  public static Specification<ApiRegistry> createdBefore(Date date) {
    return (root, query, cb) -> date != null ? cb.lessThanOrEqualTo(root.get("createdAt"), date) : null;
  }
}
