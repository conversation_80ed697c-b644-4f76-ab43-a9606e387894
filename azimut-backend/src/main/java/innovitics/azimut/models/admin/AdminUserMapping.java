package innovitics.azimut.models.admin;

import javax.persistence.ConstraintMode;
import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import innovitics.azimut.models.DbBaseEntity;
import innovitics.azimut.models.user.User;
import lombok.Getter;
import lombok.Setter;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "admin_user_mapping", uniqueConstraints = {
    @UniqueConstraint(columnNames = { "admin_user_id", "user_id" })
})
@Setter
@Getter
public class AdminUserMapping extends DbBaseEntity {

  @ManyToOne
  @JoinColumn(name = "admin_user_id", foreignKey = @javax.persistence.ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
  private AdminUser adminUser;

  @ManyToOne
  @JoinColumn(name = "user_id", foreignKey = @javax.persistence.ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
  private User user;

  @ManyToOne
  @JoinColumn(name = "created_by", foreignKey = @javax.persistence.ForeignKey(name = "none", value = ConstraintMode.NO_CONSTRAINT))
  private AdminUser createdBy;
}
