package innovitics.azimut.models.azimutdetails;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;

@JsonIgnoreProperties({ "hibernateLazyInitializer", "handler" })
@Entity
@Table(name = "azimut_details_lookup")
@Data
public class AzimutDetailsLookup {
  @Id
  @GeneratedValue(strategy = GenerationType.AUTO)
  private Long id;
  private String keyString;
  private String valueString;

}
