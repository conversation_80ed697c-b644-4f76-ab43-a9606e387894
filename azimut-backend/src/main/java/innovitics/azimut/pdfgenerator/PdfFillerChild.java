package innovitics.azimut.pdfgenerator;

import java.io.ByteArrayOutputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;

import innovitics.azimut.businessmodels.kyc.BusinessAnswer;
import innovitics.azimut.businessmodels.kyc.BusinessKYCPage;
import innovitics.azimut.businessmodels.kyc.BusinessQuestion;
import innovitics.azimut.businessmodels.kyc.BusinessSubmittedAnswer;
import innovitics.azimut.businessmodels.user.BusinessClientBankAccountDetails;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessmodels.user.BusinessUserOTP;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.digitalregistry.DigitalRegistry;
import innovitics.azimut.models.digitalregistry.DigitalRegistryAction;
import innovitics.azimut.repositories.teacomputers.CountryDynamicRepository;
import innovitics.azimut.repositories.user.UserInvestmentRepository;
import innovitics.azimut.services.digitalregistry.DigitalRegistryService;
import innovitics.azimut.utilities.businessutilities.AzimutDataLookupUtility;
import innovitics.azimut.utilities.crosslayerenums.AnswerType;
import innovitics.azimut.utilities.crosslayerenums.ClientBankAccountPDFFieldName;
import innovitics.azimut.utilities.crosslayerenums.UserIdType;
import innovitics.azimut.utilities.datautilities.ArrayUtility;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.ListUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class PdfFillerChild {

  public static final String SIGNATURE_TEXT = "Digitally signed by ";

  @Autowired
  ListUtility<BusinessQuestion> businessQuestionsListUtility;
  @Autowired
  ListUtility<BusinessKYCPage> businessKycPageListUtility;
  @Autowired
  ListUtility<BusinessSubmittedAnswer> businessSubmittedAnswerListUtility;
  @Autowired
  ListUtility<BusinessAnswer> businessAnswerListUtility;
  @Autowired
  DigitalRegistryService digitalRegistryService;
  @Autowired
  CountryDynamicRepository countryDynamicRepository;
  @Autowired
  UserInvestmentRepository userInvestmentRepository;

  public ByteArrayOutputStream fillPdfFormInChild(BusinessUserOTP businessUserOTP, String language,
      AzimutDataLookupUtility azimutDataLookupUtility, ArrayUtility arrayUtility, BusinessUser businessUser,
      List<BusinessKYCPage> businessKYCPages, String sourceContainerName, String sourceSubDirectory,
      String sourceFileName, String destinationContainerName, String destinationSubDirectory,
      String destinationFileName) throws BusinessException, IOException {

    List<String> textAnswerTypes = new ArrayList<String>();
    List<String> choiceAnswerTypes = new ArrayList<String>();

    textAnswerTypes.add(AnswerType.TEXT.getType());
    textAnswerTypes.add(AnswerType.RICH.getType());
    textAnswerTypes.add(AnswerType.EMAIL.getType());
    textAnswerTypes.add(AnswerType.PHONE.getType());
    textAnswerTypes.add(AnswerType.EMAIL.getType());
    textAnswerTypes.add(AnswerType.CALENDER.getType());
    textAnswerTypes.add(AnswerType.DROP.getType());

    choiceAnswerTypes.add(AnswerType.CHECK.getType());
    choiceAnswerTypes.add(AnswerType.RADIO.getType());

    ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
    MyLogger.info("Accessing the download input stream function in the pdf filler::::");
    PdfReader reader = new PdfReader("Azimut-Contract.pdf");
    PdfStamper stamper;
    try {
      stamper = new PdfStamper(reader, byteArrayOutputStream);
      AcroFields form = stamper.getAcroFields();

      this.fillClientData(businessUserOTP, stamper, form, language, businessUser);

      BusinessClientBankAccountDetails[] businessClientBankAccountDetails = azimutDataLookupUtility
          .getData(businessUser, true);
      MyLogger.info("Retrieved bank accounts::" + businessClientBankAccountDetails.toString());
      if (arrayUtility.isArrayPopulated(businessClientBankAccountDetails)) {
        this.fillClientBankAccountDetails(language, stamper, form, businessClientBankAccountDetails[0]);
        MyLogger.info("PDF filled:::");
      }

      if (this.businessKycPageListUtility.isListPopulated(businessKYCPages)) {
        for (BusinessKYCPage businessKYCPage : businessKYCPages) {
          this.fillPdfFields(language, stamper, form, businessKYCPage, textAnswerTypes, choiceAnswerTypes);
        }
      }
      stamper.setFormFlattening(true);
      stamper.close();
      reader.close();
      byteArrayOutputStream.close();
    } catch (FileNotFoundException e) {
      MyLogger.logStackTrace(e);
    } catch (DocumentException e) {
      MyLogger.logStackTrace(e);
    } catch (IOException e) {
      MyLogger.logStackTrace(e);
    }

    return byteArrayOutputStream;
  }

  protected void fillClientData(BusinessUserOTP businessUserOTP, PdfStamper stamper, AcroFields form, String language,
      BusinessUser businessUser) throws IOException, DocumentException, BusinessException {

    List<String> signatureFields = Arrays.asList("Text9", "Text27", "Text28", "Text29", "19", "30", "35", "50", "100",
        "15", "16", "17", "18");
    List<String> clientNameFields = Arrays.asList("Text1", "Text7", "13", "20", "Text97", "Text24");
    List<String> mobileNumberFields = Arrays.asList("24", "Text63");
    List<String> emailAddressFields = Arrays.asList("25", "Text66");
    List<String> addressFields = Arrays.asList("Text19", "21", "12");
    List<String> userIdFields = Arrays.asList("9", "11", "Text68");

    String clientName = businessUser.getFirstName() + " " + businessUser.getLastName();
    String signatureValue = "";
    if (businessUserOTP != null) {
      MyLogger.info("OTP in the business user::::" + businessUserOTP.getOtp());
      if (StringUtility.isStringPopulated(businessUserOTP.getOtp())) {
        var signDr = digitalRegistryService.getLastDigitalRegistry(businessUser.getId(),
            DigitalRegistryAction.SIGN_CONTRACT);
        var locationDr = digitalRegistryService.getLastDigitalRegistry(businessUser.getId(),
            DigitalRegistryAction.UPDATE_LOCATION);
        if (locationDr == null)
          locationDr = new DigitalRegistry();
        var userInvestment = this.userInvestmentRepository.findByUserId(businessUser.getId()).orElse(null);

        if (signDr != null) {
          signatureValue = SIGNATURE_TEXT
              + "<" + clientName + ">" + StringUtility.NEW_LINE + DateUtility
                  .changeStringDateFormat(businessUserOTP.getCreatedAt().toString(), "yyyy-MM-dd hh:mm:ss",
                      "dd/MM/yyyy.HH:mm:ss")
              + "-" + businessUserOTP.getOtp() + "-" + businessUser.getId() + "-" +
              (businessUser.getEnrollApplicantId() == null ? "" : businessUser.getEnrollApplicantId()) + "-" +
              (businessUser.getEnrollRequestId() == null ? "" : businessUser.getEnrollRequestId()) + "-" +
              businessUser.getUserId() + "-" +
              businessUser.getUserPhone() + "-" +
              (signDr.getSessionId() == null ? "" : signDr.getSessionId()) + "-" + StringUtility.NEW_LINE +
              (locationDr.getLatitude() == null ? "" : (locationDr.getLatitude() + "," + locationDr.getLongitude()))
              + "-"
              + (signDr.getIp() == null ? "" : signDr.getIp()) + "-" +
              businessUser.getDeviceId() + "-" +
              ((userInvestment == null || userInvestment.getFirstTransactionValue() == null) ? ""
                  : userInvestment.getFirstTransactionValue() + userInvestment.getFirstTransactionCurrency())
              + "-" +
              ((userInvestment == null || userInvestment.getFirstTransactionReference() == null) ? ""
                  : userInvestment.getFirstTransactionReference());
        }
        for (String signatureField : signatureFields) {
          StringUtility.arabicFields(language, stamper, form, signatureField, signatureValue, 8f);
        }
        StringUtility.arabicFields(language, stamper, form, "Text98", DateUtility.changeStringDateFormat(
            businessUserOTP.getCreatedAt().toString(), "yyyy-MM-dd hh:mm:ss", "dd.MM.yyyy HH.mm.ss"), 10f);
      }
    }
    StringUtility.arabicFields(language, stamper, form, "Text2", businessUser.getClientCode(), 10f);
    StringUtility.arabicFields(language, stamper, form, "Text62", businessUser.getDateOfBirth(), 12f);
    StringUtility.arabicFields(language, stamper, form, "10", businessUser.getDateOfRelease(), 12f);
    StringUtility.arabicFields(language, stamper, form, "Text4", DateUtility.getArabicDayName(), 12f);
    StringUtility.arabicFields(language, stamper, form, "22", businessUser.getCity(), 8f);

    for (String clientNameField : clientNameFields) {
      StringUtility.arabicFields(language, stamper, form, clientNameField, clientName, 8f);
    }
    for (String mobileNumberField : mobileNumberFields) {
      StringUtility.arabicFields(language, stamper, form, mobileNumberField, businessUser.getUserPhone(), 8f);
    }
    for (String emailAddressField : emailAddressFields) {
      StringUtility.arabicFields(language, stamper, form, emailAddressField, businessUser.getEmailAddress(), 8f);
    }
    for (String addressField : addressFields) {
      StringUtility.arabicFields(language, stamper, form, addressField,
          StringUtility.determineOuputLanguage(businessUser.getAzimutAccount().getAddressEn(),
              businessUser.getAzimutAccount().getAddressAr(), language),
          8f);
    }
    for (String userIdField : userIdFields) {
      StringUtility.arabicFields(language, stamper, form, userIdField, businessUser.getUserId(), 10f);
    }

    form.setField("Text6", DateUtility.getCurrentDayMonthYear());
    form.setField("Text3", DateUtility.getCurrentDayMonthYear());
    form.setField("Text5", (new SimpleDateFormat("EEEE")).format(new Date()));

    if (BooleanUtility.isTrue(businessUser.getIsInstitutional())) {
      form.setField("Check Box9", StringUtility.CHECK_BOX_YES);
      StringUtility.arabicFields(language, stamper, form, "Text25", UserIdType.INSTITUTIONAL.getSignature(), 10f);
      StringUtility.arabicFields(language, stamper, form, "14", UserIdType.INSTITUTIONAL.getSignature(), 10f);

      StringUtility.arabicFields(language, stamper, form, "Text62", null, 12f);
      StringUtility.arabicFields(language, stamper, form, "Text63", null, 12f);
      StringUtility.arabicFields(language, stamper, form, "Text66", null, 12f);
      StringUtility.arabicFields(language, stamper, form, "Text68", null, 12f);

      StringUtility.arabicFields(language, stamper, form, "Text103", businessUser.getUserId(), 10f);
      if (NumberUtility.areLongValuesMatching(businessUser.getIdType(), UserIdType.NATIONAL_ID.getTypeId())) {
        StringUtility.arabicFields(language, stamper, form, "Text8",
            StringUtility.determineOuputLanguage("Egypt", "مصر", language), 8f);
        StringUtility.arabicFields(language, stamper, form, "23",
            StringUtility.determineOuputLanguage("Egypt", "مصر", language), 8f);
        form.setField("Check Box13", StringUtility.CHECK_BOX_YES);

      } else if (NumberUtility.areLongValuesMatching(businessUser.getIdType(), UserIdType.PASSPORT.getTypeId())) {
        StringUtility.arabicFields(language, stamper, form, "Text8", businessUser.getCountry(), 8f);
        StringUtility.arabicFields(language, stamper, form, "23", businessUser.getCountry(), 8f);
        form.setField("Check Box14", StringUtility.CHECK_BOX_YES);

      }
    } else {
      form.setField("Check Box10", StringUtility.CHECK_BOX_YES);

      if (NumberUtility.areLongValuesMatching(businessUser.getIdType(), UserIdType.NATIONAL_ID.getTypeId())) {
        StringUtility.arabicFields(language, stamper, form, "Text8",
            StringUtility.determineOuputLanguage("Egypt", "مصر", language), 8f);
        StringUtility.arabicFields(language, stamper, form, "23",
            StringUtility.determineOuputLanguage("Egypt", "مصر", language), 8f);
        StringUtility.arabicFields(language, stamper, form, "Text25", UserIdType.NATIONAL_ID.getSignature(), 8f);
        StringUtility.arabicFields(language, stamper, form, "14", UserIdType.NATIONAL_ID.getSignature(), 8f);

        form.setField("Check Box13", StringUtility.CHECK_BOX_YES);
        form.setField("Check Box70", StringUtility.CHECK_BOX_YES);
      } else if (NumberUtility.areLongValuesMatching(businessUser.getIdType(), UserIdType.PASSPORT.getTypeId())) {
        var countryId = businessUser.getAzimutAccount().getCountryId();
        var country = countryDynamicRepository.findBySystemCountryCode(countryId);
        StringUtility.arabicFields(language, stamper, form, "Text8",
            StringUtility.determineOuputLanguage(country.getEnglishCountryName(), country.getArabicCountryName(),
                language),
            8f);
        StringUtility.arabicFields(language, stamper, form, "23",
            StringUtility.determineOuputLanguage(country.getEnglishCountryName(), country.getArabicCountryName(),
                language),
            8f);
        StringUtility.arabicFields(language, stamper, form, "Text25", UserIdType.PASSPORT.getSignature(), 8f);
        StringUtility.arabicFields(language, stamper, form, "14", UserIdType.PASSPORT.getSignature(), 8f);

        form.setField("Check Box14", StringUtility.CHECK_BOX_YES);
        form.setField("Check Box72", StringUtility.CHECK_BOX_YES);
      }
    }

  }

  protected void fillClientBankAccountDetails(String language, PdfStamper stamper, AcroFields form,
      BusinessClientBankAccountDetails businessClientBankAccountDetails) throws IOException, DocumentException {
    ClientBankAccountPDFFieldName.assignPDFValues(language, stamper, form, businessClientBankAccountDetails);
  }

  protected void fillPdfFields(String language, PdfStamper stamper, AcroFields form, BusinessKYCPage businessKYCPage,
      List<String> textAnswerTypes, List<String> choiceAnswerTypes) throws IOException, DocumentException {

    for (BusinessQuestion businessQuestion : businessKYCPage.getQuestions()) {
      this.checkQuestionTypeAndSetValue(language, stamper, form, businessQuestion, textAnswerTypes, choiceAnswerTypes);
      if (businessQuestionsListUtility.isListPopulated(businessQuestion.getSubQuestions())) {
        for (BusinessQuestion businessSubQuestion : businessQuestion.getSubQuestions()) {
          this.checkQuestionTypeAndSetValue(language, stamper, form, businessSubQuestion, textAnswerTypes,
              choiceAnswerTypes);
        }
      }
    }
  }

  private void checkQuestionTypeAndSetValue(String language, PdfStamper stamper, AcroFields form,
      BusinessQuestion businessQuestion, List<String> textAnswerTypes, List<String> choiceAnswerTypes)
      throws IOException, DocumentException {
    MyLogger.info("PDF Field Name:::" + businessQuestion.getPdfieldName());
    if (StringUtility.isStringPopulated(businessQuestion.getPdfieldName())) {

      if (textAnswerTypes.contains(businessQuestion.getAnswerType())) {
        if (this.businessSubmittedAnswerListUtility.isListPopulated(businessQuestion.getUserAnswers())) {
          MyLogger.info("Assigned Answer in pdf Field:" + businessQuestion.getPdfieldName() + " is ::"
              + businessQuestion.getUserAnswers().get(0).getAnswerValue());
          // form.setField(businessQuestion.getPdfieldName(),isCalender?
          // value:businessQuestion.getUserAnswers().get(0).getAnswerValue());
          var answer = this.convertDateForCalenderType(businessQuestion.getAnswerType(),
              businessQuestion.getUserAnswers().get(0).getAnswerValue());
          if (StringUtility.containsArabic(answer))
            StringUtility.arabicFields(language, stamper, form, businessQuestion.getPdfieldName(), answer, 8f);
          else
            form.setField(businessQuestion.getPdfieldName(), answer);
          this.checkQuestionTypeAndSetValueForRelatedAnswers(language, stamper, form,
              businessQuestion.getAnswers().get(0), businessQuestion.getUserAnswers().get(0).getRelatedUserAnswers(),
              textAnswerTypes, choiceAnswerTypes);
        }
      }
    } else if (choiceAnswerTypes.contains(businessQuestion.getAnswerType())) {
      MyLogger.info("Choice Answer:::");
      if (this.businessSubmittedAnswerListUtility.isListPopulated(businessQuestion.getUserAnswers())) {
        for (BusinessAnswer businessAnswer : businessQuestion.getAnswers()) {
          MyLogger.info("Answer:::" + businessAnswer.getId());
          for (BusinessSubmittedAnswer businessSubmittedAnswer : businessQuestion.getUserAnswers()) {
            MyLogger.info("UserAnswerID:::" + businessSubmittedAnswer.getAnswerId());
            MyLogger.info("UserAnswerValue:::" + businessSubmittedAnswer.getAnswerValue());
            if (NumberUtility.areLongValuesMatching(businessAnswer.getId(), businessSubmittedAnswer.getAnswerId())) {
              MyLogger
                  .info("Match Found:::" + businessAnswer.getId() + " and " + businessSubmittedAnswer.getAnswerId());
              MyLogger.info("Assigned Answer in pdf Field:::" + businessAnswer.getPdFieldName());
              form.setField(businessAnswer.getPdFieldName(), StringUtility.CHECK_BOX_YES);
              this.checkQuestionTypeAndSetValueForRelatedAnswers(language, stamper, form, businessAnswer,
                  businessSubmittedAnswer.getRelatedUserAnswers(), textAnswerTypes, choiceAnswerTypes);
            }
          }
        }
      }
    }

  }

  private void checkQuestionTypeAndSetValueForRelatedAnswers(String language, PdfStamper stamper, AcroFields form,
      BusinessAnswer parentBusinessAnswer, List<BusinessSubmittedAnswer> relatedUserAnswers,
      List<String> textAnswerTypes, List<String> choiceAnswerTypes) throws IOException, DocumentException {
    if (StringUtility.isStringPopulated(parentBusinessAnswer.getPdFieldName())
        && businessSubmittedAnswerListUtility.isListPopulated(relatedUserAnswers)) {
      if (textAnswerTypes.contains(relatedUserAnswers.get(0).getAnswerType())) {
        if (this.businessSubmittedAnswerListUtility.isListPopulated(relatedUserAnswers)) {
          if (NumberUtility.areIntegerValuesMatching(parentBusinessAnswer.getRelatedAnswers().size(),
              relatedUserAnswers.size())) {
            MyLogger.info("Sizes are matching will fill each value");
            for (int i = 0; i < relatedUserAnswers.size(); i++) {
              // form.setField(parentBusinessAnswer.getRelatedAnswers().get(i).getPdFieldName(),relatedUserAnswers.get(i).getAnswerValue());
              var answer = this.convertDateForCalenderType(relatedUserAnswers.get(0).getAnswerType(),
                  relatedUserAnswers.get(i).getAnswerValue());
              if (StringUtility.containsArabic(answer))
                StringUtility.arabicFields(language, stamper, form,
                    parentBusinessAnswer.getRelatedAnswers().get(i).getPdFieldName(), answer, 8f);
              else
                form.setField(parentBusinessAnswer.getRelatedAnswers().get(i).getPdFieldName(), answer);
            }
          } else {
            MyLogger.info("Sizes are Not matching will fill only the first value");
            var answer = this.convertDateForCalenderType(relatedUserAnswers.get(0).getAnswerType(),
                relatedUserAnswers.get(0).getAnswerValue());
            if (StringUtility.containsArabic(answer))
              StringUtility.arabicFields(language, stamper, form,
                  parentBusinessAnswer.getRelatedAnswers().get(0).getPdFieldName(), answer, 8f);
            else
              form.setField(parentBusinessAnswer.getRelatedAnswers().get(0).getPdFieldName(), answer);
          }
        }
      }

      else if (choiceAnswerTypes.contains(relatedUserAnswers.get(0).getAnswerType())) {
        if (this.businessSubmittedAnswerListUtility.isListPopulated(relatedUserAnswers) &&
            this.businessAnswerListUtility.isListPopulated(parentBusinessAnswer.getRelatedAnswers())) {
          for (BusinessAnswer businessRelatedAnswer : parentBusinessAnswer.getRelatedAnswers()) {
            for (BusinessSubmittedAnswer businessSubmittedRelatedAnswer : relatedUserAnswers) {
              if (NumberUtility.areLongValuesMatching(businessRelatedAnswer.getId(),
                  businessSubmittedRelatedAnswer.getAnswerId())) {
                form.setField(businessRelatedAnswer.getPdFieldName(), StringUtility.CHECK_BOX_YES);
              }
            }
          }
        }
      }
    }
  }

  String convertDateForCalenderType(String answerType, String answerValue) {
    boolean isCalender = StringUtility.stringsMatch(answerType, AnswerType.CALENDER.getType());
    String value = "";
    if (isCalender) {
      value = DateUtility.changingMilliSecondTimeStampToMonthDayYear(answerValue);
    } else {
      value = answerValue;
    }
    return value;
  }

}
