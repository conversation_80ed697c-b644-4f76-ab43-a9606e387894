package innovitics.azimut.controllers.admin;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.kyc.BusinessReview;
import innovitics.azimut.businessservices.BusinessReviewSerivce;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.admin.DTOs.GetReasonsDto;
import innovitics.azimut.controllers.admin.DTOs.GetReviewsDto;
import innovitics.azimut.controllers.admin.DTOs.ReasonDto;
import innovitics.azimut.controllers.admin.DTOs.RecordIdDto;
import innovitics.azimut.controllers.users.DTOs.EditUserAndSubmitReviewDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@RequestMapping(value = "/admin/kyc", produces = {
    MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
public class ReviewController extends BaseController {

  @Autowired
  BusinessReviewSerivce businessReviewSerivce;

  @Autowired
  GenericResponseHandler<BusinessReview> businessReviewHandler;

  @PostMapping(value = "/submitReviews", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessReview>> submitReview(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody EditUserAndSubmitReviewDto baseBusinessEntity)
      throws BusinessException, IOException, IntegrationException {
    return businessReviewHandler.generateBaseGenericResponse(BusinessReview.class,
        businessReviewSerivce.submitReviews(this.getCurrentAdminRequestHolder(token),
            baseBusinessEntity, language),
        null, null);
  }

  @PostMapping(value = "/getReviews", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessReview>> getReviews(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetReviewsDto baseBusinessEntity)
      throws BusinessException, IOException, IntegrationException {
    return businessReviewHandler.generateBaseGenericResponse(BusinessReview.class,
        businessReviewSerivce.getReviewPages(this.getCurrentAdminRequestHolder(token), baseBusinessEntity, language),
        null, null);
  }

  @PostMapping(value = "/getAllReviews", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessReview>> getAllReviews(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody RecordIdDto record)
      throws BusinessException, IOException, IntegrationException {
    return businessReviewHandler.generateBaseGenericResponse(BusinessReview.class, null,
        businessReviewSerivce.getAllReviews(record.getId(), language),
        null);
  }

  @PostMapping(value = "/addReason", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessReview>> addReason(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody ReasonDto businessReview) throws BusinessException, IOException, IntegrationException {
    return businessReviewHandler.generateBaseGenericResponse(BusinessReview.class,
        businessReviewSerivce.addReason(this.getCurrentAdminRequestHolder(token), businessReview, language), null,
        null);
  }

  @PostMapping(value = "/editReason", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessReview>> editReason(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody ReasonDto businessReview) throws BusinessException, IOException, IntegrationException {
    return businessReviewHandler.generateBaseGenericResponse(BusinessReview.class,
        businessReviewSerivce.editReason(this.getCurrentAdminRequestHolder(token), businessReview, language), null,
        null);
  }

  @PostMapping(value = "/deleteReason", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessReview>> deleteReason(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody ReasonDto businessReview) throws BusinessException, IOException, IntegrationException {
    return businessReviewHandler.generateBaseGenericResponse(BusinessReview.class,
        businessReviewSerivce.deleteReason(this.getCurrentAdminRequestHolder(token), businessReview, language), null,
        null);
  }

  @PostMapping(value = "/getReasons")
  protected ResponseEntity<BaseGenericResponse<BusinessReview>> getReasons(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetReasonsDto baseBusinessEntity)
      throws BusinessException, IOException, IntegrationException {
    return businessReviewHandler.generateBaseGenericResponse(BusinessReview.class,
        businessReviewSerivce.getReasons(this.getCurrentAdminRequestHolder(token), baseBusinessEntity, language),
        null, null);
  }

}
