package innovitics.azimut.controllers.admin.DTOs;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

import innovitics.azimut.models.user.FitsUser;
import lombok.Data;

@Data
public class FitsUserRequest {
  @NotBlank
  private String firstName;
  @NotBlank
  private String lastName;
  @NotBlank
  private String address;

  @Pattern(regexp = "\\d{2}-\\d{2}-\\d{4}")
  private String birthDate;

  @Email
  @NotBlank
  private String email;
  @NotBlank
  private String nationality;
  @NotBlank
  private String idType;
  @NotBlank
  private String idNumber;
  @NotBlank
  private String gender;
  @NotBlank
  private String mobile;
  @NotBlank
  private String occupation;
  @NotBlank
  private String country;
  @NotBlank
  private String city;
  @Pattern(regexp = "\\d{2}-\\d{2}-\\d{4}")
  private String idIssueDate;

  @Pattern(regexp = "\\d{2}-\\d{2}-\\d{4}")
  private String idMaturityDate;

  private String contactName;

  public FitsUser toFitsUser() {
    FitsUser user = new FitsUser();
    user.setFirstName(firstName);
    user.setLastName(lastName);
    user.setAddress(address);
    user.setBirthDate(birthDate);
    user.setEmail(email);
    user.setNationality(nationality);
    user.setIdType(idType);
    user.setIdNumber(idNumber);
    user.setGender(gender);
    user.setMobile(mobile);
    user.setOccupation(occupation);
    user.setCountry(country);
    user.setCity(city);
    user.setIdIssueDate(idIssueDate);
    user.setIdMaturityDate(idMaturityDate);
    return user;
  }
}
