package innovitics.azimut.controllers.admin;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.user.BusinessAzimutClient;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.admin.DTOs.RecordIdDto;
import innovitics.azimut.controllers.users.AzimutClientController;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@RequestMapping("/admin/azimut/user")
public class AzimutAdminController extends AzimutClientController {

  @Autowired
  GenericResponseHandler<BusinessAzimutClient> businessAzimutClientResponseHandler;

  @PostMapping(value = "/getAzimutClientBankAccounts", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE }, produces = { MediaType.APPLICATION_JSON_VALUE,
          MediaType.APPLICATION_XML_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessAzimutClient>> getAzimutClientBankAccounts(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody RecordIdDto searchBusinessAzimutClient)
      throws BusinessException, IOException, IntegrationException {

    this.getCurrentAdminRequestHolder(token);
    return businessAzimutClientResponseHandler.generateBaseGenericResponse(BusinessAzimutClient.class,
        this.businessClientDetailsService.getBankAccountsWithDetailsForReviewal(searchBusinessAzimutClient,
            this.businessClientDetailsService.findUserById(searchBusinessAzimutClient.getId(), true), true, null,
            language),
        null, null);

  }
}
