package innovitics.azimut.controllers.admin;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.businessmodels.user.AuthenticationResponse;
import innovitics.azimut.businessservices.BusinessAdminUserService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.admin.DTOs.AdminAuthenticateDto;
import innovitics.azimut.controllers.admin.DTOs.ChangeAdminDto;
import innovitics.azimut.controllers.admin.DTOs.ChangePasswordDto;
import innovitics.azimut.controllers.admin.DTOs.ChangeRoleDto;
import innovitics.azimut.controllers.admin.DTOs.ForgotPasswordDto;
import innovitics.azimut.controllers.authreg.DTOs.RefreshDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.Role;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@RequestMapping("/admin")
public class AdminController extends BaseController {

  private @Autowired BusinessAdminUserService businessAdminUserService;
  private @Autowired GenericResponseHandler<AuthenticationResponse<BusinessAdminUser>> authenticationResponseHandler;
  private @Autowired GenericResponseHandler<BusinessAdminUser> adminUserResponseHandler;
  private @Autowired GenericResponseHandler<Role> roleResponseHandler;

  @PostMapping(value = "/authenticate")
  public ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessAdminUser>>> createAuthenticationToken(
      @Valid @RequestBody AdminAuthenticateDto authenticationRequest)
      throws Exception, BusinessException {
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        this.businessAdminUserService.authenticateAdminUser(authenticationRequest, this.jwtUtil), null, null);
  }

  @PostMapping(value = "/refreshToken")
  public ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessAdminUser>>> refreshToken(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody RefreshDto authenticationRequest) throws Exception, BusinessException {
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        new AuthenticationResponse<BusinessAdminUser>(this.jwtUtil.validateAdminTokenInBodyThenGenerateNewToken(token,
            authenticationRequest.getRefreshToken()), null),
        null, null);
  }

  @PostMapping(value = "/addUser")
  public ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessAdminUser>>> register(
      @Valid @RequestBody AdminAuthenticateDto authenticationRequest,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token) throws Exception, BusinessException {
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        this.businessAdminUserService.register(this.getCurrentAdminRequestHolder(token), authenticationRequest), null,
        null);
  }

  @PostMapping(value = "/deleteUser")
  public ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessAdminUser>>> deleteUser(
      @Valid @RequestBody ChangeAdminDto authenticationRequest,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token) throws Exception, BusinessException {
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        this.businessAdminUserService.deleteUser(this.getCurrentAdminRequestHolder(token), authenticationRequest), null,
        null);
  }

  @PostMapping(value = "/editUser")
  public ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessAdminUser>>> editUser(
      @Valid @RequestBody ChangeAdminDto changeAdminRequest,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token) throws Exception, BusinessException {
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        this.businessAdminUserService.editUser(this.getCurrentAdminRequestHolder(token), changeAdminRequest), null,
        null);
  }

  @PostMapping(value = "/forgotPassword")
  public ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessAdminUser>>> forgotPassword(
      @Valid @RequestBody ForgotPasswordDto authenticationRequest,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws Exception, BusinessException {
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        this.businessAdminUserService.sendAutoGeneratedPassword(authenticationRequest, language), null, null);
  }

  @PostMapping(value = "/changePassword")
  public ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessAdminUser>>> changePassword(
      @Valid @RequestBody ChangePasswordDto businessAdminUser,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token) throws Exception, BusinessException {
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        businessAdminUserService
            .changePassword(this.getCurrentAdminRequestHolder(token), businessAdminUser, this.jwtUtil),
        null, null);
  }

  @PostMapping(value = "/changeAdmin")
  public ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessAdminUser>>> changeAdmin(
      @Valid @RequestBody ChangeAdminDto authenticationRequest,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token) throws Exception, BusinessException {
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        this.businessAdminUserService
            .changeAdminUser(this.getCurrentAdminRequestHolder(token), authenticationRequest),
        null, null);
  }

  @GetMapping(value = "/listAdmins")
  protected ResponseEntity<BaseGenericResponse<BusinessAdminUser>> listAdmins(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token, Integer type)
      throws BusinessException, IOException {
    this.getCurrentAdminRequestHolder(token);
    return this.adminUserResponseHandler.generateBaseGenericResponse(BusinessAdminUser.class, null,
        this.businessAdminUserService.listAdminUsers(), null);
  }

  @GetMapping(value = "/listRoles")
  protected ResponseEntity<BaseGenericResponse<Role>> listRoles(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token, Integer type)
      throws BusinessException, IOException {
    this.getCurrentAdminRequestHolder(token);
    return this.roleResponseHandler.generateBaseGenericResponse(Role.class, null,
        this.businessAdminUserService.listRoles(), null);
  }

  @PostMapping(value = "/deleteRole")
  public ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessAdminUser>>> deleteRole(
      @Valid @RequestBody ChangeRoleDto authenticationRequest,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token) throws Exception, BusinessException {
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        this.businessAdminUserService.deleteRole(this.getCurrentAdminRequestHolder(token), authenticationRequest), null,
        null);
  }

  @PostMapping(value = "/addRole")
  public ResponseEntity<BaseGenericResponse<Role>> addRole(
      @Valid @RequestBody ChangeRoleDto roleDto,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token) throws Exception, BusinessException {
    return roleResponseHandler.generateBaseGenericResponse(Role.class,
        this.businessAdminUserService
            .addRole(this.getCurrentAdminRequestHolder(token), roleDto),
        null, null);
  }

  @PostMapping(value = "/editRole")
  public ResponseEntity<BaseGenericResponse<Role>> editRole(
      @Valid @RequestBody ChangeRoleDto roleDto,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token) throws Exception, BusinessException {
    return roleResponseHandler.generateBaseGenericResponse(Role.class,
        this.businessAdminUserService
            .editRole(this.getCurrentAdminRequestHolder(token), roleDto),
        null, null);
  }

}
