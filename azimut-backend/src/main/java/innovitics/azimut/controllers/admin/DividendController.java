package innovitics.azimut.controllers.admin;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessservices.BusinessDividendService;
import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.admin.DTOs.DividendDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.Dividend;
import innovitics.azimut.models.Fund;
import innovitics.azimut.services.FundService;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@RequestMapping(value = "/admin/dividends", produces = { MediaType.APPLICATION_JSON_VALUE })
public class DividendController extends BaseController {

  private @Autowired GenericResponseHandler<Dividend> dividendHandler;
  private @Autowired GenericResponseHandler<Fund> fundHandler;
  private @Autowired BusinessDividendService dividendService;
  private @Autowired FundService fundService;

  @PostMapping(value = "/list")
  protected ResponseEntity<BaseGenericResponse<Dividend>> listDividends(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody BusinessSearchCriteria businessSearchCriteria)
      throws BusinessException, IOException {
    this.getCurrentAdminRequestHolder(token);
    return this.dividendHandler.generateBaseGenericResponse(Dividend.class, null,
        null, this.dividendService.listDividends(businessSearchCriteria));
  }

  @GetMapping(value = "/listFunds")
  protected ResponseEntity<BaseGenericResponse<Fund>> listFunds(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token)
      throws BusinessException, IOException {
    this.getCurrentAdminRequestHolder(token);
    return this.fundHandler.generateBaseGenericResponse(Fund.class, null,
        this.fundService.getAllFunds(), null);
  }

  @PostMapping(value = "/addDividend", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Dividend>> addDividend(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody DividendDto businessReview) throws BusinessException, IOException, IntegrationException {
    this.getCurrentAdminRequestHolder(token);
    return dividendHandler.generateBaseGenericResponse(Dividend.class,
        dividendService.addDividend(businessReview), null,
        null);
  }

  @PostMapping(value = "/editDividend", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Dividend>> editReason(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody DividendDto businessReview) throws BusinessException, IOException, IntegrationException {
    this.getCurrentAdminRequestHolder(token);
    return dividendHandler.generateBaseGenericResponse(Dividend.class,
        dividendService.editDividend(businessReview), null,
        null);
  }

  @PostMapping(value = "/deleteDividend", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Dividend>> deleteReason(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody DividendDto businessReview) throws BusinessException, IOException, IntegrationException {
    this.getCurrentAdminRequestHolder(token);
    return dividendHandler.generateBaseGenericResponse(Dividend.class,
        dividendService.deleteDividend(businessReview),
        null,
        null);
  }

}
