package innovitics.azimut.controllers.admin.DTOs;

import javax.validation.constraints.NotBlank;

import innovitics.azimut.enums.ListType;
import lombok.Data;

@Data
public class ManualNegativeListEntryDto {
  @NotBlank
  private String fullName;
  private String alias;
  private String nationality;
  private String dateOfBirth;
  private String passportNumber;
  private String nationalId;
  private String listSource = "MANUAL_ENTRY";
  private ListType listType;
  private String additionalInfo;
}