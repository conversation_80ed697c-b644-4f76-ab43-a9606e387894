package innovitics.azimut.controllers.admin;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessmodels.negativelist.BusinessNegativeListMatch;
import innovitics.azimut.businessservices.BusinessNegativeListService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.admin.DTOs.ManualNegativeListEntryDto;
import innovitics.azimut.controllers.admin.DTOs.NegativeListStatistics;
import innovitics.azimut.controllers.admin.DTOs.SearchNegativeListDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@RequestMapping(value = "/admin/negativelist", produces = {
    MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
public class NegativeListController extends BaseController {

  @Autowired
  private BusinessNegativeListService businessNegativeListService;

  @Autowired
  private GenericResponseHandler<BusinessNegativeListMatch> negativeListResponseHandler;

  @Autowired
  GenericResponseHandler<Boolean> booleanHandler;

  @Autowired
  private GenericResponseHandler<NegativeListStatistics> statisticsResponseHandler;

  @PostMapping(value = "/addNegativeList", consumes = {
      MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> addNegativeList(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token)
      throws BusinessException, IOException, IntegrationException {

    this.businessNegativeListService.scrapNegativeList();
    return this.booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE,
        null, null);
  }

  @PostMapping(value = "/search", consumes = {
      MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessNegativeListMatch>> searchNegativeList(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody SearchNegativeListDto searchDto)
      throws BusinessException, IOException, IntegrationException {

    var result = this.businessNegativeListService.performScreening(searchDto.getSearchTerm(),
        searchDto.getPassportNumber(), searchDto.getNationalId(),
        searchDto.getNationality(), searchDto.getDateOfBirth());

    return negativeListResponseHandler.generateBaseGenericResponse(
        BusinessNegativeListMatch.class,
        null,
        result.getMatches(),
        null);
  }

  @PostMapping(value = "/flagExistingUsers")
  protected ResponseEntity<BaseGenericResponse<Boolean>> flagExistingUsers(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestParam(name = "old", required = false) String old,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws BusinessException, IOException, IntegrationException {

    this.businessNegativeListService.flagExistingUsers(true, StringUtility.stringsMatch(old, "true"));
    return this.booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE,
        null, null);
  }

  @PostMapping("/manualAdd")
  // @PreAuthorize("hasRole('ADMIN')")
  public ResponseEntity<BaseGenericResponse<Boolean>> manualAddEntry(
      @Valid @RequestBody ManualNegativeListEntryDto dto) throws BusinessException {
    businessNegativeListService.addManualEntry(dto);
    return this.booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE,
        null, null);
  }

  @PostMapping(value = "/uploadHtmlFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  protected ResponseEntity<BaseGenericResponse<Boolean>> uploadHtmlFile(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestParam("file") MultipartFile file)
      throws BusinessException, IOException, IntegrationException {

    this.businessNegativeListService.uploadHtmlFile(file);
    return this.booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE,
        null, null);
  }

  @GetMapping(value = "/statistics")
  protected ResponseEntity<BaseGenericResponse<NegativeListStatistics>> getStatistics(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language)
      throws BusinessException, IOException, IntegrationException {

    return statisticsResponseHandler.generateBaseGenericResponse(
        NegativeListStatistics.class,
        this.businessNegativeListService.getNegativeListStatistics(),
        null,
        null);
  }
}
