package innovitics.azimut.controllers.admin;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessservices.BusinessNotificationTemplateService;
import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.admin.DTOs.NotificationDto;
import innovitics.azimut.controllers.admin.DTOs.SendNotificationDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.NotificationTemplate;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@RequestMapping(value = "/admin/notifications", produces = { MediaType.APPLICATION_JSON_VALUE })
public class NotificationCenterController extends BaseController {

  private @Autowired GenericResponseHandler<Boolean> booleanHandler;
  private @Autowired GenericResponseHandler<NotificationTemplate> notificationTemplateHandler;
  private @Autowired BusinessNotificationTemplateService notificationTemplateService;

  @PostMapping(value = "/list")
  protected ResponseEntity<BaseGenericResponse<NotificationTemplate>> listNotifications(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody BusinessSearchCriteria businessSearchCriteria)
      throws BusinessException, IOException {
    this.getCurrentAdminRequestHolder(token);
    return this.notificationTemplateHandler.generateBaseGenericResponse(NotificationTemplate.class, null,
        null, this.notificationTemplateService.listNotificationTemplates(businessSearchCriteria));
  }

  @PostMapping(value = "/send")
  protected ResponseEntity<BaseGenericResponse<Boolean>> sendNotification(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody SendNotificationDto notificationRecordIdDto)
      throws IntegrationException, Exception {
    this.getCurrentAdminRequestHolder(token);
    if (notificationRecordIdDto.getUserNationalIds() != null)
      this.notificationTemplateService.sendTemplateToUserIdList(notificationRecordIdDto.getId(),
          notificationRecordIdDto.getUserNationalIds());
    else if (notificationRecordIdDto.getUsers() != null)
      this.notificationTemplateService.sendTemplateToList(notificationRecordIdDto.getId(),
          notificationRecordIdDto.getUsers());
    else
      this.notificationTemplateService.sendTemplate(notificationRecordIdDto.getId(),
          notificationRecordIdDto.getTarget());

    return this.booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE,
        null, null);
  }

  @PostMapping(value = "/addNotification", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<NotificationTemplate>> addTemplate(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody NotificationDto businessReview) throws BusinessException, IOException, IntegrationException {
    this.getCurrentAdminRequestHolder(token);
    return notificationTemplateHandler.generateBaseGenericResponse(NotificationTemplate.class,
        notificationTemplateService.addTemplate(businessReview), null,
        null);
  }

  @PostMapping(value = "/editNotification", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<NotificationTemplate>> editReason(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody NotificationDto businessReview) throws BusinessException, IOException, IntegrationException {
    this.getCurrentAdminRequestHolder(token);
    return notificationTemplateHandler.generateBaseGenericResponse(NotificationTemplate.class,
        notificationTemplateService.editTemplate(businessReview), null,
        null);
  }

  @PostMapping(value = "/deleteNotification", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<NotificationTemplate>> deleteReason(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody NotificationDto businessReview) throws BusinessException, IOException, IntegrationException {
    this.getCurrentAdminRequestHolder(token);
    return notificationTemplateHandler.generateBaseGenericResponse(NotificationTemplate.class,
        notificationTemplateService.deleteTemplate(businessReview),
        null,
        null);
  }

}
