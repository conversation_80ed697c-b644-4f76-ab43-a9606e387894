package innovitics.azimut.controllers.admin;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.admin.DTOs.AddBankAccountDto;
import innovitics.azimut.controllers.admin.DTOs.FitsUserRequest;
import innovitics.azimut.controllers.admin.DTOs.FitsUserSearchRequest;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.user.FitsUser;
import innovitics.azimut.models.user.ReferralCode;
import innovitics.azimut.services.user.FitsUserService;
import innovitics.azimut.services.user.ReferralCodeService;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;

@RestController
@RequestMapping("/admin/fitsUsers")
public class FitsUserController extends BaseController {

  private @Autowired ReferralCodeService referralCodeService;
  private @Autowired FitsUserService fitsUserService;

  private @Autowired GenericResponseHandler<FitsUser> fitsUserHandler;
  private @Autowired GenericResponseHandler<ReferralCode> referralCodeHandler;
  private @Autowired GenericResponseHandler<Boolean> booleanHandler;

  @GetMapping("/{id}")
  public ResponseEntity<BaseGenericResponse<FitsUser>> getFitsUserById(@PathVariable Long id)
      throws BusinessException {
    FitsUser user = fitsUserService.findById(id)
        .orElseThrow(() -> new BusinessException(ErrorCode.USER_NOT_FOUND));
    return fitsUserHandler.generateBaseGenericResponse(FitsUser.class, user, null, null);
  }

  @PostMapping
  public ResponseEntity<BaseGenericResponse<FitsUser>> createFitsUser(@Valid @RequestBody FitsUserRequest request)
      throws BusinessException, IntegrationException {
    FitsUser newUser = request.toFitsUser();
    FitsUser savedUser = fitsUserService.createUser(newUser);
    return fitsUserHandler.generateBaseGenericResponse(FitsUser.class, savedUser, null, null);
  }

  @PostMapping("/{id}/uploadContract")
  public ResponseEntity<BaseGenericResponse<FitsUser>> uploadContract(
      @PathVariable Long id,
      @RequestParam("file") MultipartFile file) throws BusinessException, IOException {
    FitsUser updatedUser = fitsUserService.uploadContract(id, file);
    return fitsUserHandler.generateBaseGenericResponse(FitsUser.class, updatedUser, null, null);
  }

  @GetMapping("/referralCodes")
  public ResponseEntity<BaseGenericResponse<ReferralCode>> getAllReferralCodes() throws BusinessException {
    var referralCodes = this.referralCodeService.findAll();
    return referralCodeHandler.generateBaseGenericResponse(ReferralCode.class, null, referralCodes, null);
  }

  @PostMapping("/search")
  public ResponseEntity<BaseGenericResponse<FitsUser>> searchFitsUsers(
      @Valid @RequestBody FitsUserSearchRequest request)
      throws BusinessException {

    Pageable pageable = PageRequest.of(request.getPage(), request.getSize());
    Page<FitsUser> fitsUsersPage = fitsUserService.getFitsUsersWithPaginationAndSearch(
        request.getFirstName(),
        request.getLastName(),
        request.getMobile(),
        request.getIdNumber(),
        pageable);

    PaginatedEntity<FitsUser> paginatedEntity = new PaginatedEntity<>();
    paginatedEntity.setDataList(fitsUsersPage.getContent());
    paginatedEntity.setCurrentPage(fitsUsersPage.getNumber() + 1); // 1-based indexing
    paginatedEntity.setPageSize(fitsUsersPage.getSize());
    paginatedEntity.setNumberOfItems(fitsUsersPage.getTotalElements());
    paginatedEntity.setNumberOfPages(fitsUsersPage.getTotalPages());
    paginatedEntity.setHasPrevious(fitsUsersPage.hasPrevious());
    paginatedEntity.setHasNext(fitsUsersPage.hasNext());
    paginatedEntity.setNextPage(fitsUsersPage.hasNext() ? fitsUsersPage.getNumber() + 2 : null); // 1-based indexing

    return fitsUserHandler.generateBaseGenericResponse(FitsUser.class, null, null, paginatedEntity);
  }

  @GetMapping("/{id}/downloadContract")
  public ResponseEntity<Resource> downloadContract(
      @RequestHeader(name = StringUtility.AUTHORIZATION_HEADER) String token,
      @PathVariable Long id)
      throws BusinessException, IOException {

    byte[] contractFile = fitsUserService.getContractFileBytes(id);

    ByteArrayResource resource = new ByteArrayResource(contractFile);
    HttpHeaders headers = new HttpHeaders();
    headers.add("Cache-Control", "no-cache, no-store, must-revalidate");
    headers.add("Pragma", "no-cache");
    headers.add("Expires", "0");

    return ResponseEntity.ok()
        .headers(headers)
        .contentLength(contractFile.length)
        .contentType(MediaType.APPLICATION_OCTET_STREAM)
        .body(resource);
  }

  @PostMapping("/{id}/addBankAccount")
  public ResponseEntity<BaseGenericResponse<Boolean>> addBankAccount(
      @PathVariable Long id,
      @Valid @RequestBody AddBankAccountDto request)
      throws BusinessException, IntegrationException {

    // Call the service method to add bank account
    fitsUserService.addBankAccountToUser(id, request);

    return booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE, null, null);
  }

}
