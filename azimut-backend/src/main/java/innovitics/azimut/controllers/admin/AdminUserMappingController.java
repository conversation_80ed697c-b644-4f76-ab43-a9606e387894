package innovitics.azimut.controllers.admin;

import java.util.List;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.businessmodels.admin.BusinessAdminUserMapping;
import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.admin.DTOs.BulkAdminUserMappingRequest;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.services.admin.AdminUserMappingService;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@RequestMapping("/admin/user-mapping")
public class AdminUserMappingController extends BaseController {

  @Autowired
  private AdminUserMappingService adminUserMappingService;

  @Autowired
  private GenericResponseHandler<BusinessAdminUserMapping> mappingResponseHandler;

  @Autowired
  private GenericResponseHandler<Boolean> booleanResponseHandler;

  @PostMapping("/create")
  public ResponseEntity<BaseGenericResponse<Boolean>> bulkCreate(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody BulkAdminUserMappingRequest request) throws BusinessException {

    BusinessAdminUser currentAdmin = this.getCurrentAdminRequestHolder(token);

    adminUserMappingService.bulkCreateMappings(
        request.getAdminUserId(),
        request.getUserIds(),
        currentAdmin.getId());

    return booleanResponseHandler.generateBaseGenericResponse(Boolean.class, true, null, null);
  }

  @PostMapping("/by-admin/{adminUserId}")
  public ResponseEntity<BaseGenericResponse<BusinessAdminUserMapping>> getByAdminUserIdFiltered(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @PathVariable Long adminUserId,
      @Valid @RequestBody BusinessSearchCriteria businessSearchCriteria) throws BusinessException {

    this.getCurrentAdminRequestHolder(token);

    return mappingResponseHandler.generateBaseGenericResponse(BusinessAdminUserMapping.class, null,
        null, adminUserMappingService.listMappingsByAdminUserIdFiltered(adminUserId, businessSearchCriteria));
  }

  @GetMapping("/by-user/{userId}")
  public ResponseEntity<BaseGenericResponse<BusinessAdminUserMapping>> getByUserId(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @PathVariable Long userId) throws BusinessException {

    this.getCurrentAdminRequestHolder(token);

    List<BusinessAdminUserMapping> mappings = adminUserMappingService.findByUserId(userId);

    return mappingResponseHandler.generateBaseGenericResponse(BusinessAdminUserMapping.class, null,
        mappings, null);
  }

  @GetMapping("/by-creator/{createdById}")
  public ResponseEntity<BaseGenericResponse<BusinessAdminUserMapping>> getByCreatedById(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @PathVariable Long createdById) throws BusinessException {

    this.getCurrentAdminRequestHolder(token);

    List<BusinessAdminUserMapping> mappings = adminUserMappingService.findByCreatedById(createdById);

    return mappingResponseHandler.generateBaseGenericResponse(BusinessAdminUserMapping.class, null,
        mappings, null);
  }

  @DeleteMapping("/bulk-delete")
  public ResponseEntity<BaseGenericResponse<Boolean>> bulkDelete(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody BulkAdminUserMappingRequest request) throws BusinessException {

    BusinessAdminUser currentAdmin = this.getCurrentAdminRequestHolder(token);

    adminUserMappingService.bulkDeleteMappings(
        request.getAdminUserId(),
        request.getUserIds(),
        currentAdmin.getId());

    return booleanResponseHandler.generateBaseGenericResponse(Boolean.class, true, null, null);
  }

  @DeleteMapping("/{id}")
  public ResponseEntity<BaseGenericResponse<Boolean>> deleteById(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @PathVariable Long id) throws BusinessException {

    this.getCurrentAdminRequestHolder(token);

    adminUserMappingService.deleteById(id);

    return booleanResponseHandler.generateBaseGenericResponse(Boolean.class, true, null, null);
  }

}
