package innovitics.azimut.controllers.admin;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.kyc.BusinessKYCPage;
import innovitics.azimut.businessservices.BusinessKYCPageChildService;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.admin.DTOs.GetPageByIdDto;
import innovitics.azimut.controllers.kyc.KYCController;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@RequestMapping("/admin/review/kyc")
public class AdminKYCReviewController extends KYCController {
  @Autowired
  protected BusinessKYCPageChildService businessKYCPageChildService;
  @Autowired
  GenericResponseHandler<BusinessKYCPage> businessKYCPageResponseHandler;

  @PostMapping(value = "/getPageById", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE }, produces = {
          MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessKYCPage>> getPageById(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody GetPageByIdDto businessKYCPage) throws BusinessException, IOException, IntegrationException {

    this.getCurrentAdminRequestHolder(token);
    return businessKYCPageResponseHandler.generateBaseGenericResponse(BusinessKYCPage.class,
        this.businessKYCPageChildService.getKycPagebyId(
            this.businessKYCPageChildService.findUserById(businessKYCPage.getAppUserId(), true),
            businessKYCPage.getId(), businessKYCPage.getIsWeb(), language),
        null, null);

  }

}
