package innovitics.azimut.controllers.admin;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessservices.BusinessPopupTemplateService;
import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.admin.DTOs.PopupDto;
import innovitics.azimut.controllers.admin.DTOs.SendNotificationDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.PopupTemplate;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.fileutilities.DiskStorageUtility;

@RestController
@RequestMapping(value = "/admin/popups", produces = { MediaType.APPLICATION_JSON_VALUE })
public class PopupTemplateController extends BaseController {

  private @Autowired GenericResponseHandler<Boolean> booleanHandler;
  private @Autowired GenericResponseHandler<PopupTemplate> popupTemplateHandler;
  private @Autowired BusinessPopupTemplateService popupTemplateService;
  private @Autowired ConfigProperties configProperties;
  private @Autowired DiskStorageUtility diskStorageUtility;

  @PostMapping(value = "/list")
  protected ResponseEntity<BaseGenericResponse<PopupTemplate>> listPopups(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody BusinessSearchCriteria businessSearchCriteria)
      throws BusinessException, IOException {
    this.getCurrentAdminRequestHolder(token);
    return this.popupTemplateHandler.generateBaseGenericResponse(PopupTemplate.class, null,
        null, this.popupTemplateService.listPopupTemplates(businessSearchCriteria));
  }

  @PostMapping(value = "/send")
  protected ResponseEntity<BaseGenericResponse<Boolean>> sendPopup(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody SendNotificationDto popupRecordIdDto)
      throws IntegrationException, Exception {
    this.getCurrentAdminRequestHolder(token);
    if (popupRecordIdDto.getUserNationalIds() != null)
      this.popupTemplateService.sendTemplateToUserIdList(popupRecordIdDto.getId(),
          popupRecordIdDto.getUserNationalIds());
    else if (popupRecordIdDto.getUsers() != null)
      this.popupTemplateService.sendTemplateToList(popupRecordIdDto.getId(),
          popupRecordIdDto.getUsers());
    else
      this.popupTemplateService.sendTemplate(popupRecordIdDto.getId(),
          popupRecordIdDto.getTarget());

    return this.booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE,
        null, null);
  }

  @PostMapping(value = "/addPopup", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<PopupTemplate>> addTemplate(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody PopupDto businessReview) throws BusinessException, IOException, IntegrationException {
    this.getCurrentAdminRequestHolder(token);
    return popupTemplateHandler.generateBaseGenericResponse(PopupTemplate.class,
        popupTemplateService.addTemplate(businessReview), null,
        null);
  }

  @PostMapping(value = "/editPopup", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<PopupTemplate>> editReason(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody PopupDto businessReview) throws BusinessException, IOException, IntegrationException {
    this.getCurrentAdminRequestHolder(token);
    return popupTemplateHandler.generateBaseGenericResponse(PopupTemplate.class,
        popupTemplateService.editTemplate(businessReview), null,
        null);
  }

  @PostMapping(value = "/deletePopup", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<PopupTemplate>> deleteReason(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody PopupDto businessReview) throws BusinessException, IOException, IntegrationException {
    this.getCurrentAdminRequestHolder(token);
    return popupTemplateHandler.generateBaseGenericResponse(PopupTemplate.class,
        popupTemplateService.deleteTemplate(businessReview),
        null,
        null);
  }

  @PostMapping(value = "/uploadFile", consumes = { MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> uploadFileToPublicFolder(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestParam("file") MultipartFile file) throws IOException, BusinessException {
    this.getCurrentAdminRequestHolder(token);

    // Validate file
    if (file.isEmpty()) {
      return this.booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.FALSE,
          null, null);
    }

    // Get the public folder path from configuration
    String publicFolderPath = configProperties.getPublicFolder();

    // Upload the file using DiskStorageUtility
    String filePath = diskStorageUtility.uploadFileToPublicFolder(file, publicFolderPath);

    return this.booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE,
        null, null);
  }

}
