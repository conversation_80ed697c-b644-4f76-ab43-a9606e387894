package innovitics.azimut.controllers.gateid;

import java.io.IOException;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessservices.GateIdService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.entities.gateid.GateIdTask;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;

@RestController
@RequestMapping("/api/gateId")

public class GateIdController extends BaseController {

  private @Autowired GateIdService gateIdService;
  private @Autowired GenericResponseHandler<Boolean> booleanHandler;
  private @Autowired GenericResponseHandler<GateIdTask> gateIdTaskHandler;

  @PostMapping(value = "/gateIdFacialForm", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE }, produces = { MediaType.APPLICATION_JSON_VALUE,
          MediaType.APPLICATION_XML_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> valifyFacialFormData(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestParam(name = "straightFace", required = false) MultipartFile straightFace,
      @RequestParam(name = "leftSide", required = false) MultipartFile leftSide,
      @RequestParam(name = "rightSide", required = false) MultipartFile rightSide,
      @RequestParam(name = "language", required = false) String language) throws MaxUploadSizeExceededException,
      IllegalStateException, BusinessException, IOException, IntegrationException {

    gateIdService.saveSelfie(this.getCurrentRequestHolder(token), straightFace, leftSide, rightSide);
    return booleanHandler.generateBaseGenericResponse(Boolean.class, Boolean.TRUE, null, null);
  }

  @PostMapping(value = "/gateIdForm", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE }, produces = {
          MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
  protected ResponseEntity<BaseGenericResponse<Boolean>> valifyIdFormData(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestParam(name = "frontImage", required = false) MultipartFile frontImage,
      @RequestParam(name = "backImage", required = false) MultipartFile backImage,
      @RequestParam(name = "passportImage", required = false) MultipartFile passportImage,
      @RequestParam(name = "language", required = false) String language,
      @RequestParam(name = "documentType", required = false) String documentType) throws MaxUploadSizeExceededException,
      IllegalStateException, BusinessException, IOException, IntegrationException {
    throw new BusinessException(ErrorCode.OPERATION_NOT_PERFORMED);
    // var user = this.getCurrentRequestHolder(token);
    // if (StringUtility.stringsMatch(documentType, "passport"))
    // gateIdService.savePassport(user, passportImage);
    // else
    // gateIdService.saveNid(user, frontImage, backImage);
    // return booleanHandler.generateBaseGenericResponse(Boolean.class,
    // Boolean.TRUE, null, null);

  }

  @GetMapping(value = "/status")
  protected ResponseEntity<BaseGenericResponse<GateIdTask>> getStatus(
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token) throws BusinessException, IntegrationException {
    var task = gateIdService.getStatus(this.getCurrentRequestHolder(token));
    // set result to null if it is still pending
    if (task.getResult() != null && task.getResult().getFirstName() == null)
      task.setResult(null);
    return gateIdTaskHandler.generateBaseGenericResponse(GateIdTask.class, task, null, null);
  }

}
