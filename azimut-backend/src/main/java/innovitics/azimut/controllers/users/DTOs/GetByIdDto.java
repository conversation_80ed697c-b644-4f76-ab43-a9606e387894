package innovitics.azimut.controllers.users.DTOs;

import javax.validation.constraints.Positive;

import innovitics.azimut.businessmodels.user.BusinessUser;
import lombok.Data;

@Data
public class GetByIdDto {
  @Positive(message = "Id must be positive.")
  private Long id;

  public BusinessUser toBusinessUser() {
    var businessUser = new BusinessUser();
    businessUser.setId(id);
    return businessUser;
  }
}
