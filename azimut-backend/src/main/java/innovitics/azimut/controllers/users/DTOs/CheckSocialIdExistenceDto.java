package innovitics.azimut.controllers.users.DTOs;

import innovitics.azimut.businessmodels.user.BusinessUser;
import lombok.Data;

@Data
public class CheckSocialIdExistenceDto {
  String socialToken;
  String providerId;
  String provider;
  String deviceId;
  String deviceName;
  private String token;
  private String platform;

  public BusinessUser toBusinessUser() {
    BusinessUser businessUser = new BusinessUser();
    businessUser.setSocialToken(socialToken);
    businessUser.setProviderId(providerId);
    businessUser.setProvider(provider);
    return businessUser;
  }
}
