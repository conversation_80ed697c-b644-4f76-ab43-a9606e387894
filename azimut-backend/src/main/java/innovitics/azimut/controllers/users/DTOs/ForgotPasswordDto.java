package innovitics.azimut.controllers.users.DTOs;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

import innovitics.azimut.validations.RegexPattern;
import lombok.Data;

@Data
public class ForgotPasswordDto {
  @NotBlank(message = "Country phone code is required.")
  String countryPhoneCode;

  @NotBlank(message = "Phone number is required.")
  String phoneNumber;

  @NotBlank(message = "New password is required.")
  @Pattern(regexp = RegexPattern.Constants.PASSWORD_VALUE, message = "Invalid password.")
  String newPassword;

  String deviceId;

  @NotNull(message = "OTP is required.")
  String otp;
}
