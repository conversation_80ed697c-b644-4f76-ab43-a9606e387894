package innovitics.azimut.controllers.users.DTOs;

import java.util.Optional;

import lombok.Data;

@Data
public class CheckIfUserDeletedDto {
  Optional<String> countryPhoneCode;
  Optional<String> phoneNumber;
  Optional<String> provider;
  Optional<String> providerId;

  public CheckIfUserDeletedDto(GetByUserPhoneDto getByUserPhoneDto) {
    this.countryPhoneCode = Optional.of(getByUserPhoneDto.getCountryPhoneCode());
    this.phoneNumber = Optional.of(getByUserPhoneDto.getPhoneNumber());
    this.provider = Optional.empty();
    this.providerId = Optional.empty();
  }

  public CheckIfUserDeletedDto(CheckSocialIdExistenceDto checkSocialIdExistenceDto) {
    this.provider = Optional.of(checkSocialIdExistenceDto.getProvider());
    this.providerId = Optional.of(checkSocialIdExistenceDto.getProviderId());
    this.countryPhoneCode = Optional.empty();
    this.phoneNumber = Optional.empty();
  }
}
