package innovitics.azimut.controllers.users.DTOs;

import java.util.List;

import innovitics.azimut.businessmodels.kyc.BusinessReview;
import innovitics.azimut.businessmodels.user.AzimutAccount;
import lombok.Data;

@Data
public class EditUserAndSubmitReviewDto {
  Long id;
  Long appUserId;
  String firstName;
  String lastName;
  String emailAddress;
  String countryCode;
  String countryPhoneCode;
  String phoneNumber;
  Long otherIdType;
  String otherUserId;
  String dateOfBirth;
  String country;
  String city;
  Long pageId;
  String idData;
  String userId;
  String dateOfRelease;
  String dateOfIdExpiry;
  Integer pageOrder;

  AzimutAccount azimutAccount;
  List<BusinessReview> reviews;

}
