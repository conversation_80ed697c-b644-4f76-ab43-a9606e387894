package innovitics.azimut.controllers.users.DTOs;

import javax.validation.constraints.NotBlank;

import innovitics.azimut.businessmodels.user.BusinessUser;
import lombok.Data;

@Data
public class GetByUserPhoneDto {

  @NotBlank(message = "Phone number is required.")
  private String phoneNumber;

  @NotBlank(message = "Country phone code is required.")
  private String countryPhoneCode;

  public BusinessUser toBusinessUser() {
    BusinessUser businessUser = new BusinessUser();
    businessUser.setPhoneNumber(phoneNumber);
    businessUser.setCountryPhoneCode(countryPhoneCode);
    return businessUser;
  }
}
