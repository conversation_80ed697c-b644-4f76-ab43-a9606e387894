package innovitics.azimut.controllers.users.DTOs;

import innovitics.azimut.businessmodels.user.BusinessUser;
import lombok.Data;

@Data
public class DirectUserDto extends GetByUserPhoneDto {
  String otp;
  String sessionInfo;
  String socialToken;
  String providerId;
  String provider;
  String referralCode;

  public BusinessUser toBusinessUser() {
    BusinessUser businessUser = new BusinessUser();
    businessUser.setOtp(otp);
    businessUser.setSessionInfo(sessionInfo);
    businessUser.setSocialToken(socialToken);
    businessUser.setProviderId(providerId);
    businessUser.setProvider(provider);
    businessUser.setPhoneNumber(this.getPhoneNumber());
    businessUser.setCountryPhoneCode(this.getCountryPhoneCode());
    return businessUser;
  }
}
