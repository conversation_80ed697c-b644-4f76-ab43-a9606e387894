package innovitics.azimut.controllers.users.DTOs;

import java.util.ArrayList;
import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

import innovitics.azimut.models.user.SecurityQuestion;
import innovitics.azimut.models.user.UserSecurityQuestion;
import lombok.Data;

@Data
public class SecurityQuestionsListDto {
  @NotEmpty
  private List<@Valid SecurityQuestionDto> questions;

  public List<UserSecurityQuestion> toUserSecurityQuestions() {
    var answers = new ArrayList<UserSecurityQuestion>();
    for (SecurityQuestionDto questionAnswer : questions) {
      answers.add(questionAnswer.toUserSecurityQuestion());
    }
    return answers;
  }
}

@Data
class SecurityQuestionDto {
  @NotNull
  Long securityQuestionId;
  @NotEmpty
  String answer;

  public UserSecurityQuestion toUserSecurityQuestion() {
    var userSecurityQuestion = new UserSecurityQuestion();
    userSecurityQuestion.setAnswer(answer);
    var securityQuestion = new SecurityQuestion();
    securityQuestion.setId(securityQuestionId);
    userSecurityQuestion.setSecurityQuestion(securityQuestion);
    return userSecurityQuestion;
  }
}
