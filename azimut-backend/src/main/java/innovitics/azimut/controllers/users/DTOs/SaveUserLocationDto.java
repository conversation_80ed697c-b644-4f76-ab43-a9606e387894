package innovitics.azimut.controllers.users.DTOs;

import javax.validation.constraints.NotBlank;

import innovitics.azimut.models.user.UserLocation;
import lombok.Data;

@Data
public class SaveUserLocationDto {
  @NotBlank
  String lat;

  @NotBlank
  String longt;

  Integer userStep;

  public UserLocation toUserLocation() {
    UserLocation userLocation = new UserLocation();
    userLocation.setLat(lat);
    userLocation.setLongt(longt);
    return userLocation;
  }
}
