package innovitics.azimut.controllers;

import java.nio.file.Paths;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.utilities.fileutilities.DiskStorageUtility;

@RestController
@RequestMapping("/public/img")
public class PublicImageController {

  @Autowired
  private ConfigProperties configProperties;

  @Autowired
  private DiskStorageUtility diskStorageUtility;

  @GetMapping("/{imageName:.+}")
  public ResponseEntity<Resource> servePublicImage(@PathVariable String imageName) {
    try {
      // Get the public folder name from configuration
      String publicFolderName = configProperties.getPublicFolder();
      
      // Construct the full path relative to UPLOAD_PATH
      String publicFolderPath = diskStorageUtility.getPublicFolderPath(publicFolderName);
      
      // Construct the full file path
      String imagePath = Paths.get(publicFolderPath, imageName).toString();
      
      // Use the utility method to get the file
      Map.Entry<MediaType, byte[]> fileEntry = diskStorageUtility.getFileWithAbsolutePath(imagePath);
      
      // Check if file exists and was read successfully
      if (fileEntry == null || fileEntry.getValue() == null || fileEntry.getValue().length == 0) {
        return ResponseEntity.notFound().build();
      }
      
      // Create a ByteArrayResource from the file bytes
      ByteArrayResource resource = new ByteArrayResource(fileEntry.getValue());
      
      // Return the image with appropriate headers
      return ResponseEntity.ok()
          .contentType(fileEntry.getKey())
          .body(resource);
    } catch (Exception e) {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
  }
}