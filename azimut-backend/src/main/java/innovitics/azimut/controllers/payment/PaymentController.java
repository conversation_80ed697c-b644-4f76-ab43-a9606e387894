package innovitics.azimut.controllers.payment;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.BusinessPayment;
import innovitics.azimut.businessservices.BusinessPaymentService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.payment.DTOs.InitiatePaymentDto;
import innovitics.azimut.controllers.payment.DTOs.QueryPaymentDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@RestController
@RequestMapping(value = "/api/payment", produces = {
    MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
public class PaymentController extends BaseController {

  @Autowired
  BusinessPaymentService businessPaymentService;

  @Autowired
  GenericResponseHandler<BusinessPayment> businessPaymentResponseHandler;

  @PostMapping(value = "/initiatePayment", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessPayment>> initiatePayment(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody InitiatePaymentDto businessPayment)
      throws BusinessException, IOException, IntegrationException {

    var user = this.getCurrentRequestHolder(token);
    if (!BooleanUtility.isTrue(user.getIsVerified()))
      throw new BusinessException(ErrorCode.KYC_INCOMPLETE);

    return businessPaymentResponseHandler.generateBaseGenericResponse(BusinessPayment.class,
        this.businessPaymentService.initiatePayment(businessPayment,
            user, language, BooleanUtility.getValue(businessPayment.getIsMobile())),
        null, null);
  }

  @PostMapping(value = "/initiatePaymobPayment", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessPayment>> initiatePaymobPayment(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody InitiatePaymentDto businessPayment)
      throws BusinessException, IOException, IntegrationException {

    var user = this.getCurrentRequestHolder(token);
    if (!BooleanUtility.isTrue(user.getIsVerified()))
      throw new BusinessException(ErrorCode.KYC_INCOMPLETE);

    return businessPaymentResponseHandler.generateBaseGenericResponse(BusinessPayment.class,
        this.businessPaymentService.initiatePaymobPayment(businessPayment,
            user, language, BooleanUtility.getValue(businessPayment.getIsMobile()), true),
        null, null);
  }

  @PostMapping(value = "/queryPayment", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessPayment>> queryPayment(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody QueryPaymentDto businessPayment) throws BusinessException, IOException, IntegrationException {
    try {
      Thread.sleep(5000); // sleep 5 seconds for the payment hook to be processed
    } catch (InterruptedException e) {
      MyLogger.logStackTrace(e);
    }
    return businessPaymentResponseHandler.generateBaseGenericResponse(BusinessPayment.class,
        this.businessPaymentService.queryPayment(businessPayment.toBusinessPayment(),
            this.getCurrentRequestHolder(token), language, BooleanUtility.getValue(businessPayment.getIsMobile())),
        null, null);
  }

  @PostMapping(value = "/queryPaymobPayment", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessPayment>> queryPaymobPayment(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @Valid @RequestBody QueryPaymentDto businessPayment) throws BusinessException, IOException, IntegrationException {
    try {
      Thread.sleep(5000); // sleep 5 seconds for the payment hook to be processed
    } catch (InterruptedException e) {
      MyLogger.logStackTrace(e);
    }
    return businessPaymentResponseHandler.generateBaseGenericResponse(BusinessPayment.class,
        this.businessPaymentService.queryPaymobPayment(businessPayment.toBusinessPayment(),
            this.getCurrentRequestHolder(token), language, BooleanUtility.getValue(businessPayment.getIsMobile())),
        null, null);
  }

}
