package innovitics.azimut.controllers.payment.DTOs;

import javax.validation.constraints.NotNull;

import innovitics.azimut.businessmodels.BusinessPayment;
import lombok.Data;

@Data
public class QueryPaymentDto {
  @NotNull(message = "referenceTransactionId is required")
  String referenceTransactionId;

  Boolean isMobile;

  public BusinessPayment toBusinessPayment() {
    BusinessPayment businessPayment = new BusinessPayment();
    businessPayment.setReferenceTransactionId(referenceTransactionId);
    return businessPayment;
  }
}
