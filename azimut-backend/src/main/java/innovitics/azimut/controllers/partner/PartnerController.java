package innovitics.azimut.controllers.partner;

import java.io.IOException;
import java.time.Duration;
import java.util.NoSuchElementException;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import innovitics.azimut.businessmodels.user.AuthenticationResponse;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessservices.BusinessClientDetailsService;
import innovitics.azimut.businessservices.BusinessUserService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.partner.Dtos.PartnerUserDto;
import innovitics.azimut.controllers.partner.Dtos.PartnerUserTokenDto;
import innovitics.azimut.controllers.partner.Dtos.PartnerUserVerificationEmailDto;
import innovitics.azimut.controllers.partner.Dtos.VerifyPartnerUserOtpDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.OTPFunctionality;
import innovitics.azimut.models.partner.PartnerUser;
import innovitics.azimut.models.user.User;
import innovitics.azimut.rest.apis.teacomputers.TeaComputersAddAccountApi;
import innovitics.azimut.services.partner.PartnerService;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.Refill;

@Validated
@RestController
@RequestMapping(value = "/api/partner", produces = {
    MediaType.APPLICATION_JSON_VALUE })
public class PartnerController extends BaseController {
  private static final String API_KEY_HEADER_NAME = "X-API-KEY";
  @Autowired
  private PartnerService partnerService;
  @Autowired
  private GenericResponseHandler<String> singleAttributeHandler;
  @Autowired
  private BusinessUserService businessUserService;
  @Autowired
  GenericResponseHandler<AuthenticationResponse<BusinessUser>> authenticationResponseHandler;
  @Autowired
  private BusinessClientDetailsService businessClientDetailsService;
  @Autowired
  private TeaComputersAddAccountApi addAccountApi;

  private final Cache<String, Bucket> cache = Caffeine.newBuilder()
      .maximumSize(100_000) // Limit total entries
      .expireAfterAccess(Duration.ofMinutes(10)) // Remove unused IPs
      .build();

  private Bucket getBucket(String key) {
    return cache.get(key, k -> {
      Bandwidth limit = Bandwidth.classic(5, Refill.intervally(5, Duration.ofMinutes(1))); // 5 requests per minute
      return Bucket.builder().addLimit(limit).build();
    });
  }

  @PostMapping(value = "/partnerUser", consumes = { MediaType.APPLICATION_JSON_VALUE })
  ResponseEntity<BaseGenericResponse<String>> initiatePartnerUser(
      @RequestHeader(name = API_KEY_HEADER_NAME, required = true) String apiKey,
      @Valid @RequestBody PartnerUserDto newPartnerUserDto) throws BusinessException {
    PartnerUser partnerUser;

    try {
      partnerUser = this.partnerService.initiatePartnerUser(apiKey, newPartnerUserDto.getExternalId());
    } catch (NoSuchElementException e) {
      throw new BusinessException(ErrorCode.NO_DATA_FOUND, HttpStatus.NOT_FOUND);
    }
    String jwtToken = this.partnerService.createTokenForPartner(partnerUser);

    return singleAttributeHandler.generateBaseGenericResponse(
        JwtResponse.class, jwtToken, null, null);
  }

  @PostMapping(value = "/validateToken", consumes = { MediaType.APPLICATION_JSON_VALUE })
  ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessUser>>> validateToken(
      @Valid @RequestBody PartnerUserTokenDto jwtToken) throws BusinessException {
    User realizedUser = this.partnerService.getPartnerUserFromToken(jwtToken.getToken()).getUser();
    if (realizedUser == null)
      throw new BusinessException(ErrorCode.NO_DATA_FOUND, HttpStatus.NOT_FOUND);

    BusinessUser businessUser = this.businessUserService.getByUserId(realizedUser.getId());

    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        new AuthenticationResponse<BusinessUser>(
            this.jwtUtil.generateTokenUsingUserDetails(businessUser),
            this.businessUserService.beautifyUser(businessUser)),
        null, null);
  }

  @PostMapping(value = "/sendVerificationEmail")
  protected ResponseEntity<BaseGenericResponse<String>> sendVerificationEmail(
      @Valid @RequestBody PartnerUserVerificationEmailDto verificationEmailDto)
      throws BusinessException, IOException {
    PartnerUser partnerUser = this.partnerService.getPartnerUserFromToken(verificationEmailDto.getToken());
    if (partnerUser.getUser() != null)
      throw new BusinessException(ErrorCode.OPERATION_NOT_PERFORMED, HttpStatus.CONFLICT);

    BusinessUser businessUser = this.businessUserService.getByUserPhone(verificationEmailDto.getPhoneNumber());
    String userEmailAddress = businessUser.getEmailAddress();

    if (userEmailAddress != null)
      this.businessUserService.sendOtpEmail(businessUser, null);
    return singleAttributeHandler.generateBaseGenericResponse(EmailResponse.class,
        userEmailAddress, null, null);
  }

  @PostMapping(value = "/verifyPartnerUserOtps")
  protected ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessUser>>> verifyPartnerUserOtps(
      HttpServletRequest request,
      @Valid @RequestBody VerifyPartnerUserOtpDto verifyOtpBody)
      throws BusinessException, IntegrationException {

    // Get IP address and bucket key
    String bucketKey = verifyOtpBody.getPhoneNumber();

    // Get or create bucket
    Bucket bucket = getBucket(bucketKey);
    if (!bucket.tryConsume(1))
      throw new BusinessException(ErrorCode.RATE_LIMIT_EXCEEDED);

    // Proceed with existing OTP verification logic
    PartnerUser partnerUser = this.partnerService.getPartnerUserFromToken(verifyOtpBody.getToken());
    if (partnerUser.getUser() != null)
      throw new BusinessException(ErrorCode.OPERATION_NOT_PERFORMED, HttpStatus.CONFLICT);

    BusinessUser businessUser = this.businessUserService.getByUserPhone(verifyOtpBody.getPhoneNumber());

    boolean verifiedPhoneOtp = this.partnerService.verifyOtpForRealizedUser(businessUser,
        verifyOtpBody.getPhoneOtp(), OTPFunctionality.VERIFY_PHONE);
    boolean verifiedEmailOtp = this.partnerService.verifyOtpForRealizedUser(businessUser,
        verifyOtpBody.getEmailOtp(), OTPFunctionality.VERIFY_EMAIL);

    if (!verifiedPhoneOtp || !verifiedEmailOtp)
      throw new BusinessException(ErrorCode.INVALID_OTP, HttpStatus.UNPROCESSABLE_ENTITY);

    User matchedUser = this.partnerService.joinPartnerUserToNativeUser(partnerUser, businessUser.getId()).getUser();
    if (matchedUser == null || !matchedUser.getId().equals(businessUser.getId()))
      throw new BusinessException(ErrorCode.OPERATION_FAILURE, HttpStatus.BAD_REQUEST);

    if (businessUser.isOnFits()) {
      // add as a tea Computer user using partner userName
      var addAccountRequest = this.businessClientDetailsService.prepareAccountAdditionInputs(businessUser);
      addAccountRequest.setUserName(partnerUser.getPartner().getUserName());
      addAccountRequest.setClientTypeId(partnerUser.getPartner().getClientTypeId());
      addAccountRequest.setSignature(addAccountApi.generateSignature(addAccountRequest));
      addAccountApi.getData(addAccountRequest);
    }

    if (BooleanUtility.isFalse(businessUser.getIsEmailVerified())) {
      this.businessUserService.verifyUserEmail(businessUser, verifyOtpBody.getEmailOtp());
    }

    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        new AuthenticationResponse<BusinessUser>(
            this.jwtUtil.generateTokenUsingUserDetails(businessUser),
            this.businessUserService.beautifyUser(businessUser)),
        null, null);
  }
}
