package innovitics.azimut.controllers.partner.Dtos;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class VerifyPartnerUserOtpDto extends PartnerUserVerificationEmailDto{
    @JsonProperty("phone_otp")
    @NotEmpty(message="phone otp is empty.")
    private String phoneOtp;

    @JsonProperty("email_otp")
    @NotEmpty(message="email otp is empty.")
    private String emailOtp;
}
