package innovitics.azimut.controllers.authreg;

import java.io.IOException;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.firebase.auth.FirebaseAuthException;

import innovitics.azimut.businessmodels.user.AuthenticationResponse;
import innovitics.azimut.businessmodels.user.BusinessFlow;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessservices.BusinessUserService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.authreg.DTOs.RefreshDto;
import innovitics.azimut.controllers.users.DTOs.AuthenticateDto;
import innovitics.azimut.controllers.users.DTOs.CheckSocialIdExistenceDto;
import innovitics.azimut.controllers.users.DTOs.DirectUserDto;
import innovitics.azimut.controllers.users.DTOs.ForgotPasswordDto;
import innovitics.azimut.controllers.users.DTOs.SaveUserTemporarilyDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.partner.PartnerUser;
import innovitics.azimut.models.user.User;
import innovitics.azimut.security.RecaptchaResult;
import innovitics.azimut.security.RecaptchaUtility;
import innovitics.azimut.services.digitalregistry.DigitalRegistryService;
import innovitics.azimut.services.partner.PartnerService;
import innovitics.azimut.utilities.businessutilities.UserUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;

@Validated
@RestController
@CrossOrigin
@RequestMapping(value = "/api", produces = {
    MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
public class JwtAuthenticationController extends BaseController {

  @Autowired
  BusinessUserService businessUserService;
  @Autowired
  DigitalRegistryService digitalRegistryService;
  @Autowired
  GenericResponseHandler<AuthenticationResponse<BusinessUser>> authenticationResponseHandler;
  @Autowired
  PartnerService partnerService;
  @Autowired
  UserUtility userUtility;

  @Autowired
  RecaptchaUtility recaptchaUtility;

  @PostMapping(value = "/authenticate")
  public ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessUser>>> createAuthenticationToken(
      @Valid @RequestBody AuthenticateDto authenticateDto,
      @RequestHeader(name = StringUtility.LANGUAGE, required = false) String language,
      @RequestHeader(name = StringUtility.IP, required = false) String ip)
      throws Exception, BusinessException {
    RecaptchaResult recaptchaRes = new RecaptchaResult();
    User user = userUtility.checkIfUserExists(authenticateDto.getCountryPhoneCode() + authenticateDto.getPhoneNumber());
    Date lastLoginDate = user != null ? user.getLastLogin() : null;
    if (lastLoginDate == null)
      lastLoginDate = new Date();
    // If the last login was more than 3 days ago, create a recaptcha assessment
    Instant daysAgo = Instant.now().minus(3, ChronoUnit.DAYS);
    if (authenticateDto.getToken() != null && lastLoginDate.toInstant().isBefore(daysAgo))
      recaptchaRes = recaptchaUtility.createAssessment(authenticateDto.getToken(), authenticateDto.getPlatform(),
          authenticateDto.getCountryPhoneCode() + authenticateDto.getPhoneNumber(), "LOGIN", true);
    try {
      BusinessUser businessUser = new BusinessUser();
      businessUser = this.businessUserService.beautifyUser(this.businessUserService.getByUserPhoneAndPassword(
          authenticateDto.getCountryPhoneCode() + authenticateDto.getPhoneNumber(),
          authenticateDto.getPassword(), authenticateDto.getDeviceId(), authenticateDto.getDeviceName(), language));
      this.digitalRegistryService.recordLogin(ip, businessUser.getId());
      if (authenticateDto.getToken() != null && recaptchaRes.getAssessmentId() != null)
        recaptchaUtility.annotateLoginAssessment(
            authenticateDto.getCountryPhoneCode() + authenticateDto.getPhoneNumber(),
            recaptchaRes.getAssessmentId(), true);
      return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
          new AuthenticationResponse<BusinessUser>(this.jwtUtil.generateTokenUsingUserDetails(businessUser),
              businessUser),
          null,
          null);
    } catch (BusinessException e) {
      if (authenticateDto.getToken() != null && recaptchaRes.getAssessmentId() != null)
        recaptchaUtility.annotateLoginAssessment(
            authenticateDto.getCountryPhoneCode() + authenticateDto.getPhoneNumber(), recaptchaRes.getAssessmentId(),
            false);
      throw e;
    }
  }

  @PostMapping(value = "/refresh")
  public ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessUser>>> refreshToken(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @Valid @RequestBody RefreshDto authenticationRequest) throws Exception, BusinessException {
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        new AuthenticationResponse<BusinessUser>(this.jwtUtil.validateTokenInBodyThenGenerateNewToken(token,
            authenticationRequest.getRefreshToken(), authenticationRequest.getDeviceId()), null),
        null, null);
  }

  @PostMapping(value = "/forgotPassword", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE })
  protected ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessUser>>> forgotPassword(
      @RequestHeader(name = StringUtility.IP, required = false) String ip,
      @Valid @RequestBody ForgotPasswordDto authenticationRequest) throws BusinessException {

    String otp = authenticationRequest.getOtp();
    if (!this.userUtility.validateOTP(otp,
        authenticationRequest.getCountryPhoneCode() + authenticationRequest.getPhoneNumber())) {
      BusinessUser user = this.businessUserService
          .getByUserPhone(authenticationRequest.getCountryPhoneCode() + authenticationRequest.getPhoneNumber());
      this.digitalRegistryService.recordChangePassword(ip, user.getId(), null, null);
      throw new BusinessException(ErrorCode.INVALID_OTP);
    }

    BusinessUser businessUser = this.businessUserService
        .beautifyUser(this.businessUserService.forgotUserPassword(authenticationRequest, true));
    this.digitalRegistryService.recordChangePassword(ip, businessUser.getId(), otp, null);
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        new AuthenticationResponse<BusinessUser>(this.jwtUtil.generateTokenUsingUserDetails(businessUser),
            businessUser),
        null,
        null);
  }

  @PostMapping(value = "/saveUserTemporarily", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE })
  protected ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessUser>>> saveUserTemporarily(
      @Valid @RequestBody SaveUserTemporarilyDto saveUserTemporarilyDto,
      @RequestHeader(name = StringUtility.IP, required = false) String ip)
      throws BusinessException {
    String otp = saveUserTemporarilyDto.getOtp();
    if (!this.userUtility.validateOTP(otp,
        saveUserTemporarilyDto.getCountryPhoneCode() + saveUserTemporarilyDto.getPhoneNumber())) {
      throw new BusinessException(ErrorCode.INVALID_OTP);
    }
    if (saveUserTemporarilyDto.getToken() != null)
      recaptchaUtility.createAssessment(saveUserTemporarilyDto.getToken(), saveUserTemporarilyDto.getPlatform(),
          saveUserTemporarilyDto.getCountryPhoneCode() + saveUserTemporarilyDto.getPhoneNumber(),
          StringUtility.stringsMatch(saveUserTemporarilyDto.getPlatform(), "web") ? "REGISTRATION" : "SIGNUP", true);
    BusinessUser responseBusinessUser = this.businessUserService.saveUser(saveUserTemporarilyDto.toBusinessUser(),
        true);
    if (saveUserTemporarilyDto.getPartnerUserToken() != null) {
      PartnerUser partnerUser = this.partnerService
          .validatePartnerUserToken(saveUserTemporarilyDto.getPartnerUserToken());
      if (partnerUser.getUser() != null)
        throw new BusinessException(ErrorCode.OPERATION_NOT_PERFORMED, HttpStatus.CONFLICT);

      this.partnerService.joinPartnerUserToNativeUser(partnerUser, responseBusinessUser.getId());
    }
    this.digitalRegistryService.recordSignup(ip, responseBusinessUser.getId(), otp);
    responseBusinessUser.setPassword(null);
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        new AuthenticationResponse<BusinessUser>(
            this.jwtUtil.generateTokenUsingUserDetails(responseBusinessUser), responseBusinessUser),
        null, null);
  }

  @PostMapping(value = "/checkSocialIdExistence")
  protected ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessUser>>> checkSocialIdExistence(
      @Valid @RequestBody CheckSocialIdExistenceDto socialDto,
      @RequestHeader(name = StringUtility.IP, required = false) String ip)
      throws BusinessException, IOException, FirebaseAuthException {
    try {
      var user = this.businessUserService.checkSocialIdExistence(this.jwtUtil, socialDto);
      Date lastLoginDate = user.getUser() != null ? user.getUser().getLastLogin() : null;
      if (lastLoginDate == null)
        lastLoginDate = new Date();
      // If the last login was more than 3 days ago, create a recaptcha assessment
      Instant daysAgo = Instant.now().minus(3, ChronoUnit.DAYS);
      if (socialDto.getToken() != null && lastLoginDate.toInstant().isBefore(daysAgo)
          && NumberUtility.areIntegerValuesMatching(user.getFlowId(), BusinessFlow.SET_PASSWORD.getFlowId())) {
        var recaptchaRes = recaptchaUtility.createAssessment(socialDto.getToken(), socialDto.getPlatform(),
            user.getUser().getUserPhone(), "LOGIN", true);
        if (recaptchaRes.getAssessmentId() != null)
          recaptchaUtility.annotateLoginAssessment(user.getUser().getUserPhone(), recaptchaRes.getAssessmentId(),
              NumberUtility.areIntegerValuesMatching(user.getFlowId(), BusinessFlow.SET_PASSWORD.getFlowId()));
      }
      if (NumberUtility.areIntegerValuesMatching(user.getFlowId(), BusinessFlow.SET_PASSWORD.getFlowId())) {
        this.digitalRegistryService.recordLogin(ip, user.getUser().getId());
        if (StringUtility.isStringPopulated(socialDto.getDeviceId()))
          this.userUtility.logDevice(user.getUser().getId(), socialDto.getDeviceId(), socialDto.getDeviceName());
      }
      return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class, user, null, null);
    } catch (BusinessException e) {
      if (socialDto.getToken() != null) {
        var recaptchaRes = recaptchaUtility.createAssessment(socialDto.getToken(), socialDto.getPlatform(),
            socialDto.getProviderId(), "LOGIN", false);
        if (recaptchaRes.getAssessmentId() != null)
          recaptchaUtility.annotateLoginAssessment(socialDto.getProviderId(), recaptchaRes.getAssessmentId(), false);
      }
      throw e;
    }
  }

  @PostMapping(value = "/directUser", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessUser>>> directUser(
      @Valid @RequestBody DirectUserDto businessUser,
      @RequestHeader(name = StringUtility.IP, required = false) String ip)
      throws BusinessException, IOException, IntegrationException, FirebaseAuthException {
    var response = this.businessUserService.directUser(this.jwtUtil, businessUser);
    if (response.getUser() != null) {
      this.digitalRegistryService.recordVerifyPhone(ip, response.getUser().getId(),
          businessUser.getOtp(), null);
    }
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        response, null, null);
  }

  @GetMapping(value = "/logout")
  protected ResponseEntity<BaseGenericResponse<AuthenticationResponse<BusinessUser>>> logout(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token) throws BusinessException {
    return authenticationResponseHandler.generateBaseGenericResponse(AuthenticationResponse.class,
        this.businessUserService.logUserOut(this.getCurrentRequestHolder(token)), null, null);
  }

}
