package innovitics.azimut.controllers.digitalregistry;

import java.util.Date;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessservices.BusinessUserService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.digitalregistry.dto.IntrospectionRequestDto;
import innovitics.azimut.controllers.digitalregistry.dto.IntrospectionResponseDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.security.JwtUtil;
import innovitics.azimut.services.digitalregistry.ApiRegistryService;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import io.jsonwebtoken.Claims;

@RestController
@RequestMapping("/api/v1/token")
public class ApiRegistryController extends BaseController {
  private static final String API_KEY_HEADER_NAME = "ApiKey";
  @Autowired
  private JwtUtil jwtUtil;

  @Autowired
  private ApiRegistryService apiRegistryService;

  @Autowired
  private BusinessUserService businessUserService;

  @RequestMapping(value = "/introspect", method = { RequestMethod.GET, RequestMethod.POST })
  public IntrospectionResponseDto introspect(
      @RequestBody IntrospectionRequestDto request,
      @RequestHeader(name = API_KEY_HEADER_NAME, required = true) Optional<String> authToken,
      HttpServletRequest httpRequest) throws BusinessException {
    this.apiRegistryService.validateApiKey(authToken);
    String accessToken = request.getAccessToken();
    Claims claims = jwtUtil.validateTokenFra(accessToken);
    var res = new IntrospectionResponseDto();
    res.setValid(claims != null);
    if (claims != null) {
      Date expiration = claims.getExpiration();
      Date issuedAt = claims.getIssuedAt();
      res.setIssuedAt(issuedAt);
      res.setExpiresAt(expiration);

      var userPhone = claims.getSubject();
      try {
        var user = businessUserService.getByUserPhone(userPhone);
        if (user.getUserId() == null)
          user = businessUserService.getByOldUserPhone(userPhone);
        res.setNationalId(user.getUserId());
        if (user.getUserStep() > 1 && BooleanUtility.isFalse(user.getIsOld()) && user.getEnrollApplicantId() != null) {
          String status = "verified";
          res.setIdentityLevel(status);
        }
      } catch (Exception e) {
        var user = businessUserService.getByOldUserPhone(userPhone);
        res.setNationalId(user.getUserId());
        if (user.getUserStep() > 1 && BooleanUtility.isFalse(user.getIsOld())
            && user.getEnrollApplicantId() != null) {
          String status = "verified";
          res.setIdentityLevel(status);
        }
      }

    }

    String ip = httpRequest.getRemoteAddr();
    apiRegistryService.addApiRegistry(ip);

    return res;
  }

}
