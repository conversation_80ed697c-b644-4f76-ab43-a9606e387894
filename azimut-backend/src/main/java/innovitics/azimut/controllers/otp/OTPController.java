package innovitics.azimut.controllers.otp;

import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessmodels.user.BusinessUserOTP;
import innovitics.azimut.businessservices.BusinessOTPService;
import innovitics.azimut.businessservices.BusinessUserService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.otp.Dtos.SendOtpDto;
import innovitics.azimut.controllers.otp.Dtos.VerifyDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.models.OTPFunctionality;
import innovitics.azimut.models.digitalregistry.DigitalRegistryAction;
import innovitics.azimut.models.user.User;
import innovitics.azimut.models.user.UserContract;
import innovitics.azimut.security.RecaptchaUtility;
import innovitics.azimut.service.UserContractService;
import innovitics.azimut.services.digitalregistry.DigitalRegistryService;
import innovitics.azimut.utilities.businessutilities.UserUtility;
import innovitics.azimut.utilities.crosslayerenums.ContractType;
import innovitics.azimut.utilities.crosslayerenums.KycStatus;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.Refill;

@RestController
@RequestMapping("api/otp")
public class OTPController extends BaseController {

  @Autowired
  BusinessOTPService businessOTPService;

  @Autowired
  DigitalRegistryService digitalRegistryService;

  @Autowired
  GenericResponseHandler<BusinessUserOTP> businessUserOTPResponseHandler;

  @Autowired
  GenericResponseHandler<Boolean> booleanHandler;

  @Autowired
  BusinessUserService businessUserService;

  @Autowired
  RecaptchaUtility recaptchaUtility;

  @Autowired
  UserUtility userUtility;

  private @Autowired UserContractService userContractService;

  private final Cache<String, Bucket> cache = Caffeine.newBuilder()
      .maximumSize(100_000) // Limit total entries
      .expireAfterAccess(Duration.ofMinutes(10)) // Remove unused IPs
      .build();

  private Bucket getBucket(String key) {
    return cache.get(key, k -> {
      Bandwidth limit = Bandwidth.classic(5, Refill.intervally(5, Duration.ofMinutes(1))); // 5 requests per minute
      return Bucket.builder().addLimit(limit).build();
    });
  }

  @PostMapping(value = "/sendOtp")
  public ResponseEntity<BaseGenericResponse<Boolean>> sendOtp(@Valid @RequestBody SendOtpDto otpDto,
      @RequestHeader(name = StringUtility.AUTHORIZATION_HEADER, required = false) String token)
      throws Exception, BusinessException {

    BusinessUser user = StringUtility.isStringPopulated(token) ? this.getCurrentRequestHolder(token) : null;
    String phone = user != null ? user.getUserPhone() : otpDto.getPhone();

    var oldPhone = this.userUtility.findLatestOTPByPhone(phone);
    if (oldPhone != null &&
        StringUtility.stringsMatch(oldPhone.getFunctionality(), OTPFunctionality.VERIFY_PHONE.name())
        && oldPhone.getCreatedAt().toInstant().isAfter(Instant.now().minus(1, ChronoUnit.MINUTES))) {
      throw new IntegrationException(ErrorCode.USER_BLOCKED);
    }

    if (user == null) {
      boolean isEgypt = otpDto.getCountryCode() == null ? phone.startsWith("+20")
          : (phone.startsWith("+20") && StringUtility.stringsMatch(otpDto.getCountryCode(), "EG"));
      if (otpDto.getToken() == null) {
        throw new IntegrationException(ErrorCode.UPDATE_APP_REQUIRED);
      }
      var res = recaptchaUtility.createAssessment(otpDto.getToken(), otpDto.getPlatform(), phone,
          "TRIGGER_MFA", true, phone.startsWith("+20"));
      if (!res.getResponse()) {
        throw new IntegrationException(ErrorCode.OPERATION_NOT_PERFORMED);
      }
      this.businessOTPService.sendOnboardingOTP(phone, res.getAssessmentId(), isEgypt);
    } else {
      var otp = this.businessOTPService.sendOtp(phone, null);
      if (user.getEmailAddress() != null) {
        this.businessUserService.sendOtpEmail(user, otp);
      }
    }

    return booleanHandler.generateBaseGenericResponse(Boolean.class, true, null, null);
  }

  @PostMapping(value = "/verifyOtp")
  public ResponseEntity<BaseGenericResponse<Boolean>> verifyOtp(@Valid @RequestBody VerifyDto otpDto,
      @RequestHeader(name = StringUtility.IP, required = false) String ip,
      @RequestHeader(name = StringUtility.AUTHORIZATION_HEADER, required = false) String token)
      throws Exception, BusinessException {

    BusinessUser user = StringUtility.isStringPopulated(token) ? this.getCurrentRequestHolder(token) : null;
    String phone = user != null ? user.getUserPhone() : otpDto.getPhone();

    // Get or create bucket
    Bucket bucket = getBucket(phone);
    if (!bucket.tryConsume(1))
      throw new BusinessException(ErrorCode.RATE_LIMIT_EXCEEDED);

    if (user != null) {
      BusinessUserOTP businessUserOTP = this.businessOTPService.getPhoneOTP(phone);
      this.businessOTPService.verifyOtp(businessUserOTP, otpDto.getOtp(), user);
    } else {
      this.businessOTPService.verifyOnboardingOtp(phone, otpDto.getOtp());
    }

    return booleanHandler.generateBaseGenericResponse(Boolean.class, true, null, null);
  }

  @PostMapping(value = "/verifyContractOtp")
  public ResponseEntity<BaseGenericResponse<BusinessUserOTP>> verifyContractOtp(
      @Valid @RequestBody VerifyDto businessUserOTP,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.IP, required = false) String ip)
      throws Exception, BusinessException {
    var user = this.getCurrentRequestHolder(token);
    // Get or create bucket
    Bucket bucket = getBucket(user.getUserPhone());
    if (!bucket.tryConsume(1))
      throw new BusinessException(ErrorCode.RATE_LIMIT_EXCEEDED);

    var userOtp = this.businessOTPService.verifyContractOtp(user, businessUserOTP);
    this.businessUserService.autoAccept(user);
    var signDr = digitalRegistryService.getLastDigitalRegistry(user.getId(), DigitalRegistryAction.SIGN_CONTRACT);
    if (signDr != null
        && NumberUtility.areIntegerValuesMatching(user.getKycStatus(), KycStatus.PENDING.getStatusId())) {
      // already signed before
      this.businessUserService.setKycUpdated(user);
    }
    this.digitalRegistryService.recordSignContract(ip, user.getId(), businessUserOTP.getOtp(), token);

    return businessUserOTPResponseHandler.generateBaseGenericResponse(BusinessUserOTP.class, userOtp, null, null);
  }

  @PostMapping(value = "/verifyCustodianContractOtp")
  public ResponseEntity<BaseGenericResponse<BusinessUserOTP>> verifyCustodianContractOtp(
      @Valid @RequestBody VerifyDto businessUserOTP,
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token,
      @RequestHeader(name = StringUtility.IP, required = false) String ip)
      throws Exception, BusinessException {
    var user = this.getCurrentRequestHolder(token);
    // Get or create bucket
    Bucket bucket = getBucket(user.getUserPhone());
    if (!bucket.tryConsume(1))
      throw new BusinessException(ErrorCode.RATE_LIMIT_EXCEEDED);

    if (user.getPdfSignedAt() == null && !BooleanUtility.isTrue(user.getIsOld())) {
      throw new BusinessException(ErrorCode.CONTRACT_NOT_SIGNED);
    }
    var userContracts = userContractService.getByUserIdAndContractType(user.getId(), ContractType.REAL_ESTATE);
    if (userContracts.size() > 0) {
      throw new BusinessException(ErrorCode.CONTRACT_ALREADY_SIGNED);
    }

    var userOtp = this.businessOTPService.verifyContractGenericOtp(user, businessUserOTP);
    UserContract contract = new UserContract();
    contract.setContractType(ContractType.REAL_ESTATE);
    User dbUser = new User();
    dbUser.setId(user.getId());
    contract.setUser(dbUser);
    // todo save user signed contract pdf and set it in user contract
    userContractService.createUserContract(contract);
    this.digitalRegistryService.recordSignContract(ip, user.getId(), businessUserOTP.getOtp(), token);

    return businessUserOTPResponseHandler.generateBaseGenericResponse(BusinessUserOTP.class, userOtp, null, null);
  }
}