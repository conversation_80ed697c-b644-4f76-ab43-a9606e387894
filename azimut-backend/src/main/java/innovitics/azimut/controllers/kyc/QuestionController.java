package innovitics.azimut.controllers.kyc;

import java.io.IOException;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessmodels.kyc.BusinessQuestion;
import innovitics.azimut.businessservices.BusinessQuestionService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.controllers.kyc.DTOs.GetQuestionByIdDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.exceptions.IntegrationException;

@RestController
@RequestMapping(value = "/api/kyc/question", produces = {
    MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })

public class QuestionController extends BaseController {

  @Autowired
  BusinessQuestionService businessQuestionService;

  @Autowired
  GenericResponseHandler<BusinessQuestion> businessQuestionResponseHandler;

  @PostMapping(value = "/getQuestionById", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessQuestion>> getQuestionById(
      @Valid @RequestBody GetQuestionByIdDto businessQuestion)
      throws BusinessException, IOException, IntegrationException {
    return businessQuestionResponseHandler.generateBaseGenericResponse(BusinessQuestion.class,
        this.businessQuestionService.getById(businessQuestion.getId()), null, null);
  }

  @PostMapping(value = "/getQuestionByPageId", consumes = { MediaType.APPLICATION_JSON_VALUE,
      MediaType.APPLICATION_XML_VALUE, MediaType.APPLICATION_FORM_URLENCODED_VALUE,
      MediaType.MULTIPART_FORM_DATA_VALUE })
  protected ResponseEntity<BaseGenericResponse<BusinessQuestion>> getQuestionByPageId(
      @Valid @RequestBody GetQuestionByIdDto businessKYCPage)
      throws BusinessException, IOException, IntegrationException {
    return businessQuestionResponseHandler.generateBaseGenericResponse(BusinessQuestion.class, null,
        this.businessQuestionService.getByPageId(businessKYCPage.getId()), null);

  }

}
