package innovitics.azimut.controllers.optosigner;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import innovitics.azimut.businessservices.OptoSignerService;
import innovitics.azimut.controllers.BaseController;
import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.controllers.GenericResponseHandler;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.utilities.datautilities.StringUtility;

@RestController
@Validated
@RequestMapping("/api/optoSigner")
public class OptoSignerController extends BaseController {

  private @Autowired OptoSignerService optoSignerService;
  private @Autowired GenericResponseHandler<String> stringHandler;

  @GetMapping(value = "/token", consumes = { MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE,
      MediaType.APPLICATION_FORM_URLENCODED_VALUE, MediaType.MULTIPART_FORM_DATA_VALUE }, produces = {
          MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_XML_VALUE })
  protected ResponseEntity<BaseGenericResponse<String>> getToken(
      @RequestHeader(StringUtility.AUTHORIZATION_HEADER) String token)
      throws BusinessException {

    return stringHandler.generateBaseGenericResponse(String.class, optoSignerService.getAccessToken(), null, null);
  }

}
