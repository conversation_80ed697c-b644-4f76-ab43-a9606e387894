package innovitics.azimut.businessmodels.enroll;

import java.util.Map;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "clientDetails", singular = "clientDetails")
public class BusinessEnroll {

  private String documentType;
  private String firstImage;
  private String secondImage;

  private String straightFace;
  private String smilingFace;

  private String leftSide;
  private String rightSide;

  private String frontImage;
  private String backImage;
  private String passportImage;
  private String token;
  private Boolean imagesSimilar;
  private Float confidence;
  private String firstName;
  private String fullName;
  private String street;
  private String area;
  private String frontNid;
  private String serialNumber;
  private String backNid;
  private String releaseDate;
  private String gender;
  private Boolean amlBlackList;
  private String maritalStatus;
  private String profession;
  private String religion;
  private String husbandName;
  private String expiryDate;
  private String name;
  private String surname;
  private String passportNumber;
  private String expirationDate;
  private String dateOfBirth;
  private String sex;
  private String nationality;
  private Integer validity;
  private Integer userStep;
  private String city;
  private String lastName;
  private String country;
  private String valifyTransactionId;
  private String userId;
  protected Boolean livenessChecked;
  protected Integer nextUserStep;
  protected String language;
  protected Long firstPageId;
  protected Long azIdType;
  protected Integer verificationPercentage;
  private Map<String, Object> idData;

}
