package innovitics.azimut.businessmodels.kyc;

import java.util.List;

import innovitics.azimut.businessmodels.BaseBusinessEntity;
import innovitics.azimut.models.user.UserType;
import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "pages", singular = "page")
public class BusinessKYCPage extends BaseBusinessEntity {

  private UserType userType;
  private String title;
  private Integer pageOrder;
  private String pageDetails;
  private String pageDisclaimer;
  private int noOfQuestions;
  private List<BusinessQuestion> questions;
  private Long previousId;
  private Long nextId;
  private Boolean isAnswered;
  private Boolean isCorrect;
  protected String titleAr;
  protected String pageDetailsAr;
  protected String pageDisclaimerAr;
  protected Integer verificationPercentage;
  protected Integer nextUserStep;
  protected String documentURL;
  protected String documentName;
  protected Double documentSize;
  protected String documentSubDirectory;

}
