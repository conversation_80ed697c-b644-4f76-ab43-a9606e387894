package innovitics.azimut.businessmodels.kyc;

import java.util.List;

import innovitics.azimut.businessmodels.BaseBusinessEntity;
import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "questions", singular = "question")
public class BusinessQuestion extends BaseBusinessEntity {

  private String questionText;
  private String answerType;
  private int questionOrder;
  private Boolean isAnswerMandatory;
  private Long parentKYCPageId;
  private List<BusinessQuestion> subQuestions;
  List<BusinessAnswer> answers;
  private List<BusinessSubmittedAnswer> userAnswers;
  private Integer objectType;
  private Integer width;
  private String questionPlaceHolder;
  private String questionPlaceHolderAr;
  private String questionTextAr;
  private String pdfieldName;
  private BusinessReview review;

}
