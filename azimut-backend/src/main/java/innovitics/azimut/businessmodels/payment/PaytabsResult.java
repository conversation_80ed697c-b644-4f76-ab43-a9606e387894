package innovitics.azimut.businessmodels.payment;

import com.fasterxml.jackson.annotation.JsonProperty;

import innovitics.azimut.rest.entities.paytabs.PaytabsResponse;
import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@CustomJsonRootName(plural = "response", singular = "response")
@Data
public class PaytabsResult extends PaytabsResponse {

  @JsonProperty("cart_id")
  private String cartId;

  @JsonProperty("cart_amount")
  private String cartAmount;

  @JsonProperty("cart_description")
  private String cartDescription;

  @JsonProperty("cart_currency")
  private String cartCurrency;

  @JsonProperty("payment_info")
  private PaymentInfo paymentInfo;

  @JsonProperty("payment_result")
  private PaymentResult paymentResult;

  @JsonProperty("customer_details")
  private CustomerDetails customerDetails;

  private String token;

  // @JsonProperty("tran_currency")
  // private String transactionCurrency;

  // @JsonProperty("tran_total")
  // private String transactionTotal;

  @JsonProperty("tran_type")
  private String transactionType;

  // @JsonProperty("tran_class")
  // private String transactionClass;

  // @JsonProperty("ipn_trace")
  // private String ipnTrace;

}
