package innovitics.azimut.businessmodels.payment;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class PaymentResult {

  @JsonProperty("response_status")
  private String responseStatus;
  @JsonProperty("response_code")
  private String responseCode;
  @JsonProperty("response_message")
  private String responseMessage;
  @JsonProperty("cvv_result")
  private String cvvResult;
  @JsonProperty("avs_result")
  private String avsResult;
  @JsonProperty("transaction_time")
  private String responseTime;

  @JsonProperty("acquirer_rrn")
  private String acquirerRrn;
  @JsonProperty("acquirer_message")
  private String acquirerMessage;
}
