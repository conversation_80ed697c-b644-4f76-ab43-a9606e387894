package innovitics.azimut.businessmodels.funds;

import innovitics.azimut.utilities.crosslayerenums.OrderStatus;
import lombok.Data;

@Data
public class BusinessFundTransaction {
  private String orderDate;
  private Double orderValue;
  private Double execPrice;
  private Integer quantity;
  private String orderType;
  private Integer orderTypeId;
  private String orderStatus;
  private Integer orderStatusId;
  private Long transactionId;
  private Long fundId;
  private OrderStatus status;
  private Long azIdType;
  private String azId;
}
