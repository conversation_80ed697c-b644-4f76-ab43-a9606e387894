package innovitics.azimut.businessmodels.admin;

import innovitics.azimut.businessmodels.BaseBusinessEntity;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "adminUsers", singular = "adminUser")
public class BusinessAdminUserMapping extends BaseBusinessEntity {

  private BusinessAdminUser adminUser;

  private BusinessUser user;

  private BusinessAdminUser createdBy;
}
