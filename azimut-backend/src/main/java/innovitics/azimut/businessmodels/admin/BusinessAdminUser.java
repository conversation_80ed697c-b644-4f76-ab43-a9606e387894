package innovitics.azimut.businessmodels.admin;

import innovitics.azimut.businessmodels.BaseBusinessEntity;
import innovitics.azimut.businessmodels.user.BusinessUserInterface;
import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.ToString;

@Data
@CustomJsonRootName(plural = "admins", singular = "admin")
public class BusinessAdminUser extends BaseBusinessEntity implements BusinessUserInterface {
  @ToString.Exclude
  String password;

  String fullName;

  @Getter(AccessLevel.NONE)
  String username;

  protected String emailAddress;

  protected String systemTrx;

  private Long roleId;

  private Boolean IsFA;

  private String permissions;

  public BusinessAdminUser() {

  }

  public BusinessAdminUser(String email, String password) {
    this.setEmailAddress(email);
    this.setPassword(password);
  }

  public String getUsername() {
    return this.getEmailAddress();
  }

  public String getFirstName() {
    return this.fullName;
  }

}
