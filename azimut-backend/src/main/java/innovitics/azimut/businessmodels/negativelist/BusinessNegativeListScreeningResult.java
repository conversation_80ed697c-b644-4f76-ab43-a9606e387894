package innovitics.azimut.businessmodels.negativelist;

import java.util.List;

import innovitics.azimut.businessmodels.BaseBusinessEntity;
import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "screeningResults", singular = "screeningResult")
public class BusinessNegativeListScreeningResult extends BaseBusinessEntity {
    
    private String userName;
    private Boolean isMatch;
    private Integer matchCount;
    private String matchType; // EXACT, FUZZY, PARTIAL
    private Double confidenceScore;
    private List<BusinessNegativeListMatch> matches;
    private String screeningStatus; // CLEAR, POTENTIAL_MATCH, BLOCKED
    private String riskLevel; // LOW, MEDIUM, HIGH, CRITICAL
}
