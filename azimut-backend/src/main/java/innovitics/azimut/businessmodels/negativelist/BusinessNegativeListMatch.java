package innovitics.azimut.businessmodels.negativelist;

import innovitics.azimut.businessmodels.BaseBusinessEntity;
import innovitics.azimut.models.NegativeList;
import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "matches", singular = "match")
public class BusinessNegativeListMatch extends BaseBusinessEntity {

  private NegativeList negativeList;
  private String matchedName;
  private String listSource;
  private String matchType; // EXACT, FUZZY, PARTIAL
  private Double confidenceScore;
  private String matchedField; // FULL_NAME, FIRST_NAME, LAST_NAME, ALIAS, PASSPORT, NATIONAL_ID
  private String additionalInfo;
}
