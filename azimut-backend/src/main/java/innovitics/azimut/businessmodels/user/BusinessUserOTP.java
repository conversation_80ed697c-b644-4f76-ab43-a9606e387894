package innovitics.azimut.businessmodels.user;

import java.util.Date;

import innovitics.azimut.businessmodels.BaseBusinessEntity;
import innovitics.azimut.models.OTPMethod;
import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "otps", singular = "otp")
public class BusinessUserOTP extends BaseBusinessEntity {

  private String userPhone;

  private Integer numberOfTimes;
  private Date nextTrial;
  private String functionality;
  private OTPMethod otpMethod;

  private Integer contractType;
  Long userId;
  protected String otp;
  protected String sessionInfo;
  protected Integer kycStatus;
  private String assessmentId;

  private Date deletedAt;

  public BusinessUserOTP(String userPhone) {
    this.userPhone = userPhone;
  }

  public BusinessUserOTP() {
    // TODO Auto-generated constructor stub
  }

}
