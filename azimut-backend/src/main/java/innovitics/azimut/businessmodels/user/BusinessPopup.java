package innovitics.azimut.businessmodels.user;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

@Data
public class BusinessPopup {

  protected Long id;
  private String popupText;
  private String popupHeader;
  private String image;
  private String action;
  private Long userId;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date createdAt;
  private Boolean isRead;
  private Long popupTemplateId;

}
