package innovitics.azimut.businessmodels.user;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;

@Data
@CustomJsonRootName(plural = "credentials", singular = "credentials")
public class AuthenticationResponse<T> {

  private Token token;
  private T user;
  private int flowId;

  public AuthenticationResponse(Token token, T user) {
    this.token = token;
    this.user = user;
  }

  public AuthenticationResponse() {
  }

  public void setBusinessFlow(BusinessFlow businessFlow) {
    this.flowId = businessFlow.getFlowId();
  }

}
