package innovitics.azimut.businessmodels.user;

import java.util.Date;
import java.util.List;
import java.util.UUID;

import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;

import innovitics.azimut.businessmodels.BaseBusinessEntity;
import innovitics.azimut.businessmodels.kyc.BusinessReview;
import innovitics.azimut.models.user.UserImage;
import innovitics.azimut.models.user.UserLocation;
import innovitics.azimut.utilities.CustomJsonRootName;
import innovitics.azimut.utilities.crosslayerenums.RiskLevel;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;
import lombok.ToString;

@Data
@CustomJsonRootName(plural = "users", singular = "user")
public class BusinessUser extends BaseBusinessEntity implements BusinessUserInterface {
  @ToString.Exclude
  protected String password;

  @ToString.Exclude
  protected String secondPassword;

  @ToString.Exclude
  protected String oldPassword;

  @ToString.Exclude
  protected String newPassword;
  protected String deviceId;
  protected String deviceName;
  protected String language;
  protected String userId;
  protected String userIdType;
  protected String userIdTypeAr;
  protected Long idType;
  protected String nickName;
  protected String countryPhoneCode;
  protected String phoneNumber;
  protected String profilePicture;
  protected String signedPdf;
  protected String picturePath;
  protected String pdfPath;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date pdfSignedAt;
  protected Boolean isChangeNoApproved;
  protected Boolean isVerified;
  private Boolean isEmailVerified;
  private Boolean hasSecurityQuestions;
  private Boolean hasInjected;
  protected Boolean migrated;
  protected String countryCode;
  protected MultipartFile file;
  protected String fullName;
  private Long lastSolvedPageId;
  private Long nextPageId;
  private Integer nextUserStep;
  private Integer contractMap;
  private List<UserImage> userImages;
  private String otherUserIdType;
  private Long otherIdType;
  private String otherUserId;
  private String otherNationality;
  private Long genderId;
  private AzimutAccount azimutAccount;
  private Integer failedLogins;
  private Boolean isInstitutional;
  private Long azimutIdTypeId;
  private UserLocation userLocation;
  private Integer blockageCount;
  private Integer changePhoneCount;
  private Integer reviewCount;
  private Integer pageNumber;

  private Integer azimutAmlMatches;
  private RiskLevel risk;
  protected String emailAddress;
  private String messagingToken;
  private BusinessClientBankAccountDetails[] clientBankAccounts;
  private String solvedPages;
  private List<AzimutAccount> azimutAccounts;
  private Boolean isOld;
  private String mailingAddress;
  private Boolean isReviewed;
  private Long firstReviewedPageId;
  private String dateOfRelease;
  private String clientCode;
  private Integer deletionReasonId;
  private Boolean isSynchronized;
  private Boolean loggedIn;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date lastLogin;
  private String enrollApplicantId;
  private String enrollRequestId;
  private Long gateIdTask;
  protected String firstName;
  protected String lastName;
  @Setter(AccessLevel.NONE)
  protected BusinessFlow businessFlow;
  protected int flowId;
  protected String documentURL;
  protected Integer verificationPercentage;
  protected String country;
  protected String city;
  protected String dateOfBirth;
  private String dateOfIdExpiry;
  protected String userPhone;
  protected Integer userStep;
  protected Long firstPageId;
  protected Long transactionId;
  protected String systemTrx;
  protected Integer kycStatus;
  protected Double balance;
  protected Double totalTopup;
  private Double totalWithdraw;
  private Double totalPosition;
  private Double totalBuyValue;
  private Double totalSellValue;
  protected Boolean livenessChecked;
  protected List<BusinessReview> reviews;
  protected String provider;
  protected String providerId;
  private Boolean isMobile;
  private Boolean realEstateSigned;
  protected String socialToken;
  protected String sessionInfo;
  protected String otp;
  protected List<BusinessPopup> popups;
  protected List<BusinessNotification> notificationsList;
  protected PaginatedEntity<BusinessNotification> notifications;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  protected Date deletedAt;
  private String idData;
  private String cso;
  private String ntra;
  private String aml;
  private UUID uuid;
  private String fraStoreId;
  private String fraCompanyStoreId;
  @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "Africa/Cairo")
  private Date contractSignedAt;

  protected String ReferralCode;

  private BusinessDeletionReason[] deletionReasons;

  public BusinessUser() {
    super();
  }

  public BusinessUser(Long id, Boolean isReviewed) {
    super();
    this.id = id;
    this.isReviewed = isReviewed;
  }

  public void setBusinessFlow(BusinessFlow businessFlow) {
    this.businessFlow = businessFlow;
    this.flowId = businessFlow.getFlowId();
  }

  public void concatinate() {
    this.setUserPhone(this.getCountryPhoneCode() + this.getPhoneNumber());
  }

  @Override
  public String getUsername() {
    return this.getUserPhone();
  }

  public Boolean isChangeNoApproved() {
    return this.isChangeNoApproved;
  }

  public void setChangeNoApproved(Boolean isChangeNoApproved) {
    this.isChangeNoApproved = isChangeNoApproved;
  }

  @JsonIgnore
  public Boolean isOnFits() {
    return BooleanUtility.isTrue(isSynchronized) || BooleanUtility.isTrue(isOld);
  }

}
