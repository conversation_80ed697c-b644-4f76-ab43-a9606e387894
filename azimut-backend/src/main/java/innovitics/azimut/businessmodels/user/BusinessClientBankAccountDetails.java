package innovitics.azimut.businessmodels.user;

import java.util.List;

import innovitics.azimut.businessmodels.kyc.BusinessReview;
import innovitics.azimut.utilities.crosslayerenums.ClientBankAccountStatus;
import lombok.Data;

@Data
public class BusinessClientBankAccountDetails {

  private Long id;
  private String image;
  private String bankName;
  private String accountNumber;
  private ClientBankAccountStatus clientBankAccountStatus;
  private String statusName;
  private Long status;
  private String swiftCode;
  private String currencyName;
  private String branchName;
  private Long branchId;
  private String iban;
  private Long accountId;
  private Long accountStatus;
  private Long currencyId;
  private String englishBankName;
  private String arabicBankName;
  private String bankType;
  private Long bankId;
  private String englishBranchName;
  private String arabicBranchName;
  private String englishCurrencyName;
  private String arabicCurrencyName;
  protected Long azIdType;
  protected String azId;
  protected List<BusinessReview> reviews;
  private Boolean isActive;
  private Boolean isLocal;

}
