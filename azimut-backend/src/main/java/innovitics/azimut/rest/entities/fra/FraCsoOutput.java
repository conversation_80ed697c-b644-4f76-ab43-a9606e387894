package innovitics.azimut.rest.entities.fra;

import lombok.Data;

@Data
public class FraCsoOutput {
  private Boolean isValid;
  private String errorKey;
  private String errorCode;
  private String errorMessage;

  private FraCsoOutputData data;
  private String error_code;
  private String error_message;
}

/*
 * Error Codes List
 * Code Message
 * 400 Bad Request
 * 401 Unauthorized – invalid API Key
 * 7000 General Error
 * 7001 PersonName doesn't match!
 * 7002 PersonOtherNames doesn't match!
 * 7003 FactoryNumber doesn't match!
 * 7004 CardExpirationDate doesn't match!
 */
