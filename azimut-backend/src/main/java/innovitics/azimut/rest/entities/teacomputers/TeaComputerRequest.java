package innovitics.azimut.rest.entities.teacomputers;

import innovitics.azimut.rest.entities.BaseRestRequest;
import lombok.Data;

@Data
public class TeaComputerRequest extends BaseRestRequest {
  protected String Signature;
  protected Long IdTypeId;
  protected Long IdType;
  protected String IdNumber;
  protected String UserName;
  protected String Password;
  protected Long FundId;
  protected String Lang;

  @Override
  public String toString() {
    return "TeaComputerRequest [Signature=" + Signature + ", IdTypeId=" + IdTypeId + ", IdNumber=" + IdNumber
        + ", UserName=" + UserName + "]";
  }

}
