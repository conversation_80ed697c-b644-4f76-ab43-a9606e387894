package innovitics.azimut.rest.entities.teacomputers;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class ClientBankAccountResponse extends TeaComputerResponse {
  private String bankId;
  private String bankName;
  private String bankType;
  private String branchId;
  private String branchName;
  private String currencyId;
  private String currencyName;
  private String accountNo;
  private Long accountID;
  private String swiftCode;
  @JsonProperty("iBAN")
  private String iBAN;
  private Long statusId;
  private String accountStatus;
  private String accountStatusName;
  private String signature;
}
