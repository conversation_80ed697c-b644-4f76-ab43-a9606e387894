package innovitics.azimut.rest.entities.gateid;

import java.time.Instant;

import com.fasterxml.jackson.annotation.JsonProperty;

import innovitics.azimut.utilities.CustomJsonRootName;
import lombok.Data;
import lombok.ToString;

@Data
@CustomJsonRootName(plural = "data", singular = "data")
public class GateIdTask extends GateIdOutput {
  @ToString.Exclude
  @JsonProperty("back_cropped_image")
  private String backCroppedImage;

  @ToString.Exclude
  @JsonProperty("back_image")
  private String backImage;

  private Instant createdAt;

  @ToString.Exclude
  @JsonProperty("front_cropped_image")
  private String frontCroppedImage;

  @ToString.Exclude
  @JsonProperty("front_image")
  private String frontImage;

  private Integer id;

  @JsonProperty("project_id")
  private int projectId;

  @ToString.Exclude
  @JsonProperty("selfie_image")
  private String selfieImage;

  private Instant updatedAt;

  private GateIdResult result;
}
