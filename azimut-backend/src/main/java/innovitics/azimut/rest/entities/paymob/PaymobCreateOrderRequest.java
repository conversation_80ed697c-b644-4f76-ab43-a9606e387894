package innovitics.azimut.rest.entities.paymob;

import com.fasterxml.jackson.annotation.JsonProperty;

import innovitics.azimut.rest.entities.BaseRestRequest;
import innovitics.azimut.rest.models.paymob.PaymobItem;
import lombok.Data;

@Data
public class PaymobCreateOrderRequest extends BaseRestRequest {

  @JsonProperty("amount_cents")
  private Integer amountCents;
  private String currency = "EGP";
  private PaymobItem[] items;
  @JsonProperty("auth_token")
  private String authToken;
  @JsonProperty("merchant_order_id")
  private String merchantOrderId;
}
