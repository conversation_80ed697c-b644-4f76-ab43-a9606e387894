package innovitics.azimut.rest.entities.paymob;

import com.fasterxml.jackson.annotation.JsonProperty;

import innovitics.azimut.rest.entities.BaseRestRequest;
import innovitics.azimut.rest.models.paymob.PaymobBillingData;
import lombok.Data;

@Data
public class PaymobCreatePaymentTokenRequest extends BaseRestRequest {

  @JsonProperty("amount_cents")
  private Integer amountCents;
  private String currency = "EGP";
  @JsonProperty("billing_data")
  private PaymobBillingData billingData;
  private PaymobBillingData customer;
  @JsonProperty("auth_token")
  private String authToken;
  @JsonProperty("integration_id")
  private Integer integrationId;
  @JsonProperty("order_id")
  private Integer orderId;

}
