package innovitics.azimut.rest.entities.gateid;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
public class GateIdFrontOutput extends GateIdOutput {
  @JsonProperty("color_score")
  Double colorScore;
  @JsonProperty("fraud_front")
  Boolean fraudFront;

  @ToString.Exclude
  String front;

  String photo_type;

  @JsonProperty("rotation_angle")
  Integer rotationAngle;
}
