package innovitics.azimut.rest.entities.teacomputers;

import com.fasterxml.jackson.annotation.JsonProperty;

import innovitics.azimut.rest.entities.BaseRestResponse;

public class TeaComputerResponse extends BaseRestResponse {
  @JsonProperty("Signature")
  protected String Signature;

  @JsonProperty("Message")
  protected String Message;

  @JsonProperty("ErrorCode")
  protected String ErrorCode;

  @JsonProperty("errorMessage")
  protected String errorMessage;

  public String getSignature() {
    return Signature;
  }

  public void setSignature(String signature) {
    Signature = signature;
  }

  public String getMessage() {
    return Message;
  }

  public void setMessage(String message) {
    Message = message;
  }

  public String getErrorCode() {
    return ErrorCode;
  }

  public void setErrorCode(String errorCode) {
    ErrorCode = errorCode;
  }

  public String getErrorMessage() {
    return errorMessage;
  }

  public void setErrorMessage(String errorMessage) {
    this.errorMessage = errorMessage;
  }

  @Override
  public String toString() {
    return "TeaComputerResponse [Signature=" + Signature + ", Message=" + Message + ", ErrorCode=" + ErrorCode
        + "]";
  }
}
