package innovitics.azimut.rest.entities.paytabs;

import com.fasterxml.jackson.annotation.JsonProperty;

import innovitics.azimut.rest.entities.BaseRestRequest;
import lombok.Data;

@Data
public class PaytabsRequest extends BaseRestRequest {

	@JsonProperty("profile_id")
	protected Integer profileId;
	@JsonProperty("tran_type")
	protected String transType;
	@JsonProperty("tran_class")
	protected String transClass;
	@JsonProperty("paypage_lang")
	protected String payPageLang;
	@JsonProperty("callback")
	private String callbackUrl;
	@JsonProperty("return")
	private String returnUrl;
}
