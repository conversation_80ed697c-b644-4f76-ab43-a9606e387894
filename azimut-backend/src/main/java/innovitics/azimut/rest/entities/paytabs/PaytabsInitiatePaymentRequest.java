package innovitics.azimut.rest.entities.paytabs;

import com.fasterxml.jackson.annotation.JsonProperty;

import innovitics.azimut.rest.models.paytabs.CustomerDetails;
import innovitics.azimut.rest.models.paytabs.ShippingDetails;
import lombok.Data;

@Data
public class PaytabsInitiatePaymentRequest extends PaytabsRequest {

	@JsonProperty("cart_id")
	private String cartId;
	@JsonProperty("cart_amount")
	private Double cartAmount;
	@JsonProperty("cart_currency")
	private String cartCurrency;
	@JsonProperty("cart_description")
	private String cartDescription;
	@JsonProperty("customer_details")
	private CustomerDetails customerDetails;
	@JsonProperty("shipping_details")
	private ShippingDetails shippingDetails;
}
