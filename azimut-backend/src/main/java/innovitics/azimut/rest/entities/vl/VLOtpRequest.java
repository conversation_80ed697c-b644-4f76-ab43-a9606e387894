package innovitics.azimut.rest.entities.vl;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class VLOtpRequest {
  @JsonProperty("UserName")
  private String username;
  @JsonProperty("Password")
  private String password;
  @JsonProperty("Service")
  private String service;
  @JsonProperty("Dial")
  private String phone;

  private String otp;

  @Override
  public String toString() {
    return "VLOtpRequest [service=" + service + ", phone=" + phone + "]";
  }
}
