package innovitics.azimut.rest.apis.paytabs;

import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Date;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.paytabs.PaytabsRequest;
import innovitics.azimut.rest.entities.paytabs.PaytabsResponse;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;
import lombok.Data;

@Data
public abstract class RestPaytabsApi<I extends PaytabsRequest, O extends PaytabsResponse>
    extends RestBaseApi<I, O> {

  private static final String TRANSACTION_TYPE = "sale";
  private static final String TRANSACTION_CLASS = "ecom";

  private String locale;

  protected String generateBaseURL(String params) {
    return this.configProperties.getPaytabsUrl();
  }

  @Override
  public IntegrationException handleException(Exception exception) {
    return this.validateExceptionType(exception);
  }

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = super.generateHeaders();
    headers.add(StringUtility.PAYTABS_AUTHORIZATION_HEADER, this.configProperties.getPaytabsServerKey());
    headers.add("lang", locale);
    MyLogger.info("Generated Headers:::" + headers.toString());
    return headers;
  }

  protected IntegrationException validateExceptionType(Exception exception) {
    MyLogger.info("Stack trace:::");

    MyLogger.logStackTrace(exception);

    if (exception instanceof IntegrationException) {
      return (IntegrationException) exception;
    }

    if (exception instanceof HttpClientErrorException) {

      IntegrationException integrationException = this.handleError((HttpClientErrorException) exception);
      return integrationException;
    }

    return new IntegrationException(ErrorCode.FAILED_TO_INTEGRATE);

  }

  @Override
  public IntegrationException handleError(HttpClientErrorException httpClientErrorException) {
    MyLogger.info("httpClientErrorException:::" + httpClientErrorException.toString());
    int errorCode = ErrorCode.FAILED_TO_INTEGRATE.getCode();
    String errorMessage = "";
    PaytabsResponse paytabsResponse = new PaytabsResponse();
    ObjectMapper mapper = new ObjectMapper();
    try {
      MyLogger.info("Parsing the exception to the teaComputerResponse:::");
      MyLogger.info("httpClientErrorException.getResponseBodyAsString():::"
          + httpClientErrorException.getResponseBodyAsString());
      paytabsResponse = mapper.readValue(httpClientErrorException.getResponseBodyAsString(),
          PaytabsResponse.class);
      errorMessage = paytabsResponse.getMessage();
      errorCode = paytabsResponse.getCode();

      MyLogger.info("paytabsResponse:::" + paytabsResponse.toString());
    } catch (JsonProcessingException e) {
      MyLogger.info("Failed to Parse:::");
      MyLogger.logStackTrace(e);
      return new IntegrationException(ErrorCode.FAILED_TO_INTEGRATE);
    }

    IntegrationException integrationException = new IntegrationException(errorCode, new Date(), errorMessage,
        errorMessage, errorMessage, httpClientErrorException.getStackTrace());
    return integrationException;
  }

  @Override
  public void validateResponse(ResponseEntity<O> responseEntity) throws IntegrationException {
    if (!this.validateResponseStatus(responseEntity)) {
      throw new IntegrationException(ErrorCode.FAILED_TO_INTEGRATE);
    }
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(I input) {
    this.populateCredentials(input);
    return this.stringify(input);
  };

  private void populateCredentials(PaytabsRequest request) {
    request.setProfileId(Integer.valueOf(this.configProperties.getPaytabsProfileId()));
    request.setTransType(TRANSACTION_TYPE);
    request.setTransClass(TRANSACTION_CLASS);
  }

  @Override
  protected RestTemplate restTemplate() {
    try {
      // Trust manager that does not validate certificate chains
      TrustManager[] trustAllCerts = new TrustManager[] {
          new X509TrustManager() {
            public void checkClientTrusted(X509Certificate[] certs, String authType) {
            }

            public void checkServerTrusted(X509Certificate[] certs, String authType) {
            }

            public X509Certificate[] getAcceptedIssuers() {
              return new X509Certificate[0];
            }
          }
      };

      // Install the all-trusting trust manager
      SSLContext sslContext = SSLContext.getInstance("TLS");
      sslContext.init(null, trustAllCerts, new SecureRandom());

      // Create an SSL socket factory with our all-trusting manager
      SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(
          sslContext,
          NoopHostnameVerifier.INSTANCE);

      CloseableHttpClient httpClient = HttpClients.custom()
          .setSSLSocketFactory(csf)
          .build();

      HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);

      return new RestTemplate(requestFactory);
    } catch (Exception e) {
      return new RestTemplate(); // Fallback to default RestTemplate if SSL setup fails
    }
  }
}
