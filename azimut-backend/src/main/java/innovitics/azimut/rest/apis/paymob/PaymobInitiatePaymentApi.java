package innovitics.azimut.rest.apis.paymob;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.paymob.PaymobInitiatePaymentRequest;
import innovitics.azimut.rest.entities.paymob.PaymobInitiatePaymentResponse;
import innovitics.azimut.security.AES;

@Service
public class PaymobInitiatePaymentApi extends RestBaseApi<PaymobInitiatePaymentRequest, PaymobInitiatePaymentResponse> {

  @Autowired
  AES aes;
  private static final String PATH = "/v1/intention";

  @Override
  protected String generateURL(String params) {
    return configProperties.getPaymobUrl() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  public HttpHeaders generateHeaders() {
    var headers = super.generateHeaders();
    headers.set("Authorization", "Token " + configProperties.getPaymobSecretKey());
    return headers;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(PaymobInitiatePaymentRequest input) {
    Integer[] paymentMethods = { Integer.valueOf(configProperties.getPaymobWalletIntegrationId()) };
    input.setPaymentMethods(paymentMethods);
    return super.generateRequestFromInput(input);
  }

}
