package innovitics.azimut.rest.apis.enroll;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.entities.enroll.EnrollGetApplicationIdOutput;

@Service
public class EnrollGetApplicationsIdByRequestId
    extends RestEnrollApiBase<Void, EnrollGetApplicationIdOutput> {

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getEnrollUrl() + "/api/v1/Applicant/GetApplicantIdByRequestId?requestId=" + params;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.GET;
  }

  @Override
  protected Boolean showOutput() {
    return false;
  }

}
