package innovitics.azimut.rest.apis.enroll;

import java.security.SecureRandom;
import java.security.cert.X509Certificate;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

import innovitics.azimut.rest.RestBaseApi;

public abstract class EnrollBaseRestApi<I, O> extends RestBaseApi<I, O> {

  @Override
  protected RestTemplate restTemplate() {
    try {
      // Trust manager that does not validate certificate chains
      TrustManager[] trustAllCerts = new TrustManager[] {
          new X509TrustManager() {
            public void checkClientTrusted(X509Certificate[] certs, String authType) {
            }

            public void checkServerTrusted(X509Certificate[] certs, String authType) {
            }

            public X509Certificate[] getAcceptedIssuers() {
              return new X509Certificate[0];
            }
          }
      };

      // Install the all-trusting trust manager
      SSLContext sslContext = SSLContext.getInstance("TLS");
      sslContext.init(null, trustAllCerts, new SecureRandom());

      // Create an SSL socket factory with our all-trusting manager
      SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(
          sslContext,
          NoopHostnameVerifier.INSTANCE);

      CloseableHttpClient httpClient = HttpClients.custom()
          .setSSLSocketFactory(csf)
          .build();

      HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);

      return new RestTemplate(requestFactory);
    } catch (Exception e) {
      return new RestTemplate(); // Fallback to default RestTemplate if SSL setup fails
    }
  }
}
