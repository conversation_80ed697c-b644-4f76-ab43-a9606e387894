package innovitics.azimut.rest.apis.gateid;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.gateid.GateIdAuthOutput;
import innovitics.azimut.rest.entities.gateid.GateIdOutput;
import innovitics.azimut.security.JwtUtil;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;

@Component
public abstract class RestGateIdApiBase<I, O extends GateIdOutput> extends RestBaseApi<I, O> {
  @Autowired
  private GateIdAuth gateIdAuth;

  @Autowired
  private JwtUtil jwtUtil;

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = super.generateHeaders();
    if (gateIdAuth.getToken() == null || jwtUtil.isExternalTokenExpired(gateIdAuth.getToken())) {
      try {
        GateIdAuthOutput res = gateIdAuth.getData(null);
        gateIdAuth.setToken(res.getAccessToken());
      } catch (IntegrationException e) {
        this.exceptionHandler.logException(e);
      }
    }
    headers.setBearerAuth(gateIdAuth.getToken());
    return headers;
  }

  @Override
  protected void validateResponse(ResponseEntity<O> responseEntity) throws IntegrationException {
    super.validateResponse(responseEntity);
    if (responseEntity.getBody().getError() != null) {
      int errorCode = ErrorCode.FAILED_TO_INTEGRATE.getCode();
      IntegrationException integrationException = new IntegrationException(errorCode,
          new Date(), responseEntity.getBody().getError(), null,
          responseEntity.getBody().getError(), null);
      throw integrationException;
    }
  }

}
