package innovitics.azimut.rest.apis.enroll;

import java.time.Instant;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.entities.enroll.EnrollAuthOutput;

@Component
public abstract class RestEnrollApiBase<I, O> extends EnrollBaseRestApi<I, O> {
  @Autowired
  private EnrollAuth enrollAuth;
  private String token;
  private Instant expirationDate;

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = super.generateHeaders();
    if (token == null || expirationDate.isBefore(Instant.now())) {
      try {
        EnrollAuthOutput res = this.enrollAuth.getData(null);
        this.token = res.getToken();
        this.expirationDate = res.getExpirationDate();
      } catch (IntegrationException e) {
        this.exceptionHandler.logException(e);
      }
    }
    headers.setBearerAuth(token);
    return headers;
  }

}
