package innovitics.azimut.rest.apis.recaptcha;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.recaptcha.RecaptchaAnnotateRequest;

@Service
public class RecaptchaAnnotate<PERSON><PERSON> extends RestBaseApi<RecaptchaAnnotateRequest, String> {

  private String token;

  @Override
  protected String generateURL(String assessmentId) {
    // params: assessmentId should be "projects/{project}/assessments/{assessment}"
    return "https://recaptchaenterprise.googleapis.com/v1/" + assessmentId + ":annotate";
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = super.generateHeaders();
    headers.setBearerAuth(token);
    return headers;
  }

  public void setToken(String token) {
    this.token = token;
  }

}
