package innovitics.azimut.rest.apis.paymob;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.paymob.PaymobPayRequest;
import innovitics.azimut.rest.entities.paymob.PaymobPayResponse;
import innovitics.azimut.security.AES;

@Service
public class PaymobPayApi extends RestBaseApi<PaymobPayRequest, PaymobPayResponse> {

  @Autowired
  AES aes;
  private static final String PATH = "/api/acceptance/payments/pay";

  @Override
  protected String generateURL(String params) {
    return configProperties.getPaymobOrderUrl() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

}
