package innovitics.azimut.rest.apis.fra;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.fra.FraSendContractInput;
import innovitics.azimut.rest.entities.fra.FraSendContractOutput;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;
import lombok.Data;

@Service
@Data
public class FraSendContractApi extends RestBaseApi<FraSendContractInput, FraSendContractOutput> {
  private String jwtToken;

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getFraUrl() + "/Contract/StoreContract";
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = super.generateHeaders();
    headers.set("ApiKey", this.configProperties.getFraApiKey());
    headers.set("IdentityJWT", jwtToken);
    return headers;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(FraSendContractInput input) {
    input.setCompanyName(this.configProperties.getFraCompanyName());
    return this.stringify(input);
  };

  @Override
  protected void validateResponse(ResponseEntity<FraSendContractOutput> responseEntity) throws IntegrationException {
    super.validateResponse(responseEntity);
    if (!responseEntity.getStatusCode().is2xxSuccessful()) {
      MyLogger.info("FraSendContractApi: Error in response from FRA API" + responseEntity.getStatusCode() + " "
          + responseEntity.getBody());
      throw new IntegrationException(ErrorCode.FAILED_TO_INTEGRATE);
    }
  }

}
