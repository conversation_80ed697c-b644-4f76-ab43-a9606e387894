package innovitics.azimut.rest.apis.teacomputers;

import java.text.DecimalFormat;
import java.util.UUID;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.trading.BaseAzimutTrading;
import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.entities.teacomputers.PlaceOrderRequest;
import innovitics.azimut.rest.entities.teacomputers.PlaceOrderResponse;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.utilities.crosslayerenums.OrderType;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class TeaComputersPlaceOrderApi extends RestTeaComputersApi<PlaceOrderRequest, PlaceOrderResponse> {

  private static final String PATH = "/PlaceOrder";

  @Override
  protected String generateURL(String params) {
    return super.generateBaseURL() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected String generateSignature(PlaceOrderRequest placeOrderRequest) {
    DecimalFormat df = new DecimalFormat("0.###################");
    String orderValue = null;
    if (placeOrderRequest.getOrderValue() != null) {
      orderValue = df.format(placeOrderRequest.getOrderValue());
      if (!orderValue.contains(".")) {
        orderValue = orderValue + ".0"; // Ensure that the order value has a decimal point
      }
      MyLogger.info("Generating with order value: " + placeOrderRequest.getOrderValue() + " " + orderValue);
    }
    return this.teaComputersSignatureGenerator
        .generateSignature(true,
            placeOrderRequest.getUserName() != null ? placeOrderRequest.getUserName()
                : this.configProperties.getTeaComputersKey(),
            placeOrderRequest.getIdTypeId().toString(), placeOrderRequest.getIdNumber(),
            placeOrderRequest.getFundID().toString(),
            NumberUtility.areLongValuesMatching(placeOrderRequest.getOrderTypeId(),
                (long) OrderType.BUY.getTypeId()) ? orderValue
                    : "");
  }

  @Override
  protected String generateResponseSignature(PlaceOrderRequest request, TeaComputerResponse response) {
    var placeOrderResponse = (PlaceOrderResponse) response;
    return this.teaComputersSignatureGenerator.generateSignature(true,
        request.getUserName() != null ? request.getUserName() : this.configProperties.getTeaComputersKey(),
        placeOrderResponse.getMessage(),
        placeOrderResponse.getTransactionID().toString());
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse response) {
    // not used
    var placeOrderResponse = (PlaceOrderResponse) response;
    return teaComputersSignatureGenerator.generateSignature(placeOrderResponse.getMessage(),
        placeOrderResponse.getTransactionID().toString());
  }

  @Override
  public IntegrationException handleException(Exception exception) {
    MyLogger.info("Handling the Exception in the Place Orders API:::");
    if (exception instanceof IntegrationException)
      return (IntegrationException) exception;

    return this.exceptionHandler.handleAsIntegrationException(exception, ErrorCode.FAILED_TO_INTEGRATE);
  }

  public PlaceOrderRequest createPlaceOrderRequest(BaseAzimutTrading baseAzimutTrading) {
    PlaceOrderRequest request = new PlaceOrderRequest();

    request.setIdTypeId(baseAzimutTrading.getAzIdType());
    request.setIdNumber(baseAzimutTrading.getAzId());
    request.setFundID(baseAzimutTrading.getFundId());
    request.setOrderDate(DateUtility.getCurrentDayMonthYear());
    request.setOrderTypeId(baseAzimutTrading.getOrderTypeId());
    request.setOrderValue(baseAzimutTrading.getOrderValue());
    request.setExternalOrderID(UUID.randomUUID().toString());
    request.setQuantity(baseAzimutTrading.getQuantity());
    if (baseAzimutTrading.getPartnerUserName() != null)
      request.setUserName(baseAzimutTrading.getPartnerUserName());
    request.setSignature(this.generateSignature(request));
    return request;
  }
}
