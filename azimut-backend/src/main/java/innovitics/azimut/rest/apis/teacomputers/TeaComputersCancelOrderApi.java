package innovitics.azimut.rest.apis.teacomputers;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.trading.BaseAzimutTrading;
import innovitics.azimut.rest.entities.teacomputers.PlaceOrderRequest;
import innovitics.azimut.rest.entities.teacomputers.PlaceOrderResponse;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;

@Component
public class TeaComputersCancelOrderApi extends RestTeaComputersApi<PlaceOrderRequest, PlaceOrderResponse> {
  public static final String PATH = "/CancelOrder";

  @Override
  protected String generateSignature(PlaceOrderRequest placeOrderRequest) {
    return this.teaComputersSignatureGenerator.generateSignature(true,
        placeOrderRequest.getUserName() != null ? placeOrderRequest.getUserName()
            : this.configProperties.getTeaComputersKey(),
        placeOrderRequest.getIdTypeId().toString(), placeOrderRequest.getIdNumber());
  }

  @Override
  public String generateURL(String params) {
    return super.generateBaseURL() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected String generateResponseSignature(PlaceOrderRequest request, TeaComputerResponse response) {
    return this.teaComputersSignatureGenerator.generateSignature(true,
        request.getUserName() != null ? request.getUserName() : this.configProperties.getTeaComputersKey(),
        response.getMessage());
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse response) {
    // not used
    var placeOrderResponse = (PlaceOrderResponse) response;
    return teaComputersSignatureGenerator.generateSignature(placeOrderResponse.getMessage());
  }

  public PlaceOrderRequest createCancelOrderRequest(BaseAzimutTrading baseAzimutTrading) {
    PlaceOrderRequest request = new PlaceOrderRequest();

    request.setTransactionID(baseAzimutTrading.getTransactionId().toString());
    request.setIdTypeId(baseAzimutTrading.getAzIdType());
    request.setIdNumber(baseAzimutTrading.getAzId());
    if (baseAzimutTrading.getPartnerUserName() != null)
      request.setUserName(baseAzimutTrading.getPartnerUserName());
    request.setSignature(this.generateSignature(request));

    return request;
  }
}
