package innovitics.azimut.rest.apis.optosigner;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.optosigner.OptoSignerAuthInput;
import innovitics.azimut.rest.entities.optosigner.OptoSignerAuthOutput;

@Service
public class OptoSignerAuth extends RestBaseApi<OptoSignerAuthInput, OptoSignerAuthOutput> {

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getOptoSignerUrl() + "/auth/login";
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(OptoSignerAuthInput input) {
    OptoSignerAuthInput authInput = new OptoSignerAuthInput();
    authInput.setEmail(this.configProperties.getOptoSignerUsername());
    authInput.setPassword(this.configProperties.getOptoSignerPassword());
    return this.stringify(authInput);
  };

}
