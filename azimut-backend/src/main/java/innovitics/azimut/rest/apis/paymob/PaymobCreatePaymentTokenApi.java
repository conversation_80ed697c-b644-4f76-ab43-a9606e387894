package innovitics.azimut.rest.apis.paymob;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.paymob.PaymobCreatePaymentTokenRequest;
import innovitics.azimut.rest.entities.paymob.PaymobTokenResponse;

@Service
public class PaymobCreatePaymentTokenApi extends RestBaseApi<PaymobCreatePaymentTokenRequest, PaymobTokenResponse> {

  private static final String PATH = "/api/acceptance/payment_keys";

  @Override
  protected String generateURL(String params) {
    return configProperties.getPaymobOrderUrl() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

}
