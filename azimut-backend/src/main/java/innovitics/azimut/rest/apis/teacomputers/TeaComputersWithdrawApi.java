package innovitics.azimut.rest.apis.teacomputers;

import java.util.Arrays;

import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.users.DTOs.WithdrawDto;
import innovitics.azimut.rest.entities.teacomputers.InjectRequest;
import innovitics.azimut.rest.entities.teacomputers.InjectResponse;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.utilities.crosslayerenums.ModuleType;
import innovitics.azimut.utilities.datautilities.DateUtility;

@Component
public class TeaComputersWithdrawApi extends RestTeaComputersApi<InjectRequest, InjectResponse> {

  @Override
  protected String generateURL(String params) {
    return super.generateBaseURL() + "/" + params;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected String generateSignature(InjectRequest request) {
    return this.teaComputersSignatureGenerator.generateSignature("", request.getIdTypeId().toString(),
        request.getIdNumber());
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse teaComputerResponse) {
    InjectResponse request = (InjectResponse) teaComputerResponse;
    return this.teaComputersSignatureGenerator.generateSignature("", request.getOrderId());
  }

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.setAccept(Arrays.asList(new MediaType[] { MediaType.APPLICATION_JSON }));
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.add("lang", this.getLocale());
    return headers;
  }

  public InjectRequest generateWithdrawRequest(BusinessUser tokenizedBusinessUser,
      WithdrawDto baseAzimutTrading) {
    InjectRequest request = new InjectRequest();
    this.populateCredentials(request);
    request.setIdTypeId(tokenizedBusinessUser.getAzimutIdTypeId());
    request.setIdNumber(tokenizedBusinessUser.getUserId());
    request.setOrderDate(DateUtility.getCurrentDayMonthYear());
    request.setModuleTypeId(ModuleType.CASH.getTypeId());
    request.setOrderValue(baseAzimutTrading.getOrderValue());
    request.setAccountNo(baseAzimutTrading.getAccountId());
    request.setAccountId(baseAzimutTrading.getAccountId());
    request.setCurrencyId(baseAzimutTrading.getCurrencyId());
    request.setSignature(this.generateSignature(request));

    return request;
  }
}
