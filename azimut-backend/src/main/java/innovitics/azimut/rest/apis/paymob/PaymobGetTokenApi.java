package innovitics.azimut.rest.apis.paymob;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.paymob.PaymobTokenRequest;
import innovitics.azimut.rest.entities.paymob.PaymobTokenResponse;

@Service
public class PaymobGetTokenApi extends RestBaseApi<PaymobTokenRequest, PaymobTokenResponse> {

  private static final String PATH = "/api/auth/tokens";

  @Override
  protected String generateURL(String params) {
    return configProperties.getPaymobOrderUrl() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(PaymobTokenRequest input) {
    input.setApiKey(configProperties.getPaymobApiKey());
    return super.generateRequestFromInput(input);
  }

}
