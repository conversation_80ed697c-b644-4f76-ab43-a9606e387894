package innovitics.azimut.rest.apis.teacomputers;

import java.util.ArrayList;
import java.util.List;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.user.BusinessClientBankAccountDetails;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.controllers.users.DTOs.GetAzimutClientBankAccountsDto;
import innovitics.azimut.rest.entities.teacomputers.ClientBankAccountResponse;
import innovitics.azimut.rest.entities.teacomputers.GetClientBankAccountsRequest;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class TeaComputersGetClientBankAccountsApi
    extends RestTeaComputersApi<GetClientBankAccountsRequest, ClientBankAccountResponse[]> {
  public static final String PATH = "/GetClientBankAcc";
  // public static final String ACCOUNT_NOT_EXISTING="185";
  // public static final String ACTIVE_ACCOUNT_STATUS="2";

  @Override
  protected String generateURL(String params) {
    return super.generateBaseURL() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.GET;
  }

  @Override
  protected String generateSignature(GetClientBankAccountsRequest getClientBankAccountsRequest) {
    return this.teaComputersSignatureGenerator.generateSignature("",
        getClientBankAccountsRequest.getIdTypeId().toString(), getClientBankAccountsRequest.getIdNumber());
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse teaComputerResponse) {
    var clientBankAccountResponse = (ClientBankAccountResponse) teaComputerResponse;
    return this.teaComputersSignatureGenerator.generateSignature("", clientBankAccountResponse.getBankId().toString(),
        clientBankAccountResponse.getAccountNo());
  }

  public GetClientBankAccountsRequest prepareRequest(GetAzimutClientBankAccountsDto businessAzimutClient,
      BusinessUser tokenizedBusinessUser) {
    GetClientBankAccountsRequest getClientAccountsRequest = new GetClientBankAccountsRequest();
    getClientAccountsRequest.setIdTypeId(tokenizedBusinessUser.getAzimutIdTypeId());
    getClientAccountsRequest.setIdNumber(tokenizedBusinessUser.getUserId());
    getClientAccountsRequest.setAccountID(businessAzimutClient.getAccountId());
    getClientAccountsRequest.setSignature(this.generateSignature(getClientAccountsRequest));

    return getClientAccountsRequest;
  }

  public List<BusinessClientBankAccountDetails> getBanksAccountsFromResponse(
      ClientBankAccountResponse[] clientBankAccountResponses) {

    List<BusinessClientBankAccountDetails> businessClientBankAccountDetails = new ArrayList<BusinessClientBankAccountDetails>();

    if (clientBankAccountResponses == null || clientBankAccountResponses.length == 0)
      return businessClientBankAccountDetails;

    for (ClientBankAccountResponse bankResponse : clientBankAccountResponses) {
      BusinessClientBankAccountDetails accountDetails = new BusinessClientBankAccountDetails();

      accountDetails.setId(Long.parseLong(bankResponse.getBankId()));
      accountDetails.setBankName(bankResponse.getBankName());
      accountDetails.setBankType(bankResponse.getBankType());
      if (StringUtility.isStringPopulated(bankResponse.getBranchId()))
        accountDetails.setBranchId(Long.parseLong(bankResponse.getBranchId()));
      accountDetails.setBranchName(bankResponse.getBranchName());
      accountDetails.setCurrencyName(bankResponse.getCurrencyName());
      accountDetails.setCurrencyId(Long.parseLong(bankResponse.getCurrencyId()));
      accountDetails.setAccountNumber(bankResponse.getAccountNo());
      accountDetails.setIban(bankResponse.getIBAN());
      accountDetails.setAccountId(bankResponse.getAccountID());
      accountDetails.setStatus(bankResponse.getStatusId());
      accountDetails.setStatusName(bankResponse.getAccountStatusName());
      accountDetails.setAccountStatus(Long.parseLong(bankResponse.getAccountStatus()));
      accountDetails.setSwiftCode(bankResponse.getSwiftCode());
      accountDetails.setIsLocal(false);
      MyLogger.info("BusinessClientBankAccountDetails::::" + businessClientBankAccountDetails.toString());

      businessClientBankAccountDetails.add(accountDetails);
    }

    return businessClientBankAccountDetails;
  }
}
