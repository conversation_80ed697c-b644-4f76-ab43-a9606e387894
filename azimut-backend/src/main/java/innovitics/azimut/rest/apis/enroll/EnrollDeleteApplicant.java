package innovitics.azimut.rest.apis.enroll;

import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.entities.enroll.EnrollDeleteApplicantResponse;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;

@Component
public class EnrollDeleteApplicant extends RestEnrollApiBase<Void, EnrollDeleteApplicantResponse> {

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getEnrollUrl() + "/api/v1/Applicant/DeleteApplicant?identifier=" + params;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.DELETE;
  }

  @Override
  protected void validateResponse(ResponseEntity<EnrollDeleteApplicantResponse> responseEntity)
      throws IntegrationException {
    super.validateResponse(responseEntity);
    if (responseEntity.getBody().getStatus() != 0)
      throw new IntegrationException(ErrorCode.OPERATION_NOT_PERFORMED);
  }

}
