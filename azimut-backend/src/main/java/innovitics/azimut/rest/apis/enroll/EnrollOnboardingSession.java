package innovitics.azimut.rest.apis.enroll;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.enroll.EnrollAuthOutput;
import innovitics.azimut.rest.entities.enroll.EnrollOnboardingSessionInput;

@Service
public class EnrollOnboardingSession extends RestBaseApi<EnrollOnboardingSessionInput, EnrollAuthOutput> {

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getEnrollUrl() + "/api/v1/Auth/GenerateOnboardingSessionToken";
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(EnrollOnboardingSessionInput input) {
    EnrollOnboardingSessionInput authInput = new EnrollOnboardingSessionInput();
    authInput.setTenantId(this.configProperties.getEnrollTenantId());
    authInput.setTenantSecret(this.configProperties.getEnrollTenantSecret());
    authInput.setDeviceId("web");
    return this.stringify(authInput);
  };

}
