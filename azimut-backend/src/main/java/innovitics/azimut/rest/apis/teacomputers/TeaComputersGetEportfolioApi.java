package innovitics.azimut.rest.apis.teacomputers;

import java.util.ArrayList;
import java.util.List;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessmodels.user.EportfolioDetail;
import innovitics.azimut.rest.entities.teacomputers.EportfolioData;
import innovitics.azimut.rest.entities.teacomputers.GetEportfolioRequest;
import innovitics.azimut.rest.entities.teacomputers.GetEportfolioResponse;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;

@Component
public class TeaComputersGetEportfolioApi extends RestTeaComputersApi<GetEportfolioRequest, GetEportfolioResponse> {
  public final static String PATH = "/portfolio/Summary";

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getTeaComputersEportUrl() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.GET;
  }

  @Override
  protected String generateSignature(GetEportfolioRequest getEportfolioRequest) {
    return this.teaComputersSignatureGenerator.generateSignature(true,
        this.configProperties.getTeaComputersEportfolioKey(), getEportfolioRequest.getIdType().toString(),
        getEportfolioRequest.getIdNumber());
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse teaComputerResponse) {
    GetEportfolioResponse getEportfolioResponse = (GetEportfolioResponse) teaComputerResponse;
    return this.teaComputersSignatureGenerator.generateSignature(true,
        this.configProperties.getTeaComputersEportfolioKey(), "Summary", getEportfolioResponse.getData()[0].getId());
  }

  public GetEportfolioRequest generateRequest(BusinessUser tokenizedBusinessUser, String language) {
    GetEportfolioRequest request = new GetEportfolioRequest();
    request.setIdType(tokenizedBusinessUser.getAzimutIdTypeId());
    request.setIdNumber(tokenizedBusinessUser.getUserId());
    request.setSignature(this.generateSignature(request));
    request.setLang(language);

    return request;
  }

  public List<EportfolioDetail> getEportfolioDetailsFromResponse(GetEportfolioResponse response) {
    List<EportfolioDetail> eportfolioDetails = new ArrayList<EportfolioDetail>();
    EportfolioData[] eportfolioData = response.getData();

    if (eportfolioData == null)
      return eportfolioDetails;

    for (EportfolioData eportfolioDatum : eportfolioData) {
      EportfolioDetail eportfolioDetail = new EportfolioDetail();
      eportfolioDetail.setId(eportfolioDatum.getId());
      eportfolioDetail.setClientId(eportfolioDatum.getClientID());
      eportfolioDetail.setPortfolioId(eportfolioDatum.getPortfolioID());
      eportfolioDetail.setName(eportfolioDatum.getName());
      eportfolioDetail.setValue(eportfolioDatum.getValue());
      eportfolioDetail.setWeight(eportfolioDatum.getWeight());
      eportfolioDetails.add(eportfolioDetail);
    }

    return eportfolioDetails;
  }
}
