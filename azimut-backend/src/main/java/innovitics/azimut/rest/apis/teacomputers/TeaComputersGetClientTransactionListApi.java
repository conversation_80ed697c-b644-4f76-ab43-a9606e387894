package innovitics.azimut.rest.apis.teacomputers;

import java.util.ArrayList;
import java.util.List;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.BusinessTransaction;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.rest.entities.teacomputers.GetTransactionsRequest;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.rest.entities.teacomputers.TransactionResponse;
import innovitics.azimut.utilities.crosslayerenums.CurrencyType;
import innovitics.azimut.utilities.crosslayerenums.TransactionOrderType;
import innovitics.azimut.utilities.crosslayerenums.TransactionStatus;
import innovitics.azimut.utilities.datautilities.StringUtility;

@Component
public class TeaComputersGetClientTransactionListA<PERSON>
    extends RestTeaComputersApi<GetTransactionsRequest, TransactionResponse[]> {

  public static final String PATH = "/GetInformsTrans";

  @Override
  protected String generateURL(String params) {
    return super.generateBaseURL() + PATH;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.GET;
  }

  @Override
  protected String generateSignature(GetTransactionsRequest getTransactionsRequest) {
    return this.teaComputersSignatureGenerator.generateSignature("", getTransactionsRequest.getIdTypeId().toString(),
        getTransactionsRequest.getIdNumber());
  }

  @Override
  protected String generateResponseSignature(TeaComputerResponse teaComputerResponse) {
    TransactionResponse transactionResponse = (TransactionResponse) teaComputerResponse;

    return this.teaComputersSignatureGenerator.generateSignature("", transactionResponse.getTransValue(),
        transactionResponse.getNetValue());
  }

  public GetTransactionsRequest prepareRequest(BusinessUser tokenizedBusinessUser) {
    GetTransactionsRequest request = new GetTransactionsRequest();
    request.setIdTypeId(tokenizedBusinessUser.getAzimutIdTypeId());
    request.setIdNumber(tokenizedBusinessUser.getUserId());
    request.setFromDate(StringUtility.SEARCH_FROM_TRANSACTION_DATE);
    request.setToDate(StringUtility.SEARCH_TO_TRANSACTION_DATE);
    request.setSignature(this.generateSignature(request));
    return request;
  }

  public List<BusinessTransaction> getBusinessTransactionsFromResponses(
      TransactionResponse[] getTransactionsResponses, String language) {

    List<BusinessTransaction> businessTransactions = new ArrayList<BusinessTransaction>();

    if (getTransactionsResponses == null)
      return businessTransactions;

    for (TransactionResponse response : getTransactionsResponses) {
      BusinessTransaction businessTransaction = new BusinessTransaction();

      businessTransaction.setAmount(
          response.getTransValue() == null ? null : Double.valueOf(response.getTransValue()));
      businessTransaction.setTrxDate(response.getTransDateTimeStamp());
      businessTransaction.setReferenceNumber(response.getReferenceNumber());

      long currencyId = response.getCurrencyId();
      CurrencyType currencyType = CurrencyType.getById(currencyId);

      int statusTypeId = Integer.parseInt(response.getStatusType());
      TransactionStatus transactionStatus = TransactionStatus.getById(statusTypeId);

      int orderTypeId = Integer.parseInt(response.getOrderType());
      TransactionOrderType orderType = TransactionOrderType.getById(orderTypeId);

      if (currencyType != null)
        businessTransaction.setCurrency(
            StringUtility.stringsMatch(language, "ar") ? currencyType.getTypeAr() : currencyType.getType());
      else
        businessTransaction.setCurrency(response.getCurrencyName());

      if (response.getStatusType() != null)
        businessTransaction.setStatus(transactionStatus.getStatusId());

      if (response.getOrderType() != null)
        businessTransaction.setType(orderType.getTypeId());

      businessTransactions.add(businessTransaction);
    }

    return businessTransactions;
  }
}
