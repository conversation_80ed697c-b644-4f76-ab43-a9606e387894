package innovitics.azimut.rest.apis.gateid;

import java.io.IOException;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;

import innovitics.azimut.rest.entities.gateid.GateIdFrontOutput;
import innovitics.azimut.rest.entities.gateid.GateIdImageInput;
import innovitics.azimut.utilities.fileutilities.MultipartInputStreamFileResource;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class GateIdUploadFront
    extends RestGateIdApiBase<GateIdImageInput, GateIdFrontOutput> {

  @Override
  protected String generateURL(String taskId) {
    return this.configProperties.getGateIdUrl() + "/detect/front/" + taskId;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = super.generateHeaders();
    headers.setContentType(MediaType.MULTIPART_FORM_DATA);
    return headers;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(GateIdImageInput input) {
    LinkedMultiValueMap<String, Object> map = new LinkedMultiValueMap<>();

    try {
      map.add("image", new MultipartInputStreamFileResource(input.getImage().getInputStream(),
          input.getImage().getOriginalFilename()));
    } catch (IOException e) {
      MyLogger.logStackTrace(e);
    }
    return new HttpEntity<>(map, this.generateHeaders());
  }

}
