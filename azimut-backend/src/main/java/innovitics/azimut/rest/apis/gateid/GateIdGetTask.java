package innovitics.azimut.rest.apis.gateid;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.entities.gateid.GateIdTask;

@Service
public class GateIdGetTask
    extends RestGateIdApiBase<Void, GateIdTask> {

  @Override
  protected String generateURL(String taskId) {
    return this.configProperties.getGateIdUrl() + "/tasks/" + taskId;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.GET;
  }

}
