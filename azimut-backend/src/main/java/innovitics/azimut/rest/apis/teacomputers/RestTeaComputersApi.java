package innovitics.azimut.rest.apis.teacomputers;

import java.net.URI;
import java.util.Date;

import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpEntityEnclosingRequestBase;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerRequest;
import innovitics.azimut.rest.entities.teacomputers.TeaComputerResponse;
import innovitics.azimut.security.TeaComputersSignatureGenerator;
import innovitics.azimut.utilities.datautilities.ArrayUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;
import lombok.Data;

@Data
public abstract class RestTeaComputersApi<I extends TeaComputerRequest, O>
    extends RestBaseApi<I, O> {

  public final static String ENGLISH = "en-US";
  public final static String ARABIC = "ar-EG";
  private String locale;

  protected @Autowired ArrayUtility arrayUtility;

  @Autowired
  TeaComputersSignatureGenerator teaComputersSignatureGenerator;

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = super.generateHeaders();
    if (StringUtility.isStringPopulated(locale)) {
      if (StringUtility.stringsMatch(locale, StringUtility.ARABIC)) {
        locale = ARABIC;
      } else if (StringUtility.stringsMatch(locale, StringUtility.ENGLISH)) {
        locale = ENGLISH;
      }
    }

    headers.add("lang", locale);
    MyLogger.info("Generated Headers:::" + headers.toString());
    return headers;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(I input) {
    this.populateCredentials(input);
    return super.generateRequestFromInput(input);
  }

  protected void populateCredentials(TeaComputerRequest request) {
    if (request.getUserName() == null) {
      request.setUserName(this.configProperties.getTeaComputersUsername());
      request.setPassword(this.configProperties.getTeaComputersPassword());
    } else
      request.setPassword(request.getUserName());
  }

  protected String generateBaseURL() {
    return this.configProperties.getTeaComputersUrl();
  }

  @Override
  protected RestTemplate restTemplate() {
    final RestTemplate authRestTemplate = new RestTemplate();
    var factory = new HttpComponentsClientHttpRequestWithBodyFactory();
    if (!this.configProperties.isProduction()
        && StringUtility.isStringPopulated(this.configProperties.getProxyUsername())) {
      final String username = this.configProperties.getProxyUsername();
      final String password = this.configProperties.getProxyPassword();
      final String proxyUrl = this.configProperties.getProxyUrl();
      final int port = Integer.valueOf(this.configProperties.getProxyPort());

      CredentialsProvider credsProvider = new BasicCredentialsProvider();
      credsProvider.setCredentials(
          new AuthScope(proxyUrl, port),
          new UsernamePasswordCredentials(username, password));

      HttpHost myProxy = new HttpHost(proxyUrl, port);
      HttpClientBuilder clientBuilder = HttpClientBuilder.create();

      clientBuilder.setProxy(myProxy).setDefaultCredentialsProvider(credsProvider).disableCookieManagement();

      HttpClient httpClient = clientBuilder.build();
      factory.setHttpClient(httpClient);
    }
    authRestTemplate.setRequestFactory(factory);
    return authRestTemplate;
  }

  @Override
  protected void validateResponse(I request, ResponseEntity<O> responseEntity) throws IntegrationException {
    super.validateResponse(responseEntity);
    O body = responseEntity.getBody();
    if (body == null) {
      throw new IntegrationException(ErrorCode.NO_DATA_FOUND);
    }
    if (body.getClass().isArray()) {
      /// array
      TeaComputerResponse[] bodyArr = (TeaComputerResponse[]) body;
      if (this.arrayUtility.isArrayPopulated(bodyArr)) {
        for (TeaComputerResponse response : bodyArr) {
          if (response != null) {
            if ((StringUtility.stringsDontMatch(this.generateResponseSignature(request, response),
                response.getSignature()))
                || !StringUtility.isStringPopulated(response.getSignature())) {
              throw new IntegrationException(ErrorCode.INVALID_SIGNATURE);
            }

          }
        }
      }
    } else {
      /// not array
      TeaComputerResponse response = (TeaComputerResponse) body;
      if ((StringUtility.stringsDontMatch(this.generateResponseSignature(request, response),
          response.getSignature()))
          || !StringUtility.isStringPopulated(response.getSignature())) {
        throw new IntegrationException(ErrorCode.INVALID_SIGNATURE);
      }
    }
  }

  protected abstract String generateSignature(I teaComputerRequest);

  protected String generateResponseSignature(I teaComputerRequest, TeaComputerResponse teaComputerResponse) {
    return generateResponseSignature(teaComputerResponse);
  }

  protected abstract String generateResponseSignature(TeaComputerResponse teaComputerResponse);

  @Override
  public IntegrationException handleError(HttpClientErrorException httpClientErrorException) {
    MyLogger.info("httpClientErrorException:::" + httpClientErrorException.toString());
    int errorCode = ErrorCode.FAILED_TO_INTEGRATE.getCode();
    String errorMessage = "";
    TeaComputerResponse teaComputerResponse = new TeaComputerResponse();
    ObjectMapper mapper = new ObjectMapper();
    try {
      MyLogger.info("Parsing the exception to the teaComputerResponse:::");
      MyLogger.info("httpClientErrorException.getResponseBodyAsString():::"
          + httpClientErrorException.getResponseBodyAsString());
      teaComputerResponse = mapper.readValue(httpClientErrorException.getResponseBodyAsString(),
          TeaComputerResponse.class);
      errorMessage = teaComputerResponse.getMessage();
      if (StringUtility.isStringPopulated(teaComputerResponse.getErrorCode())) {
        errorCode = Integer.valueOf(teaComputerResponse.getErrorCode());
      }

      MyLogger.info("teaComputerResponse:::" + teaComputerResponse.toString());
    } catch (JsonProcessingException e) {
      MyLogger.info("Failed to Parse:::");
      MyLogger.logStackTrace(e);
      return new IntegrationException(ErrorCode.FAILED_TO_INTEGRATE);
    }

    IntegrationException integrationException = new IntegrationException(errorCode, new Date(), errorMessage, null,
        errorMessage, httpClientErrorException.getStackTrace());
    return integrationException;
  }

  private static final class HttpComponentsClientHttpRequestWithBodyFactory
      extends HttpComponentsClientHttpRequestFactory {
    @Override
    protected HttpUriRequest createHttpUriRequest(HttpMethod httpMethod, URI uri) {
      if (httpMethod == HttpMethod.GET) {
        return new HttpGetRequestWithEntity(uri);
      }
      return super.createHttpUriRequest(httpMethod, uri);
    }
  }

  private static final class HttpGetRequestWithEntity extends HttpEntityEnclosingRequestBase {
    public HttpGetRequestWithEntity(final URI uri) {
      super.setURI(uri);
    }

    @Override
    public String getMethod() {
      return HttpMethod.GET.name();
    }

  }

}
