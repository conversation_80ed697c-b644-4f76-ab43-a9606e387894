package innovitics.azimut.rest.apis.gateid;

import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.entities.gateid.GateIdResult;

@Service
public class GateIdNidOcr
    extends RestGateIdApiBase<Void, GateIdResult> {

  @Override
  protected String generateURL(String taskId) {
    return this.configProperties.getGateIdUrl() + "/ocr/nid/" + taskId;
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

}
