package innovitics.azimut.rest.apis.enroll;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import innovitics.azimut.rest.entities.enroll.EnrollAuthInput;
import innovitics.azimut.rest.entities.enroll.EnrollAuthOutput;

@Service
public class EnrollAuth extends EnrollBaseRestApi<EnrollAuthInput, EnrollAuthOutput> {

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getEnrollUrl() + "/api/v1/Auth/GenerateAuthToken";
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(EnrollAuthInput input) {
    EnrollAuthInput authInput = new EnrollAuthInput();
    authInput.setTenantId(this.configProperties.getEnrollTenantId());
    authInput.setTenantSecret(this.configProperties.getEnrollTenantSecret());
    return this.stringify(authInput);
  };

}
