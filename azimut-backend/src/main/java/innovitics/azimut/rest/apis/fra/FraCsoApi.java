package innovitics.azimut.rest.apis.fra;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import innovitics.azimut.exceptions.IntegrationException;
import innovitics.azimut.rest.RestBaseApi;
import innovitics.azimut.rest.entities.fra.FraCsoInput;
import innovitics.azimut.rest.entities.fra.FraCsoOutput;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Service
public class FraCso<PERSON>pi extends RestBaseApi<FraCsoInput, FraCsoOutput> {

  @Override
  protected String generateURL(String params) {
    return this.configProperties.getFraUrl() + "/NationalIdValidator/IsValid";
  }

  @Override
  protected HttpMethod chooseHttpMethod() {
    return HttpMethod.POST;
  }

  @Override
  public HttpHeaders generateHeaders() {
    HttpHeaders headers = super.generateHeaders();
    headers.set("ApiKey", this.configProperties.getFraApiKey());
    return headers;
  }

  @Override
  protected HttpEntity<?> generateRequestFromInput(FraCsoInput input) {
    input.setCompanyName(this.configProperties.getFraCompanyName());
    return this.stringify(input);
  };

  @Override
  protected void validateResponse(ResponseEntity<FraCsoOutput> responseEntity) throws IntegrationException {
    super.validateResponse(responseEntity);
    if (!responseEntity.getStatusCode().is2xxSuccessful()) {
      MyLogger.info("FraCsoApi: Error in response from FRA API" + responseEntity.getStatusCode() + " "
          + responseEntity.getBody());
      throw new IntegrationException(ErrorCode.FAILED_TO_INTEGRATE);
    }
  }

}
