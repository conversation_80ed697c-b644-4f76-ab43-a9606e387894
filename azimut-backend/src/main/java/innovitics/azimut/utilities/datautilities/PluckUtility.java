package innovitics.azimut.utilities.datautilities;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import innovitics.azimut.utilities.logging.MyLogger;

public class PluckUtility {

  public static <T, F> List<F> pluck(String fieldName, Class<F> fieldType, List<T> list, Class<T> listType,
      ListUtility<T> listUtility) {
    ArrayList<F> result = new ArrayList<F>();
    try {
      Field f = listType.getField(fieldName);
      if (listUtility.isListPopulated(list)) {
        for (T element : list) {
          result.add(fieldType.cast(f.get(element)));
        }
      }
    } catch (Exception exception) {
      MyLogger.logStackTrace(exception);
      MyLogger.info("Could not pluck the data");
    }
    return result;
  }
}
