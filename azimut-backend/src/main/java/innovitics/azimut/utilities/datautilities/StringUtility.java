package innovitics.azimut.utilities.datautilities;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;

import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfStamper;

import innovitics.azimut.controllers.BaseGenericResponse;
import innovitics.azimut.utilities.ParentUtility;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

public final class StringUtility extends ParentUtility {

  protected static final Logger logger = LoggerFactory.getLogger(StringUtility.class);
  public static final List<String> TRUE = Arrays.asList("TRUE", "true", "1");
  public static final String SUCCESS = "success";
  public static final int SUCCESS_CODE = 0;
  public static final String AUTHORIZATION_HEADER = "Authorization";
  public static final String ADMIN_HEADER = "isAdmin";
  public static final String PAYTABS_AUTHORIZATION_HEADER = "authorization";
  public static final String USER_CLASS_NAME = "user";
  public static final String AZIMUT_Client_CLASS_NAME = "azimut client";
  public static final String AUTHENTICATION_CLASS_NAME = "authentication request";
  public static final String SHA_256_ALGORITHM = "SHA-256";
  public static final String UTF_8_ENCODING = "UTF-8";
  public final static String NATIONAL_ID_DOCUMENT_TYPE = "egy_nid";
  public final static String PASSPORT_DOCUMENT_TYPE = "passport";
  public final static String EGYPT = "Egypt";
  public final static String EG = "EG";
  public final static long CLIENT_AML = 1;
  public final static long CLIENT_AML_BLACKLIST = 0;
  public final static String OCCUPATION = "NULL";
  public final static String ZERO = "0";
  public final static String COMMA = ",";
  public final static String SPACE = " ";
  public final static String NEW_LINE = "\n";
  public final static String ENGLISH = "en";
  public final static String ARABIC = "ar";
  public final static String LANGUAGE = "lang";
  public final static String IP = "X-Forwarded-For";
  public static final String SIGNATURE_HEADER = "Signature";
  public final static String PDF_TEMPLATE = "quotation2-copy";
  public final static String PAGE_NUMBER = "page-";
  public final static String CONTRACTS_SUBDIRECTORY = "userContracts";
  public final static String CONTRACT_DOCUMENT_NAME = "contract";
  public final static String PHYSICAL_CONTRACT_DOCUMENT_NAME = "physicalContract";
  public final static String WEB_DEVICE = "WEB";
  public final static String INFORM_WITHDRAW = "InformWithdraw";
  public final static String INFORM_DEPOSIT = "InformDeposit";
  public final static String VALUATION_REPORT = "Report/SendValReport";
  public final static String REQUEST_STATEMENT = "Statment/Report";
  public final static List<String> ORDER_STATUSES = Arrays.asList("GetExecutedOrders", "GetPendingOrders");
  public static final String EXECUTED_ORDERS = "GetExecutedOrders";
  public static final String PENDING_ORDERS = "GetPendingOrders";
  public static final String CANCELED_ORDERS = "GetCanceledOrders";
  public final static String PDF_EXTENSION = "pdf";
  public final static String SEARCH_FROM_TRANSACTION_DATE = "01-12-1950";
  public final static String SEARCH_TO_TRANSACTION_DATE = "12-12-2050";
  public final static String MIN_INITIAL = "Min. Initial";
  public final static String ADDITIONAL_SUBSCRIPTION_UNITS = "Additional Subscription Units";
  public final static String TRANSACTION_SERIAL_PARAM_NAME = "serial";
  public final static String BANK_ID = "bankId";
  public final static String ACCOUNT_ID = "accountId";
  public final static String[] INCLUDED_STATUSES = new String[] { "PG", "H", "P", "V", "E", "D", "OTHER", "Pending",
      "Failed" };
  public final static String PAYTABS_SUCCESS_STATUS = "A";
  public final static String PAYMOB_PENDING_STATUS = "Pending";
  public final static String PAYMOB_SUCCESS_STATUS = "Success";
  public final static String PAYMOB_FAILED_STATUS = "Failed";
  public final static Integer[] RELEVANT_ACTIONS = new Integer[] { 1, 2 };
  public final static List<Integer> TEACOMPUTER_VALIDITY_ERROR_CODES = Arrays.asList(ErrorCode.INVALID_CLIENT.getCode(),
      ErrorCode.INVALID_CLIENT_STATUS.getCode());

  public final static String CHECK_BOX_YES = "Yes";

  public final static String NEXT_STEP_FIELD = "nextUserStep";

  public final static String KYC_STATUS_FIELD = "kycStatus";

  public final static String IS_VERIFIED_FIELD = "isVerified";

  public final static String FUND_ID_FIELD = "fundId";

  public final static String FIRST_PAGE_ID_FIELD = "firstPageId";

  public final static String LIVENESS_CHECK_FIELD = "livenessChecked";

  public final static List<String> MEDIA_TYPES = Arrays.asList(MediaType.APPLICATION_PDF_VALUE,
      MediaType.IMAGE_JPEG_VALUE, MediaType.IMAGE_PNG_VALUE, MediaType.APPLICATION_XML_VALUE,
      "image/webp");

  public final static int MINIMUM_KEY_LENGTH = 5;

  public final static String KEY_PADDING = "00000";

  public static String getClassName(Object object) {
    return object.getClass().getName();
  }

  public static Boolean isStringPopulated(String string) {
    logger.info(string);
    return (string != null && !string.isEmpty() && !string.equals("") && !string.equals("NULL"));
  }

  public static Boolean isStringArrayPopulated(String[] strings) {
    return (strings != null && strings.length != 0);
  }

  public static Boolean isStringListPopulated(List<String> strings) {
    return (strings != null && strings.size() != 0);
  }

  public static boolean matchesPattern(String input, String regexPattern) {

    boolean match = true;
    final Pattern pattern = Pattern.compile(regexPattern);
    if (isStringPopulated(input) && !pattern.matcher(input).matches()) {
      match = false;
    }
    return match;
  }

  public static String convertToJson(ErrorCode errorCode) throws JsonProcessingException {

    ObjectMapper mapper = new ObjectMapper();
    /*
     * mapper.registerModule(new JavaTimeModule());
     * mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
     */

    BaseGenericResponse<String> baseGenericResponse = new BaseGenericResponse<String>();
    baseGenericResponse.setMessage(errorCode.getMessage());
    baseGenericResponse.setStatus(errorCode.getCode());
    baseGenericResponse.setTransactionId(Thread.currentThread().getName());
    return mapper.writeValueAsString(baseGenericResponse);
  }

  public static String convertToJson(Object object) {
    String json = "";
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      objectMapper.setSerializationInclusion(Include.NON_NULL);
      objectMapper.setSerializationInclusion(Include.NON_ABSENT);
      objectMapper.setSerializationInclusion(Include.NON_EMPTY);

      json = objectMapper.writeValueAsString(object);
    } catch (Exception exception) {
      logger.info("Could not stringfy");
      MyLogger.logStackTrace(exception);
    }
    return json;
  }

  public static String addLeadingAndTrailingSpaces(String string) {
    return " " + string + " ";
  }

  public static String surroundWithBrackets(String[] strings) {
    return " in (" + String.join(",", strings) + ")";
  }

  public static String surroundForLike(String string) {
    return " like '%" + string + "%'";
  }

  public static boolean validateStringValueWithRegexPattern(String input, String regexPattern, boolean mandatory) {
    logger.info("Input::" + input);
    logger.info("Pattern::" + regexPattern);
    logger.info("Mandatory?::" + mandatory);

    boolean result = false;
    if (mandatory) {
      result = isStringPopulated(input) && matchesPattern(input, regexPattern);
    }

    else if (!mandatory) {
      if (isStringPopulated(input)) {
        result = matchesPattern(input, regexPattern);
      } else {
        result = true;
      }
    }
    logger.info("Result::" + result);
    return result;
  }

  public static String getSubstring(String input, int beginningIndex) {
    return input.substring(beginningIndex);
  }

  public static boolean stringsMatch(String input1, String input2) {
    boolean result = isStringPopulated(input1) && isStringPopulated(input2) && input1.equals(input2);
    logger.info("Comparison result:::" + result);
    return result;
  }

  public static boolean stringsDontMatch(String input1, String input2) {
    return isStringPopulated(input1) && isStringPopulated(input2) && !input1.equals(input2);
  }

  public static String generateSubStringStartingFromCertainIndex(String input, char value) {

    int index = input.indexOf(value);
    return input.substring(index);
  }

  public static String generateSubStringStartingFromCertainIndex(String input, int value) {
    return input.substring(value);
  }

  public static List<String> splitStringUsingCharacter(String input, String splittingCharacter) {
    if (isStringPopulated(input) && isStringPopulated(splittingCharacter))
      return Arrays.asList(input.split(splittingCharacter));
    else
      return null;

  }

  public static List<Integer> splitStringUsingCharacterToIntegerArray(String input, String splittingCharacter) {

    if (isStringPopulated(input) && isStringPopulated(splittingCharacter)) {
      List<Integer> integerArray = new ArrayList<Integer>();
      List<String> stringArray = Arrays.asList(input.split(splittingCharacter));
      for (String string : stringArray) {
        integerArray.add(Integer.valueOf(string));
      }

      return integerArray;
    } else
      return null;

  }

  public static String getStringValue(byte[] contentAsByteArray, String characterEncoding) {
    try {
      String value = new String(contentAsByteArray, 0, contentAsByteArray.length, characterEncoding);
      return value;
    } catch (UnsupportedEncodingException e) {
      MyLogger.logStackTrace(e);
    }
    return "";
  }

  public static String generateAmountStringWithoutDecimalPoints(Double amount) {
    return (splitStringUsingCharacter(String.valueOf(amount), "\\.")).get(0);
  }

  public static String generateAmountStringWithoutDecimalPoints(String amount) {
    if (isStringPopulated(amount)) {
      return (splitStringUsingCharacter(amount, "\\.")).get(0);
    } else
      return "";
  }

  public static boolean areTheTwoListsIdentical(List<String> list1, List<String> list2) {
    boolean result = true;
    int counter = 0;
    for (String valueFromList1 : list1) {
      for (String valueFromList2 : list2) {
        if (stringsMatch(valueFromList1, valueFromList2)) {
          counter++;
        }
      }
    }
    result = (NumberUtility.areIntegerValuesMatching(counter, list1.size())
        && NumberUtility.areIntegerValuesMatching(counter, list2.size()));
    MyLogger.info("Are the two lists are identical::" + result);
    return result;
  }

  public static String determineOuputLanguage(String english, String arabic, String language) {
    MyLogger.info("lang::" + language);
    if (isStringPopulated(language)) {
      if (stringsMatch(language, ENGLISH)) {
        return english;
      } else if (stringsMatch(language, ARABIC)) {
        return arabic;
      }
    } else {
      return english;
    }
    return null;
  }

  public static String convertArabicToEnglish(String arabicNumerals) {
    // Create a mapping of Arabic numerals to English numerals
    Map<Character, Character> numeralMapping = new HashMap<>();
    numeralMapping.put('\u0660', '0');
    numeralMapping.put('\u0661', '1');
    numeralMapping.put('\u0662', '2');
    numeralMapping.put('\u0663', '3');
    numeralMapping.put('\u0664', '4');
    numeralMapping.put('\u0665', '5');
    numeralMapping.put('\u0666', '6');
    numeralMapping.put('\u0667', '7');
    numeralMapping.put('\u0668', '8');
    numeralMapping.put('\u0669', '9');

    // StringBuilder to build the result
    StringBuilder englishNumerals = new StringBuilder();

    // Iterate over each character in the input string
    for (char c : arabicNumerals.toCharArray()) {
      if (numeralMapping.containsKey(c)) {
        // Replace Arabic numeral with English numeral using the mapping
        englishNumerals.append(numeralMapping.get(c));
      } else {
        // If it's not an Arabic numeral, just append the character as is
        englishNumerals.append(c);
      }
    }

    return englishNumerals.toString();
  }

  public static boolean containsArabic(String text) {
    // Regular expression for Arabic Unicode block
    String arabicPattern = "[\\u0600-\\u06FF\\u0750-\\u077F\\u08A0-\\u08FF\\uFB50-\\uFDFF\\uFE70-\\uFEFF]";
    return text != null && text.matches(".*" + arabicPattern + ".*");
  }

  public static void arabicFields(String language, PdfStamper stamper, AcroFields form, String fieldName, String value,
      Object fontSize) throws DocumentException, IOException {
    // BaseFont unicode =BaseFont.createFont("arabtype.ttf", BaseFont.IDENTITY_H,
    // BaseFont.EMBEDDED);
    BaseFont unicode = BaseFont.createFont("simpfxo.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
    stamper.getAcroFields().setFieldProperty(fieldName, "textfont", unicode, null);
    stamper.getAcroFields().setFieldProperty(fieldName, "textsize", fontSize, null);
    form.setField(fieldName, value);
    MyLogger.info("FieldName::" + fieldName);
    MyLogger.info("FieldValue::" + value);
  }

  public static String removeEmptyCharacters(String input) {
    if (isStringPopulated(input)) {
      String output = input.replace(" ", "+");
      return output;
    } else
      return "";
  }

  public static MediaType findMediaType(String fileName) {

    var extension = generateSubStringStartingFromCertainIndex(fileName, '.').substring(1).toLowerCase();

    for (String mediaType : MEDIA_TYPES) {
      if (mediaType.contains(extension)) {
        return MediaType.valueOf(mediaType);
      }
      if ("jpg".equals(extension)) {
        return MediaType.IMAGE_JPEG;
      }
    }
    return MediaType.APPLICATION_OCTET_STREAM;
  }

  /**
   * Extracts passport numbers from the given text.
   * A passport number is defined as any word containing more than 5 digits.
   * 
   * @param text The input text containing potential passport numbers
   * @return A string of found passport numbers joined with \" - \", or an empty
   *         string if none found
   */
  public static String extractPassportNumbers(String text) {
    if (!isStringPopulated(text)) {
      return "";
    }

    List<String> passportNumbers = new ArrayList<>();

    // Split text into words (using whitespace and common punctuation as delimiters)
    String[] words = text.split("[\\s,;.!?()\\[\\]{}]+");

    for (String word : words) {
      if (word.isEmpty()) {
        continue;
      }

      // Count digits in the word
      int digitCount = 0;
      for (char c : word.toCharArray()) {
        if (Character.isDigit(c)) {
          digitCount++;
        }
      }

      // If word contains more than 5 digits, consider it a passport number
      if (digitCount > 5) {
        passportNumbers.add(word);
      }
    }

    // Join all found passport numbers with " - "
    return String.join(" - ", passportNumbers);
  }

}