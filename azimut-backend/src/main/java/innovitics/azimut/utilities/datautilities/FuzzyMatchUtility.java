package innovitics.azimut.utilities.datautilities;

public class FuzzyMatchUtility {

  /** Main API: returns a score in [0,1] */
  public static double similarity(String name1, String name2) {
    String[] a = tokenize(normalizeArabic(name1));
    String[] b = tokenize(normalizeArabic(name2));

    int lcs = lcsLengthFuzzy(a, b);
    return (a.length + b.length) == 0 ? 1.0 : (2.0 * lcs) / (a.length + b.length);
  }

  /** Normalize Arabic: remove diacritics, unify variants, trim spaces */
  static String normalizeArabic(String s) {
    if (s == null)
      return "";
    // Remove Arabic diacritics
    String out = s.replaceAll("[\\u064B-\\u065F]", "");
    // Unify Alef variants
    out = out.replace('أ', 'ا').replace('إ', 'ا').replace('آ', 'ا');
    // Unify Yeh/<PERSON><PERSON> and <PERSON><PERSON> on Yeh
    out = out.replace('ى', 'ي').replace('ئ', 'ي');
    // Teh Marbuta -> Heh
    out = out.replace('ة', 'ه');
    // Remove Tatweel
    out = out.replace("ـ", "");
    // Normalize spaces
    out = out.trim().replaceAll("\\s+", " ");
    return out;
  }

  static String[] tokenize(String s) {
    return s.isEmpty() ? new String[0] : s.split("\\s+");
  }

  /** Word-level LCS with fuzzy equality (Levenshtein similarity threshold) */
  static int lcsLengthFuzzy(String[] a, String[] b) {
    int n = a.length, m = b.length;
    int[][] dp = new int[n + 1][m + 1];

    for (int i = 1; i <= n; i++) {
      String wi = a[i - 1];
      for (int j = 1; j <= m; j++) {
        String wj = b[j - 1];
        if (wordsEqualFuzzy(wi, wj)) {
          dp[i][j] = dp[i - 1][j - 1] + 1;
        } else {
          dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
        }
      }
    }
    return dp[n][m];
  }

  /** Fuzzy equality for words using normalized Levenshtein similarity */
  static boolean wordsEqualFuzzy(String w1, String w2) {
    // Dynamic threshold: a bit more forgiving for very short names (e.g., "احمد")
    int maxLen = Math.max(w1.length(), w2.length());
    double threshold = (maxLen <= 4) ? 0.75 : 0.80; // tune if needed
    return levenshteinSim(w1, w2) >= threshold;
  }

  /** Levenshtein similarity in [0,1], normalized by max length */
  static double levenshteinSim(String s1, String s2) {
    int n = s1.length(), m = s2.length();
    if (n == 0 && m == 0)
      return 1.0;
    int[][] d = new int[n + 1][m + 1];
    for (int i = 0; i <= n; i++)
      d[i][0] = i;
    for (int j = 0; j <= m; j++)
      d[0][j] = j;

    for (int i = 1; i <= n; i++) {
      char c1 = s1.charAt(i - 1);
      for (int j = 1; j <= m; j++) {
        char c2 = s2.charAt(j - 1);
        int cost = (c1 == c2) ? 0 : 1;
        d[i][j] = Math.min(
            Math.min(d[i - 1][j] + 1, // deletion
                d[i][j - 1] + 1), // insertion
            d[i - 1][j - 1] + cost // substitution
        );
      }
    }
    int dist = d[n][m];
    int maxLen = Math.max(n, m);
    return 1.0 - (double) dist / (double) maxLen;
  }

}
