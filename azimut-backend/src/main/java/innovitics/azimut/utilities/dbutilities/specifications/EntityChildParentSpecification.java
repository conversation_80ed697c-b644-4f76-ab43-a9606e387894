package innovitics.azimut.utilities.dbutilities.specifications;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.Path;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.persistence.criteria.Subquery;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Component;

import innovitics.azimut.models.admin.AdminUserMapping;
import innovitics.azimut.models.kyc.Review;
import innovitics.azimut.models.kyc.UserAnswer;
import innovitics.azimut.models.user.UserInvestment;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.dbutilities.SearchCriteria;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class EntityChildParentSpecification<T, P> extends BaseSpecification
    implements Specification<T> {
  public final static Logger logger = LogManager.getLogger(EntityChildParentSpecification.class.getName());

  private static final long serialVersionUID = 1L;

  public EntityChildParentSpecification<T, P> findByCriteria(List<SearchCriteria> searchCriteriaList) {
    return new EntityChildParentSpecification<T, P>() {
      /**
       *
       */
      private static final long serialVersionUID = 1L;

      @Override
      public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
        List<Predicate> predicates = new ArrayList<>();

        for (SearchCriteria searchCriteria : searchCriteriaList) {

          switch (searchCriteria.getOperation()) {
            case GREATER_THAN:
              predicates.add(criteriaBuilder.greaterThan(root.get(searchCriteria.getKey()),
                  searchCriteria.getValue().toString()));
              break;
            case LESS_THAN:
              predicates.add(criteriaBuilder.lessThan(root.get(searchCriteria.getKey()),
                  searchCriteria.getValue().toString()));
              break;
            case GREATER_THAN_EQUAL:
              predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get(searchCriteria.getKey()),
                  searchCriteria.getValue().toString()));
              break;
            case LESS_THAN_EQUAL:
              predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get(searchCriteria.getKey()),
                  searchCriteria.getValue().toString()));
              break;
            case NOT_EQUAL:
              predicates.add(criteriaBuilder.or(
                  criteriaBuilder.notEqual(root.get(searchCriteria.getKey()), searchCriteria.getValue()),
                  criteriaBuilder.isNull(root.get(searchCriteria.getKey()))));
              break;
            case EQUAL:
              predicates.add(
                  criteriaBuilder.equal(root.get(searchCriteria.getKey()), searchCriteria.getValue()));
              break;
            case LIKE:
              predicates.add(criteriaBuilder.like(criteriaBuilder.lower(root.get(searchCriteria.getKey())),
                  "%" + searchCriteria.getValue().toString().toLowerCase() + "%"));
              break;
            case LIKE_END:
              predicates.add(criteriaBuilder.like(criteriaBuilder.lower(root.get(searchCriteria.getKey())),
                  searchCriteria.getValue().toString().toLowerCase() + "%"));
              break;
            case LIKE_START:
              predicates.add(criteriaBuilder.like(criteriaBuilder.lower(root.get(searchCriteria.getKey())),
                  "%" + searchCriteria.getValue().toString().toLowerCase()));
              break;
            case IN:
              predicates.add(
                  criteriaBuilder.in(root.get(searchCriteria.getKey())).value(searchCriteria.getValueList()));
              break;
            case NOT_IN:
              predicates.add(criteriaBuilder.or(
                  criteriaBuilder.not(root.get(searchCriteria.getKey()).in(searchCriteria.getValueList())),
                  criteriaBuilder.isNull(root.get(searchCriteria.getKey()))));
              break;
            case IS_NULL:
              predicates.add(
                  criteriaBuilder.isNull(root.get(searchCriteria.getKey())));
              break;
            case IS_NOT_NULL:
              predicates.add(
                  criteriaBuilder.isNotNull(root.get(searchCriteria.getKey())));
              break;

            case BETWEEN:
              Path<Date> entityDate = root.get(searchCriteria.getKey());
              predicates.add(criteriaBuilder.between(entityDate, getComparingDates(searchCriteria.getRangeFrom(), 0),
                  getComparingDates(searchCriteria.getRangeTo(), 1)));
              break;
            case BEFORE:
              Path<Date> beforeEntityDate = root.get(searchCriteria.getKey());
              predicates.add(
                  criteriaBuilder.lessThan(beforeEntityDate, getComparingDates((String) searchCriteria.getValue(), 0)));
              break;
            case AFTER:
              Path<Date> afterEntityDate = root.get(searchCriteria.getKey());
              predicates.add(criteriaBuilder.greaterThan(afterEntityDate,
                  getComparingDates((String) searchCriteria.getValue(), 0)));
              break;
            case PARENT_GREATER_THAN:
              Join<T, P> joinParentGreaterThan = root.join(searchCriteria.getJoiningColumn());
              predicates.add(criteriaBuilder.greaterThan(joinParentGreaterThan.get(searchCriteria.getKey()),
                  searchCriteria.getValue().toString()));
              break;
            case PARENT_LESS_THAN:
              Join<T, P> joinParentLessThan = root.join(searchCriteria.getJoiningColumn());
              predicates.add(criteriaBuilder.lessThan(joinParentLessThan.get(searchCriteria.getKey()),
                  searchCriteria.getValue().toString()));
              break;
            case PARENT_GREATER_THAN_EQUAL:
              Join<T, P> joinParentGreaterThanEqual = root.join(searchCriteria.getJoiningColumn());
              predicates
                  .add(criteriaBuilder.greaterThanOrEqualTo(joinParentGreaterThanEqual.get(searchCriteria.getKey()),
                      searchCriteria.getValue().toString()));
              break;
            case PARENT_LESS_THAN_EQUAL:
              Join<T, P> joinParentLessThanEqual = root.join(searchCriteria.getJoiningColumn());
              predicates.add(criteriaBuilder.lessThanOrEqualTo(joinParentLessThanEqual.get(searchCriteria.getKey()),
                  searchCriteria.getValue().toString()));
              break;
            case PARENT_NOT_EQUAL:
              Join<T, P> joinParentNotEqual = root.join(searchCriteria.getJoiningColumn());
              predicates.add(
                  criteriaBuilder.notEqual(joinParentNotEqual.get(searchCriteria.getKey()), searchCriteria.getValue()));
              break;
            case PARENT_EQUAL:
              Join<T, P> joinParentEqual = root.join(searchCriteria.getJoiningColumn());
              predicates.add(
                  criteriaBuilder.equal(joinParentEqual.get(searchCriteria.getKey()), searchCriteria.getValue()));
              break;
            case PARENT_LIKE:
              Join<T, P> joinParentLike = root.join(searchCriteria.getJoiningColumn());
              predicates.add(criteriaBuilder.like(criteriaBuilder.lower(joinParentLike.get(searchCriteria.getKey())),
                  "%" + searchCriteria.getValue().toString().toLowerCase() + "%"));
              break;
            case PARENT_LIKE_END:
              Join<T, P> joinParentLikeEnd = root.join(searchCriteria.getJoiningColumn());
              predicates.add(criteriaBuilder.like(criteriaBuilder.lower(joinParentLikeEnd.get(searchCriteria.getKey())),
                  searchCriteria.getValue().toString().toLowerCase() + "%"));
              break;
            case PARENT_LIKE_START:
              Join<T, P> joinParentLikeStart = root.join(searchCriteria.getJoiningColumn());
              predicates
                  .add(criteriaBuilder.like(criteriaBuilder.lower(joinParentLikeStart.get(searchCriteria.getKey())),
                      "%" + searchCriteria.getValue().toString().toLowerCase()));
              break;
            case PARENT_IN:
              Join<T, P> joinParentIn = root.join(searchCriteria.getJoiningColumn());
              predicates.add(
                  criteriaBuilder.in(joinParentIn.get(searchCriteria.getKey())).value(searchCriteria.getValueList()));
              break;
            case PARENT_NOT_IN:
              Join<T, P> joinParentNotIn = root.join(searchCriteria.getJoiningColumn());
              predicates.add(
                  criteriaBuilder.not(joinParentNotIn.get(searchCriteria.getKey())).in(searchCriteria.getValueList()));
              break;
            case ADMIN_USER:
              Long adminUserId = Long.valueOf(searchCriteria.getValue().toString());
              Subquery<Long> subquery = query.subquery(Long.class);
              Root<AdminUserMapping> adminUserMappingRoot = subquery.from(AdminUserMapping.class);
              subquery.select(adminUserMappingRoot.get("user").get("id"))
                  .where(
                      criteriaBuilder.equal(adminUserMappingRoot.get("user").get("id"), root.get("id")),
                      criteriaBuilder.equal(adminUserMappingRoot.get("adminUser").get("id"), adminUserId));
              predicates.add(criteriaBuilder.exists(subquery));
              break;
            case INJECTED:
              Subquery<Long> subqueryInjected = query.subquery(Long.class);
              Root<UserInvestment> injectedRoot = subqueryInjected.from(UserInvestment.class);
              Double start = Double.valueOf(searchCriteria.getValueList().get(0).toString());
              MyLogger.info(searchCriteria.getKey() + " start with ? " + searchCriteria.getKey().startsWith("funds_"),
                  true);
              Expression<Double> keyExpression;
              if (searchCriteria.getKey().startsWith("funds_")) {
                // Create JSON_EXTRACT expression
                var fundSearch = "$.\"" + searchCriteria.getKey().substring(6) + "\"";
                MyLogger.info("setting fund search " + fundSearch, true);
                keyExpression = criteriaBuilder.function(
                    "JSON_EXTRACT",
                    Double.class,
                    injectedRoot.get("funds"),
                    criteriaBuilder.literal(fundSearch));
              } else {
                keyExpression = injectedRoot.get(searchCriteria.getKey());
              }
              if (searchCriteria.getValueList().size() < 2) {
                subqueryInjected.select(injectedRoot.get("user").get("id"))
                    .where(
                        criteriaBuilder.greaterThan(keyExpression, start),
                        criteriaBuilder.equal(injectedRoot.get("user").get("id"), root.get("id")));
              } else {
                Double end = Double.valueOf(searchCriteria.getValueList().get(1).toString());
                subqueryInjected.select(injectedRoot.get("user").get("id"))
                    .where(
                        criteriaBuilder.between(keyExpression, start, end),
                        criteriaBuilder.equal(injectedRoot.get("user").get("id"), root.get("id")));
              }
              predicates.add(criteriaBuilder.exists(subqueryInjected));
              break;
            case REVIEW_DOES_NOT_EXIST:
              // Subquery to find users with reviews
              Subquery<Long> subqueryNotExist = query.subquery(Long.class);
              Root<Review> reviewRootNotExist = subqueryNotExist.from(Review.class);
              // Correlate the subquery with the main query (user ID match)
              subqueryNotExist.select(reviewRootNotExist.get("userId"))
                  .where(criteriaBuilder.equal(reviewRootNotExist.get("userId"), root.get("id")),
                      criteriaBuilder.isNull(reviewRootNotExist.get("deletedAt")));
              predicates.add(criteriaBuilder.not(criteriaBuilder.exists(subqueryNotExist)));
              break;
            case REVIEW_EXIST:
              // Subquery to find users with reviews
              Subquery<Long> subqueryExist = query.subquery(Long.class);
              Root<Review> reviewRootExist = subqueryExist.from(Review.class);
              // Correlate the subquery with the main query (user ID match)
              subqueryExist.select(reviewRootExist.get("userId"))
                  .where(criteriaBuilder.equal(reviewRootExist.get("userId"), root.get("id")),
                      criteriaBuilder.isNull(reviewRootExist.get("deletedAt")));
              predicates.add(criteriaBuilder.exists(subqueryExist));
              break;
            case OFFLINE:
              Join<T, P> joinOfflineParent = root.join("userType");
              predicates.add(criteriaBuilder.or(
                  criteriaBuilder.equal(joinOfflineParent.get("azimutIdTypeId"), 3),
                  criteriaBuilder.notEqual(root.get("countryPhoneCode"), "+20")));
              predicates.add(criteriaBuilder.equal(root.get("contractMap"), 1));
              break;
            case QUESTION_ANSWER:
              Subquery<Long> subqueryInvestment = query.subquery(Long.class);
              Root<UserAnswer> InvestmentRoot = subqueryInvestment.from(UserAnswer.class);
              subqueryInvestment.select(InvestmentRoot.get("id"))
                  .where(criteriaBuilder.equal(InvestmentRoot.get("userId"), root.get("id")),
                      criteriaBuilder.equal(InvestmentRoot.get("questionId"),
                          Long.valueOf((String) searchCriteria.getKey())),
                      criteriaBuilder.isNull(InvestmentRoot.get("deletedAt")),
                      criteriaBuilder.equal(InvestmentRoot.get("answerId"),
                          Long.valueOf((String) searchCriteria.getValue())));
              predicates.add(criteriaBuilder.exists(subqueryInvestment));
              break;
            case VALIDATION:
              Subquery<Long> validationSubqueryExist = query.subquery(Long.class);
              Root<Review> validationReviewRootExist = validationSubqueryExist.from(Review.class);
              if (StringUtility.stringsMatch((String) searchCriteria.getKey(), "any") ||
                  StringUtility.stringsMatch((String) searchCriteria.getKey(), "none")) {
                // we check for any validation review
                validationSubqueryExist.select(validationReviewRootExist.get("userId"))
                    .where(criteriaBuilder.equal(validationReviewRootExist.get("userId"), root.get("id")),
                        // check for comment containing "CSO" or "NTRA" or "AML"
                        criteriaBuilder.or(
                            criteriaBuilder.like(validationReviewRootExist.get("comment"), "%CSO%"),
                            criteriaBuilder.like(validationReviewRootExist.get("comment"), "%NTRA%"),
                            criteriaBuilder.like(validationReviewRootExist.get("comment"), "%AML%")),
                        criteriaBuilder.isNull(validationReviewRootExist.get("deletedAt")));
                if (StringUtility.stringsMatch((String) searchCriteria.getKey(), "none")) {
                  // This means that the user passed all validations
                  predicates.add(criteriaBuilder.not(criteriaBuilder.exists(validationSubqueryExist)));
                  predicates.add(criteriaBuilder.isNotNull(root.get("cso")));
                  predicates.add(criteriaBuilder.isNotNull(root.get("ntra")));
                  predicates.add(criteriaBuilder.isNotNull(root.get("aml")));
                } else {
                  predicates.add(criteriaBuilder.exists(validationSubqueryExist));
                }
              } else if (StringUtility.stringsMatch((String) searchCriteria.getKey(), "not-done")) {
                predicates.add(criteriaBuilder.or(
                    criteriaBuilder.isNull(root.get("cso")),
                    criteriaBuilder.isNull(root.get("ntra")),
                    criteriaBuilder.isNull(root.get("aml"))));
              } else {
                // Otherwise, we check for a specific comment starting with the key
                // Correlate the subquery with the main query (user ID match)
                validationSubqueryExist.select(validationReviewRootExist.get("userId"))
                    .where(criteriaBuilder.equal(validationReviewRootExist.get("userId"), root.get("id")),
                        criteriaBuilder.like(validationReviewRootExist.get("comment"),
                            (String) searchCriteria.getKey() + "%"),
                        criteriaBuilder.isNull(validationReviewRootExist.get("deletedAt")));
                predicates.add(criteriaBuilder.exists(validationSubqueryExist));
              }
              break;
            case PARENT_IS_NULL:
              Join<T, P> joinParentIsNull = root.join(searchCriteria.getJoiningColumn());
              predicates.add(
                  criteriaBuilder.isNull(joinParentIsNull.get(searchCriteria.getKey())));
              break;
            case PARENT_IS_NOT_NULL:
              Join<T, P> joinParentIsNotNull = root.join(searchCriteria.getJoiningColumn());
              predicates.add(
                  criteriaBuilder.isNotNull(joinParentIsNotNull.get(searchCriteria.getKey())));
              break;
            case PARENT_BETWEEN:
              Path<Date> parentEntityDate = root.get(searchCriteria.getKey());
              predicates
                  .add(criteriaBuilder.between(parentEntityDate, getComparingDates(searchCriteria.getRangeFrom(), 0),
                      getComparingDates(searchCriteria.getRangeTo(), 1)));
              break;
            case PARENT_BEFORE:
              Path<Date> beforeParentEntityDate = root.get(searchCriteria.getKey());
              predicates.add(criteriaBuilder.lessThan(beforeParentEntityDate,
                  getComparingDates((String) searchCriteria.getValue(), 0)));
              break;
            case PARENT_AFTER:
              Path<Date> afterParentEntityDate = root.get(searchCriteria.getKey());
              predicates.add(criteriaBuilder.greaterThan(afterParentEntityDate,
                  getComparingDates((String) searchCriteria.getValue(), 0)));
              break;

            default:
              break;
          }
          query.distinct(BooleanUtility.isTrue(searchCriteria.getGetDistinct()));
        }
        return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
      }

    };
  }

  @Override
  public Predicate toPredicate(Root<T> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) {
    // TODO Auto-generated method stub
    return null;
  }
}
