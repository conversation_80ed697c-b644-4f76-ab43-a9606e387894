package innovitics.azimut.utilities.dbutilities;

import java.util.ArrayList;
import java.util.List;

import innovitics.azimut.utilities.datautilities.ListUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SearchCriteria {
  private String key;
  private Object value;
  private List<Object> valueList;
  private List<String> stringValueList;
  private SearchOperation operation;
  private String joiningColumn;
  private String rangeFrom;
  private String rangeTo;
  private Boolean getDistinct;

  public SearchCriteria(String key, List<Object> valueList, SearchOperation operation, String joiningColumn) {
    super();
    this.key = key;
    this.valueList = valueList;
    this.operation = operation;
    this.joiningColumn = joiningColumn;
  }

  public SearchCriteria(String key, List<Object> valueList, SearchOperation operation, String joiningColumn,
      ListUtility<Object> objectListUtility) {
    super();
    this.key = key;
    this.valueList = valueList;
    this.operation = operation;
    this.joiningColumn = joiningColumn;

    if (StringUtility.stringsMatch(key, "id")) {
      this.valueList = this.changeDataType(valueList, Long.class, objectListUtility);
    }

  }

  public SearchCriteria(String key, List<String> valueList, SearchOperation operation, String joiningColumn,
      boolean StringList) {
    super();
    this.key = key;
    this.stringValueList = valueList;
    this.operation = operation;
    this.joiningColumn = joiningColumn;
  }

  public SearchCriteria(String key, Object value, SearchOperation operation, String joiningColumn) {
    super();
    this.key = key;
    this.value = value;
    this.operation = operation;
    this.joiningColumn = joiningColumn;
  }

  public SearchCriteria(String key, String rangeFrom, String rangeTo, SearchOperation operation, String joiningColumn) {
    super();
    this.key = key;
    this.operation = operation;
    this.joiningColumn = joiningColumn;
    this.rangeFrom = rangeFrom;
    this.rangeTo = rangeTo;
  }

  public SearchCriteria() {
  }

  @Override
  public String toString() {
    return "SearchCriteria [key=" + key + ", value=" + value + ", valueList=" + valueList + ", operation="
        + operation + ", joiningColumn=" + joiningColumn + "]";
  }

  protected <D> void changeDataType(List<SearchCriteria> searchCriteriaList, String key, Class<D> clazz,
      ListUtility<Object> objectListUtility) {
    for (SearchCriteria searchCriteria : searchCriteriaList) {
      if (StringUtility.stringsMatch(searchCriteria.getKey(), key)
          && objectListUtility.isListPopulated(searchCriteria.getValueList())) {
        List<Object> modifiedTypeObjects = new ArrayList<Object>();
        for (Object object : searchCriteria.getValueList()) {
          if (clazz.isInstance(0)) {
            modifiedTypeObjects.add(Integer.valueOf((String) object));
          } else if (clazz.isInstance(0l)) {
            modifiedTypeObjects.add(Long.valueOf((String) object));
          }

        }
        searchCriteria.setValueList(modifiedTypeObjects);
      }
    }
  }

  protected <D> List<Object> changeDataType(List<Object> valueList, Class<D> clazz,
      ListUtility<Object> objectListUtility) {

    if (objectListUtility.isListPopulated(valueList)) {
      List<Object> modifiedTypeObjects = new ArrayList<Object>();
      for (Object object : valueList) {
        if (clazz.isInstance(0)) {
          modifiedTypeObjects.add(Integer.valueOf((String) object));
        } else if (clazz.isInstance(0l)) {
          modifiedTypeObjects.add(Long.valueOf((String) object));
        }

      }
      return modifiedTypeObjects;

    } else
      return null;

  }

}