package innovitics.azimut.utilities.dbutilities;

public enum SearchOperation {

  G<PERSON><PERSON>ER_THAN,
  LESS_THAN,
  GREATER_THAN_EQUAL,
  LESS_THAN_EQUAL,
  NOT_EQUAL,
  EQUAL,
  LIKE,
  <PERSON>I<PERSON>_START,
  LIKE_END,
  IN,
  NOT_IN,
  IS_NULL,
  IS_NOT_NULL,
  BETWEE<PERSON>,
  B<PERSON>ORE,
  AFTER,
  GROUP_BY,
  PARENT_GREATER_THAN,
  PARENT_LESS_THAN,
  PARENT_GREATER_THAN_EQUAL,
  PARENT_LESS_THAN_EQUAL,
  PARENT_NOT_EQUAL,
  PARENT_EQUAL,
  PARENT_LIKE,
  PARENT_LIKE_START,
  PARENT_LIKE_END,
  PARENT_IN,
  PARENT_NOT_IN,
  PARENT_IS_NULL,
  INJECTED,
  ADMIN_USER,
  REVIEW_DOES_NOT_EXIST,
  REVIEW_EXIST,
  <PERSON><PERSON>IN<PERSON>,
  QUESTION_ANSWER,
  <PERSON><PERSON><PERSON>AT<PERSON>,
  PARENT_IS_NOT_NULL,
  PARENT_BETWEEN,
  PARENT_BEFORE,
  PARENT_AFTER,
  PARENT_GROUP_BY,
  COUNT,
  MAX,
  MIN,
  AVG,
  SUM

}
