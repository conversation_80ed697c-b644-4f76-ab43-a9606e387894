package innovitics.azimut.utilities.businessutilities;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Comparator;

import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.funds.BusinessFundTransaction;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class FundTransactionSortCompare implements Comparator<BusinessFundTransaction> {
  SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");

  @Override
  public int compare(BusinessFundTransaction o1, BusinessFundTransaction o2) {
    try {
      return simpleDateFormat.parse(o1.getOrderDate()).compareTo(simpleDateFormat.parse(o2.getOrderDate()));
    } catch (ParseException e) {
      MyLogger.logStackTrace(e);
    }
    return 0;
  }

}
