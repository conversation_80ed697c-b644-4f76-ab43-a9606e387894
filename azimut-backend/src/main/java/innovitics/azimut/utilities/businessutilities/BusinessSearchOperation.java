package innovitics.azimut.utilities.businessutilities;

public enum BusinessSearchOperation {

  SEARCH("SEARCH", 0),
  FILTER("FILTER", 1),
  GT("GT", 2),
  LT("LT", 3),
  NULL("IS_NULL", 4),
  NOT_NULL("IS_NOT_NULL", 5),
  REVIEW_NOT_EXIST("REVIEW_NOT_EXIST", 6),
  REVIEW_EXIST("REVIEW_EXIST", 7),
  QUESTION_ANSWER("QUESTION_ANSWER", 8),
  VALIDATION("VALIDATION", 9),
  PARENT_EQUAL("PARENT_EQUAL", 10),
  NOT_IN("NOT_IN", 11),
  OFFLINE("OFFLINE", 12),
  INJECTED("INJECTED", 13),
  GREATER_THAN("GREATER_THAN", 14),
  LESS_THAN("LESS_THAN", 15),
  ADMIN_USER("ADMIN_USER", 16);

  BusinessSearchOperation(
      String operation,
      int code) {
    this.code = code;
    this.operation = operation;
  }

  private final int code;
  private final String operation;

  public int getCode() {
    return code;
  }

  public String getOperation() {
    return operation;
  }

}
