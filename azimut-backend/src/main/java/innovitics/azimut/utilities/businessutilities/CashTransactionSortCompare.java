package innovitics.azimut.utilities.businessutilities;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Comparator;

import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.BusinessTransaction;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class CashTransactionSortCompare implements Comparator<BusinessTransaction> {

  SimpleDateFormat simpleDateFormat = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss");

  @Override
  public int compare(BusinessTransaction o1, BusinessTransaction o2) {
    try {
      return simpleDateFormat.parse(o1.getTrxDate()).compareTo(simpleDateFormat.parse(o2.getTrxDate()));
    } catch (ParseException e) {
      MyLogger.logStackTrace(e);
    }
    return 0;

  }

}
