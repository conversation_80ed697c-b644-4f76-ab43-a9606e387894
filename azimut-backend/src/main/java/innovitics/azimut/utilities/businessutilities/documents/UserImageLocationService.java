package innovitics.azimut.utilities.businessutilities.documents;

import java.io.IOException;
import java.util.Map.Entry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.models.user.UserImage;
import innovitics.azimut.services.kyc.UserImageService;
import innovitics.azimut.utilities.crosslayerenums.DocumentType;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.fileutilities.ParentStorage;

@Service
public class UserImageLocationService extends DocumentLocationService {

  @Autowired
  UserImageService userImageService;

  @Override
  public boolean isType(String type) {
    return StringUtility.stringsMatch(type, DocumentType.USER_IMAGE.getType());
  }

  @Override
  public Entry<MediaType, byte[]> getFileAndExtension(ParentStorage storage, BusinessUser businessUser,
      String validityToken, Long documentId) throws IOException {
    UserImage userImage = userImageService.getUserImageById(documentId);
    if (userImage != null) {
      return storage.getFileWithAbsolutePath(storage.generateLocalPath(this.configProperties.getBlobKYCDocuments(),
          userImage.getImageName(), userImage.getImageSubDirectory(), true, 2L));
    } else
      return null;
  }

}
