package innovitics.azimut.utilities.businessutilities.documents;

import java.io.IOException;
import java.util.Map.Entry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.models.kyc.UserAnswer;
import innovitics.azimut.services.kyc.UserAnswerSubmissionService;
import innovitics.azimut.utilities.crosslayerenums.DocumentType;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.fileutilities.ParentStorage;

@Service
public class KycDocumentLocationService extends DocumentLocationService {

  @Autowired
  UserAnswerSubmissionService userAnswerSubmissionService;

  @Override
  public boolean isType(String type) {
    return StringUtility.stringsMatch(type, DocumentType.KYC_DOCUMENT.getType());
  }

  @Override
  public Entry<MediaType, byte[]> getFileAndExtension(ParentStorage storage, BusinessUser businessUser,
      String validityToken, Long documentId) throws IOException {
    UserAnswer userAnswer = this.userAnswerSubmissionService.getUserAnswersById(documentId);

    if (userAnswer != null) {
      return storage.getFileWithAbsolutePath(storage.generateLocalPath(this.configProperties.getBlobKYCDocuments(),
          userAnswer.getDocumentName(), userAnswer.getDocumentSubDirectory(), true, 2L));
    } else
      return null;
  }

}
