package innovitics.azimut.utilities.businessutilities;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.businessmodels.user.AzimutAccount;
import innovitics.azimut.businessmodels.user.BusinessNotification;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessmodels.user.BusinessUserOTP;
import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.controllers.users.DTOs.CheckIfUserDeletedDto;
import innovitics.azimut.controllers.users.DTOs.GetUserNotificationsDto;
import innovitics.azimut.controllers.users.DTOs.SaveUserLocationDto;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.models.Notification;
import innovitics.azimut.models.OTPFunctionality;
import innovitics.azimut.models.digitalregistry.DigitalRegistry;
import innovitics.azimut.models.digitalregistry.DigitalRegistryAction;
import innovitics.azimut.models.kyc.Review;
import innovitics.azimut.models.kyc.UserAnswer;
import innovitics.azimut.models.user.User;
import innovitics.azimut.models.user.UserDevice;
import innovitics.azimut.models.user.UserImage;
import innovitics.azimut.models.user.UserInteraction;
import innovitics.azimut.models.user.UserLocation;
import innovitics.azimut.models.user.UserOTP;
import innovitics.azimut.models.user.UserType;
import innovitics.azimut.security.AES;
import innovitics.azimut.services.NotificationCenterService;
import innovitics.azimut.services.digitalregistry.DigitalRegistryService;
import innovitics.azimut.services.kyc.ReviewService;
import innovitics.azimut.services.kyc.UserAnswerSubmissionService;
import innovitics.azimut.services.kyc.UserImageService;
import innovitics.azimut.services.kyc.UserTypeService;
import innovitics.azimut.services.user.GenderService;
import innovitics.azimut.services.user.UserDeviceService;
import innovitics.azimut.services.user.UserInteractionService;
import innovitics.azimut.services.user.UserLocationService;
import innovitics.azimut.services.user.UserOTPService;
import innovitics.azimut.services.user.UserService;
import innovitics.azimut.utilities.ParentUtility;
import innovitics.azimut.utilities.crosslayerenums.DocumentType;
import innovitics.azimut.utilities.crosslayerenums.KycStatus;
import innovitics.azimut.utilities.crosslayerenums.UserImageType;
import innovitics.azimut.utilities.crosslayerenums.UserStep;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.ListUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.dbutilities.DatabaseConditions;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.fileutilities.BlobData;
import innovitics.azimut.utilities.fileutilities.SecureStorageService;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.utilities.mapping.NotificationMapper;
import innovitics.azimut.utilities.mapping.UserMapper;
import innovitics.azimut.utilities.mapping.UserOtpMapper;

@Component
public class UserUtility extends ParentUtility {
  @Autowired
  UserDeviceService userDeviceService;
  @Autowired
  UserService userService;
  @Autowired
  SecureStorageService storageService;
  @Autowired
  UserImageService userImageService;
  @Autowired
  protected ListUtility<UserImage> listUtility;
  @Autowired
  protected ListUtility<UserDevice> userDeviceListUtility;
  @Autowired
  protected ListUtility<UserLocation> userLocationListUtility;
  @Autowired
  protected ListUtility<Notification> notificationListUtility;

  @Autowired
  GenderService genderService;
  @Autowired
  AES aes;
  @Autowired
  UserLocationService userLocationService;
  @Autowired
  UserTypeService userTypeService;
  @Autowired
  UserInteractionService userInteractionService;
  @Autowired
  NotificationCenterService notificationCenterService;
  @Autowired
  NotificationMapper notificationMapper;
  @Autowired
  UserOTPService userOTPService;
  @Autowired
  UserOtpMapper userOtpMapper;

  protected @Autowired UserAnswerSubmissionService userAnswerSubmissionService;

  @Autowired
  DigitalRegistryService digitalRegistryService;

  @Autowired
  ReviewService reviewService;

  @Autowired
  protected UserMapper userMapper;

  public void upsertDeviceIdAudit(User user, String deviceId, String deviceName, UserDevice updatedUserDevice) {
    if (updatedUserDevice != null) {

      updatedUserDevice.setUpdatedAt(new Date());
      updatedUserDevice.setUser(user);
      if (StringUtility.isStringPopulated(deviceName))
        updatedUserDevice.setDeviceName(deviceName);
      this.userDeviceService.updateUserDevice(updatedUserDevice);
    } else {
      UserDevice userDevice = new UserDevice();
      userDevice.setCreatedAt(new Date());
      userDevice.setUpdatedAt(new Date());
      userDevice.setDeviceId(deviceId);
      if (StringUtility.isStringPopulated(deviceName))
        userDevice.setDeviceName(deviceName);
      userDevice.setUser(user);
      userDevice.setUserPhone(user.getUserPhone());
      userDevice.setUserId(user.getUserId());
      this.userDeviceService.updateUserDevice(userDevice);
    }

  }

  public UserImage createUserImageRecord(BusinessUser businessUser, MultipartFile file, UserImageType userImageType)
      throws IOException, BusinessException {
    // BlobData blobData=this.uploadFile(file, businessUser);
    UserImage userImage = new UserImage();

    User user = new User();
    user.setId(businessUser.getId());
    userImage.setUser(user);

    userImage.setUserIdType(businessUser.getIdType());
    userImage.setImageType(userImageType.getTypeId());
    // userImage.setImageUrl("");
    // userImage.setPrivateUrl(blobData.getConcatinated(true));
    userImage.setImageName(file.getOriginalFilename());
    userImage.setImageSubDirectory(this.generateUserImageSubDirectory(businessUser));
    userImage.setUserPhone(businessUser.getUserPhone());
    userImage.setCreatedAt(new Date());
    userImage.setFile(file);
    return userImage;

  }

  public BlobData uploadFile(MultipartFile file, BusinessUser businessUser) throws IOException, BusinessException {
    return storageService.uploadFile(file, true, this.configProperties.getBlobKYCDocuments(),
        this.generateUserImageSubDirectory(businessUser), true);
  }

  public BusinessUser isOldUserStepGreaterThanNewUserStep(BusinessUser tokenizedBusinessUser, Integer newUserStep) {

    if (tokenizedBusinessUser != null && NumberUtility.areIntegerValuesMatching(tokenizedBusinessUser.getKycStatus(),
        KycStatus.FIRST_TIME.getStatusId())) {
      if (newUserStep != null) {
        MyLogger.info("New user step::::" + newUserStep.toString());
      }
      if (tokenizedBusinessUser.getUserStep() != null) {
        MyLogger.info("Old user step::::" + tokenizedBusinessUser.getUserStep().toString());
      }
      if (tokenizedBusinessUser.getUserStep() != null) {

        if (!NumberUtility.areIntegerValuesMatching(newUserStep, UserStep.LEFT_AND_RIGHT.getStepId())) {
          if (!NumberUtility.isNewValueLessThanOrEqualOldValue(tokenizedBusinessUser.getUserStep(), newUserStep)) {
            if (tokenizedBusinessUser != null) {
              if (tokenizedBusinessUser.getVerificationPercentage() != null) {
                int oldVerificationPercentage = tokenizedBusinessUser.getVerificationPercentage();
                tokenizedBusinessUser
                    .setVerificationPercentage(oldVerificationPercentage + UserStep.findWeightById(newUserStep));
              } else {
                tokenizedBusinessUser.setVerificationPercentage(UserStep.findWeightById(newUserStep));

              }
            }

            tokenizedBusinessUser.setUserStep(newUserStep);
          }
        } else {
          if (tokenizedBusinessUser != null) {
            if (tokenizedBusinessUser.getVerificationPercentage() != null) {
              int oldVerificationPercentage = tokenizedBusinessUser.getVerificationPercentage();

              if (oldVerificationPercentage + UserStep.LEFT_AND_RIGHT.getWeight() <= 100) {
                tokenizedBusinessUser
                    .setVerificationPercentage(oldVerificationPercentage + UserStep.LEFT_AND_RIGHT.getWeight());
              }
            } else {
              tokenizedBusinessUser.setVerificationPercentage(UserStep.LEFT_AND_RIGHT.getWeight());
            }
            if (!NumberUtility.isNewValueLessThanOrEqualOldValue(tokenizedBusinessUser.getUserStep(), newUserStep)) {
              tokenizedBusinessUser.setUserStep(newUserStep);
            }
          }
        }
      } else {
        tokenizedBusinessUser.setUserStep(newUserStep);
      }
    }
    return tokenizedBusinessUser;
  }

  public void softDeleteUserImages(BusinessUser businessUser) {
    this.userImageService.softDeleteOldUserIdImages(businessUser.getId());
    this.userImageService.softDeleteOldUserFaceImages(businessUser.getId());
  }

  public void removeImagesFromBlobAndDb(BusinessUser businessUser, boolean deleteIdImages) throws IOException {
    List<UserImage> userImages = new ArrayList<UserImage>();
    if (deleteIdImages) {
      userImages = this.getUserImagesFromDb(businessUser, new Integer[] { UserImageType.FRONT_IMAGE.getTypeId(),
          UserImageType.BACK_IMAGE.getTypeId(), UserImageType.PASSPORT_IMAGE.getTypeId() });
    } else {
      userImages = this.getUserImagesFromDb(businessUser, new Integer[] { UserImageType.LEFT.getTypeId(),
          UserImageType.RIGHT.getTypeId(), UserImageType.STRAIGHT.getTypeId(), UserImageType.SMILING.getTypeId() });
    }
    if (this.listUtility.isListPopulated(userImages)) {
      for (UserImage userImage : userImages) {
        if (userImage.getDeletedAt() == null)
          this.storageService.deleteFile(this.configProperties.getBlobKYCDocuments(), userImage.getImageName(),
              userImage.getImageSubDirectory(), false, null);
      }
    }

    if (deleteIdImages) {
      this.userImageService.softDeleteOldUserIdImages(businessUser.getId());
    } else {
      this.userImageService.softDeleteOldUserFaceImages(businessUser.getId());
    }
  }

  public String generateUserImageSubDirectory(BusinessUser businessUser) {
    return "ValifyImages/" + DateUtility.getCurrentDayMonthYear() + "/" + businessUser.getUserPhone();
  }

  public List<UserImage> getUserImagesFromDb(BusinessUser tokenizedBusinessUser, Integer[] imageTypes) {
    List<UserImage> userImages = this.userImageService.getUserImagesByUserAndTypes(tokenizedBusinessUser.getId(),
        imageTypes);
    tokenizedBusinessUser.setUserImages(userImages);
    return userImages;
  }

  public ByteArrayOutputStream getCompanySignedPdf(BusinessUser businessUser, String filename) throws IOException {
    var bytes = getCompanySignedPdfBytes(businessUser, filename);
    ByteArrayOutputStream out = new ByteArrayOutputStream(bytes.length);
    out.write(bytes, 0, bytes.length);
    return out;
  }

  public byte[] getCompanySignedPdfBytes(BusinessUser businessUser, String filename) throws IOException {
    var path = storageService.generateLocalPath(configProperties.getBlobDigitalPdfPath(),
        businessUser.getPdfPath(), filename + "." + StringUtility.PDF_EXTENSION,
        false);
    MyLogger.info("Company Signed PDF File path: " + path);
    Entry<MediaType, byte[]> fileBytesWithExtension = this.storageService.getFileWithAbsolutePath(aes.encrypt(path));
    return fileBytesWithExtension.getValue();
  }

  public ByteArrayOutputStream getSignedPdf(BusinessUser businessUser, String filename) throws IOException {
    var bytes = getSignedPdfBytes(businessUser, filename);
    ByteArrayOutputStream out = new ByteArrayOutputStream(bytes.length);
    out.write(bytes, 0, bytes.length);
    return out;
  }

  public byte[] getSignedPdfBytes(BusinessUser businessUser, String filename) throws IOException {
    var path = storageService.generateLocalPath(configProperties.getBlobDigitalPdfPath(),
        businessUser.getId().toString(), filename + "." + StringUtility.PDF_EXTENSION,
        false);
    MyLogger.info("Signed PDF File path: " + path);
    Entry<MediaType, byte[]> fileBytesWithExtension = this.storageService.getFileWithAbsolutePath(aes.encrypt(path));
    return fileBytesWithExtension.getValue();
  }

  public List<UserImage> getUserImages(BusinessUser tokenizedBusinessUser, Integer[] imageTypes, boolean showUser) {
    List<UserImage> userImages = new ArrayList<UserImage>();
    try {
      userImages = this.userImageService.getUserImagesByUserAndTypes(tokenizedBusinessUser.getId(), imageTypes);

      for (UserImage userImage : userImages) {
        String stringUserImageId = String.valueOf(userImage.getId());
        userImage.setImageUrl(this.storageService.generateFileRetrievalUrl(this.configProperties.getBlobKYCDocuments(),
            userImage.getImageName(), userImage.getImageSubDirectory(), true, null, stringUserImageId,
            DocumentType.USER_IMAGE));
        if (!showUser) {
          userImage.setUser(null);
        }
      }

      tokenizedBusinessUser.setUserImages(userImages);
    } catch (Exception exception) {
      if (this.exceptionHandler.isABusinessException(exception)) {
        return null;
      }
    }
    return userImages;
  }

  public void uploadUserImages(List<UserImage> userImages,
      BusinessUser businessUser) throws BusinessException, IOException {
    if (this.listUtility.isListPopulated(userImages)) {
      for (UserImage userImage : userImages) {
        BlobData blobData = this.uploadFile(userImage.getFile(), businessUser);
        if (blobData != null) {
          userImage.setImageName(blobData.getFileName());
        }
      }
    }
  }

  public String encryptUserPassword(String password) {
    return this.aes.encrypt(password);
  }

  public void addUserLocation(BusinessUser tokenizedBusinessUser, SaveUserLocationDto saveUserLocationDto)
      throws BusinessException {

    try {
      var userLocation = saveUserLocationDto.toUserLocation();
      this.userLocationService.softdeleteOldUserLocations(tokenizedBusinessUser.getId());
      userLocation.setUserId(tokenizedBusinessUser.getId());
      userLocation.setCreatedAt(new Date());
      this.userLocationService.addUserLocation(userLocation);
    } catch (Exception exception) {
      throw this.exceptionHandler.handleBusinessException(exception, ErrorCode.USER_LOCATION_NOT_SAVED);
    }
  }

  public UserLocation getUserLocation(BusinessUser tokenizedBusinessUser) throws Exception {
    try {
      var location = this.userLocationService.findByUserId(tokenizedBusinessUser.getId());
      return location == null ? new UserLocation() : location;
    } catch (Exception exception) {
      this.exceptionHandler.getNullIfNonExistent(exception);
      return new UserLocation();
    }

  }

  public User saveOldUser(String countryPhoneCode, String phoneNumber, List<AzimutAccount> azimutAccounts)
      throws Exception {
    User user = new User();
    user.setCountryPhoneCode(countryPhoneCode);
    user.setPhoneNumber(phoneNumber);
    user.setIsVerified(true);
    user.setIsOld(true);
    user.setIsEmailVerified(true);
    user.setUserStep(UserStep.FINISHED.getStepId());
    user.concatinate();
    user.setCreatedAt(new Date());
    user.setUpdatedAt(new Date());
    user.setKycStatus(KycStatus.APPROVED.getStatusId());

    String userId = "";
    Long userIdType = 0L;

    if (azimutAccounts.size() == 1 && azimutAccounts.get(0) != null) {
      userId = azimutAccounts.get(0).getAzId();
      userIdType = azimutAccounts.get(0).getAzIdType();
      UserType userType = this.getUserTypeByTeacomputerId(userIdType);
      if (userType == null) {
        userIdType = this.userTypeService.addUserType(userIdType).getId();
      }
      user.setUserType(userType);
      user.setUserId(userId);
    }

    MyLogger.info("Old user being saved::::::::::::::::::::::" + user.toString());
    this.userService.save(user);
    setOldUserData(user);
    return user;
  }

  public void setOldUserData(User user) {
    try {
      var oldUsers = userService.findDeletedByPhoneNumber(user.getCountryPhoneCode(),
          user.getPhoneNumber());
      if (oldUsers != null && !oldUsers.isEmpty()) {
        var oldUser = oldUsers.get(oldUsers.size() - 1);
        var signDr = digitalRegistryService.getLastDigitalRegistry(oldUser.getId(),
            DigitalRegistryAction.SIGN_CONTRACT);
        if (signDr == null) {
          for (User otherOldUser : oldUsers) {
            if (NumberUtility.areLongValuesMatching(oldUser.getId(), otherOldUser.getId()))
              continue;
            signDr = digitalRegistryService.getLastDigitalRegistry(otherOldUser.getId(),
                DigitalRegistryAction.SIGN_CONTRACT);
            if (signDr != null) {
              oldUser = otherOldUser;
              break;
            }
          }
        }
        if (oldUser.getUserStep() < 11 && BooleanUtility.isTrue(user.getIsOld()) && signDr == null) {
          MyLogger.info("Old user set less than 11! Contract not signed");
          return;
        }
        if (user.getDeviceId() == null) {
          user.setDeviceId(oldUser.getDeviceId());
          user.setDeviceName(oldUser.getDeviceName());
        }
        if (user.getLanguage() == null) {
          user.setLanguage(oldUser.getLanguage());
        }
        user.setVerificationPercentage(oldUser.getVerificationPercentage());
        user.setCountryCode(oldUser.getCountryCode());
        user.setLastSolvedPageId(oldUser.getLastSolvedPageId());
        user.setNextPageId(oldUser.getNextPageId());
        user.setUserStep(oldUser.getUserStep());
        user.setContractMap(oldUser.getContractMap());
        user.setFirstName(oldUser.getFirstName());
        user.setLastName(oldUser.getLastName());
        user.setCountry(oldUser.getCountry());
        user.setCity(oldUser.getCity());
        user.setOtherIdType(oldUser.getOtherIdType());
        user.setOtherUserId(oldUser.getOtherUserId());
        user.setOtherNationality(oldUser.getOtherNationality());
        user.setOtherUserIdType(oldUser.getOtherUserIdType());
        user.setDateOfIdExpiry(oldUser.getDateOfIdExpiry());
        user.setIdData(oldUser.getIdData());
        user.setCso(oldUser.getCso());
        user.setNtra(oldUser.getNtra());
        user.setAml(oldUser.getAml());
        user.setUuid(oldUser.getUuid());
        user.setFraStoreId(oldUser.getFraStoreId());
        user.setFraCompanyStoreId(oldUser.getFraCompanyStoreId());
        user.setDateOfBirth(oldUser.getDateOfBirth());
        user.setTeacomputersAddressAr(oldUser.getTeacomputersAddressAr());
        user.setTeacomputersAddressEn(oldUser.getTeacomputersAddressEn());
        user.setTeacomputersCityId(oldUser.getTeacomputersCityId());
        user.setTeacomputersCountryId(oldUser.getTeacomputersCountryId());
        user.setTeacomputersClientaml(oldUser.getTeacomputersClientaml());
        user.setTeacomputersIssueCityId(oldUser.getTeacomputersIssueCityId());
        user.setTeacomputersIssueCountryId(oldUser.getTeacomputersIssueCountryId());
        user.setGenderId(oldUser.getGenderId());
        user.setTeacomputersOccupation(oldUser.getTeacomputersOccupation());
        user.setLivenessChecked(oldUser.getLivenessChecked());
        user.setSolvedPages(oldUser.getSolvedPages());
        user.setIsOld(oldUser.getIsOld());
        user.setKycStatus(oldUser.getKycStatus());
        user.setIsVerified(oldUser.getIsVerified());
        if (NumberUtility.areIntegerValuesMatching(user.getKycStatus(), KycStatus.REJECTED.getStatusId()))
          user.setIsVerified(false);
        user.setTeacomputersNationalityId(oldUser.getTeacomputersNationalityId());
        user.setDateOfRelease(oldUser.getDateOfRelease());
        user.setIsSynchronized(oldUser.getIsSynchronized());
        user.setPdfPath(oldUser.getPdfPath());
        user.setPdfSignedAt(oldUser.getPdfSignedAt());
        if (StringUtility.stringsMatch(user.getEmailAddress(), oldUser.getEmailAddress())
            && BooleanUtility.isTrue(oldUser.getIsEmailVerified())) {
          user.setIsEmailVerified(oldUser.getIsEmailVerified());
        }
        if (BooleanUtility.isTrue(oldUser.getIsEmailVerified()) && user.getEmailAddress() == null) {
          user.setIsEmailVerified(oldUser.getIsEmailVerified());
          user.setEmailAddress(oldUser.getEmailAddress());
        }
        if (user.getUserId() == null) {
          user.setUserId(oldUser.getUserId());
          user.setUserType(oldUser.getUserType());
        }
        if (user.getNickName() == null)
          user.setNickName(oldUser.getNickName());
        user.setEnrollApplicantId(oldUser.getEnrollApplicantId());
        user.setGateIdTask(oldUser.getGateIdTask());
        user.setEnrollRequestId(oldUser.getEnrollRequestId());
        user.setReferralCode(oldUser.getReferralCode());
        user.setSignedPdf(oldUser.getSignedPdf());
        userService.save(user);

        // userLocations
        var location = userLocationService.findByUserId(oldUser.getId());
        if (location == null) {
          for (User otherOldUser : oldUsers) {
            if (NumberUtility.areLongValuesMatching(oldUser.getId(), otherOldUser.getId()))
              continue;
            location = userLocationService.findByUserId(otherOldUser.getId());
            if (location != null)
              break;
          }
        }
        if (location != null) {
          UserLocation locationCopy = new UserLocation();
          locationCopy.setLongt(location.getLongt());
          locationCopy.setLat(location.getLat());
          locationCopy.setUserId(user.getId());
          userLocationService.addUserLocation(locationCopy);
        }

        // userImages
        Integer imageTypes[] = { UserImageType.FRONT_IMAGE.getTypeId(), UserImageType.BACK_IMAGE.getTypeId(),
            UserImageType.PASSPORT_IMAGE.getTypeId(), UserImageType.STRAIGHT.getTypeId() };
        var userImages = this.userImageService.getUserImagesByUserAndTypes(oldUser.getId(), imageTypes);
        if (userImages.isEmpty()) {
          for (User otherOldUser : oldUsers) {
            if (NumberUtility.areLongValuesMatching(oldUser.getId(), otherOldUser.getId()))
              continue;
            userImages = this.userImageService.getUserImagesByUserAndTypes(otherOldUser.getId(), imageTypes);
            if (!userImages.isEmpty())
              break;
          }
        }
        if (!userImages.isEmpty()) {
          List<UserImage> userImagesCopy = new ArrayList<>();
          for (UserImage userImage : userImages) {
            UserImage userImageCopy = new UserImage();
            userImageCopy.setUserIdType(userImage.getUserIdType());
            userImageCopy.setUserPhone(userImage.getUserPhone());
            userImageCopy.setImageSubDirectory(userImage.getImageSubDirectory());
            userImageCopy.setImageType(userImage.getImageType());
            userImageCopy.setImageName(userImage.getImageName());
            userImageCopy.setUser(user);
            userImagesCopy.add(userImageCopy);
          }
          this.userImageService.saveImages(userImagesCopy);
        }

        // userOtp // sign contract deletedAt last
        var userOtp = this.userOTPService.findLatestByUserId(oldUser.getId(), OTPFunctionality.SIGN_CONTRACT);
        if (userOtp == null) {
          for (User otherOldUser : oldUsers) {
            if (NumberUtility.areLongValuesMatching(oldUser.getId(), otherOldUser.getId()))
              continue;
            userOtp = userOTPService.findLatestByUserId(otherOldUser.getId(), OTPFunctionality.SIGN_CONTRACT);
            if (userOtp != null)
              break;
          }
        }
        if (userOtp != null) {
          UserOTP userOtpCopy = new UserOTP();
          userOtpCopy.setUserPhone(userOtp.getUserPhone());
          userOtpCopy.setOtp(userOtp.getOtp());
          userOtpCopy.setFunctionality(userOtp.getFunctionality());
          userOtpCopy.setOtpMethod(userOtp.getOtpMethod());
          userOtpCopy.setSessionInfo(userOtp.getSessionInfo());
          userOtpCopy.setContractType(userOtp.getContractType());
          userOtpCopy.setAssessmentId(userOtp.getAssessmentId());
          userOtpCopy.setCreatedAt(userOtp.getCreatedAt());
          userOtpCopy.setUserId(user.getId());
          userOTPService.save(userOtpCopy);
        }

        // Digital Registry
        var locationDr = digitalRegistryService.getLastDigitalRegistry(oldUser.getId(),
            DigitalRegistryAction.UPDATE_LOCATION);
        if (locationDr == null) {
          for (User otherOldUser : oldUsers) {
            if (NumberUtility.areLongValuesMatching(oldUser.getId(), otherOldUser.getId()))
              continue;
            locationDr = digitalRegistryService.getLastDigitalRegistry(otherOldUser.getId(),
                DigitalRegistryAction.UPDATE_LOCATION);
            if (locationDr != null)
              break;
          }
        }
        if (locationDr != null) {
          var newLocationDr = new DigitalRegistry();
          newLocationDr.setAction(locationDr.getAction());
          newLocationDr.setCreatedAt(locationDr.getCreatedAt());
          newLocationDr.setIp(locationDr.getIp());
          newLocationDr.setLatitude(locationDr.getLatitude());
          newLocationDr.setLongitude(locationDr.getLongitude());
          newLocationDr.setOtp(locationDr.getOtp());
          newLocationDr.setSessionId(locationDr.getSessionId());
          newLocationDr.setUser(user);
          digitalRegistryService.saveDigitalRegistryRecord(newLocationDr);
        }
        if (signDr != null) {
          var newSignDr = new DigitalRegistry();
          newSignDr.setAction(signDr.getAction());
          newSignDr.setCreatedAt(signDr.getCreatedAt());
          newSignDr.setIp(signDr.getIp());
          newSignDr.setLatitude(signDr.getLatitude());
          newSignDr.setLongitude(signDr.getLongitude());
          newSignDr.setOtp(signDr.getOtp());
          newSignDr.setSessionId(signDr.getSessionId());
          newSignDr.setUser(user);
          digitalRegistryService.saveDigitalRegistryRecord(newSignDr);
        }

        // userAnswers // deletedAt last
        var userAnswers = this.userAnswerSubmissionService.getDeletedAnswersByUserId(oldUser.getId());
        if (userAnswers.isEmpty()) {
          for (User otherOldUser : oldUsers) {
            if (NumberUtility.areLongValuesMatching(oldUser.getId(), otherOldUser.getId()))
              continue;
            userAnswers = userAnswerSubmissionService.getDeletedAnswersByUserId(otherOldUser.getId());
            if (!userAnswers.isEmpty())
              break;
          }
        }
        if (!userAnswers.isEmpty()) {
          List<UserAnswer> userAnswersCopy = new ArrayList<>();
          for (UserAnswer userAnswer : userAnswers) {
            UserAnswer userAnswerCopy = new UserAnswer();
            userAnswerCopy.setQuestionId(userAnswer.getQuestionId());
            userAnswerCopy.setAnswerValue(userAnswer.getAnswerValue());
            userAnswerCopy.setAnswerType(userAnswer.getAnswerType());
            userAnswerCopy.setCountryPhoneCode(userAnswer.getCountryPhoneCode());
            userAnswerCopy.setDocumentName(userAnswer.getDocumentName());
            userAnswerCopy.setDocumentUrl(userAnswer.getDocumentUrl());
            userAnswerCopy.setRelatedAnswerId(userAnswer.getRelatedAnswerId());
            userAnswerCopy.setDocumentSize(userAnswer.getDocumentSize());
            userAnswerCopy.setDocumentSubDirectory(userAnswer.getDocumentSubDirectory());
            userAnswerCopy.setAnswerId(userAnswer.getAnswerId());
            userAnswerCopy.setPageId(userAnswer.getPageId());
            userAnswerCopy.setCountryCode(userAnswer.getCountryCode());
            userAnswerCopy.setCreatedAt(userAnswer.getCreatedAt());
            userAnswerCopy.setUserId(user.getId());
            userAnswerCopy.setDeletedAt(null);
            userAnswersCopy.add(userAnswerCopy);
          }
          userAnswerSubmissionService.submitAnswers(userAnswersCopy);
        }

        // Reviews
        var reviews = reviewService.getAllNonDeletedReviewsByUserId(oldUser.getId());
        if (reviews.isEmpty()) {
          for (User otherOldUser : oldUsers) {
            if (NumberUtility.areLongValuesMatching(oldUser.getId(), otherOldUser.getId()))
              continue;
            reviews = reviewService.getAllNonDeletedReviewsByUserId(otherOldUser.getId());
            if (!reviews.isEmpty())
              break;
          }
        }
        if (!reviews.isEmpty()) {
          List<Review> userReviewsCopy = new ArrayList<>();
          for (Review review : reviews) {
            Review reviewCopy = new Review();
            reviewCopy.setAccountId(review.getAccountId());
            reviewCopy.setActionMaker(review.getActionMaker());
            reviewCopy.setComment(review.getComment());
            reviewCopy.setCreatedAt(review.getCreatedAt());
            reviewCopy.setMandatoryQuestion(review.getMandatoryQuestion());
            reviewCopy.setPageId(review.getPageId());
            reviewCopy.setPageOrder(review.getPageOrder());
            reviewCopy.setQuestionId(review.getQuestionId());
            reviewCopy.setReason(review.getReason());
            reviewCopy.setResult(review.getResult());
            reviewCopy.setUpdatedAt(review.getUpdatedAt());
            reviewCopy.setUserId(user.getId());
            userReviewsCopy.add(reviewCopy);
          }
          reviewService.submitReviews(userReviewsCopy);
        }
      }
    } catch (Exception exception) {
      MyLogger.logStackTrace(exception);
      MyLogger.info("No deleted user found::::::");
    }
  }

  public UserType getUserTypeByTeacomputerId(Long teacomputerId) throws Exception {
    try {
      return this.userTypeService.getUserTypeByTeacomputerId(teacomputerId);
    } catch (Exception exception) {
      this.exceptionHandler.getNullIfNonExistent(exception);
      return null;
    }

  }

  public UserInteraction addUserInteraction(String countryCode, String countryPhoneCode, String phoneNumber,
      String email, String body, Integer type, MultipartFile file) throws BusinessException {
    UserInteraction userInteraction = new UserInteraction();
    if (file != null) {
      try {
        String url = "";
        String subDirectory = DateUtility.getCurrentDayMonthYear();
        url = this.storageService
            .uploadFile(file, false, this.configProperties.getBlobUserInteractionPath(), subDirectory, true).getUrl();
        userInteraction.setPrivateUrl(url);
        userInteraction.setImageName(file.getOriginalFilename());
        userInteraction.setImagePath(subDirectory);
      } catch (BusinessException | IOException e) {
        MyLogger.info("Failed to upload the file::::");
        MyLogger.logStackTrace(e);
      }
    }
    userInteraction.setCountryCode(countryCode);
    userInteraction.setCountryPhoneCode(countryPhoneCode);
    userInteraction.setPhoneNumber(phoneNumber);
    userInteraction.setEmail(email);
    userInteraction.setBody(body);
    userInteraction.setType(type);
    userInteraction.setCreatedAt(new Date());
    userInteraction.setUpdatedAt(new Date());
    try {
      return this.userInteractionService.addUserInteraction(userInteraction);
    } catch (Exception exception) {
      throw this.exceptionHandler.handleBusinessException(exception, ErrorCode.OPERATION_NOT_PERFORMED);
    }
  }

  public List<UserInteraction> getUserInteractions(Integer type) throws BusinessException {
    try {
      return this.userInteractionService.getUserInteractionsByType(type);
    } catch (Exception exception) {
      this.exceptionHandler.getNullIfNonExistent(exception);
      return new ArrayList<UserInteraction>();
    }
  }

  public PaginatedEntity<BusinessNotification> getUserNotifications(BusinessUser tokenizedBusinessUser,
      GetUserNotificationsDto businessUser, String language) throws BusinessException {
    DatabaseConditions databaseConditions = new DatabaseConditions();
    BusinessSearchCriteria businessSearchCriteria = new BusinessSearchCriteria();
    Sort sort = Sort.by("createdAt").descending();
    Integer pageNumber = businessUser.getPageNumber() - 1;
    Integer pageSize = Integer.parseInt(this.configProperties.getPageSize());
    businessSearchCriteria.setPageNumber(pageNumber);
    businessSearchCriteria.setPageSize(pageSize);
    databaseConditions.setPageRequest(PageRequest.of(pageNumber, pageSize, sort));
    return notificationMapper.convertBasicPageToBusinessPage(
        this.notificationCenterService.findAllByUserId(tokenizedBusinessUser.getId(), databaseConditions),
        businessSearchCriteria, language);

  }

  public List<BusinessNotification> getUnreadNotifications(BusinessUser tokenizedBusinessUser, String language) {
    List<Notification> notifications = this.notificationCenterService.findUnreadByUserId(tokenizedBusinessUser.getId());
    return notificationMapper.convertBasicListToBusinessList(notifications, language);
  }

  public void readNotification(Long id) {
    this.notificationCenterService.readNotification(id);
  }

  public void upsertOTP(BusinessUserOTP responseBusinessUserOTP, ErrorCode errorCode, boolean save)
      throws BusinessException {
    try {
      this.userOTPService.save(this.userOtpMapper.convertBusinessUnitToBasicUnit(responseBusinessUserOTP, save));
    } catch (Exception exception) {
      throw this.exceptionHandler.handleBusinessException(exception, errorCode);
    }

  }

  public void deleteOTP(BusinessUserOTP responseBusinessUserOTP) {
    this.userOTPService.deleteOTP(this.userOtpMapper.convertBusinessUnitToBasicUnit(responseBusinessUserOTP, false));
  }

  public BusinessUserOTP findOTPByUserId(Long userId, OTPFunctionality functionality) {
    return this.userOtpMapper
        .convertBasicUnitToBusinessUnit(this.userOTPService.findLatestByUserId(userId, functionality));
  }

  public BusinessUserOTP findLatestOTPByPhone(String phone) {
    return this.userOtpMapper
        .convertBasicUnitToBusinessUnit(this.userOTPService.findLatestByPhone(phone));
  }

  public boolean validateOTP(String currentOtp, String userPhone) {
    BusinessUserOTP validOtp = this.findLatestOTPByPhone(userPhone);

    if (validOtp == null)
      return false;

    return validOtp.getOtp().equals(currentOtp);
  }

  public void deleteOldUserOTPs(Long userId) {
    this.userOTPService.deleteOldUserOTPs(userId);
  }

  public Map<String, String> generateUserMap(Integer nextStep, Long firstPageId, Integer kycStatus,
      Boolean isVerified) {
    Map<String, String> values = new HashMap<String, String>();

    if (nextStep != null)
      values.put(StringUtility.NEXT_STEP_FIELD, String.valueOf(nextStep));

    if (nextStep != null && NumberUtility.areIntegerValuesMatching(nextStep, UserStep.ID_IMAGES.getStepId())) {
      values.put(StringUtility.LIVENESS_CHECK_FIELD, Boolean.FALSE.toString());
    }

    if (firstPageId != null)
      values.put(StringUtility.FIRST_PAGE_ID_FIELD, String.valueOf(firstPageId));

    if (kycStatus != null)
      values.put(StringUtility.KYC_STATUS_FIELD, String.valueOf(kycStatus));

    if (isVerified != null)
      values.put(StringUtility.IS_VERIFIED_FIELD, String.valueOf(isVerified));

    return values;
  }

  public BusinessUserOTP findLatestOTP(Long userId) {
    return userOtpMapper.convertBasicUnitToBusinessUnit(this.userOTPService.findLatest(userId));
  }

  public void checkIfUserIsDeleted(CheckIfUserDeletedDto businessUser) throws BusinessException {
    var countryPhoneCode = businessUser.getCountryPhoneCode();
    var phoneNumber = businessUser.getPhoneNumber();
    var provider = businessUser.getProvider();
    var providerId = businessUser.getProviderId();
    User deletedUser = new User();
    try {
      MyLogger.info("Looking for the deleted user:::");
      deletedUser = this.userService.findDeletedUserByPhoneNumberOrBySocialCredentials(countryPhoneCode, phoneNumber,
          provider, providerId);
    } catch (Exception exception) {
      MyLogger.info("Exception occured while checking for the deleted user existence:::");
      if (!(this.exceptionHandler.isExceptionOfTypeEntityNotFoundException(exception)
          || this.exceptionHandler.isExceptionOfTypeNoResultException(exception)
          || this.exceptionHandler.isExceptionOfTypeNoSuchElementException(exception)))
        throw exception;
    }
    if (deletedUser != null) {
      MyLogger.info("The deleted user exists");
      throw new BusinessException(ErrorCode.USER_DELETED);
    }
  }

  public User checkIfUserIsBlockedAndProceed(String username, String password, boolean login) throws BusinessException {
    User user = checkIfUserExists(username);
    if (user != null) {
      if (user.getFailedLogins() != null) {
        if (user.getFailedLogins().intValue() >= this.configProperties.getBlockageNumberOfTrialsInt()
            .intValue()) {
          throw new BusinessException(ErrorCode.USER_BLOCKED);
        }
      }
      boolean passwordVerified = login
          ? StringUtility.stringsMatch(user.getPassword(), this.encryptUserPassword(password))
          : true;
      if (passwordVerified) {
        if (login) {
          this.resetFailedLogins(user);
        }
      } else {
        this.incrementLoginFailures(user);
        throw new BusinessException(ErrorCode.USER_NOT_FOUND);
      }
    }
    return user;

  }

  public User checkIfUserExists(String username) {
    try {
      return this.userService.findByUserPhone(username);
    } catch (Exception exception) {
      MyLogger.info("User not existing::::::");
      return null;
    }
  }

  private void incrementLoginFailures(User user) {
    if (user != null) {

      if (user.getFailedLogins() == null) {
        user.setFailedLogins(1);
      } else {
        int oldValue = user.getFailedLogins();
        user.setFailedLogins(oldValue + 1);
      }
      this.userService.update(user);
    }
  }

  private void resetFailedLogins(User user) {
    if (user != null) {
      if (user.getFailedLogins() != null && !NumberUtility.areIntegerValuesMatching(user.getFailedLogins(), 0)) {
        user.setFailedLogins(0);
        this.userService.update(user);
      }
    }
  }

  public void updateDeviceLastUpdateDateUsingUserIdAndDeviceId(Long userId, String deviceId) {
    if (deviceId == null) {
      deviceId = aes.encrypt(String.valueOf(userId) + StringUtility.WEB_DEVICE);
    }
    try {
      this.userDeviceService.updateDeviceLastUpdateDateUsingUserIdAndDeviceId(userId, deviceId);
    } catch (Exception exception) {
      MyLogger.info("Failed To refresh");
      MyLogger.logStackTrace(exception);
    }
  }

  public void handleMutipleLoginsAndDeviceLogging(Long userId, BusinessUser businessUser, String deviceId,
      String deviceName, String language, boolean login) throws BusinessException {
    if (businessUser != null) {
      if (login) {
        this.preventMultipleLogins(businessUser, deviceId);
      }
      if (StringUtility.isStringPopulated(language) && (businessUser.getDeviceId() == null
          || (businessUser.getDeviceName() == null && StringUtility.isStringPopulated(deviceName))
          || !StringUtility.stringsMatch(businessUser.getLanguage(), language))) {
        var user = userService.findById(userId);
        if (user.getDeviceId() == null)
          user.setDeviceId(deviceId);
        if (user.getDeviceName() == null)
          user.setDeviceName(deviceName);
        if (StringUtility.isStringPopulated(language))
          user.setLanguage(language);
        this.userService.save(user);
      }
    }
    this.userService.updateUserLogin(userId);
    this.logDevice(userId, deviceId, deviceName);
  }

  public void preventMultipleLogins(BusinessUser businessUser, String deviceId) throws BusinessException {
    if (BooleanUtility.isTrue(businessUser.getLoggedIn())) {
      if (businessUser.getLastLogin() != null) {
        LocalDateTime now = LocalDateTime.now();
        MyLogger.info("now:::" + now.toString());
        LocalDateTime nowMinusMinutes = this.configProperties.isSummerTime()
            ? now.plusHours(1).minusMinutes(Long.valueOf(this.configProperties.getJwTokenDurationInMinutes()))
            : now.minusMinutes(Long.valueOf(this.configProperties.getJwTokenDurationInMinutes()));
        MyLogger.info("now minus minutes:::" + nowMinusMinutes.toString());
        LocalDateTime lastlogin = this.configProperties.isSummerTime()
            ? LocalDateTime.ofInstant(businessUser.getLastLogin().toInstant(), ZoneId.systemDefault()).plusHours(1)
            : LocalDateTime.ofInstant(businessUser.getLastLogin().toInstant(), ZoneId.systemDefault());
        MyLogger.info("last login:::" + lastlogin);
        // if (!StringUtility.isStringPopulated(deviceId)) {
        // MyLogger.info("deviceId empty");
        // deviceId = aes.encrypt(userId.toString() + StringUtility.WEB_DEVICE);
        // MyLogger.info("deviceId:::::" + deviceId);
        // }
        if (!StringUtility.isStringPopulated(deviceId) && nowMinusMinutes.isBefore(lastlogin)) {
          throw new BusinessException(ErrorCode.MULTIPLE_LOGINS);
        }

        List<UserDevice> userDevices = this.userDeviceService.getUserDevicesWithUserId(businessUser.getId());

        if (userDeviceListUtility.isListPopulated(userDevices)) {
          for (UserDevice userDevice : userDevices) {
            LocalDateTime deviceLogin = this.configProperties.isSummerTime()
                ? LocalDateTime.ofInstant(userDevice.getUpdatedAt().toInstant(), ZoneId.systemDefault()).plusHours(1)
                : LocalDateTime.ofInstant(userDevice.getUpdatedAt().toInstant(), ZoneId.systemDefault());

            if (nowMinusMinutes.isBefore(deviceLogin)
                && !StringUtility.stringsMatch(userDevice.getDeviceId(), deviceId)) {
              throw new BusinessException(ErrorCode.MULTIPLE_LOGINS);
            }
          }
        }
      }
    }
  }

  public void logDevice(Long userId, String deviceId, String deviceName) {
    if (!StringUtility.isStringPopulated(deviceId)) {
      MyLogger.info("deviceId empty");
      deviceId = aes.encrypt(userId.toString() + StringUtility.WEB_DEVICE);
      MyLogger.info("deviceId:::::" + deviceId);
    }
    UserDevice userDevice = this.userDeviceService.getUserDevicesWithUserIdAndDeviceIdIfExisting(userId, deviceId);
    User user = new User();
    user.setId(userId);
    this.upsertDeviceIdAudit(user, deviceId, deviceName, userDevice);
  }

}
