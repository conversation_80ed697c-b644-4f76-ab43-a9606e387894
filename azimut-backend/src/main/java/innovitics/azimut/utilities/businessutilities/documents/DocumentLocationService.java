package innovitics.azimut.utilities.businessutilities.documents;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.fileutilities.ParentStorage;

public abstract class DocumentLocationService {

	@Autowired ConfigProperties configProperties;
	
	
	public abstract boolean isType(String type);
	
	public abstract Entry<MediaType,byte[]> getFileAndExtension(ParentStorage storage,BusinessUser businessUser,String validityToken,Long documentId) throws IOException;
	
	
	Entry<MediaType,byte[]> getPopulatedSingleEntry(String fileName,byte[] bytes)
	{
		Map<MediaType,byte[]> singleEntryMap=new HashMap<MediaType,byte[]>();
		singleEntryMap.put(MediaType.valueOf(StringUtility.generateSubStringStartingFromCertainIndex(fileName, '.')),bytes);
		Map.Entry<MediaType,byte[]> entry = singleEntryMap.entrySet().iterator().next();		
		return entry;
	}
}
