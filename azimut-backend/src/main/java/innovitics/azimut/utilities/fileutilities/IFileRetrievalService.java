package innovitics.azimut.utilities.fileutilities;

import java.io.IOException;
import java.util.Map.Entry;

import org.springframework.http.MediaType;

import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.exceptions.BusinessException;

public interface IFileRetrievalService {

	
	public Entry<MediaType,byte[]> getFile(ParentStorage storage,BusinessUser businessUser,String validityToken,Long documentId,String documentType) throws ClassNotFoundException, IOException, BusinessException;
	

}
