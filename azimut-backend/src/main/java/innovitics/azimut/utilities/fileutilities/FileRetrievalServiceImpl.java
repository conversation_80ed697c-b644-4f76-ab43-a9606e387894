package innovitics.azimut.utilities.fileutilities;

import java.io.IOException;
import java.util.Map.Entry;

import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import innovitics.azimut.ApplicationContextProvider;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.utilities.businessutilities.documents.DocumentLocationService;
@Component
public class FileRetrievalServiceImpl implements IFileRetrievalService {
	
	DocumentLocationService findDocumentLocationService(String documentType) throws ClassNotFoundException, BusinessException
	{		
		DocumentLocationService documentLocationService= ApplicationContextProvider.getApplicationContext().getBeansOfType(DocumentLocationService.class)
		.entrySet()
        .stream()
        .filter(entry -> entry.getValue().isType(documentType))
        .findFirst().get().getValue();
		
		if(documentLocationService==null)
		{
			throw new ClassNotFoundException();
		}

		return documentLocationService;
	}

	@Override
	public Entry<MediaType,byte[]> getFile(ParentStorage storage,BusinessUser businessUser, String validityToken, Long documentId,String documentType) throws ClassNotFoundException, IOException, BusinessException 
	{
		return this.findDocumentLocationService(documentType).getFileAndExtension(storage,businessUser, validityToken, documentId);
	}


}
