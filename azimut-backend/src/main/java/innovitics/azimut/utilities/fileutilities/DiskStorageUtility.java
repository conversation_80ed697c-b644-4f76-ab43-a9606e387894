package innovitics.azimut.utilities.fileutilities;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.utilities.crosslayerenums.DocumentType;
import innovitics.azimut.utilities.datautilities.ArrayUtility;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class DiskStorageUtility extends ParentStorage {

  private static final String UPLOAD_PATH = "E:\\Tomcat\\webapps\\media\\";

  private static final String PATH = "/media";
  @Autowired
  ArrayUtility arrayUtility;

  @Override
  public BlobData uploadFile(MultipartFile file, boolean generateSasToken, String containerName, String subDirectory,
      boolean useOriginalFileName, Object... options) throws IOException {
    String fileName = "";
    BlobData blobData = new BlobData();
    fileName = this.generateRandomName(file);
    if (!StringUtility.isStringPopulated(subDirectory)) {
      subDirectory = DateUtility.getCurrentYearMonth();
    }
    double size = file.getSize();
    double kilobytes = (size / 1024);
    double megabytes = (kilobytes / 1024);
    byte[] bytes = file.getBytes();

    String secureSubdirectory = this.secureSubdirectory(subDirectory);

    String filePath = this.gotoFile(containerName, secureSubdirectory, fileName);
    Path path = Paths.get(UPLOAD_PATH + "\\" + containerName + "\\" + secureSubdirectory);
    Files.createDirectories(path);

    BufferedOutputStream stream = new BufferedOutputStream(new FileOutputStream(new File(filePath)));
    stream.write(bytes);
    stream.close();
    blobData.setFileName(fileName);
    blobData.setFileSize(megabytes);
    blobData.setSubDirectory(subDirectory);

    blobData.setUrl(this.generateUrl(containerName, secureSubdirectory, fileName, generateSasToken, null, options));

    return blobData;
  }

  @Override
  public String generateFileRetrievalUrl(String path, String fileName, String subDirectory, boolean generateWithToken,
      Object... options) throws IOException {
    return this.generateUrl(path, subDirectory, fileName, generateWithToken, null, options);
  }

  @Override
  public String generateFileRetrievalUrl(String path, String fileName, String subDirectory, boolean generateWithToken,
      Long tokenValidityMinutes, Object... options) throws IOException {
    return this.generateUrl(path, subDirectory, fileName, generateWithToken, tokenValidityMinutes, options);
  }

  public void copyFile(String sourceContainerName, String destinationContainerName, String subDirectory,
      String fileName, boolean generateSasToken) throws IOException {
    try {
      File src = new File(this.gotoFile(sourceContainerName, this.secureSubdirectory(subDirectory), fileName));
      Path path = Paths
          .get(UPLOAD_PATH + "\\" + destinationContainerName + "\\" + this.secureSubdirectory(subDirectory));
      Files.createDirectories(path);
      File dest = new File(this.gotoFile(destinationContainerName, this.secureSubdirectory(subDirectory), fileName));

      Files.copy(src.toPath(), dest.toPath());
    } catch (Exception exception) {
      MyLogger.info("Could not copy the file");
      MyLogger.logStackTrace(exception);
    }

  }

  @Override
  public String deleteFile(String sourceContainerName, String fileName, String subDirectory, boolean generateWithToken,
      Long tokenValidityMinutes) throws IOException {

    try {
      Path path = Paths.get(this.gotoFile(sourceContainerName, this.secureSubdirectory(subDirectory), fileName));
      Files.deleteIfExists(path);
      MyLogger.info("Deleted the file: " + fileName);
    } catch (Exception exception) {
      MyLogger.info("Could not delete the file");
      MyLogger.logStackTrace(exception);
    }
    return null;
  }

  String generateUrl(String containerName, String subDirectory, String fileName) {
    String firstPart = this.configProperties.getAppUrl() + PATH + "/" + containerName + "/";
    String secondPart = StringUtility.isStringPopulated(subDirectory) ? (subDirectory + "/" + fileName) : fileName;
    MyLogger.info("Generated URL:::" + firstPart + secondPart);
    return firstPart + secondPart;
  }

  String generateUrl(String containerName, String subDirectory, String fileName, boolean withSasToken,
      Long tokenValidityInMinutes, Object... options) {
    MyLogger.info("Options:::" + options.toString());
    StringBuffer stringBuffer = new StringBuffer();

    if (this.arrayUtility.isArrayPopulated(options)) {
      stringBuffer.append(this.configProperties.getAppUrl() + "/api/user/getFile");
      String documentId = (String) options[0];
      String documentType = ((DocumentType) options[1]).getType();
      stringBuffer.append("?documentId=" + this.encrypt(documentId));
      stringBuffer.append("&documentType=" + this.encrypt(documentType));
      stringBuffer.append("&tokenized=" + withSasToken);
      if (withSasToken) {
        stringBuffer.append("&token=" + this.generateToken(documentId, documentType,
            tokenValidityInMinutes != null ? String.valueOf(tokenValidityInMinutes)
                : this.configProperties.getSasTokenDurationInMinutes()));
      }
    } else {
      stringBuffer.append(this.configProperties.getAppUrl() + "/api/user/getTempFile");
      stringBuffer.append("?path=" + this.encrypt(this.gotoFile(containerName, subDirectory, fileName)));
      stringBuffer.append("&fileName=" + this.encrypt(fileName));
      if (withSasToken) {
        stringBuffer.append("&token=" + this.generateToken(containerName, subDirectory, fileName,
            tokenValidityInMinutes != null ? String.valueOf(tokenValidityInMinutes)
                : this.configProperties.getSasTokenDurationInMinutes()));
      }

    }

    return stringBuffer.toString();

  }

  String gotoFile(String containerName, String subDirectory, String fileName) {

    String value = StringUtility.isStringPopulated(subDirectory)
        ? UPLOAD_PATH + containerName + "\\" + subDirectory + "\\" + fileName
        : UPLOAD_PATH + containerName + "\\" + fileName;
    String securedValue = this.secureSubdirectory(value);
    MyLogger.info("Value:::::::::" + securedValue);
    return securedValue;
  }

  String secureSubdirectory(String subDirectory) {
    return /* this.hash(this.manipulateSubdirectory(subDirectory)) */ /* subDirectory */this
        .manipulateSubdirectory(subDirectory);
  }

  String manipulateSubdirectory(String subdirectory) {
    return StringUtility.isStringPopulated(subdirectory) ? subdirectory.replaceAll("\\/", "\\\\") : null;

  }

  @Override
  public String generateLocalPath(String path, String fileName, String subDirectory, boolean generateWithToken,
      Long tokenValidityMinutes) {
    return this.gotoFile(path, this.secureSubdirectory(subDirectory), fileName);
  }

  public String generateToken(String path, String subDirectory, String fileName, String expiryTime) {
    return fileTokenizationUtility
        .generateTokenUsingFileLocation(fileName, this.gotoFile(path, subDirectory, fileName), expiryTime)
        .getTokenString();
  }

  public String generateToken(String documentId, String documentType, String expiryTime) {
    return fileTokenizationUtility.generateTokenUsingFileLocation(documentId, documentType, expiryTime)
        .getTokenString();
  }

  @Override
  public void copyFileAbsolutePath(String sourcePath, String destinationPath) throws IOException {
    try {
      File src = new File(sourcePath);
      File dest = new File(destinationPath);
      Files.copy(src.toPath(), dest.toPath());
    } catch (Exception exception) {
      MyLogger.info("Could not copy the file");
      MyLogger.logStackTrace(exception);
    }

  }

  @Override
  public Entry<MediaType, byte[]> getFileWithAbsolutePath(String filePath) throws IOException {
    {
      File file = new File(filePath);

      byte[] bytes = new byte[(int) file.length()];

      FileInputStream fileInputStream = null;
      try {
        fileInputStream = new FileInputStream(file);
        fileInputStream.read(bytes);
      } finally {
        if (fileInputStream != null) {
          fileInputStream.close();
        }
      }
      Map<MediaType, byte[]> singleEntryMap = new HashMap<MediaType, byte[]>();
      singleEntryMap.put(StringUtility.findMediaType(file.getName()), bytes);
      Map.Entry<MediaType, byte[]> entry = singleEntryMap.entrySet().iterator().next();
      return entry;
    }
  }

  @Override
  public BlobData uploadFileToBlob(InputStream inputStream, boolean generateSasToken, String containerName,
      String subDirectory, String extension) throws IOException, BusinessException {
    /*
     * BlobData blobData=new BlobData();
     * String secureSubdirectory=this.secureSubdirectory(subDirectory);
     * String fileName=this.generateRandomName(extension);
     * Path path =
     * Paths.get(UPLOAD_PATH+"\\"+containerName+"\\"+this.secureSubdirectory(
     * subDirectory));
     * Files.createDirectories(path);
     * File dest = new
     * File(this.gotoFile(containerName,this.secureSubdirectory(subDirectory),
     * fileName));
     * FileOutputStream fileOutputStream = new FileOutputStream(dest);
     * IOUtils.copy(inputStream, fileOutputStream);
     * blobData.setSubDirectory(subDirectory);
     * blobData.setFileName(fileName);
     * blobData.setUrl(this.generateFileRetrievalUrl(containerName, fileName,
     * secureSubdirectory, generateSasToken));
     * return blobData;
     */
    return this.uploadFileToBlob(this.generateRandomName(extension), inputStream, generateSasToken, containerName,
        subDirectory, extension);
  }

  @Override
  public void emptySubDirectory(String containerName, String subDirectory) throws IOException, BusinessException {
    String path = UPLOAD_PATH + "\\" + containerName + "\\" + this.secureSubdirectory(subDirectory);
    FileUtils.deleteQuietly(new File(path));

  }

  @Override
  public BlobData uploadFileToBlob(String fileName, InputStream inputStream, boolean generateSasToken,
      String containerName, String subDirectory, String extension) throws IOException, BusinessException {
    BlobData blobData = new BlobData();
    StringBuffer fileNameWithExtension = new StringBuffer(fileName);
    fileNameWithExtension.append(".");
    fileNameWithExtension.append(extension);
    String newFileName = fileNameWithExtension.toString();
    String secureSubdirectory = this.secureSubdirectory(subDirectory);
    Path path = Paths.get(UPLOAD_PATH + "\\" + containerName + "\\" + this.secureSubdirectory(subDirectory));
    Files.createDirectories(path);
    File dest = new File(this.gotoFile(containerName, this.secureSubdirectory(subDirectory), newFileName));
    FileOutputStream fileOutputStream = new FileOutputStream(dest);
    IOUtils.copy(inputStream, fileOutputStream);
    blobData.setSubDirectory(subDirectory);
    blobData.setFileName(newFileName);
    blobData.setUrl(this.generateFileRetrievalUrl(containerName, newFileName, secureSubdirectory, generateSasToken));
    return blobData;
  }

  /**
   * Uploads a file to the public folder
   * 
   * @param file             The MultipartFile to upload
   * @param publicFolderPath The absolute path to the public folder
   * @return The path where the file was saved
   * @throws IOException
   */
  public String uploadFileToPublicFolder(MultipartFile file, String publicFolderName) throws IOException {
    try {
      String publicFolderPath = this.getPublicFolderPath(publicFolderName);

      // Create the file path
      String fileName = file.getOriginalFilename();
      Path filePath = Paths.get(publicFolderPath, fileName);

      // Handle file name conflicts by appending a timestamp
      if (Files.exists(filePath)) {
        String fileNameWithoutExtension = fileName.contains(".") ? fileName.substring(0, fileName.lastIndexOf("."))
            : fileName;
        String extension = fileName.contains(".") ? fileName.substring(fileName.lastIndexOf(".")) : "";
        String uniqueFileName = fileNameWithoutExtension + "_" + System.currentTimeMillis() + extension;
        filePath = Paths.get(publicFolderPath, uniqueFileName);
      }

      // Save the file
      File convertFile = new File(filePath.toString());
      try (FileOutputStream fos = new FileOutputStream(convertFile)) {
        fos.write(file.getBytes());
      }

      return filePath.toString();
    } catch (IOException e) {
      MyLogger.info("Error uploading file to public folder: " + e.getMessage());
      MyLogger.logStackTrace(e);
      throw e;
    } catch (Exception e) {
      MyLogger.info("Unexpected error uploading file to public folder: " + e.getMessage());
      MyLogger.logStackTrace(e);
      throw new IOException("Failed to upload file to public folder", e);
    }
  }

  /**
   * Constructs the full path for the public folder relative to UPLOAD_PATH
   * 
   * @param publicFolderName The name of the public folder
   * @return The full path to the public folder
   */
  public String getPublicFolderPath(String publicFolderName) {
    String publicFolderPath = UPLOAD_PATH + "\\" + publicFolderName;
    return this.secureSubdirectory(publicFolderPath);
  }
}
