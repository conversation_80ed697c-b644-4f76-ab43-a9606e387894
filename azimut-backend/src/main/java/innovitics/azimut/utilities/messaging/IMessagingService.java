package innovitics.azimut.utilities.messaging;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.utilities.crosslayerenums.Messages;

public interface IMessagingService {

  void send(MessagingUtility messagingUtility, BusinessUser to, Messages message, String language,
      Object... options)
      throws BusinessException;

  void send(MessagingUtility messagingUtility, BusinessUser to, String message, String messageAr, String title,
      String titleAr, String language, Object... options) throws BusinessException;

  void send(MessagingUtility messagingUtility, BusinessAdminUser to, String message, String messageAr, String title,
      String titleAr, String language, Object... options) throws BusinessException;

}
