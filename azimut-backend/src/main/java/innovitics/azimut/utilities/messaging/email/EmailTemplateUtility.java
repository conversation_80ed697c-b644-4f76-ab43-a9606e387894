package innovitics.azimut.utilities.messaging.email;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;
import org.springframework.ui.freemarker.FreeMarkerTemplateUtils;

import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateException;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.utilities.exceptionhandling.ErrorCode;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class EmailTemplateUtility extends EmailUtility {

  @Autowired
  private Configuration configuration;

  public void sendOfflineMailWithAttachment(String firstName, String lastName, String to, String subject,
      ByteArrayOutputStream byteArrayOutputStream) throws BusinessException {

    MyLogger.info("Inside the template Utility::");
    MimeMessage message = this.emailSender.createMimeMessage();
    Map<String, Object> model = new HashMap<>();
    model.put("firstName", firstName);
    model.put("lastName", lastName);
    model.put("arabic1", " يرجى العثور على العقد المرفق. يرجى توقيع العقد وإعادته إلينا.");
    model.put("arabic2", " عنواننا هو: القرية الذكية | مبنى B 16 | صندوق بريد 12577 | الجيزة، مصر");
    try {
      MimeMessageHelper helper = new MimeMessageHelper(message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
          StandardCharsets.UTF_8.name());
      Template template = configuration.getTemplate("email-template-offline.ftl");
      String html = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
      helper.setTo(to);
      helper.setText(html, true);
      helper.setSubject(subject);
      helper.setFrom(this.configProperties.getMailFrom());
      helper.setBcc(this.configProperties.getAzimutBccMail().split(","));
      ClassPathResource logo = new ClassPathResource("logo.svg");
      ClassPathResource image = new ClassPathResource("image001.png");
      helper.addInline("logo", logo);
      helper.addInline("image", image);

      this.attach(helper, byteArrayOutputStream);
      emailSender.send(message);
      MyLogger.info("Email was sent to: " + to);
    } catch (MessagingException | IOException | TemplateException exception)
    // catch (MessagingException exception)
    {
      MyLogger.logStackTrace(exception);
      throw new BusinessException(ErrorCode.FAILED_TO_SEND_EMAIL);
    }
  }

  public void sendMailWithAttachment(String firstName, String lastName, String to, String subject, String body,
      ByteArrayOutputStream byteArrayOutputStream, boolean sendCopyToAzimut) throws BusinessException {

    MyLogger.info("Inside the template Utility::");
    MimeMessage message = this.emailSender.createMimeMessage();
    Map<String, Object> model = new HashMap<>();
    model.put("firstName", firstName);
    model.put("lastName", lastName);
    try {
      MimeMessageHelper helper = new MimeMessageHelper(message, MimeMessageHelper.MULTIPART_MODE_MIXED_RELATED,
          StandardCharsets.UTF_8.name());
      Template template = configuration.getTemplate("email-template.ftl");
      String html = FreeMarkerTemplateUtils.processTemplateIntoString(template, model);
      helper.setTo(to);
      helper.setText(html, true);
      helper.setSubject(subject);
      helper.setFrom(this.configProperties.getMailFrom());
      helper.setBcc(this.configProperties.getAzimutBccMail().split(","));
      ClassPathResource logo = new ClassPathResource("logo.svg");
      ClassPathResource image = new ClassPathResource("image001.png");
      helper.addInline("logo", logo);
      helper.addInline("image", image);

      this.attach(helper, byteArrayOutputStream);
      emailSender.send(message);
      MyLogger.info("Email was sent to: " + to);
    } catch (MessagingException | IOException | TemplateException exception)
    // catch (MessagingException exception)
    {
      MyLogger.logStackTrace(exception);
      throw new BusinessException(ErrorCode.FAILED_TO_SEND_EMAIL);
    }
  }

  @Override
  protected void send(BusinessUser to, String message, String title, String messageAr, String titleAr,
      String language,
      Object... options) throws BusinessException {
    if (options != null && options.length > 0 && options[0] != null) {
      try {
        this.sendMailWithAttachment(to.getFirstName(), to.getLastName(), to.getEmailAddress(), title, message,
            (ByteArrayOutputStream) options[0], (Boolean) options[1]);
      } catch (MailException ex) {
        MyLogger.error("Email could not be sent::::::::::");
        MyLogger.logStackTrace(ex);
        throw new BusinessException(ErrorCode.FAILED_TO_SEND_EMAIL);
      }
    } else {
      sendSimpleMessage(to.getEmailAddress(), title, message);
    }
  }
}
