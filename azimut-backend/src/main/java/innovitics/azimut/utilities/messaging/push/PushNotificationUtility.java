package innovitics.azimut.utilities.messaging.push;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.google.firebase.messaging.ApnsConfig;
import com.google.firebase.messaging.Aps;
import com.google.firebase.messaging.ApsAlert;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.FirebaseMessagingException;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.Notification;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.repositories.user.UserDynamicRepository;
import innovitics.azimut.services.NotificationCenterService;
import innovitics.azimut.utilities.crosslayerenums.Messages;
import innovitics.azimut.utilities.datautilities.ArrayUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.logging.MyLogger;
import innovitics.azimut.utilities.messaging.MessagingUtility;

@Service
public class PushNotificationUtility extends MessagingUtility {

  @Autowired
  NotificationCenterService notificationCenterService;
  @Autowired
  ArrayUtility arrayUtility;
  @Autowired
  FireBaseInitializer fireBaseInitializer;
  private final FirebaseMessaging firebaseMessaging;

  @Autowired
  private UserDynamicRepository userDynamicRepository;

  public PushNotificationUtility(FirebaseMessaging firebaseMessaging) {
    this.firebaseMessaging = firebaseMessaging;
  }

  public String sendNotification(String title, String body, String language, String token, String type,
      Object... options)
      throws FirebaseMessagingException, IOException {
    String response = "";
    Notification notification = Notification
        .builder()
        .setTitle(title)
        .setBody(body)
        .build();
    if (StringUtility.isStringPopulated(token)) {
      // Create a message builder
      Message.Builder messageBuilder = Message.builder().setToken(token).setNotification(notification);
      ApsAlert apsAlert = ApsAlert.builder()
          .setTitle(title)
          .setBody(body)
          .build();
      Aps aps = Aps.builder()
          .setAlert(apsAlert) // <--- Set alert as a dictionary (ApsAlert object)
          .setSound("default") // <--- Set your sound here ("default" or custom filename)
          .build();
      ApnsConfig apnsConfig = ApnsConfig.builder()
          .putHeader("apns-priority", "10")
          .setAps(aps)
          .build();
      messageBuilder.setApnsConfig(apnsConfig);
      // Add additional fields if options are provided
      if (arrayUtility.isArrayPopulated(options)) {
        MyLogger.info("Full:::");
        messageBuilder.putAllData(this.generateFields(options));
      } else {
        MyLogger.info("Empty:::");
      }
      // Build and send the message
      Message message = messageBuilder.build();
      response = firebaseMessaging.send(message);
      MyLogger.info("Push Notification to token: " + token + " Response::" + response);
    }
    return response;
  }

  @Override
  protected void send(BusinessAdminUser to, String message, String title, String messageAr, String titleAr,
      String language,
      Object... options) {
    // Not implemented
  }

  private void sendInternal(BusinessUser to, String message, String title, String messageAr, String titleAr,
      String language, String type, Object... options) {
    try {

      if (StringUtility.isStringPopulated(to.getMessagingToken()))
        this.sendNotification(StringUtility.determineOuputLanguage(title, titleAr, language),
            StringUtility.determineOuputLanguage(message, messageAr, language), language, to.getMessagingToken(),
            type, options);
    } catch (FirebaseMessagingException fme) {
      if (fme.getHttpResponse().getStatusCode() == 404) {
        MyLogger.info("Requested entity not found. Notification failed.");
        userDynamicRepository.removeMessagingToken(to.getId());
      } else {
        MyLogger.info("Failed to push Notification: " + fme.getMessage());
        MyLogger.logStackTrace(fme);
      }
    } catch (IOException exception) {
      MyLogger.info("Failed to push Notification");
      MyLogger.logStackTrace(exception);
    }
  }

  @Override
  protected void send(BusinessUser to, String message, String title, String messageAr, String titleAr,
      String language,
      Object... options) {
    sendInternal(to, message, title, messageAr, titleAr, language, "general", options);
    Map<String, String> hashMap = new HashMap<String, String>();
    if (arrayUtility.isArrayPopulated(options) && options.length > 1) {
      hashMap = generateFields(options);
      Integer navigation = Integer.parseInt(hashMap.get("navigation"));
      Integer liveNavigation = Integer.parseInt(hashMap.get("liveNavigation"));
      Long fundId = hashMap.get("fundId") != null ? Long.parseLong(hashMap.get("fundId")) : null;

      this.notificationCenterService.addNotification(to.getId(), title, message, titleAr, messageAr, language,
          navigation, liveNavigation, fundId, null, null);

    } else {
      if (arrayUtility.isArrayPopulated(options))
        this.notificationCenterService.addNotification(to.getId(), title, message, titleAr, messageAr, language, null,
            null, null, (String) options[0], null);
      else
        this.notificationCenterService.addNotification(to.getId(), title, message, titleAr, messageAr, language, null,
            null, null, null, null);
    }

  }

  public void sendCallbackNotification(BusinessUser to, Messages message, Long fundId, String notificationType,
      String fundName) {
    var messageBody = fundName != null ? String.format(message.getMessage(), fundName) : message.getMessage();
    var messageBodyAr = fundName != null ? String.format(message.getMessage(), fundName) : message.getMessageAr();
    this.sendCallbackNotification(to, messageBody, message.getTitle(), messageBodyAr, message.getTitleAr(),
        StringUtility.isStringPopulated(to.getLanguage()) ? to.getLanguage() : StringUtility.ENGLISH, fundId,
        notificationType);
  }

  public void sendCallbackNotification(BusinessUser to, String message, String title, String messageAr, String titleAr,
      String language, Long fundId, String notificationType) {
    sendInternal(to, message, title, messageAr, titleAr, language,
        StringUtility.stringsMatch(notificationType, "Transaction") ? "cash" : notificationType.toLowerCase());
    this.notificationCenterService.addNotification(to.getId(), title, message, titleAr, messageAr, language, null,
        null, fundId, null, notificationType);
  }

  private Map<String, String> generateFields(Object[] objectArray) {
    Map<String, String> hashMap = new HashMap<String, String>();
    int arraySize = objectArray.length;
    if (arrayUtility.isArrayPopulated(objectArray)) {
      if (arraySize > 1) {
        MyLogger.info("objectArray[0]" + objectArray[0]);
        hashMap.put("navigation", (String) objectArray[0]);
        MyLogger.info("objectArray[1]" + objectArray[1]);
        hashMap.put("liveNavigation", (String) objectArray[1]);
      }
      if (arraySize > 2) {
        Map<String, String> values = (HashMap<String, String>) objectArray[2];

        if (values.get(StringUtility.NEXT_STEP_FIELD) != null)
          hashMap.put(StringUtility.NEXT_STEP_FIELD, String.valueOf(values.get(StringUtility.NEXT_STEP_FIELD)));

        if (values.get(StringUtility.KYC_STATUS_FIELD) != null)
          hashMap.put(StringUtility.KYC_STATUS_FIELD, String.valueOf(values.get(StringUtility.KYC_STATUS_FIELD)));

        if (values.get(StringUtility.FUND_ID_FIELD) != null)
          hashMap.put(StringUtility.FUND_ID_FIELD, String.valueOf(values.get(StringUtility.FUND_ID_FIELD)));

        if (values.get(StringUtility.FIRST_PAGE_ID_FIELD) != null)
          hashMap.put(StringUtility.FIRST_PAGE_ID_FIELD, String.valueOf(values.get(StringUtility.FIRST_PAGE_ID_FIELD)));

        if (values.get(StringUtility.IS_VERIFIED_FIELD) != null)
          hashMap.put(StringUtility.IS_VERIFIED_FIELD, String.valueOf(values.get(StringUtility.IS_VERIFIED_FIELD)));

      }
    }
    return hashMap;
  }

}
