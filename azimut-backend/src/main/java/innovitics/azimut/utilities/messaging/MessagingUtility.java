package innovitics.azimut.utilities.messaging;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.utilities.ParentUtility;

public abstract class MessagingUtility extends ParentUtility {

  abstract protected void send(BusinessUser to, String message, String title, String messageAr, String titleAr,
      String language, Object... options) throws BusinessException;

  abstract protected void send(BusinessAdminUser to, String message, String title, String messageAr, String titleAr,
      String language, Object... options) throws BusinessException;
}
