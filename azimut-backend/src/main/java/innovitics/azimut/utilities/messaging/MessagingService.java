package innovitics.azimut.utilities.messaging;

import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.admin.BusinessAdminUser;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.utilities.crosslayerenums.Messages;

@Component
public class MessagingService implements IMessagingService {

  @Override
  public void send(MessagingUtility messagingUtility, BusinessUser to, Messages message, String language,
      Object... options)
      throws BusinessException {
    messagingUtility.send(to, message.getMessage(), message.getTitle(), message.getMessageAr(), message.getTitleAr(),
        language, options);

  }

  @Override
  public void send(MessagingUtility messagingUtility, BusinessUser to, String message, String messageAr,
      String title,
      String titleAr, String language, Object... options) throws BusinessException {
    messagingUtility.send(to, message, title, messageAr, titleAr, language, options);
  }

  @Override
  public void send(MessagingUtility messagingUtility, BusinessAdminUser to, String message, String messageAr,
      String title,
      String titleAr, String language, Object... options) throws BusinessException {
    messagingUtility.send(to, message, title, messageAr, titleAr, language, options);
  }
}
