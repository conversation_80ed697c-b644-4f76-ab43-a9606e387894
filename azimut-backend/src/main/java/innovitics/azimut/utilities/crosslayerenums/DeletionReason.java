package innovitics.azimut.utilities.crosslayerenums;

import java.util.ArrayList;
import java.util.List;

import innovitics.azimut.businessmodels.user.BusinessDeletionReason;

public enum DeletionReason {

  ANOTHER_ACCOUNT(1, "I have another account", "لدي حساب آخر"),
  DONT_USE_ACCOUNT(2, "I don't use this account anymore", "أنا لا أستخدم هذا الحساب بعد الآن"),
  TOO_MANY_FORGOT_PASSWORD_EMAILS(3, "I got too many forgot password emails",
      "لقد وصلني العديد من رسائل البريد الإلكتروني لنسيان كلمة المرور"),
  SECURITY_CONCERNS(4, "Security concerns", "مخاوف أمنية"),
  PRIVACY_CONCERNS(5, "Privacy concerns", "مخاوف الخصوصية"),
  NONE(6, "None of the above", "لا شيء مما سبق");

  DeletionReason(int reasonId, String reason, String reasonAr) {
    this.reasonId = reasonId;
    this.reason = reason;
    this.reasonAr = reasonAr;
  }

  private final int reasonId;
  private final String reason;
  private final String reasonAr;

  public int getReasonId() {
    return reasonId;
  }

  public String getReason() {
    return reason;
  }

  public String getReasonAr() {
    return reasonAr;
  }

  public static BusinessDeletionReason[] getDeletionReasons() {
    List<BusinessDeletionReason> businessDeletionReasonsList = new ArrayList<BusinessDeletionReason>();
    for (DeletionReason deletionReason : DeletionReason.values()) {
      BusinessDeletionReason businessDeletionReason = new BusinessDeletionReason();
      businessDeletionReason.setReason(deletionReason.reason);
      businessDeletionReason.setReasonAr(deletionReason.reasonAr);
      businessDeletionReason.setReasonId(deletionReason.reasonId);
      businessDeletionReasonsList.add(businessDeletionReason);
    }
    BusinessDeletionReason[] businessDeletionReasonArray = new BusinessDeletionReason[businessDeletionReasonsList
        .size()];
    businessDeletionReasonsList.toArray(businessDeletionReasonArray);
    return businessDeletionReasonArray;
  }

}
