package innovitics.azimut.utilities.crosslayerenums;

public enum Messages {

  KYC_ACCEPTED_SIGNED_AT_AZIMUT(
      "Congratulations! Your Azimut account has been approved. To activate your account and start buying and selling funds, we kindly request you to visit our nearest office to sign the contract. We look forward to seeing you soon and thank you for choosing <PERSON><PERSON><PERSON><PERSON>.",
      "Account Approval Confirmation", "تمت الموافقة", "نجاح"),
  KYC_ACCEPTED(
      "Great news! Your account with Azimut has been approved. You can now buy and sell funds with ease. Thank you for choosing <PERSON><PERSON><PERSON><PERSON>.",
      "Account Activated", "تمت الموافقة", "نجاح"),
  KYC_REJECTED(
      "Your KYC information has been disapproved. To complete your account activation, we kindly request you to review some of the data shared within the app. Please log in to your account and update your information.",
      "Action Required: Review Your KYC Information", "تم الرفض", "رفض"),
  KYC_ATTACHMENT("Hello, please find attached your contract.", "Signed Contract", "العقد مرفق", " العقد الموقع"),
  NEW_PASSWORD("Hello, your new password is: ", "New Dashboard Password", "كلمة المرور الجديدة هي : ",
      "كلمة المرور الجديدة"),
  OTP_EMAIL("Your verification code for azinvest: %s Don't share this code with anyone", "Azimut OTP",
      "كود المرور هو : %s", "كود المرور "),
  OTP_PHONE("Your verification code for azinvest: %s Don't share this code with anyone", "", "", ""),

  ACCOUNT_ACTIVATED(
      "Your account is active, start your investments journey NOW",
      "Your Account is Ready !",
      "حسابك شغال دلوقتي، جاهز تبدأ رحلتك استثماراتك؟",
      "حسابك مفعل الان !"),
  ACCOUNT_DISABLED(
      "your account is suspended ! … update your account now !",
      "your account is inactive !",
      "حسابك محتاجك عشان خامل ! حدث حسابك و خليك مستثمر!",
      "حسابك خامد !"),
  CASHIN_EXECUTED(
      "your cash is in and ready to grow, start your investments journey NOW",
      "Cash Order Update",
      "فلوسك مستنياك تستثمرها ! اختار صندوقك و يلا استثمر !",
      "تحديث لفلوسك في ازيموت"),
  CASHIN_CANCELED(
      "Cashin Order has been canceled",
      "Cash Order Canceled",
      "تم إلغاء أمر الدفع",
      "إلغاء الأمر"),
  CASHOUT_EXECUTED(
      "your cash is on the way to YOUR bank !",
      "Cash out processed!",
      "فلوسك في طريقها لحسابك البنكي ! ",
      "طلب السحب اتنفذ !"),
  CASHOUT_CANCELED(
      "Cashout has been canceled",
      "cash out cancelled!",
      "تم إلغاء السحب",
      "طلب السحب اترفض !"),
  BUY_ORDER_EXECUTED(
      "Your Buy order in %s has been executed!",
      "Order Update!",
      "انت دلوقتي مستثمر في صندوق %s! خليك دايما مستثمر!",
      "بيانات المعاملة !"),
  BUY_ORDER_CANCELED(
      "Your Buy order in %s has been Canceled! Check your azimut update!",
      "Order Update!",
      "تم إلغاء أمر الشراء في %s!",
      "بيانات المعاملة !"),
  SELL_ORDER_EXECUTED(
      "Your Sell order in %s has been Executed!",
      "Order Update!",
      "امر البيع الخاص بصندوق %s اتنفذ ! فلوسك في حسابك في ازيموت مستنياك !",
      "بيانات المعاملة !"),
  SELL_ORDER_CANCELED(
      "Your Sell order in %s has been Canceled! Check your azimut update!",
      "Order Update!",
      "تم إلغاء أمر البيع في %s!",
      "بيانات المعاملة !"),

  OFFLINE_CONTRACT_ATTACHMENT(
      "Hello, please find attached your contract. Please sign it and send it back to us. \n Our address is: Smart Village | Building B 16 | PO-Box 12577 | Giza, Egypt \n\n "
          + " مرحبًا، يرجى العثور على العقد المرفق. يرجى توقيع العقد وإعادته إلينا. عنواننا هو: القرية الذكية | مبنى B 16 | صندوق بريد 12577 | الجيزة، مصر",
      "Azimut Contract",
      " مرحبًا، يرجى العثور على العقد المرفق. يرجى توقيع العقد وإعادته إلينا. عنواننا هو: القرية الذكية | مبنى B 16 | صندوق بريد 12577 | الجيزة، مصر",
      " العقد الموقع"),

  OFFLINE_CONTRACT_NOTIFICATION(
      "Hello, contract sent to your email. Please sign it and send it back to us.",
      "Signed Contract",
      " مرحبًا، تم إرسال العقد إلى بريدك الإلكتروني. يرجى توقيع العقد وإعادته إلينا.",
      " العقد الموقع")

  ;

  Messages(String message, String title, String messageAr, String titleAr) {
    this.title = title;
    this.message = message;
    this.titleAr = titleAr;
    this.messageAr = messageAr;
  }

  private final String message;
  private final String title;
  private final String messageAr;
  private final String titleAr;

  public String getMessage() {
    return message;
  }

  public String getTitle() {
    return title;
  }

  public String getMessageAr() {
    return messageAr;
  }

  public String getTitleAr() {
    return titleAr;
  }

}
