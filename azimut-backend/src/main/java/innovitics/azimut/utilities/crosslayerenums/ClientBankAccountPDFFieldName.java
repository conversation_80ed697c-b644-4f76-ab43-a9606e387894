package innovitics.azimut.utilities.crosslayerenums;

import java.io.IOException;

import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.PdfStamper;

import innovitics.azimut.businessmodels.user.BusinessClientBankAccountDetails;
import innovitics.azimut.utilities.datautilities.StringUtility;

public enum ClientBankAccountPDFFieldName {

  BANK_NAME(1, "26"),
  ACCOUNT_NUMBER(2, "29"),
  BRANCH_NAME(3, "27"),
  SWIFT_CODE(4, ""),
  CURRENCY_NAME(5, "");

  ClientBankAccountPDFFieldName(int id, String fieldName) {
    this.id = id;
    this.fieldName = fieldName;
  }

  private final int id;
  private final String fieldName;

  public int getId() {
    return id;
  }

  public String getFieldName() {
    return fieldName;
  }

  public static void assignPDFValues(String language, PdfStamper stamper, AcroFields form,
      BusinessClientBankAccountDetails businessClientBankAccountDetails) throws IOException, DocumentException {
    for (ClientBankAccountPDFFieldName clientBankAccountPDFFieldName : values()) {
      if (StringUtility.stringsMatch(clientBankAccountPDFFieldName.fieldName, BANK_NAME.fieldName)) {
        StringUtility.arabicFields(language, stamper, form, clientBankAccountPDFFieldName.fieldName,
            StringUtility.determineOuputLanguage(businessClientBankAccountDetails.getEnglishBankName(),
                businessClientBankAccountDetails.getArabicBankName(), language),
            15f);
      }
      if (StringUtility.stringsMatch(clientBankAccountPDFFieldName.fieldName, ACCOUNT_NUMBER.fieldName)) {
        form.setField(clientBankAccountPDFFieldName.fieldName, businessClientBankAccountDetails.getAccountNumber());
      }
      if (StringUtility.stringsMatch(clientBankAccountPDFFieldName.fieldName, BRANCH_NAME.fieldName)) {
        StringUtility.arabicFields(language, stamper, form, clientBankAccountPDFFieldName.fieldName,
            StringUtility.determineOuputLanguage(businessClientBankAccountDetails.getEnglishBranchName(),
                businessClientBankAccountDetails.getArabicBranchName(), language),
            15f);
      }
      if (StringUtility.stringsMatch(clientBankAccountPDFFieldName.fieldName, SWIFT_CODE.fieldName)) {
        form.setField(clientBankAccountPDFFieldName.fieldName, businessClientBankAccountDetails.getSwiftCode());
      }
      if (StringUtility.stringsMatch(clientBankAccountPDFFieldName.fieldName, CURRENCY_NAME.fieldName)) {
        StringUtility.arabicFields(language, stamper, form, clientBankAccountPDFFieldName.fieldName,
            StringUtility.determineOuputLanguage(businessClientBankAccountDetails.getEnglishCurrencyName(),
                businessClientBankAccountDetails.getArabicCurrencyName(), language),
            15f);
      }
    }
  }

}
