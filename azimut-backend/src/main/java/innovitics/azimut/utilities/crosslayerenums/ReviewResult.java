package innovitics.azimut.utilities.crosslayerenums;

public enum ReviewResult {

  PENDING(0, "pending", "قيد التصحيح"),
  APPROVED(1, "approved", "صحيح"),
  REJECTED(2, "rejected", "خطآ");

  ReviewResult(long resultId, String status, String statusAr) {
    this.resultId = resultId;
    this.status = status;
    this.statusAr = statusAr;
  }

  private final long resultId;
  private final String status;
  private final String statusAr;

  public long getResultId() {
    return resultId;
  }

  public String getStatus() {
    return status;
  }

  public String getStatusAr() {
    return statusAr;
  }

}
