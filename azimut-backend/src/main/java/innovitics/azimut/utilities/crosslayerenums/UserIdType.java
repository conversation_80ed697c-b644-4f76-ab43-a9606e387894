package innovitics.azimut.utilities.crosslayerenums;

import innovitics.azimut.utilities.datautilities.StringUtility;

public enum UserIdType {

  NATIONAL_ID(1, 1, "National Id", "البطاقة", "بنفسي/بشخصية"),
  FAMILY_ID(2, 2, "Family ID", "بطاقة عائلية", "بنفسي/بشخصية"),
  PASSPORT(3, 3, "Passport", "جواز السفر", "بنفسي/بشخصية"),
  DRIVING_LICENSE(4, 4, "Driving License", "رخصة قيادة", "بنفسي/بشخصية"),
  INSTITUTIONAL(5, 5, "Institutional", "مؤسسة", "مفوض بالتوقيع"),
  OTHER_6(6, 6, "Other", "آخر", ""),
  MINISTERIAL_DECREE(7, 7, "Ministerial Decree", "قرار وزاري", ""),
  OTHER_8(8, 8, "Other", "آخر", ""),
  OTHER(0, 0, "Other", "أخرى", ""),
  ;

  UserIdType(long typeId, long teacomputerTypeId, String type, String typeAr, String signature) {
    this.teacomputerTypeId = teacomputerTypeId;
    this.typeId = typeId;
    this.type = type;
    this.typeAr = typeAr;
    this.signature = signature;
  }

  private final long typeId;
  private final long teacomputerTypeId;
  private final String type;
  private final String typeAr;
  private final String signature;

  public long getTypeId() {
    return typeId;
  }

  public String getType() {
    return type;
  }

  public long getTeacomputerTypeId() {
    return teacomputerTypeId;
  }

  public String getTypeAr() {
    return typeAr;
  }

  public String getSignature() {
    return signature;
  }

  public static UserIdType getById(long id) {
    for (UserIdType userIdType : values()) {
      if (userIdType.typeId == id) {
        return userIdType;
      }
    }
    return UserIdType.OTHER;
  }

  public static UserIdType getByIdTypeName(String idTypeName) {
    for (UserIdType userIdType : values()) {
      if (StringUtility.stringsMatch(userIdType.getType(), idTypeName)) {
        return userIdType;
      }
    }
    return UserIdType.OTHER;
  }
}
