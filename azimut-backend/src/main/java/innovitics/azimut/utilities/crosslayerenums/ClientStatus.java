package innovitics.azimut.utilities.crosslayerenums;

public enum ClientStatus {
  ACTIVE(1, "Active"),
  DISABLED(2, "Disable"),
  KYC_CHECK(3, "KYC Check"),
  DORMANT(4, "<PERSON>rman<PERSON>"),
  <PERSON>ROZEN(5, "Frozen"),
  <PERSON>IGHT_KYC(6, "Light KYC"),
  HOLD(7, "Hold Purchase")

  ;

  ClientStatus(int typeId, String type) {
    this.typeId = typeId;
    this.type = type;
  }

  private final int typeId;
  private final String type;

  public int getTypeId() {
    return typeId;
  }

  public String getType() {
    return type;
  }
}
