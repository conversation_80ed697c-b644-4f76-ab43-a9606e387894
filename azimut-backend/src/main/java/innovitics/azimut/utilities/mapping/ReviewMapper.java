package innovitics.azimut.utilities.mapping;

import java.util.Date;

import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.kyc.BusinessReview;
import innovitics.azimut.models.kyc.Reason;
import innovitics.azimut.models.kyc.Review;

@Component
public class ReviewMapper extends Mapper<Review, BusinessReview> {

  @Override
  public Review convertBusinessUnitToBasicUnit(BusinessReview businessReview, boolean save) {
    Review review = new Review();
    review.setId(businessReview.getId());
    review.setQuestionId(businessReview.getQuestionId());
    review.setPageId(businessReview.getPageId());
    review.setPageOrder(businessReview.getPageOrder());
    review.setResult(businessReview.getStatus());
    review.setComment(businessReview.getComment());
    if (save) {
      review.setCreatedAt(new Date());
      review.setUpdatedAt(new Date());
    } else {
      review.setCreatedAt(businessReview.getCreatedAt());
      review.setUpdatedAt(new Date());
    }

    if (businessReview.getReason() != null) {
      Reason reason = new Reason();
      reason.setId(businessReview.getReason().getId());
      review.setReason(reason);
    }
    return review;
  }

  @Override
  public BusinessReview convertBasicUnitToBusinessUnit(Review s) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public BusinessReview convertBasicUnitToBusinessUnit(Review s, String language) {
    // TODO Auto-generated method stub
    return null;
  }

}
