package innovitics.azimut.utilities.mapping;

import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.user.BusinessNotification;
import innovitics.azimut.models.Notification;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.StringUtility;

@Component
public class NotificationMapper extends Mapper<Notification, BusinessNotification> {

  @Override
  protected Notification convertBusinessUnitToBasicUnit(BusinessNotification businessNotification, boolean save) {
    Notification notification = new Notification();

    notification.setId(businessNotification.getId());

    notification.setNotificationHeader(businessNotification.getNotificationHeader());
    notification.setNotificationText(businessNotification.getNotificationText());
    notification.setNotificationHeaderAr(businessNotification.getNotificationHeader());
    notification.setNotificationTextAr(businessNotification.getNotificationText());
    notification.setLink(businessNotification.getLink());
    notification.setFundId(businessNotification.getFundId());
    notification.setNotificationType(businessNotification.getNotificationType());

    notification.setIsRead(BooleanUtility.getValue(notification.getIsRead()));

    return notification;
  }

  @Override
  protected BusinessNotification convertBasicUnitToBusinessUnit(Notification notification) {
    return null;
  }

  @Override
  protected BusinessNotification convertBasicUnitToBusinessUnit(Notification notification, String language) {
    BusinessNotification businessNotification = new BusinessNotification();
    businessNotification.setId(notification.getId());
    businessNotification.setNotificationHeader(StringUtility.determineOuputLanguage(
        notification.getNotificationHeader(), notification.getNotificationHeaderAr(), language));
    businessNotification.setNotificationText(StringUtility.determineOuputLanguage(notification.getNotificationText(),
        notification.getNotificationTextAr(), language));
    businessNotification.setIsRead(BooleanUtility.getValue(notification.getIsRead()));
    businessNotification.setCreatedAt(notification.getCreatedAt());
    businessNotification.setPath(notification.getNavigation());
    businessNotification.setFundId(notification.getFundId());
    businessNotification.setLink(notification.getLink());
    businessNotification.setNotificationType(notification.getNotificationType());
    return businessNotification;
  }

}
