package innovitics.azimut.utilities.mapping;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.user.AzimutAccount;
import innovitics.azimut.businessmodels.user.BusinessUser;
import innovitics.azimut.businessutilities.BusinessSearchCriteria;
import innovitics.azimut.models.digitalregistry.DigitalRegistryAction;
import innovitics.azimut.models.user.User;
import innovitics.azimut.models.user.UserInvestment;
import innovitics.azimut.models.user.UserSecurityQuestion;
import innovitics.azimut.models.user.UserType;
import innovitics.azimut.repositories.user.UserInvestmentRepository;
import innovitics.azimut.repositories.user.UserSecurityQuestionRepository;
import innovitics.azimut.services.digitalregistry.DigitalRegistryService;
import innovitics.azimut.services.kyc.ReviewService;
import innovitics.azimut.services.kyc.UserTypeService;
import innovitics.azimut.utilities.businessutilities.ReviewUtility;
import innovitics.azimut.utilities.crosslayerenums.KycStatus;
import innovitics.azimut.utilities.crosslayerenums.UserIdType;
import innovitics.azimut.utilities.crosslayerenums.UserStep;
import innovitics.azimut.utilities.datautilities.BooleanUtility;
import innovitics.azimut.utilities.datautilities.DateUtility;
import innovitics.azimut.utilities.datautilities.NumberUtility;
import innovitics.azimut.utilities.datautilities.PaginatedEntity;
import innovitics.azimut.utilities.datautilities.StringUtility;
import innovitics.azimut.utilities.exceptionhandling.ExceptionHandler;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class UserMapper extends Mapper<User, BusinessUser> {

  @Autowired
  UserTypeService userTypeService;
  @Autowired
  ReviewUtility reviewUtility;
  @Autowired
  ReviewService reviewService;
  @Autowired
  ExceptionHandler exceptionHandler;
  @Autowired
  private UserSecurityQuestionRepository userSecurityQuestionRepository;
  private @Autowired UserInvestmentRepository userInvestmentRepository;
  private @Autowired DigitalRegistryService digitalRegistryService;

  @Override
  public User convertBusinessUnitToBasicUnit(BusinessUser businessUser, boolean save) {

    User user = new User();

    if (businessUser != null) {
      MyLogger.info("Business User to be editted::" + businessUser.toString());
      // this.convertBusinessDatesToBasicDates(user, businessUser, save);

      user.setId(businessUser.getId());
      if (businessUser.getUserPhone() != null)
        user.setUserPhone(businessUser.getUserPhone());
      if (businessUser.getPassword() != null)
        user.setPassword(aes.encrypt(businessUser.getPassword()));
      if (businessUser.getCountryPhoneCode() != null)
        user.setCountryPhoneCode(businessUser.getCountryPhoneCode());
      if (businessUser.getPhoneNumber() != null)
        user.setPhoneNumber(businessUser.getPhoneNumber());
      if (businessUser.getEmailAddress() != null)
        user.setEmailAddress(businessUser.getEmailAddress());
      if (businessUser.getNickName() != null)
        user.setNickName(businessUser.getNickName());
      if (businessUser.getDeviceId() != null)
        user.setDeviceId(businessUser.getDeviceId());
      if (businessUser.getDeviceName() != null)
        user.setDeviceName(businessUser.getDeviceName());
      if (businessUser.getLanguage() != null)
        user.setLanguage(businessUser.getLanguage());
      if (businessUser.getProfilePicture() != null)
        user.setProfilePicture(businessUser.getProfilePicture());
      if (businessUser.getUserId() != null)
        user.setUserId(businessUser.getUserId());

      if (businessUser.getIdType() != null) {
        if (NumberUtility.areLongValuesMatching(businessUser.getIdType(), UserIdType.INSTITUTIONAL.getTypeId())) {
          user.setIsInstitutional(true);
        } else {
          UserType userType = new UserType();
          userType.setId(businessUser.getIdType());
          user.setUserType(userType);
        }

      }

      if (businessUser.getSignedPdf() != null)
        user.setSignedPdf(businessUser.getSignedPdf());
      if (businessUser.getPicturePath() != null)
        user.setPicturePath(businessUser.getPicturePath());
      if (businessUser.getPdfPath() != null)
        user.setPdfPath(businessUser.getPdfPath());
      user.setPdfSignedAt(businessUser.getPdfSignedAt());
      user.setUpdatedAt(DateUtility.getCurrentDate());
      businessUser.setUpdatedAt(DateUtility.getCurrentDate());
      if (businessUser.isChangeNoApproved() != null)
        user.setIsChangeNoApproved(businessUser.isChangeNoApproved());
      if (businessUser.getIsVerified() != null)
        user.setIsVerified(businessUser.getIsVerified());
      if (businessUser.getIsEmailVerified() != null)
        user.setIsEmailVerified(businessUser.getIsEmailVerified());
      if (BooleanUtility.isTrue(businessUser.getHasSecurityQuestions())) {
        List<UserSecurityQuestion> securityQuestions = new ArrayList<>();
        securityQuestions.add(null);
        user.setUserSecurityQuestions(securityQuestions);
      }
      if (businessUser.getVerificationPercentage() != null) {
        if (businessUser.getVerificationPercentage().intValue() > 100) {
          businessUser.setVerificationPercentage(100);
        }
        user.setVerificationPercentage(businessUser.getVerificationPercentage());
      }
      if (businessUser.getMigrated() != null)
        user.setMigrated(businessUser.getMigrated());
      if (businessUser.getCreatedAt() != null)
        user.setCreatedAt(businessUser.getCreatedAt());
      if (businessUser.getCountryCode() != null)
        user.setCountryCode(businessUser.getCountryCode());
      if (businessUser.getLastSolvedPageId() != null)
        user.setLastSolvedPageId(businessUser.getLastSolvedPageId());
      if (businessUser.getNextPageId() != null)
        user.setNextPageId(businessUser.getNextPageId());
      if (businessUser.getUserStep() != null)
        user.setUserStep(businessUser.getUserStep());
      if (businessUser.getContractMap() != null)
        user.setContractMap(businessUser.getContractMap());

      if (businessUser.getCountry() != null)
        user.setCountry(businessUser.getCountry());

      if (businessUser.getCity() != null)
        user.setCity(businessUser.getCity());

      if (businessUser.getFirstName() != null)
        user.setFirstName(businessUser.getFirstName());

      if (businessUser.getLastName() != null)
        user.setLastName(businessUser.getLastName());

      if (businessUser.getDateOfBirth() != null)
        user.setDateOfBirth(businessUser.getDateOfBirth());

      if (businessUser.getDateOfIdExpiry() != null)
        user.setDateOfIdExpiry(businessUser.getDateOfIdExpiry());

      if (businessUser.getIdData() != null)
        user.setIdData(businessUser.getIdData());

      user.setCso(businessUser.getCso());
      user.setNtra(businessUser.getNtra());
      user.setAml(businessUser.getAml());
      user.setUuid(businessUser.getUuid());
      user.setFraStoreId(businessUser.getFraStoreId());
      user.setFraCompanyStoreId(businessUser.getFraCompanyStoreId());

      if (businessUser.getOtherIdType() != null)
        user.setOtherIdType(businessUser.getOtherIdType());
      if (businessUser.getOtherUserId() != null)
        user.setOtherUserId(businessUser.getOtherUserId());

      if (businessUser.getOtherNationality() != null)
        user.setOtherNationality(businessUser.getOtherNationality());

      if (businessUser.getGenderId() != null)
        user.setGenderId(businessUser.getGenderId());

      if (businessUser.getFailedLogins() != null)
        user.setFailedLogins(businessUser.getFailedLogins());

      if (businessUser.getAzimutAccount() != null) {
        AzimutAccount azimutAccount = businessUser.getAzimutAccount();

        if (azimutAccount.getAddressAr() != null)
          user.setTeacomputersAddressAr(azimutAccount.getAddressAr());
        if (azimutAccount.getAddressEn() != null)
          user.setTeacomputersAddressEn(azimutAccount.getAddressEn());
        if (azimutAccount.getCityId() != null)
          user.setTeacomputersCityId(azimutAccount.getCityId());
        if (azimutAccount.getCountryId() != null)
          user.setTeacomputersCountryId(azimutAccount.getCountryId());

        if (azimutAccount.getClientAML() != null)
          user.setTeacomputersClientaml(azimutAccount.getClientAML());
        else
          user.setTeacomputersClientaml(StringUtility.CLIENT_AML);

        if (azimutAccount.getIDIssueCountryId() != null)
          user.setTeacomputersIssueCountryId(azimutAccount.getIDIssueCountryId());
        if (azimutAccount.getIDIssueCityId() != null)
          user.setTeacomputersIssueCityId(azimutAccount.getIDIssueCityId());

        if (azimutAccount.getOccupation() != null)
          user.setTeacomputersOccupation(azimutAccount.getOccupation());

        if (azimutAccount.getNationalityId() != null)
          user.setTeacomputersNationalityId(azimutAccount.getNationalityId());
      }

      user.setLivenessChecked(businessUser.getLivenessChecked());

      if (BooleanUtility.isTrue(businessUser.getIsInstitutional()))
        user.setIsInstitutional(true);

      if (businessUser.getSolvedPages() != null)
        user.setSolvedPages(businessUser.getSolvedPages());

      if (businessUser.getIsOld() != null) {
        user.setIsOld(businessUser.getIsOld());
      }
      if (businessUser.getMailingAddress() != null) {
        user.setMailingAddress(businessUser.getMailingAddress());
      }
      if (businessUser.getIsReviewed() != null) {
        user.setIsReviewed(businessUser.getIsReviewed());
      }
      if (businessUser.getKycStatus() != null) {
        user.setKycStatus(businessUser.getKycStatus());
      }
      if (businessUser.getMessagingToken() != null) {
        user.setMessagingToken(businessUser.getMessagingToken());
      }
      if (businessUser.getDateOfRelease() != null) {
        user.setDateOfRelease(businessUser.getDateOfRelease());
      }
      if (businessUser.getProvider() != null) {
        user.setProvider(businessUser.getProvider());
      }
      if (businessUser.getProviderId() != null) {
        user.setProviderId(businessUser.getProviderId());
      }
      if (businessUser.getClientCode() != null) {
        user.setClientCode(businessUser.getClientCode());
      }
      if (businessUser.getDeletedAt() != null) {
        user.setDeletedAt(businessUser.getDeletedAt());
      }
      if (businessUser.getDeletionReasonId() != null) {
        user.setDeletionReasonId(businessUser.getDeletionReasonId());
      }
      if (businessUser.getIsSynchronized() != null) {
        user.setIsSynchronized(businessUser.getIsSynchronized());
      }
      if (businessUser.getAzimutAmlMatches() != null) {
        user.setAzimutAmlMatches(businessUser.getAzimutAmlMatches());
      }
      if (businessUser.getRisk() != null) {
        user.setRisk(businessUser.getRisk());
      }
      if (businessUser.getLoggedIn() != null) {
        user.setLoggedIn(businessUser.getLoggedIn());
      }
      if (businessUser.getLastLogin() != null) {
        user.setLastLogin(businessUser.getLastLogin());
      }
      if (businessUser.getEnrollApplicantId() != null) {
        user.setEnrollApplicantId(businessUser.getEnrollApplicantId());
      }
      if (businessUser.getGateIdTask() != null) {
        user.setGateIdTask(businessUser.getGateIdTask());
      }
      if (businessUser.getEnrollRequestId() != null) {
        user.setEnrollRequestId(businessUser.getEnrollRequestId());
      }
      if (businessUser.getReferralCode() != null) {
        user.setReferralCode(businessUser.getReferralCode());
      }

      user.concatinate();

      MyLogger.info("User::" + user.toString());

      return user;

    }

    return user;

  }

  @Override
  public BusinessUser convertBasicUnitToBusinessUnit(User user) {
    BusinessUser businessUser = new BusinessUser();
    if (user != null) {
      MyLogger.info("User before conversion::" + user.toString());
      // this.convertBasicDatesToBusinessDates(user, businessUser);

      if (user.getId() != null)
        businessUser.setId(user.getId());
      if (user.getUserPhone() != null)
        businessUser.setUserPhone(user.getUserPhone());
      if (user.getPassword() != null)
        businessUser.setPassword(aes.decrypt(user.getPassword()));
      if (user.getCountryPhoneCode() != null)
        businessUser.setCountryPhoneCode(user.getCountryPhoneCode());
      if (user.getPhoneNumber() != null)
        businessUser.setPhoneNumber(user.getPhoneNumber());
      if (user.getEmailAddress() != null)
        businessUser.setEmailAddress(user.getEmailAddress());
      if (user.getNickName() != null)
        businessUser.setNickName(user.getNickName());
      if (user.getDeviceId() != null)
        businessUser.setDeviceId(user.getDeviceId());
      if (user.getDeviceName() != null)
        businessUser.setDeviceName(user.getDeviceName());
      if (user.getLanguage() != null)
        businessUser.setLanguage(user.getLanguage());
      if (user.getProfilePicture() != null)
        businessUser.setProfilePicture(user.getProfilePicture());
      if (user.getUserId() != null)
        businessUser.setUserId(user.getUserId());
      if (user.getUserType() != null) {
        businessUser.setUserIdType(user.getUserType().getIdType());
        businessUser.setIdType(user.getUserType().getId());
        businessUser.setAzimutIdTypeId(user.getUserType().getAzimutIdTypeId());
        businessUser.setFirstPageId(user.getUserType().getFirstPageId());
      }
      if (BooleanUtility.isTrue(user.getIsInstitutional())) {
        businessUser.setFirstPageId(this.getInstitutionalFirstPageId());
        businessUser.setIsInstitutional(true);
      }

      if (user.getProfilePicture() != null)
        businessUser.setProfilePicture(user.getProfilePicture());
      if (user.getSignedPdf() != null)
        businessUser.setSignedPdf(user.getSignedPdf());
      if (user.getPicturePath() != null)
        businessUser.setPicturePath(user.getPicturePath());
      if (user.getPdfPath() != null)
        businessUser.setPdfPath(user.getPdfPath());
      businessUser.setPdfSignedAt(user.getPdfSignedAt());
      if (user.getIsChangeNoApproved() != null)
        businessUser.setChangeNoApproved(user.getIsChangeNoApproved());
      if (user.getIsVerified() != null)
        businessUser.setIsVerified(user.getIsVerified());
      if (user.getIsEmailVerified() != null)
        businessUser.setIsEmailVerified(user.getIsEmailVerified());
      if (userSecurityQuestionRepository.countByUserId(user.getId()) > 0)
        businessUser.setHasSecurityQuestions(true);
      if (user.getVerificationPercentage() != null)
        businessUser.setVerificationPercentage(user.getVerificationPercentage());
      if (user.getMigrated() != null)
        businessUser.setMigrated(user.getMigrated());
      if (user.getCreatedAt() != null)
        businessUser.setCreatedAt(user.getCreatedAt());
      if (user.getUpdatedAt() != null)
        businessUser.setUpdatedAt(user.getUpdatedAt());
      if (user.getCountryCode() != null)
        businessUser.setCountryCode(user.getCountryCode());
      if (user.getLastSolvedPageId() != null)
        businessUser.setLastSolvedPageId(user.getLastSolvedPageId());
      if (user.getNextPageId() != null)
        businessUser.setNextPageId(user.getNextPageId());
      if (user.getLivenessChecked() != null)
        businessUser.setLivenessChecked(user.getLivenessChecked());
      else
        businessUser.setLivenessChecked(false);

      if (user.getUserStep() != null) {
        businessUser.setUserStep(user.getUserStep().intValue());

        if (NumberUtility.areIntegerValuesMatching(user.getUserStep().intValue(), UserStep.KYC.getStepId())) {
          if (user.getLastSolvedPageId() != null && user.getNextPageId() != null) {
            if (NumberUtility.areLongValuesMatching(user.getLastSolvedPageId(), user.getNextPageId())) {
              businessUser.setNextUserStep(user.getUserStep().intValue() + 1);
            } else {
              businessUser.setNextUserStep(UserStep.KYC.getStepId());
            }
          } else if (user.getLastSolvedPageId() == null || user.getNextPageId() == null) {
            businessUser.setNextUserStep(UserStep.KYC.getStepId());
          }
          businessUser.setUserStep(businessUser.getNextUserStep());
        } else {
          if (NumberUtility.areIntegerValuesMatching(user.getUserStep().intValue(), UserStep.CLIENT_DATA.getStepId())
              || NumberUtility.areIntegerValuesMatching(user.getUserStep().intValue(),
                  UserStep.BANK_REFERENCES_IGNORE.getStepId()))
            businessUser.setNextUserStep(user.getUserStep().intValue() + 2);

          else if (NumberUtility.areIntegerValuesMatching(user.getUserStep().intValue(),
              UserStep.UNDER_REVIEW.getStepId()))
            businessUser.setNextUserStep(UserStep.UNDER_REVIEW.getStepId());

          else if (NumberUtility.areIntegerValuesMatching(user.getUserStep().intValue(), UserStep.FINISHED.getStepId()))
            businessUser.setNextUserStep(UserStep.FINISHED.getStepId());

          else
            businessUser.setNextUserStep(user.getUserStep().intValue() + 1);
        }
      } else if (user.getUserStep() == null) {
        businessUser.setUserStep(0);
        businessUser.setNextUserStep(1);
      }
      if (user.getContractMap() != null)
        businessUser.setContractMap(user.getContractMap());

      if (user.getCountry() != null)
        businessUser.setCountry(user.getCountry());

      if (user.getCity() != null)
        businessUser.setCity(user.getCity());

      if (user.getFirstName() != null)
        businessUser.setFirstName(user.getFirstName());

      if (user.getLastName() != null)
        businessUser.setLastName(user.getLastName());

      if (user.getDateOfBirth() != null)
        businessUser.setDateOfBirth(user.getDateOfBirth());

      if (user.getDateOfIdExpiry() != null)
        businessUser.setDateOfIdExpiry(user.getDateOfIdExpiry());

      if (user.getIdData() != null)
        businessUser.setIdData(user.getIdData());

      businessUser.setCso(user.getCso());
      businessUser.setNtra(user.getNtra());
      businessUser.setAml(user.getAml());
      businessUser.setUuid(user.getUuid());
      businessUser.setFraStoreId(user.getFraStoreId());
      businessUser.setFraCompanyStoreId(user.getFraCompanyStoreId());

      if (user.getOtherIdType() != null)
        businessUser.setOtherIdType(user.getOtherIdType());
      else
        businessUser.setOtherIdType(0L);

      if (user.getOtherUserId() != null)
        businessUser.setOtherUserId(user.getOtherUserId());

      if (user.getOtherNationality() != null)
        businessUser.setOtherNationality(user.getOtherNationality());

      if (user.getGenderId() != null)
        businessUser.setGenderId(user.getGenderId());

      if (user.getFailedLogins() != null)
        businessUser.setFailedLogins(user.getFailedLogins());
      else
        businessUser.setFailedLogins(0);

      AzimutAccount azimutAccount = new AzimutAccount();

      if (user.getTeacomputersAddressAr() != null)
        azimutAccount.setAddressAr(user.getTeacomputersAddressAr());

      if (user.getTeacomputersAddressEn() != null)
        azimutAccount.setAddressEn(user.getTeacomputersAddressEn());

      if (user.getTeacomputersCityId() != null)
        azimutAccount.setCityId(user.getTeacomputersCityId());

      if (user.getTeacomputersCountryId() != null)
        azimutAccount.setCountryId(user.getTeacomputersCountryId());

      if (user.getTeacomputersIssueCountryId() != null)
        azimutAccount.setIDIssueCountryId(user.getTeacomputersIssueCountryId());

      if (user.getTeacomputersIssueCityId() != null)
        azimutAccount.setIDIssueCityId(user.getTeacomputersIssueCityId());

      if (user.getTeacomputersClientaml() != null)
        azimutAccount.setClientAML(user.getTeacomputersClientaml());

      if (user.getTeacomputersOccupation() != null)
        azimutAccount.setOccupation(user.getTeacomputersOccupation());

      if (user.getTeacomputersNationalityId() != null)
        azimutAccount.setNationalityId(user.getTeacomputersNationalityId());

      businessUser.setAzimutAccount(azimutAccount);

      if (user.getSolvedPages() != null)
        businessUser.setSolvedPages(user.getSolvedPages());

      if (user.getIsOld() != null) {
        businessUser.setIsOld(user.getIsOld());
      }
      if (user.getMailingAddress() != null) {
        businessUser.setMailingAddress(user.getMailingAddress());
      }
      if (user.getIsReviewed() != null) {
        businessUser.setIsReviewed(user.getIsReviewed());
      }
      if (user.getKycStatus() != null) {
        businessUser.setKycStatus(user.getKycStatus());

        if (NumberUtility.areIntegerValuesMatching(user.getKycStatus(), KycStatus.REJECTED.getStatusId())
            || NumberUtility.areIntegerValuesMatching(user.getKycStatus(), KycStatus.UPDATED.getStatusId())) {
          var nextStep = this.reviewUtility.calculateUserStepUnderReview(businessUser);
          businessUser.setNextUserStep(nextStep);
          businessUser.setUserStep(nextStep);
          var firstPageId = getFirstReviewPageId(user.getId());
          if (firstPageId != null)
            businessUser.setFirstPageId(firstPageId);

        }

      }
      if (user.getMessagingToken() != null) {
        businessUser.setMessagingToken(user.getMessagingToken());
      }
      if (user.getDateOfRelease() != null) {
        businessUser.setDateOfRelease(user.getDateOfRelease());
      }
      if (user.getProvider() != null) {
        businessUser.setProvider(user.getProvider());
      }
      if (user.getProviderId() != null) {
        businessUser.setProviderId(user.getProviderId());
      }
      if (user.getClientCode() != null) {
        businessUser.setClientCode(user.getClientCode());
      }
      if (user.getDeletedAt() != null) {
        businessUser.setDeletedAt(user.getDeletedAt());
      }

      if (user.getDeletionReasonId() != null) {
        businessUser.setDeletionReasonId(user.getDeletionReasonId());
      }
      if (user.getIsSynchronized() != null) {
        businessUser.setIsSynchronized(user.getIsSynchronized());
      }
      if (user.getAzimutAmlMatches() != null) {
        businessUser.setAzimutAmlMatches(user.getAzimutAmlMatches());
      }
      if (user.getRisk() != null) {
        businessUser.setRisk(user.getRisk());
      }
      if (user.getLoggedIn() != null) {
        businessUser.setLoggedIn(user.getLoggedIn());
      }

      if (user.getLastLogin() != null) {
        businessUser.setLastLogin(user.getLastLogin());
      }
      if (user.getEnrollApplicantId() != null) {
        businessUser.setEnrollApplicantId(user.getEnrollApplicantId());
      }
      if (user.getGateIdTask() != null) {
        businessUser.setGateIdTask(user.getGateIdTask());
      }
      if (user.getEnrollRequestId() != null) {
        businessUser.setEnrollRequestId(user.getEnrollRequestId());
      }
      if (user.getReferralCode() != null) {
        businessUser.setReferralCode(user.getReferralCode());
      }
      businessUser.concatinate();

    }
    MyLogger.info("Business User::" + businessUser.toString());
    return businessUser;

  }

  public BusinessUser convertBasicUnitToBusinessUnitMinimal(User user) {
    BusinessUser businessUser = new BusinessUser();
    if (user != null) {
      MyLogger.info("User before conversion::" + user.toString());
      // this.convertBasicDatesToBusinessDates(user, businessUser);

      if (user.getId() != null)
        businessUser.setId(user.getId());
      if (user.getUserPhone() != null)
        businessUser.setUserPhone(user.getUserPhone());
      if (user.getNickName() != null)
        businessUser.setNickName(user.getNickName());
      if (user.getKycStatus() != null) {
        businessUser.setKycStatus(user.getKycStatus());
      }
      if (user.getFirstName() != null) {
        businessUser.setFirstName(user.getFirstName());
      }
      if (user.getLastName() != null) {
        businessUser.setLastName(user.getLastName());
      }
      if (user.getIsVerified() != null) {
        businessUser.setIsVerified(BooleanUtility.getValue(user.getIsVerified()));
      }
      if (user.getEmailAddress() != null) {
        businessUser.setEmailAddress(user.getEmailAddress());
      }
      if (user.getIsSynchronized() != null) {
        businessUser.setIsSynchronized(user.getIsSynchronized());
      }
      if (user.getUserId() != null)
        businessUser.setUserId(user.getUserId());
      businessUser.setCreatedAt(user.getCreatedAt());
    }
    return businessUser;

  }

  public BusinessUser convertBasicUnitToBusinessUnitCompacted(User user) {
    BusinessUser businessUser = new BusinessUser();
    if (user != null) {
      MyLogger.info("User before conversion::" + user.toString());
      // this.convertBasicDatesToBusinessDates(user, businessUser);

      if (user.getId() != null)
        businessUser.setId(user.getId());
      if (user.getUserPhone() != null)
        businessUser.setUserPhone(user.getUserPhone());
      if (user.getNickName() != null)
        businessUser.setNickName(user.getNickName());
      if (user.getCountryPhoneCode() != null)
        businessUser.setCountryPhoneCode(user.getCountryPhoneCode());
      if (user.getPhoneNumber() != null)
        businessUser.setPhoneNumber(user.getPhoneNumber());
      if (user.getUserType() != null) {
        businessUser.setUserIdType(user.getUserType().getIdType());
        businessUser.setIdType(user.getUserType().getId());
        businessUser.setAzimutIdTypeId(user.getUserType().getAzimutIdTypeId());

        if (BooleanUtility.isTrue(user.getIsInstitutional())) {
          businessUser.setFirstPageId(this.getInstitutionalFirstPageId());
        } else {
          businessUser.setFirstPageId(user.getUserType().getFirstPageId());
        }
      }
      if (user.getKycStatus() != null) {
        businessUser.setKycStatus(user.getKycStatus());
      }
      if (user.getFirstName() != null) {
        businessUser.setFirstName(user.getFirstName());
      }
      if (user.getLastName() != null) {
        businessUser.setLastName(user.getLastName());
      }
      if (user.getIsVerified() != null) {
        businessUser.setIsVerified(BooleanUtility.getValue(user.getIsVerified()));
      }
      if (user.getIsEmailVerified() != null) {
        businessUser.setIsEmailVerified(BooleanUtility.getValue(user.getIsEmailVerified()));
      }
      if (userSecurityQuestionRepository.countByUserId(user.getId()) > 0)
        businessUser.setHasSecurityQuestions(true);
      if (user.getEmailAddress() != null) {
        businessUser.setEmailAddress(user.getEmailAddress());
      }
      var signDr = digitalRegistryService.getLastDigitalRegistry(businessUser.getId(),
          DigitalRegistryAction.SIGN_CONTRACT);
      if (signDr != null)
        businessUser.setContractSignedAt(signDr.getCreatedAt());

      if (user.getProvider() != null) {
        businessUser.setProvider(user.getProvider());
      }
      if (user.getProviderId() != null) {
        businessUser.setProviderId(user.getProviderId());
      }
      if (user.getContractMap() != null) {
        businessUser.setContractMap(user.getContractMap());
      }
      if (user.getIsSynchronized() != null) {
        businessUser.setIsSynchronized(user.getIsSynchronized());
      }
      if (user.getReferralCode() != null) {
        businessUser.setReferralCode(user.getReferralCode());
      }

      if (BooleanUtility.isTrue(user.getIsOld()))
        businessUser.setIsOld(user.getIsOld());

      if (user.getUserId() != null)
        businessUser.setUserId(user.getUserId());
      businessUser.setSignedPdf(user.getSignedPdf());
      businessUser.setCreatedAt(user.getCreatedAt());
      businessUser.setUpdatedAt(user.getUpdatedAt());
      businessUser.setUserStep(user.getUserStep());
      businessUser.setCso(user.getCso());
      businessUser.setNtra(user.getNtra());
      businessUser.setAml(user.getAml());
      businessUser.setFraStoreId(user.getFraStoreId());
      AzimutAccount azimutAccount = new AzimutAccount();
      azimutAccount.setClientAML(user.getTeacomputersClientaml());
      azimutAccount.setCountryId(user.getTeacomputersCountryId());
      azimutAccount.setCityId(user.getTeacomputersCityId());
      businessUser.setAzimutAccount(azimutAccount);
      businessUser.setAzimutAmlMatches(user.getAzimutAmlMatches());
      businessUser.concatinate();
      businessUser.setReviewCount(this.reviewService.countByUserIdAndDeletedAtIsNull(businessUser.getId()));
      UserInvestment userInvestment = this.userInvestmentRepository.findByUserId(user.getId())
          .orElse(null);
      if (userInvestment != null) {
        businessUser.setBalance(userInvestment.getBalance());
        businessUser.setTotalPosition(userInvestment.getTotalPosition());
        businessUser.setTotalBuyValue(userInvestment.getTotalBuyValue());
        businessUser.setTotalSellValue(userInvestment.getTotalSellValue());
        businessUser.setTotalTopup(userInvestment.getTotalTopup());
        businessUser.setTotalWithdraw(userInvestment.getTotalWithdraw());
      }
    }
    MyLogger.info("Business User::" + businessUser.toString());
    return businessUser;

  }

  @Override
  protected BusinessUser convertBasicUnitToBusinessUnit(User s, String language) {
    // TODO Auto-generated method stub
    return null;
  }

  private Long getInstitutionalFirstPageId() {
    try {
      return userTypeService.getUserTypeById(UserIdType.INSTITUTIONAL.getTypeId()).getFirstPageId();
    } catch (Exception exception) {
      MyLogger.info("Could not retireve the Institutional First Page Id::: ");
      MyLogger.logStackTrace(exception);
      return null;
    }
  }

  private Long getFirstReviewPageId(Long id) {
    try {
      return reviewService.getIdOfThePageWithLeastOrder(id);
    } catch (Exception exception) {
      MyLogger.info("Could not retireve the First reviewd page Id::: ");
      MyLogger.logStackTrace(exception);
      return null;
    }
  }

  @Override
  public PaginatedEntity<BusinessUser> convertBasicPageToBusinessPage(Page<User> baseEntities,
      BusinessSearchCriteria searchCriteria) {

    PaginatedEntity<BusinessUser> paginatedEntity = new PaginatedEntity<BusinessUser>();

    paginatedEntity.setCurrentPage(searchCriteria.getPageNumber());
    paginatedEntity.setPageSize(searchCriteria.getPageSize());
    paginatedEntity.setNumberOfPages(baseEntities.getTotalPages());
    paginatedEntity.setNumberOfItems(baseEntities.getTotalElements());
    paginatedEntity.setHasNext(!baseEntities.isLast());
    paginatedEntity.setHasPrevious(!baseEntities.isFirst());

    if (this.baseListUtility.isListPopulated(baseEntities.getContent())) {
      List<BusinessUser> businessUsers = new ArrayList<BusinessUser>();
      for (User user : baseEntities.getContent()) {
        businessUsers.add(this.convertBasicUnitToBusinessUnitCompacted(user));
      }
      paginatedEntity.setDataList(businessUsers);
    }
    return paginatedEntity;

  }
}
