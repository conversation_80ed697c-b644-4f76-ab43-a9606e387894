package innovitics.azimut.utilities.mapping;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import innovitics.azimut.businessmodels.admin.BusinessAdminUserMapping;
import innovitics.azimut.models.admin.AdminUserMapping;

@Component
public class AdminUserMappingMapper extends Mapper<AdminUserMapping, BusinessAdminUserMapping> {

  @Autowired
  private AdminUserMapper adminUserMapper;

  @Autowired
  private UserMapper userMapper;

  @Override
  public AdminUserMapping convertBusinessUnitToBasicUnit(BusinessAdminUserMapping businessAdminUserMapping,
      boolean save) {
    return null;
  }

  @Override
  public BusinessAdminUserMapping convertBasicUnitToBusinessUnit(AdminUserMapping adminUserMapping) {
    BusinessAdminUserMapping businessAdminUserMapping = new BusinessAdminUserMapping();

    if (adminUserMapping != null) {
      businessAdminUserMapping.setId(adminUserMapping.getId());
      businessAdminUserMapping.setCreatedAt(adminUserMapping.getCreatedAt());
      businessAdminUserMapping.setUpdatedAt(adminUserMapping.getUpdatedAt());

      if (adminUserMapping.getAdminUser() != null) {
        businessAdminUserMapping
            .setAdminUser(adminUserMapper.convertBasicUnitToBusinessUnit(adminUserMapping.getAdminUser()));
      }

      if (adminUserMapping.getUser() != null) {
        businessAdminUserMapping.setUser(userMapper.convertBasicUnitToBusinessUnitMinimal(adminUserMapping.getUser()));
      }

      if (adminUserMapping.getCreatedBy() != null) {
        businessAdminUserMapping
            .setCreatedBy(adminUserMapper.convertBasicUnitToBusinessUnit(adminUserMapping.getCreatedBy()));
      }
    }

    return businessAdminUserMapping;
  }

  @Override
  public BusinessAdminUserMapping convertBasicUnitToBusinessUnit(AdminUserMapping s, String language) {
    return null;
  }
}
