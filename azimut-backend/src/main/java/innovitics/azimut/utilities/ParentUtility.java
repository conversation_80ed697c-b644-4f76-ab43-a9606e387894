package innovitics.azimut.utilities;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.exceptions.BusinessException;
import innovitics.azimut.security.AES;
import innovitics.azimut.utilities.exceptionhandling.ExceptionHandler;
import innovitics.azimut.utilities.logging.MyLogger;

@Component
public class ParentUtility {
  @Autowired
  protected ExceptionHandler exceptionHandler;
  @Autowired
  protected ConfigProperties configProperties;
  @Autowired
  protected AES aes;

  public Object getValueUsingReflection(Object object, String methodName, Object[] parameters, Class<?>[] paramterTypes)
      throws BusinessException {
    try {
      Method method = object.getClass().getDeclaredMethod(methodName, paramterTypes);
      MyLogger.info("Method Name::" + methodName);
      Object result = method.invoke(object, parameters);
      return result;
    } catch (NoSuchMethodException | SecurityException | IllegalAccessException | IllegalArgumentException
        | InvocationTargetException exception) {
      MyLogger.info("Could not return the method invocation");
      MyLogger.logStackTrace(exception);
      if (this.exceptionHandler.isInvocationException(exception)) {
        MyLogger.info("Detecting if the exception was caused by an integration exception:::");
        Exception cause = (Exception) ((InvocationTargetException) exception).getCause();
        throw this.exceptionHandler.handleException(cause);
      }
    }
    return null;
  }

}
