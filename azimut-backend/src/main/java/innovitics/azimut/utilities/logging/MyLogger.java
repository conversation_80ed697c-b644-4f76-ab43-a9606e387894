package innovitics.azimut.utilities.logging;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import innovitics.azimut.ApplicationContextProvider;
import innovitics.azimut.configproperties.ConfigProperties;
import innovitics.azimut.utilities.datautilities.DateUtility;

public final class MyLogger {
  protected static final Logger logger = LoggerFactory.getLogger(MyLogger.class);

  private static String idgen() {
    // return
    // CurrentRequestHolder.get()!=null&&StringUtility.isStringPopulated(CurrentRequestHolder.get().getSystemTrx())?CurrentRequestHolder.get().getSystemTrx():"";
    return Thread.currentThread().getName();
  }

  public static void info(String value) {
    info(value, false);
  }

  public static void info(String value, Boolean forceLog) {
    // Skip logging for admin and digital-registry paths unless it's from
    // AccessLogFilter
    if (shouldLogInfo() || forceLog) {
      logger.info("Trx Id: " + idgen() + " : " + value);
    }
  }

  private static boolean shouldLogInfo() {
    String currentUri = RequestContextHolder.getCurrentUri();
    boolean isExcludedPath = currentUri != null &&
        (currentUri.startsWith("/admin/") || currentUri.startsWith("/api/digital-registry/"));

    return !isExcludedPath;
  }

  public static void error(String value) {
    // writeLog(getSpacing()+"Error Trx Id: "+idgen()+" : "+value);
    logger.error("Error Trx Id: " + idgen() + " : " + value);
  }

  public static void logStackTrace(Exception exception) {
    StringWriter stringWriter = new StringWriter();
    PrintWriter printWriter = new PrintWriter(stringWriter);
    exception.printStackTrace(printWriter);
    error("Error Message:" + exception.getMessage());
    error("Exception Cause:" + exception.getCause());
    error("Stack trace:" + stringWriter.toString());
  }

  public static void writeLog(String content) {
    try {

      BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(
          (ApplicationContextProvider.getApplicationContext().getBeansOfType(ConfigProperties.class))
              .get("innovitics.azimut.configproperties.ConfigProperties").getLogFilePath() + "\\"
              + "application-" + DateUtility.getCurrentDayMonthYear() + ".log",
          true));
      bufferedWriter.write(content);
      bufferedWriter.close();
    } catch (IOException exception) {
      exception.printStackTrace();
    }

  }

  public static String getSpacing() {
    return "\n" + DateUtility.getCurrentTimeStamp() + " ";
  }
}
