package innovitics.azimut.configproperties;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.ConstructorBinding;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import lombok.Getter;

@Component
@Configuration
@ConstructorBinding
@ConfigurationProperties
@Getter
public class ConfigProperties {

  @Value("${used.profile}")
  private String usedProfile;

  @Value("${blob.connection.string}")
  private String blobConnectionString;

  @Value("${blob.account.name}")
  private String blobAccountName;

  @Value("${blob.container.name.profile-pictures}")
  private String blobProfilePicturePath;

  @Value("${blob.container.name.user-interactions}")
  private String blobUserInteractionPath;

  @Value("${blob.container.name.signed-pdfs}")
  private String blobSignedPdfPath;

  @Value("${blob.container.name.digitally-signed-pdfs}")
  private String blobDigitalPdfPath;

  @Value("${blob.container.name.fits-signed-pdfs}")
  private String blobFitsDigitalPdfPath;

  @Value("${blob.container.name.unsigned-pdf}")
  private String blobUnsignedPdfPath;

  @Value("${blob.container.name.unsigned-pdf.subDirectory}")
  private String blobUnsignedPdfPathSubDirectory;

  @Value("${blob.phoneNumberChangeDocument.name}")
  private String phoneNumberChangeDocumentName;

  @Value("${blob.custodianDocument.name}")
  private String custodianDocumentName;

  @Value("${blob.container.public}")
  private String publicFolder;

  @Value("${blob.container.name.kyc.documents}")
  private String blobKYCDocuments;

  @Value("${blob.container.name.kyc.documents.container}")
  private String blobKYCDocumentsContainer;

  @Value("${blob.container.name.kyc.documents.temp}")
  private String blobKYCDocumentsTemp;

  @Value("${blob.container.name.kyc.documents.temp.container}")
  private String blobKYCDocumentsTempContainer;

  @Value("${blob.temp.deletion.hours}")
  private String blobTempDeletionInHours;

  @Value("${blob.generate.sas.token}")
  private boolean generateBlobSasToken;

  @Value("${blob.sas.token.duration.minutes}")
  private String sasTokenDurationInMinutes;

  @Value("${blob.sas.document.token.duration.minutes}")
  private String sasDocumentTokenDurationInMinutes;

  @Value("${blob.container.url}")
  private String blobContainerUrl;

  @Value("${token.validity.minutes}")
  private String jwTokenDurationInMinutes;

  @Value("${admin.token.validity.minutes}")
  private String adminJwTokenDurationInMinutes;

  @Value("${token.key}")
  private String jwTokenKey;

  @Value("${token.encryption.key}")
  private String jwTokenEncryptionKey;

  @Value("${token.encryption.init.vector}")
  private String jwTokenInitVector;

  @Value("${blockage.duration.minutes}")
  private String blockageDurationInMinutes;

  @Value("${blockage.number.of.trials}")
  private String blockageNumberOfTrials;

  @Value("${summer.time}")
  private boolean summerTime;

  @Value("${profile.picture.max.size.bytes}")
  private String profilePictureMaximumSizeInBytes;

  @Value("${phone.number.pdf.max.size.bytes}")
  private String phoneNumberMaximumSizeInBytes;

  @Value("${azimut.url}")
  private String azimutUrl;

  @Value("${azimut.fund.images.url}")
  private String azimutFundImagesUrl;

  @Value("${azimut.bcc.mail}")
  private String azimutBccMail;

  @Value("${vl.url}")
  private String vlUrl;
  @Value("${vl.username}")
  private String vlUsername;
  @Value("${vl.password}")
  private String vlPassword;
  @Value("${vl.service}")
  private String vlService;
  @Value("${vl.sms.url}")
  private String vlCustomSMSUrl;
  @Value("${vl.sender}")
  private String vlSender;

  @Value("${opto.signer.url}")
  private String optoSignerUrl;
  @Value("${opto.signer.username}")
  private String optoSignerUsername;
  @Value("${opto.signer.password}")
  private String optoSignerPassword;

  @Value("${ezagel.url}")
  private String ezagelUrl;
  @Value("${ezagel.username}")
  private String ezagelUsername;
  @Value("${ezagel.password}")
  private String ezagelPassword;
  @Value("${ezagel.service}")
  private String ezagelService;
  @Value("${ezagel.sender}")
  private String ezagelSender;

  @Value("${twilio.account.sid}")
  private String twilioAccountSid;
  @Value("${twilio.auth.token}")
  private String twilioAuthToken;
  @Value("${twilio.service.sid}")
  private String twilioServiceSid;

  @Value("${gateid.url}")
  private String gateIdUrl;
  @Value("${gateid.username}")
  private String gateIdUsername;
  @Value("${gateid.password}")
  private String gateIdPassword;
  @Value("${gateid.project.id}")
  private String gateIdProjectId;

  @Value("${enroll.url}")
  private String enrollUrl;
  @Value("${enroll.tenant.id}")
  private String enrollTenantId;
  @Value("${enroll.tenant.secret}")
  private String enrollTenantSecret;
  @Value("${enroll.client.id}")
  private String enrollClientId;
  @Value("${enroll.client.secret}")
  private String enrollClientSecret;

  @Value("${tea.computers.url}")
  private String teaComputersUrl;
  @Value("${tea.computers.eport.url}")
  private String teaComputersEportUrl;

  @Value("${tea.computers.key}")
  private String teaComputersKey;

  @Value("${tea.computers.eportfolio.key}")
  private String teaComputersEportfolioKey;

  @Value("${tea.computers.eportfolio.username}")
  private String teaComputersEportfolioUsername;

  @Value("${tea.computers.eportfolio.password}")
  private String teaComputersEportfolioPassword;

  @Value("${proxy.username}")
  private String proxyUsername;
  @Value("${proxy.password}")
  private String proxyPassword;
  @Value("${proxy.url}")
  private String proxyUrl;
  @Value("${proxy.port}")
  private String proxyPort;

  @Value("${tea.computers.username}")
  private String teaComputersUsername;

  @Value("${tea.computers.password}")
  private String teaComputersPassword;

  @Value("${log.file.path}")
  private String logFilePath;

  @Value("${real.estate.page.id}")
  private String realEstatePageId;

  @Value("${page.size}")
  private String pageSize;

  @Value("${paymob.url}")
  private String paymobUrl;

  @Value("${paymob.order.url}")
  private String paymobOrderUrl;

  @Value("${paymob.api.key}")
  private String paymobApiKey;

  @Value("${paymob.public.key}")
  private String paymobPublicKey;

  @Value("${paymob.secret.key}")
  private String paymobSecretKey;

  @Value("${paymob.wallet.integration.id}")
  private String paymobWalletIntegrationId;

  @Value("${paymob.hmac}")
  private String paymobHmac;

  @Value("${paymob.callback.url}")
  private String paymobCallBackUrl;

  @Value("${paytabs.url}")
  private String paytabsUrl;

  @Value("${paytabs.profile.id}")
  private String paytabsProfileId;

  @Value("${paytabs.merchant.id}")
  private String paytabsMerchantId;

  @Value("${paytabs.server.key}")
  private String paytabsServerKey;

  @Value("${paytabs.client.key}")
  private String paytabsClientKey;

  @Value("${paytabs.mobile.server.key}")
  private String paytabsMobileServerKey;

  @Value("${paytabs.mobile.client.key}")
  private String paytabsMobileClientKey;

  @Value("${paytabs.callback.url}")
  private String paytabsCallBackUrl;

  @Value("${paytabs.return.url}")
  private String paytabsReturnUrl;

  @Value("${temp.file.delete.delay.hours}")
  private String tempFileDeleteDelayInMinutes;

  @Value("${firebase.url}")
  private String firebaseUrl;

  @Value("${firebase.web.key}")
  private String firebaseWebKey;

  @Value("${spring.mail.username}")
  private String mailUserName;

  @Value("${mail.from}")
  private String mailFrom;

  @Value("${app.url}")
  private String appUrl;

  @Value("${is.production}")
  private boolean isProduction;

  @Value("${trading.path}")
  private String tradingPath;

  @Value("${otp.path}")
  private String otpPath;

  @Value("${login.path}")
  private String loginPath;

  @Value("${app.version}")
  private String appVersion;

  @Value("${fra.url}")
  private String fraUrl;
  @Value("${fra.api.key}")
  private String fraApiKey;
  @Value("${fra.company.name}")
  private String fraCompanyName;

  public Integer getBlockageNumberOfTrialsInt() {
    return Integer.parseInt(blockageNumberOfTrials);
  };

  @Profile("dev")
  @Bean
  public String devDatabaseConnection() {
    System.out.println("DEVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVV");
    return "DB connection for DEV - H2";
  }

  @Profile("test")
  @Bean
  public String testDatabaseConnection() {
    System.out.println("TEEEEEEEEEEEEEESSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSST");
    return "DB Connection to RDS_TEST - Low Cost Instance";
  }

  @Profile("prod")
  @Bean
  public String prodDatabaseConnection() {
    System.out.println("PROOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOOD");
    return "DB Connection to RDS_TEST - Low Cost Instance";
  }

}
