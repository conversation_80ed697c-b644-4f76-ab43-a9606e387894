import { Fragment } from "react";
import { Link } from "react-router-dom";

import LocaleChanger from "@/components/shared/LocaleChanger/LocaleChanger";

import logo from "../assets/img/logo.png";

interface AuthLayoutProps {
  title: string;
  description: string;
  children: React.ReactNode;
}

function AuthLayout({ title, description, children }: AuthLayoutProps) {
  return (
    <Fragment>
      <title>{title}</title>

      <div className="flex-col py-4 u-flex-center-screen">
        <div className="container">
          <div className="flex flex-col items-center gap-4">
            <Link
              to="/"
              aria-label="Return to home page"
              title="Return to home page"
            >
              <img
                className="h-12 text-primary-500 dark:text-white"
                src={logo}
              />
            </Link>
            <h1 className="m-0 text-3xl font-bold capitalize">{title}</h1>
            <p className="m-0 text-lg u-text-muted">{description}</p>
            <div className="w-full max-w-lg">{children}</div>
          </div>

          <div className="flex mt-8">
            <LocaleChanger className="mx-auto" />
          </div>
        </div>
      </div>
    </Fragment>
  );
}

export default AuthLayout;
