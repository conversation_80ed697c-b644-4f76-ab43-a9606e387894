import "./assets/styles/tailwind/tailwind.scss";

import { Provider as <PERSON><PERSON><PERSON><PERSON><PERSON> } from "jotai";
import React from "react";
import ReactDOM from "react-dom/client";
import { Provider } from "react-redux";

import { ToastContainer } from "./plugins/hot-toast";
import { I18nCustomProvider } from "./plugins/i18n-next";
import Root from "./Root";
import { store } from "./store/store";

ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
  <React.StrictMode>
    <Provider store={store}>
      <JotaiProvider>
        <I18nCustomProvider>
          <Root />
          <ToastContainer />
        </I18nCustomProvider>
      </JotaiProvider>
    </Provider>
  </React.StrictMode>
);
