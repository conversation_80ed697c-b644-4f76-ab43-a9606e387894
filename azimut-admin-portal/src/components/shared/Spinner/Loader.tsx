import { CSSProperties } from "react";
import { DotLoader } from "react-spinners";

const override: CSSProperties = {
  display: "block",
  margin: "0 auto",
  borderColor: "red",
};

const Loader = () => {
  return (
    <div
      className="sweet-loading flex-col py-4 u-flex-center-screen items-center gap-4"
      style={{ position: "absolute", width: "100%", minHeight: "50%" }}
    >
      <DotLoader
        color={"#3b82f6"}
        loading={true}
        cssOverride={override}
        size={80}
        speedMultiplier={1.5}
        aria-label="Loading Spinner"
        data-testid="loader"
      />
    </div>
  );
};

export default Loader;
