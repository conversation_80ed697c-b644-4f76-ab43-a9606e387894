import CloseIcon from "@mui/icons-material/Close";
import FilterListIcon from "@mui/icons-material/FilterList";
import {
  Autocomplete,
  Box,
  Divider,
  Drawer,
  IconButton,
  MenuItem,
  TextField,
  Typography,
} from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { Dayjs } from "dayjs";
import { Fragment } from "react";
import { useTranslation } from "react-i18next";

interface FilterDrawerProps {
  open: boolean;
  onClose: () => void;

  // KYC Filters
  investmentAmount: string;
  setInvestmentAmount: (value: string) => void;
  investmentAmountStates: Record<string, any>;
  annualIncome: string;
  setAnnualIncome: (value: string) => void;
  annualIncomeStates: Record<string, any>;
  fatca: string;
  setFatca: (value: string) => void;
  fatcaStates: Record<string, any>;
  showKycFilters: boolean;

  // Financial Filters
  minTopup: number | undefined;
  handleMinTopup: (e: React.ChangeEvent<HTMLInputElement>) => void;
  maxTopup: number | undefined;
  handleMaxTopup: (e: React.ChangeEvent<HTMLInputElement>) => void;
  minWithdrawal: number | undefined;
  handleMinWithdrawal: (e: React.ChangeEvent<HTMLInputElement>) => void;
  maxWithdrawal: number | undefined;
  handleMaxWithdrawal: (e: React.ChangeEvent<HTMLInputElement>) => void;
  minBuy: number | undefined;
  handleMinBuy: (e: React.ChangeEvent<HTMLInputElement>) => void;
  maxBuy: number | undefined;
  handleMaxBuy: (e: React.ChangeEvent<HTMLInputElement>) => void;
  minSell: number | undefined;
  handleMinSell: (e: React.ChangeEvent<HTMLInputElement>) => void;
  maxSell: number | undefined;
  handleMaxSell: (e: React.ChangeEvent<HTMLInputElement>) => void;
  minBalance: number | undefined;
  handleMinBalance: (e: React.ChangeEvent<HTMLInputElement>) => void;
  maxBalance: number | undefined;
  handleMaxBalance: (e: React.ChangeEvent<HTMLInputElement>) => void;
  fundId: number | undefined;
  setFundId: (value: number | undefined) => void;
  funds: any[];
  showFinancialFilters: boolean;

  // Location Filters
  countryId: number | undefined;
  setCountryId: (value: number | undefined) => void;
  countries: any;
  cityId: number | undefined;
  setCityId: (value: number | undefined) => void;
  cities: any;
  loadingCities: boolean;

  // Date Range Filters
  startDate: Dayjs | null;
  setStartDate: (value: Dayjs | null) => void;
  endDate: Dayjs | null;
  setEndDate: (value: Dayjs | null) => void;

  setPage: (value: number) => void;
}

export const FilterDrawer = ({
  open,
  onClose,
  investmentAmount,
  setInvestmentAmount,
  investmentAmountStates,
  annualIncome,
  setAnnualIncome,
  annualIncomeStates,
  fatca,
  setFatca,
  fatcaStates,
  showKycFilters,
  minTopup,
  handleMinTopup,
  maxTopup,
  handleMaxTopup,
  minWithdrawal,
  handleMinWithdrawal,
  maxWithdrawal,
  handleMaxWithdrawal,
  minBuy,
  handleMinBuy,
  maxBuy,
  handleMaxBuy,
  minSell,
  handleMinSell,
  maxSell,
  handleMaxSell,
  minBalance,
  handleMinBalance,
  maxBalance,
  handleMaxBalance,
  fundId,
  setFundId,
  funds,
  showFinancialFilters,
  countryId,
  setCountryId,
  countries,
  cityId,
  setCityId,
  cities,
  loadingCities,
  startDate,
  setStartDate,
  endDate,
  setEndDate,
  setPage,
}: FilterDrawerProps) => {
  const { t } = useTranslation("common");

  return (
    <Drawer
      anchor="right"
      open={open}
      onClose={onClose}
      sx={{
        "& .MuiDrawer-paper": {
          width: 400,
          padding: 3,
        },
      }}
    >
      <Box
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          mb: 1,
        }}
      >
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          <FilterListIcon />
          <Typography variant="h6">{t("Advanced Filters")}</Typography>
        </Box>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </Box>

      {/* KYC Filters */}
      {showKycFilters && (
        <Fragment>
          <Typography
            variant="subtitle1"
            sx={{ fontWeight: 600, mb: 2 }}
          >
            {t("KYC Filters")}
          </Typography>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2, mb: 3 }}>
            <Box sx={{ display: "flex", gap: 2 }}>
              <TextField
                select
                fullWidth
                label={t("Investment Size")}
                size="small"
                value={investmentAmount}
                onChange={e => {
                  setInvestmentAmount(e.target.value);
                  setPage(0);
                }}
              >
                {Object.entries(investmentAmountStates).map(option => (
                  <MenuItem
                    key={option[0]}
                    value={option[0]}
                  >
                    {t(option[1].label)}
                  </MenuItem>
                ))}
              </TextField>

              <TextField
                select
                fullWidth
                label={t("Annual Income")}
                size="small"
                value={annualIncome}
                onChange={e => {
                  setAnnualIncome(e.target.value);
                  setPage(0);
                }}
              >
                {Object.entries(annualIncomeStates).map(option => (
                  <MenuItem
                    key={option[0]}
                    value={option[0]}
                  >
                    {t(option[1].label)}
                  </MenuItem>
                ))}
              </TextField>
            </Box>

            <Box sx={{ display: "flex", gap: 2 }}>
              <TextField
                select
                fullWidth
                label={t("FATCA")}
                size="small"
                value={fatca}
                onChange={e => {
                  setFatca(e.target.value);
                  setPage(0);
                }}
              >
                {Object.entries(fatcaStates).map(option => (
                  <MenuItem
                    key={option[0]}
                    value={option[0]}
                  >
                    {t(option[1].label)}
                  </MenuItem>
                ))}
              </TextField>
              <Box sx={{ flex: 1 }} />
            </Box>
          </Box>
          <Divider sx={{ mb: 1 }} />
        </Fragment>
      )}

      {/* Financial Filters */}
      {showFinancialFilters && (
        <Fragment>
          <Typography
            variant="subtitle1"
            sx={{ fontWeight: 600, mb: 2 }}
          >
            {t("Financial Filters")}
          </Typography>
          <Box sx={{ display: "flex", flexDirection: "column", gap: 2, mb: 3 }}>
            <Box sx={{ display: "flex", gap: 2 }}>
              <TextField
                fullWidth
                label={t("Min Topup")}
                type="number"
                size="small"
                defaultValue={minTopup}
                onChange={handleMinTopup}
              />
              <TextField
                fullWidth
                label={t("Max Topup")}
                type="number"
                size="small"
                defaultValue={maxTopup}
                onChange={handleMaxTopup}
              />
            </Box>

            <Box sx={{ display: "flex", gap: 2 }}>
              <TextField
                fullWidth
                label={t("Min Withdrawal")}
                type="number"
                size="small"
                defaultValue={minWithdrawal}
                onChange={handleMinWithdrawal}
              />
              <TextField
                fullWidth
                label={t("Max Withdrawal")}
                type="number"
                size="small"
                defaultValue={maxWithdrawal}
                onChange={handleMaxWithdrawal}
              />
            </Box>

            <Box sx={{ display: "flex", gap: 2 }}>
              <TextField
                fullWidth
                label={t("Min Buy")}
                type="number"
                size="small"
                defaultValue={minBuy}
                onChange={handleMinBuy}
              />
              <TextField
                fullWidth
                label={t("Max Buy")}
                type="number"
                size="small"
                defaultValue={maxBuy}
                onChange={handleMaxBuy}
              />
            </Box>

            <Box sx={{ display: "flex", gap: 2 }}>
              <TextField
                fullWidth
                label={t("Min Sell")}
                type="number"
                size="small"
                defaultValue={minSell}
                onChange={handleMinSell}
              />
              <TextField
                fullWidth
                label={t("Max Sell")}
                type="number"
                size="small"
                defaultValue={maxSell}
                onChange={handleMaxSell}
              />
            </Box>

            <Box sx={{ display: "flex", gap: 2 }}>
              <Autocomplete
                fullWidth
                options={funds?.filter(e => e.teacomputerId) || []}
                getOptionLabel={option => option.name}
                value={funds?.find(f => f.teacomputerId === fundId) || null}
                onChange={(event: any, newValue) => {
                  setFundId(newValue?.teacomputerId);
                  setPage(0);
                }}
                renderInput={params => (
                  <TextField
                    {...params}
                    label={t("Fund")}
                    size="small"
                  />
                )}
              />
              <Box sx={{ flex: 1 }} />
            </Box>
            <Box sx={{ display: "flex", gap: 2 }}>
              <TextField
                fullWidth
                label={t("Min Balance")}
                type="number"
                size="small"
                defaultValue={minBalance}
                onChange={handleMinBalance}
              />
              <TextField
                fullWidth
                label={t("Max Balance")}
                type="number"
                size="small"
                defaultValue={maxBalance}
                onChange={handleMaxBalance}
              />
            </Box>
          </Box>
          <Divider sx={{ mb: 1 }} />
        </Fragment>
      )}

      {/* Date Range Filters */}
      <Typography
        variant="subtitle1"
        sx={{ fontWeight: 600, mb: 2 }}
      >
        {t("Date Range")}
      </Typography>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <Box sx={{ display: "flex", gap: 2 }}>
            <DatePicker
              label={t("Start Date")}
              value={startDate}
              onChange={(e: Dayjs | null) => {
                if (!e || e?.isValid()) {
                  setStartDate(e);
                  setPage(0);
                }
              }}
              slotProps={{
                textField: { size: "small", fullWidth: true },
              }}
            />
            <DatePicker
              label={t("End Date")}
              value={endDate}
              onChange={(e: Dayjs | null) => {
                if (!e || e?.isValid()) {
                  setEndDate(e);
                  setPage(0);
                }
              }}
              slotProps={{
                textField: { size: "small", fullWidth: true },
              }}
            />
          </Box>
        </LocalizationProvider>
      </Box>

      <Divider sx={{ mb: 1 }} />
      {/* Location Filters */}
      <Typography
        variant="subtitle1"
        sx={{ fontWeight: 600, mb: 2 }}
      >
        {t("Location")}
      </Typography>
      <Box sx={{ display: "flex", flexDirection: "column", gap: 2, mb: 3 }}>
        <Box sx={{ display: "flex", gap: 2 }}>
          <Autocomplete
            fullWidth
            size="small"
            options={countries?.countries || []}
            getOptionLabel={(option: any) => option.englishCountryName}
            value={
              countries?.countries?.find(
                (c: any) => c.systemCountryCode === countryId
              ) || null
            }
            onChange={(event: any, newValue: any) => {
              setCountryId(newValue?.systemCountryCode);
              setCityId(undefined);
              setPage(0);
            }}
            renderInput={params => (
              <TextField
                {...params}
                label={t("Country")}
              />
            )}
          />
          <Autocomplete
            fullWidth
            size="small"
            options={cities?.cities || []}
            disabled={!countryId || loadingCities}
            getOptionLabel={(option: any) => option.englishCityName}
            value={
              cities?.cities?.find((c: any) => c.systemCityCode === cityId) ||
              null
            }
            onChange={(event: any, newValue: any) => {
              setCityId(newValue?.systemCityCode);
              setPage(0);
            }}
            renderInput={params => (
              <TextField
                {...params}
                label={t("City")}
              />
            )}
          />
        </Box>
      </Box>
    </Drawer>
  );
};
