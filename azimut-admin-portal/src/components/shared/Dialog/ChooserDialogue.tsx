import {
  <PERSON>alog,
  DialogTitle,
  <PERSON>,
  <PERSON>Item,
  ListItemButton,
  ListItemText,
} from "@mui/material";
import { useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useDispatch } from "react-redux";

import {
  getFileFromBase64,
  listCertificates,
  optoSignContract,
  signContract,
} from "../../../api/services/sign-contract";
import { azimutApi } from "../../../hooks/azimutApi";

export interface SimpleDialogProps {
  toSignUsers: string[];
  setLoading: (value: boolean) => void;
}

export interface Certificate {
  commonname: string;
  subjectName: string;
  issuer: string;
  startdate: string;
  enddate: string;
  thumbprint: string;
}

function ChooserDialog(props: SimpleDialogProps) {
  const { setLoading, toSignUsers } = props;

  const [open, setOpen] = useState(false);
  const [certificates, setCertificates] = useState<Certificate[]>([]);

  const [getContract] = azimutApi.useLazyGetContractQuery();
  const [sendSignedContract, { error, isError }] =
    azimutApi.useSignContractMutation();

  const dispatch = useDispatch();

  useEffect(() => {
    if (toSignUsers.length > 0) handleChooseSignature();
  }, [toSignUsers]);

  useEffect(() => {
    if (isError || error) {
      const errorVal = error as any;
      const errorString = errorVal.data.message || errorVal.message;
      toast.error(`Error: ${errorString}`);
      console.error(`Sign Error = ${error}`);
    }
  }, [isError, error]);

  const handleClose = () => {
    setOpen(false);
  };

  const handleListItemClick = (value: Certificate) => {
    setOpen(false);
    if (value) {
      handleSignContract(value);
    }
  };

  const handleChooseSignature = async () => {
    const res = await listCertificates();
    console.log(res.certificateDetailsArray);
    if (res.certificateDetailsArray) {
      const certificatesList = res.certificateDetailsArray.filter(
        (e: any) =>
          e.thumbprint.toLowerCase() ===
            "290db09184b0f24746330f446511745122fba6bb" ||
          e.thumbprint.toLowerCase() ===
            "0650c266eb7e1446010c08ecfae3660d816171b4" ||
          e.thumbprint.toLowerCase() ===
            "6FC4E062E002741E8C851592D46F5444234130A9".toLowerCase() // new cetificate
      );
      res.certificateDetailsArray = res.certificateDetailsArray.filter(
        (e: any) => !e.subjectName || !e.subjectName.includes("localhost")
      );
      if (certificatesList.length === 1)
        handleSignContract(certificatesList[0]);
      else if (res.certificateDetailsArray.length === 1)
        handleSignContract(res.certificateDetailsArray[0]);
      else {
        setCertificates(
          certificatesList.length > 1
            ? certificatesList
            : res.certificateDetailsArray
        );
        setOpen(true);
      }
    } else toast.error("Couldn't get certificates");
  };

  const handleSignContract = async (value: Certificate) => {
    setLoading(true);
    for (const userId of toSignUsers) {
      const contract = await getContract(userId).unwrap();
      let signedContractBase64;
      if (value.subjectName) {
        const optoRes = await optoSignContract(
          contract.trim(),
          value.thumbprint
        );
        signedContractBase64 = optoRes?.signedPdfBase64;
      } else {
        const deltaRes = await signContract(contract, value.thumbprint);
        signedContractBase64 = deltaRes?.data;
      }
      if (signedContractBase64) {
        const blob = getFileFromBase64(signedContractBase64);
        try {
          const res = await sendSignedContract({
            file: blob,
            userId,
          }).unwrap();
          if (res.status == 0)
            toast.success(`User ${userId} Contract Submitted Successfully`);
          else toast.error(`User ${userId} Error: ${res.message}`);
        } catch (error: any) {
          toast.error(`User ${userId} Error: ${error?.data?.message}`);
        }
      } else {
        toast.error("Error signing contract");
      }
    }
    dispatch(azimutApi.util.invalidateTags(["ClientsList"]));
    setLoading(false);
  };

  return (
    <Dialog
      onClose={handleClose}
      open={open}
    >
      <DialogTitle>Choose Certificate</DialogTitle>
      <List sx={{ pt: 0 }}>
        {certificates.map(certificate => (
          <ListItem
            disableGutters
            key={certificate.thumbprint}
          >
            <ListItemButton onClick={() => handleListItemClick(certificate)}>
              <ListItemText primary={certificate.commonname} />
            </ListItemButton>
          </ListItem>
        ))}
      </List>
    </Dialog>
  );
}

export default ChooserDialog;
