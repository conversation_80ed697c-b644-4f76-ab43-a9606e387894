import { Button } from "@mui/material";
import { useTranslation } from "react-i18next";

import Dialog from "./Dialog";

interface ConfirmationDialogProps {
  open: boolean;
  title: string;
  message: string;
  onClose: () => void;
  onConfirm: () => void;
  confirmText?: string;
  cancelText?: string;
  confirmColor?:
    | "primary"
    | "secondary"
    | "error"
    | "info"
    | "success"
    | "warning";
  loading?: boolean;
}

const ConfirmationDialog = ({
  open,
  title,
  message,
  onClose,
  onConfirm,
  confirmText,
  cancelText,
  confirmColor = "primary",
  loading = false,
}: ConfirmationDialogProps) => {
  const { t } = useTranslation("common");

  return (
    <Dialog
      open={open}
      title={title}
      onClose={onClose}
      actions={
        <div className="flex justify-end gap-2 p-4">
          <Button
            variant="outlined"
            onClick={onClose}
          >
            {cancelText || t("Cancel")}
          </Button>
          <Button
            variant="contained"
            color={confirmColor}
            onClick={onConfirm}
            disabled={loading}
          >
            {confirmText || t("Confirm")}
          </Button>
        </div>
      }
    >
      <p>{message}</p>
    </Dialog>
  );
};

export default ConfirmationDialog;
