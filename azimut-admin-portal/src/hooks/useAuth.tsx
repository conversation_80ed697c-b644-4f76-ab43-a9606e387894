import { createContext, PropsWith<PERSON>hildren, useContext, useMemo } from "react";

import { AuthUser } from "./types";
import { useLocalStorage } from "./useLocalStorage";

type Context = {
  user: AuthUser;
  setUser: (data: AuthUser | null) => void;
};

const AuthContext = createContext<Context | null>(null);

export const AuthProvider = ({ children }: PropsWithChildren) => {
  const [user, setUser] = useLocalStorage("user", null);

  const value = useMemo(
    () => ({
      user,
      setUser,
    }),
    [user, setUser]
  );
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  return useContext(AuthContext);
};
