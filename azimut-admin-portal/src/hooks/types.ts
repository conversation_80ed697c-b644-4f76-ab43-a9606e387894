export type AuthUser = {
  auth: string;
  refreshToken: string;
  user: User;
};

export type AzimutUser = {
  id: number;
  userId: string; // national id or passport
  userIdType: string;
  idType: number;
  nickName: string;
  countryPhoneCode: string;
  phoneNumber: string;
  isEmailVerified: boolean;
  hasSecurityQuestions: boolean;
  contractMap: number;
  country: string;
  countryCode: string;
  createdAt: string;
  updatedAt: string;
  contractSignedAt: string;
  dateOfBirth: string;
  dateOfIdExpiry: string;
  dateOfRelease: string;
  deviceId: string;
  userStep: number;
  firstName: string;
  lastName: string;
  genderId: number;
  blockageCount: number;
  changePhoneCount: number;
  reviewCount: number;
  failedLogins: number;
  hasInjected: boolean;
  city: string;
  isChangeNoApproved: boolean;
  signedPdf: string;
  pdfSignedAt: string;
  isSynchronized: boolean;
  lastSolvedPageId: number;
  livenessChecked: boolean;
  otherIdType: number;
  otherNationality: string;
  referralCode: string;
  solvedPages: string;
  enrollApplicantId: string;
  azimutIdTypeId: number;
  pageNumber: number;
  emailAddress: string;
  messagingToken: string;
  flowId: number;
  userPhone: string;
  firstPageId: number;
  kycStatus: number;
  isOld: boolean;
  provider: string;
  providerId: string;
  username: string;
  isVerified: boolean;
  azimutAccount: AzimutAccount;
  reviews: Review[];
  userImages: UserImage[];
  idData: string;
  cso: string;
  ntra: string;
  aml: string;
  fraStoreId: string;
  fraCompanyStoreId: string;
  userLocation: UserLocation;
  azimutAmlMatches: number;
  risk: string;
};

export type UserBankAccount = {
  bankId: number;
  currencyId: number;
  accountNo: string;
  iban: string;
  bankType: string; // BANK or WALLET
};

type UserLocation = {
  longt: string;
  lat: string;
};

type UserImage = {
  id: number;
  imageName: string;
  imageSubDirectory: string;
  imageType: number;
  imageUrl: string;
  userIdType: number;
  userPhone: string;
};

type AzimutAccount = {
  addressAr: string;
  addressEn: string;
  cityId: number;
  clientAML: number;
  countryId: number;
  nationalityId: number;
  occupation: string;
};

export type RejectionReason = {
  editedAt?: string;
  editedBy?: number;
  id?: number;
  reason: string;
  reasonAr: string;
};

export type NotificationTemplate = {
  createdAt?: string;
  id?: number;
  title: string;
  body: string;
  titleAr: string;
  bodyAr: string;
  link?: string;
  sent?: boolean;
  sentGroups?: string[];
};

export type PopupTemplate = {
  createdAt?: string;
  id?: number;
  title: string;
  body: string;
  titleAr: string;
  bodyAr: string;
  link?: string;
  image: string; // URL to the image
  sent?: boolean;
  sentGroups?: string[];
};

export type Dividend = {
  createdAt?: string;
  id?: number;
  fundId?: number;
  teacomputerId: number;
  dividendDate: string;
  amount: number;
};

export type Fund = {
  createdAt?: string;
  id: number;
  name: string;
  teacomputerId: number;
};

export type ListUsers = {
  currentPage: number;
  dataList: AzimutUser[];
  hasNext: boolean;
  hasPrevious: boolean;
  numberOfItems: number;
  numberOfPages: number;
  pageSize: number;
};

export type ListTransactions = {
  currentPage: number;
  dataList: any[];
  hasNext: boolean;
  hasPrevious: boolean;
  numberOfItems: number;
  numberOfPages: number;
  pageSize: number;
};

export type ListNotifications = {
  currentPage: number;
  dataList: NotificationTemplate[];
  hasNext: boolean;
  hasPrevious: boolean;
  numberOfItems: number;
  numberOfPages: number;
  pageSize: number;
};

export type ListPopups = {
  currentPage: number;
  dataList: PopupTemplate[];
  hasNext: boolean;
  hasPrevious: boolean;
  numberOfItems: number;
  numberOfPages: number;
  pageSize: number;
};

export type GetNotificationsFilter = {
  sent?: string;
  page: number;
  rowsPerPage: number;
  title?: string;
};

export type GetPopupsFilter = {
  sent?: string;
  page: number;
  rowsPerPage: number;
  title?: string;
};

export type ListDividends = {
  currentPage: number;
  dataList: Dividend[];
  hasNext: boolean;
  hasPrevious: boolean;
  numberOfItems: number;
  numberOfPages: number;
  pageSize: number;
};

export type GetDividendsFilter = {
  fundId: number | undefined;
  page: number;
  rowsPerPage: number;
};

type SearchAndFilter =
  | {
      key: string;
      value: string;
      operation: string;
    }
  | {
      key: string;
      values: string[];
      operation: string;
    };
export type ListUserFilteredParam = {
  pageNumber: number;
  pageSize: number;
  searchesAndFilters: SearchAndFilter[];
  sortingParam: string;
};

export type Order = "asc" | "desc";

export type GetTransactionsFilter = {
  synced: string;
  startDate?: string;
  endDate?: string;
  page: number;
  rowsPerPage: number;
};

export type ComplianceLog = {
  adminUserId: number;
  checkType: string;
  createdAt: string;
};

export type GetUsersFilter = {
  name: string;
  kycStatus: string;
  userStep: string;
  email: string;
  phone: string;
  id: number | undefined;
  userId: string;
  contractMap: string;
  countryId?: number;
  cityId?: number;
  validation: string;
  negativeListType: string;
  isDigital: boolean;
  isVerified: string;
  isSynchronized: string;
  contractSigned: string;
  nationality: string;
  referralCode: string;
  startDate?: string;
  endDate?: string;
  adminUserId?: number;
  minTopup?: number;
  maxTopup?: number;
  minWithdrawal?: number;
  maxWithdrawal?: number;
  minBuy?: number;
  maxBuy?: number;
  minSell?: number;
  maxSell?: number;
  fundId?: number;
  minBalance?: number;
  maxBalance?: number;
  order: Order;
  orderBy: string;
  page: number;
  rowsPerPage: number;
  investmentAmount: string;
  annualIncome: string;
  fatca: string;
  sentToFra: string;
};

export type KycPage = {
  id: number;
  isAnswered: boolean;
  nextId: number;
  noOfQuestions: number;
  pageOrder: number;
  previousId: number;
  questions: Question[];

  title: string;
  verificationPercentage: number;
};

export type Question = {
  id: number;
  questionText: string;
  answerType: AnswerType;
  questionOrder: number;
  isAnswerMandatory: boolean;
  width: number;
  answers: Answer[];
  review: Review;
  userAnswers: UserAnswer[];
};

export type UserAnswer = {
  answerId: number;
  answerType: AnswerType;
  answerValue: string;
  documentName: string;
  documentSubDirectory: string;
  documentURL: string;
  id: number;
  questionId: number;
  relatedUserAnswers: UserAnswer[];
};

export type Review = {
  createdAt?: string;
  actionMaker?: number;
  id?: number;
  mandatoryQuestion?: boolean;
  pageId: number;
  questionId: number;
  status: number;
  reasonId?: number;
  comment?: string;
  updated?: boolean;
};

export type ReviewPage = {
  appUserId: string;
  nextPageId: number;
  pageId: number;
  pageOrder: number;
  reviews: Review[];
};

export type Answer = {
  answerOption: string;
  answerOrder: string;
  answerPlaceHolder: string;
  answerType: AnswerType;
  id: number;
  isAnswerMandatory: boolean;
  isRelatedAnswerMandatory: boolean;
  pdFieldName: string;
  relatedAnswers: Answer[];
  relatedQuestionText: string;
};

export enum AnswerType {
  RADIO = "RADIO",
  CHECK = "CHECK",
  DROP = "DROP",
  TEXT = "TEXT",
  RICH = "RICH",
  EMAIL = "EMAIL",
  CALENDER = "CALENDER",
  DOCUMENT = "DOCUMENT",
  PHONE = "PHONE",
}

export type BankAccount = {
  accountId: number;
  accountNumber: string;
  accountStatus: number;
  arabicBankName: string;
  arabicBranchName: string;
  arabicCurrencyName: string;
  bankId: number;
  branchId: number;
  currencyId: number;
  englishBankName: string;
  englishBranchName: string;
  englishCurrencyName: string;
  iban: string;
  isLocal: boolean;
  status: number;
  statusName: string;
  reviews: Review[]; // todo check
};

export interface User {
  id?: number;
  fullName: string;
  username?: string;
  password?: string;
  permissions?: string | string[];
  emailAddress: string;
  firstName?: string;
  roleId?: number;
  isFA?: boolean;
}

export interface Role {
  id?: number;
  name: string;
  permissions: string;
}

export type UserCredentials = { email: string; password: string };

export type AzimutAuthRes = {
  status: number;
  message: string;
  response: {
    credentials: {
      token: {
        tokenString: string;
        tokenExpiry: string;
        refreshTokenString: string;
        refreshTokenExpiry: string;
      };
      user: User;
      flowId: number;
    };
  };
};

export type UserFund = {
  availableToSell: number;
  avgcost: number;
  currencyId: number;
  currencyName: string;
  currencyRate: number;
  fundName: string;
  fundType: string;
  lastPriceUpdateDate: string;
  logo: string;
  quantity: number;
  teacomputerId: number;
  totalAmount: number;
  tradePrice: number;
};

export type NegativeListResult = {
  negativeList: NegativeListEntry;
  matchType: string; // EXACT, FUZZY, PARTIAL
  matchedField: string; // FULL_NAME, FIRST_NAME, LAST_NAME, ALIAS, PASSPORT, NATIONAL_ID
  confidenceScore: number;
};

export type NegativeListEntry = {
  id: number;
  fullName: string;
  firstName?: string;
  lastName?: string;
  alias?: string;
  nationality?: string;
  dateOfBirth?: string;
  placeOfBirth?: string;
  passportNumber?: string;
  nationalId?: string;
  listSource: string;
  listType?: string;
  referenceNumber?: string;
  address?: string;
  profession?: string;
  additionalInfo?: string;
  createdAt?: string;
  updatedAt?: string;
};

export type NegativeListStatistics = {
  totalRecords: number;
  totalFiles: number;
  recordsBySource: Record<string, number>;
  recordsByType: Record<string, number>;
};

export type NegativeListSearchFilter = {
  searchTerm?: string;
  listSource?: string;
  nationality?: string;
  dateOfBirth?: string;
  passportNumber?: string;
  nationalId?: string;
  searchType?: string;
};

export type NegativeListAdd = {
  fileUrl?: string;
  listType: string;
  description?: string;
};

export type AdminUserMapping = {
  id: number;
  user: AzimutUser;
  adminUser: User;
  createdBy: User;
  createdAt: string;
};

export type CreateUserMappingRequest = {
  adminUserId: number;
  userIds: string[];
};

export type SearchAndFilterWithParent = SearchAndFilter & {
  parentColumn?: string;
};

export type GetMappingsByAdminFilter = {
  pageNumber: number;
  pageSize: number;
  sortingParam: string;
  asc: boolean;
  searchesAndFilters: SearchAndFilterWithParent[];
};

export type ListUserMappings = {
  currentPage: number;
  dataList: AdminUserMapping[];
  hasNext: boolean;
  hasPrevious: boolean;
  numberOfItems: number;
  numberOfPages: number;
  pageSize: number;
};
