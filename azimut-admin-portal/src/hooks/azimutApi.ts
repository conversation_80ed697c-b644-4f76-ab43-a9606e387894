import {
  BaseQueryFn,
  create<PERSON><PERSON>,
  <PERSON>tchA<PERSON><PERSON>,
  fetchBaseQuery,
  FetchBaseQueryError,
} from "@reduxjs/toolkit/query/react";

import {
  AuthUser,
  AzimutUser,
  BankAccount,
  ComplianceLog,
  Dividend,
  Fund,
  GetDividendsFilter,
  GetNotificationsFilter,
  GetPopupsFilter,
  GetTransactionsFilter,
  GetUsersFilter,
  KycPage,
  ListDividends,
  ListNotifications,
  ListPopups,
  ListTransactions,
  ListUsers,
  NegativeListResult,
  NegativeListSearchFilter,
  NegativeListStatistics,
  NotificationTemplate,
  PopupTemplate,
  RejectionReason,
  Review,
  ReviewPage,
  UserBankAccount,
} from "./types";

const backendUrl = import.meta.env.VITE_APP_BACKEND_URL;

const baseQuery = fetchBaseQuery({
  baseUrl: backendUrl,
  prepareHeaders: async headers => {
    const user: AuthUser = JSON.parse(localStorage.getItem("user")!);
    const token = user.auth;
    const lang = JSON.parse(localStorage.getItem("language")!);
    localStorage.setItem("lastActivity", new Date().toString());

    // If we have a token set in state, let's assume that we should be passing it.
    if (token) {
      headers.set("authorization", `Bearer ${token}`);
    }
    headers.set("isadmin", "1");
    headers.set("Accept", "application/json");
    headers.set("lang", lang);

    return headers;
  },
});

export const baseQueryWithReauth: BaseQueryFn<
  string | FetchArgs,
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const result = await baseQuery(args, api, extraOptions);
  if (
    result.error &&
    (result.error.status === 403 || result.error.status === 401)
  ) {
    localStorage.removeItem("user");
  }
  return result;
};

const getKycPageArgs = (userId: string, pageId: string) => ({
  url: "/admin/review/kyc/getPageById",
  method: "POST",
  body: { appUserId: userId, id: pageId, isWeb: true },
});

export const azimutApi = createApi({
  reducerPath: "azimutApi",
  tagTypes: [
    "Clients",
    "ClientsList",
    "Reasons",
    "Transactions",
    "Notifications",
    "Popups",
    "Dividends",
    "NegativeList",
  ],
  baseQuery: baseQueryWithReauth,
  endpoints: builder => ({
    createFitsUser: builder.mutation<any, any>({
      query: body => ({
        url: "/admin/fitsUsers",
        method: "POST",
        body,
      }),
      transformResponse: (r: any) => r.response.fitsUser,
    }),
    uploadFitsContract: builder.mutation<any, { id: string; file: File }>({
      query: ({ id, file }) => {
        const formData = new FormData();
        formData.append("file", file);
        return {
          url: `/admin/fitsUsers/${id}/uploadContract`,
          method: "POST",
          body: formData,
        };
      },
      transformResponse: (r: any) => r.response.fitsUser,
    }),
    getFitsUser: builder.query<any, string>({
      query: userId => ({
        url: `/admin/fitsUsers/${userId}`,
        method: "GET",
      }),
      transformResponse: (r: any) => r.response.fitsUser,
    }),
    addFitsBankAccount: builder.mutation<
      any,
      { id: string; bank: UserBankAccount }
    >({
      query: ({ id, bank }) => {
        return {
          url: `/admin/fitsUsers/${id}/addBankAccount`,
          method: "POST",
          body: bank,
        };
      },
      transformResponse: (r: any) => r.response.fitsUser,
    }),
    fitsUsersSearch: builder.query<
      any,
      {
        firstName: string;
        lastName: string;
        mobile: string;
        idNumber: string;
        page: number;
        size: number;
      }
    >({
      query: body => ({
        url: "/admin/fitsUsers/search",
        method: "POST",
        body,
      }),
      transformResponse: (r: any) => r.response.fitsUsers,
    }),
    getFitsContract: builder.query<string, string>({
      query: userId => ({
        url: `/admin/fitsUsers/${userId}/downloadContract`,
        method: "GET",
        responseHandler: (response: any) => {
          if (!response.ok) {
            return { error: response.statusText, status: response.status };
          }
          return response.blob();
        },
      }),
      transformResponse: async (blob: Blob) => {
        const base64 = await blobToBase64(blob);
        return (
          "                        " + base64.substring(base64.indexOf(",") + 1)
        );
      },
    }),
    getLookupData: builder.query<
      any,
      { entityTypeId: number; countryId?: number; bankType?: string }
    >({
      query: body => ({
        url: "/api/azimut/user/getAzimutLookUpData",
        method: "POST",
        body,
      }),
      transformResponse: (r: any) => r.response.azAccount.lookupData,
    }),
    getAllReferralCodes: builder.query<any, void>({
      query: () => ({
        url: "/admin/fitsUsers/referralCodes",
        method: "GET",
      }),
      transformResponse: (r: any) => r.response.referralCodes,
    }),
    getUsers: builder.query<ListUsers, GetUsersFilter>({
      query: args => {
        const {
          name,
          kycStatus,
          userStep,
          email,
          phone,
          id,
          userId,
          contractMap,
          referralCode,
          adminUserId,
          startDate,
          endDate,
          isVerified,
          isSynchronized,
          validation,
          negativeListType,
          sentToFra,
          contractSigned,
          nationality,
          order,
          orderBy,
          isDigital,
          countryId,
          cityId,
          page,
          rowsPerPage,
          investmentAmount,
          minTopup,
          maxTopup,
          minWithdrawal,
          maxWithdrawal,
          minBuy,
          maxBuy,
          minSell,
          maxSell,
          fundId,
          minBalance,
          maxBalance,
          annualIncome,
          fatca,
        } = args;
        localStorage.setItem(
          "filters",
          JSON.stringify({
            ...args,
            rowsPerPage: rowsPerPage > 25 ? 10 : rowsPerPage,
          })
        );
        const searchesAndFilters: any = [];
        if (minTopup || maxTopup) {
          let values = [];
          if (!minTopup) values = ["0", maxTopup?.toString()];
          else if (!maxTopup) values = [minTopup.toString()];
          else values = [minTopup.toString(), maxTopup.toString()];
          searchesAndFilters.push({
            key: "totalTopup",
            values,
            operation: "INJECTED",
          });
        }
        if (minWithdrawal || maxWithdrawal) {
          let values = [];
          if (!minWithdrawal) values = ["0", maxWithdrawal?.toString()];
          else if (!maxWithdrawal) values = [minWithdrawal.toString()];
          else values = [minWithdrawal.toString(), maxWithdrawal.toString()];
          searchesAndFilters.push({
            key: "totalWithdraw",
            values,
            operation: "INJECTED",
          });
        }
        if (minBuy || maxBuy) {
          let values = [];
          if (!minBuy) values = ["0", maxBuy?.toString()];
          else if (!maxBuy) values = [minBuy.toString()];
          else values = [minBuy.toString(), maxBuy.toString()];
          searchesAndFilters.push({
            key: "totalBuyValue",
            values,
            operation: "INJECTED",
          });
        }
        if (minSell || maxSell) {
          let values = [];
          if (!minSell) values = ["0", maxSell?.toString()];
          else if (!maxSell) values = [minSell.toString()];
          else values = [minSell.toString(), maxSell.toString()];
          searchesAndFilters.push({
            key: "totalSellValue",
            values,
            operation: "INJECTED",
          });
        }
        if (minBalance || maxBalance) {
          let values = [];
          if (!minBalance) values = ["0", maxBalance?.toString()];
          else if (!maxBalance) values = [minBalance.toString()];
          else values = [minBalance.toString(), maxBalance.toString()];
          searchesAndFilters.push({
            key: fundId ? "funds_" + fundId : "totalPosition",
            values,
            operation: "INJECTED",
          });
        }
        if (countryId) {
          searchesAndFilters.push({
            key: "teacomputersCountryId",
            values: [countryId.toString()],
            operation: "FILTER",
          });
        }
        if (cityId) {
          searchesAndFilters.push({
            key: "teacomputersCityId",
            values: [cityId.toString()],
            operation: "FILTER",
          });
        }
        if (kycStatus === "0.1") {
          // auto accepted
          searchesAndFilters.push({
            key: "kycStatus",
            values: ["1"],
            operation: "FILTER",
          });
          searchesAndFilters.push({
            key: "",
            operation: "REVIEW_NOT_EXIST",
          });
          searchesAndFilters.push({
            key: "isOld",
            operation: "IS_NULL",
          });
        } else if (kycStatus === "1") {
          searchesAndFilters.push({
            key: "kycStatus",
            values: [kycStatus],
            operation: "FILTER",
          });
          searchesAndFilters.push({
            key: "",
            operation: "REVIEW_EXIST",
          });
        } else if (kycStatus == "2.1") {
          searchesAndFilters.push(
            ...[
              {
                key: "",
                values: ["1"],
                operation: "INJECTED",
              },
              {
                key: "kycStatus",
                values: ["1"],
                operation: "FILTER",
              },
              {
                key: "",
                operation: "REVIEW_EXIST",
              },
              {
                key: "none",
                operation: "VALIDATION",
              },
              {
                key: "isOld",
                operation: "IS_NULL",
              },
              {
                key: "signedPdf",
                operation: "IS_NULL",
              },
              {
                key: "isOld",
                operation: "IS_NULL",
              },
            ]
          );
        } else if (kycStatus !== "-1")
          searchesAndFilters.push({
            key: "kycStatus",
            values: [kycStatus],
            operation: "FILTER",
          });

        if (investmentAmount !== "-1") {
          searchesAndFilters.push({
            value: parseInt(investmentAmount),
            key: "38",
            operation: "QUESTION_ANSWER",
          });
        }
        if (annualIncome !== "-1") {
          searchesAndFilters.push({
            value: parseInt(annualIncome),
            key: "55",
            operation: "QUESTION_ANSWER",
          });
        }
        if (fatca !== "-1") {
          searchesAndFilters.push({
            value: parseInt(fatca),
            key: "82",
            operation: "QUESTION_ANSWER",
          });
        }

        if (userStep !== "-1") {
          if (userStep == "1")
            searchesAndFilters.push({
              key: "userStep",
              values: [0, 1, 2],
              operation: "FILTER",
            });
          else if (userStep == "7")
            searchesAndFilters.push({
              key: "userStep",
              values: [6, 7],
              operation: "FILTER",
            });
          else
            searchesAndFilters.push({
              key: "userStep",
              values: [parseInt(userStep)],
              operation: "FILTER",
            });
        }

        if (negativeListType !== "-1") {
          if (negativeListType == "1") {
            searchesAndFilters.push({
              key: "teacomputersClientaml",
              values: ["0"],
              operation: "FILTER",
            });
            searchesAndFilters.push({
              key: "azimutAmlMatches",
              value: 0,
              operation: "GREATER_THAN",
            });
          } else if (negativeListType == "2") {
            searchesAndFilters.push({
              key: "teacomputersClientaml",
              values: ["0"],
              operation: "FILTER",
            });
            searchesAndFilters.push({
              key: "aml",
              value: '"matched": true',
              operation: "SEARCH",
            });
          }
        }

        if (validation !== "-1") {
          if (validation == "1")
            searchesAndFilters.push({
              key: "cso",
              value: "7001",
              operation: "SEARCH",
            });
          if (validation == "2")
            searchesAndFilters.push({
              key: "cso",
              value: "7002",
              operation: "SEARCH",
            });
          if (validation == "3")
            searchesAndFilters.push({
              key: "cso",
              value: "7003",
              operation: "SEARCH",
            });
          if (validation == "4")
            searchesAndFilters.push({
              key: "cso",
              value: "7004",
              operation: "SEARCH",
            });
          if (validation == "5")
            searchesAndFilters.push({
              key: "cso",
              value: "7000",
              operation: "SEARCH",
            });
          if (validation == "51")
            searchesAndFilters.push({
              key: "cso",
              value: "8001",
              operation: "SEARCH",
            });
          else if (validation == "6")
            searchesAndFilters.push({
              key: "ntra",
              value: "6001",
              operation: "SEARCH",
            });
          else if (validation == "7")
            searchesAndFilters.push({
              key: "ntra",
              value: "6000",
              operation: "SEARCH",
            });
          else if (validation == "8") {
            searchesAndFilters.push({
              key: "teacomputersClientaml",
              values: ["0"],
              operation: "FILTER",
            });
            searchesAndFilters.push({
              key: "aml",
              value: '"matched": true',
              operation: "SEARCH",
            });
          } else if (validation == "9")
            searchesAndFilters.push({
              // rejected
              key: "any",
              operation: "VALIDATION",
            });
          else if (validation == "10")
            searchesAndFilters.push({
              // accepted
              key: "none",
              operation: "VALIDATION",
            });

          if (validation == "11") {
            // not done
            searchesAndFilters.push({
              key: "not-done",
              operation: "VALIDATION",
            });
          } else {
            searchesAndFilters.push({
              key: "isOld",
              operation: "IS_NULL",
            });
          }
        }

        if (nationality == "1")
          searchesAndFilters.push({
            key: "country",
            values: ["EGY", "Egypt"],
            operation: "FILTER",
          });
        else if (nationality == "2")
          searchesAndFilters.push({
            key: "country",
            values: ["EGY", "Egypt"],
            operation: "NOT_IN",
          });

        if (isDigital) {
          searchesAndFilters.push({
            key: "countryPhoneCode",
            values: ["+20"],
            operation: "FILTER",
          });
          searchesAndFilters.push({
            key: "azimutIdTypeId",
            values: ["1"],
            operation: "FILTER",
            parentColumn: "userType",
          });
          searchesAndFilters.push({
            key: "isOld",
            operation: "IS_NULL",
          });
        }

        if (adminUserId) {
          searchesAndFilters.push({
            key: "",
            value: adminUserId.toString(),
            operation: "ADMIN_USER",
          });
        }

        if (contractMap == "1") {
          // digital
          searchesAndFilters.push({
            key: "contractMap",
            values: [1],
            operation: "FILTER",
          });
          searchesAndFilters.push({
            key: "countryPhoneCode",
            values: ["+20"],
            operation: "FILTER",
          });
          searchesAndFilters.push({
            key: "azimutIdTypeId",
            values: ["1"],
            operation: "FILTER",
            parentColumn: "userType",
          });
        } else if (contractMap == "3")
          // passport or international phone
          searchesAndFilters.push({
            key: "",
            operation: "OFFLINE",
          });
        else if (contractMap == "2") {
          searchesAndFilters.push({
            key: "signedPdf",
            value: "offline",
            operation: "SEARCH",
          });
        }
        if (isVerified == "1")
          searchesAndFilters.push({
            key: "isVerified",
            values: [true],
            operation: "FILTER",
          });
        else if (isVerified == "2")
          searchesAndFilters.push({
            key: "isVerified",
            operation: "IS_NULL",
          });
        if (isSynchronized == "1")
          searchesAndFilters.push({
            key: "isSynchronized",
            values: [true],
            operation: "FILTER",
          });
        else if (isSynchronized == "2") {
          searchesAndFilters.push({
            key: "isSynchronized",
            operation: "IS_NULL",
          });
          searchesAndFilters.push({
            key: "isOld",
            operation: "IS_NULL",
          });
        }

        if (sentToFra == "1")
          searchesAndFilters.push({
            key: "fraStoreId",
            operation: "IS_NOT_NULL",
          });
        else if (sentToFra == "2")
          searchesAndFilters.push({
            key: "fraStoreId",
            operation: "IS_NULL",
          });

        if (contractSigned == "1") {
          // signed by azimut and not sent to FRA
          searchesAndFilters.push({
            key: "signedPdf",
            operation: "IS_NOT_NULL",
          });
          searchesAndFilters.push({
            key: "signedPdf",
            values: ["offline"],
            operation: "NOT_IN",
          });
          searchesAndFilters.push({
            key: "fraStoreId",
            operation: "IS_NOT_NULL",
          });
          searchesAndFilters.push({
            key: "fraCompanyStoreId",
            operation: "IS_NULL",
          });
        } else if (contractSigned == "0") {
          searchesAndFilters.push({
            key: "signedPdf",
            operation: "IS_NOT_NULL",
          });
          searchesAndFilters.push({
            key: "signedPdf",
            values: ["offline"],
            operation: "NOT_IN",
          });
        } else if (contractSigned == "2") {
          searchesAndFilters.push({
            key: "signedPdf",
            operation: "IS_NULL",
          });
          searchesAndFilters.push({
            key: "isOld",
            operation: "IS_NULL",
          });
        } else if (contractSigned == "3") {
          searchesAndFilters.push({
            key: "signedPdf",
            value: "offline",
            operation: "SEARCH",
          });
          searchesAndFilters.push({
            key: "isOld",
            values: [true],
            operation: "FILTER",
          });
        } else if (contractSigned == "4") {
          searchesAndFilters.push({
            key: "signedPdf",
            value: "offline",
            operation: "SEARCH",
          });
          searchesAndFilters.push({
            key: "isOld",
            operation: "IS_NULL",
          });
        } else if (contractSigned == "5") {
          searchesAndFilters.push({
            key: "isOld",
            values: [true],
            operation: "FILTER",
          });
          searchesAndFilters.push({
            key: "signedPdf",
            values: ["offline"],
            operation: "NOT_IN",
          });
        }

        if (startDate)
          searchesAndFilters.push({
            key: "createdAt",
            value: startDate,
            operation: "GT",
          });
        if (endDate) {
          const date = new Date(endDate);
          date.setDate(date.getDate() + 1);
          searchesAndFilters.push({
            key: "createdAt",
            value: date.toISOString(),
            operation: "LT",
          });
        }
        if (name)
          searchesAndFilters.push({
            key: "nickName",
            value: name,
            operation: "SEARCH",
          });
        if (referralCode)
          searchesAndFilters.push({
            key: "referralCode",
            value: referralCode,
            operation: "SEARCH",
          });

        if (email)
          searchesAndFilters.push({
            key: "emailAddress",
            value: email,
            operation: "SEARCH",
          });

        if (userId)
          searchesAndFilters.push({
            key: "userId",
            value: userId,
            operation: "SEARCH",
          });

        if (phone)
          searchesAndFilters.push({
            key: "userPhone",
            value: phone,
            operation: "SEARCH",
          });

        if (id) searchesAndFilters.push({ id });
        const data = {
          pageNumber: page,
          pageSize: rowsPerPage,
          sortingParam: orderBy,
          asc: order == "asc",
          searchesAndFilters,
        };

        return {
          url: "/admin/users/listUsersFiltered",
          method: "POST",
          body: data,
        };
      },
      transformResponse: (r: any) => r.response.users,
      providesTags: ["ClientsList"],
    }),

    getContract: builder.query<string, string>({
      query: userId => ({
        url: `/admin/users/contract.pdf?userId=${userId}`,
        method: "GET",
        responseHandler: (response: any) => {
          if (!response.ok) {
            return { error: response.statusText, status: response.status };
          }
          return response.blob();
        },
      }),
      transformResponse: async (blob: Blob) => {
        const base64 = await blobToBase64(blob);
        return (
          "                        " + base64.substring(base64.indexOf(",") + 1)
        );
      },
    }),

    getReasons: builder.query<RejectionReason[], string | void>({
      query: search => ({
        url: "/admin/kyc/getReasons",
        method: "POST",
        body: { search: search || "" },
      }),
      transformResponse: (r: any) => r.response.data.reasons,
      providesTags: ["Reasons"],
    }),

    deleteReason: builder.mutation<any, RejectionReason>({
      query: reason => ({
        url: "/admin/kyc/deleteReason",
        method: "POST",
        body: { reason },
      }),
      invalidatesTags: ["Reasons"],
    }),

    editReason: builder.mutation<any, RejectionReason>({
      query: reason => ({
        url: "/admin/kyc/editReason",
        method: "POST",
        body: { reason },
      }),
      invalidatesTags: ["Reasons"],
    }),

    addReason: builder.mutation<any, RejectionReason>({
      query: reason => ({
        url: "/admin/kyc/addReason",
        method: "POST",
        body: { reason },
      }),
      invalidatesTags: ["Reasons"],
    }),

    getClientDetails: builder.query<AzimutUser, string>({
      query: userId => ({
        url: "/admin/users/getClientOCRDetails",
        method: "POST",
        body: { id: userId },
      }),
      providesTags: ["Clients"],
      transformResponse: (r: any) => r.response.user,
    }),

    getReferralCodes: builder.query<string[], void>({
      query: () => ({
        url: "/admin/users/referralCodes",
        method: "GET",
      }),
      transformResponse: (r: any) => r.response.String,
    }),

    getTransactions: builder.query<ListTransactions, GetTransactionsFilter>({
      query: args => {
        const { startDate, endDate, synced, page, rowsPerPage } = args;
        const searchesAndFilters: any = [];
        if (synced == "1")
          searchesAndFilters.push({
            key: "teacomputerStatus",
            operation: "IS_NULL",
          });
        else if (synced == "2")
          searchesAndFilters.push({
            key: "teacomputerStatus",
            operation: "IS_NOT_NULL",
          });
        if (startDate)
          searchesAndFilters.push({
            key: "createdAt",
            value: startDate,
            operation: "GT",
          });
        if (endDate) {
          const date = new Date(endDate);
          date.setDate(date.getDate() + 1);
          searchesAndFilters.push({
            key: "createdAt",
            value: date.toISOString(),
            operation: "LT",
          });
        }

        const data = {
          pageNumber: page + 1,
          pageSize: rowsPerPage,
          sortingParam: "id",
          asc: false,
          searchesAndFilters,
        };
        return {
          url: "/admin/injection/list",
          method: "POST",
          body: data,
        };
      },
      transformResponse: (r: any) => r.response.response,
      providesTags: ["Transactions"],
    }),

    retryInject: builder.mutation<any, number>({
      query: id => ({
        url: "/admin/injection/retry",
        method: "POST",
        body: { id },
      }),
      invalidatesTags: ["Transactions"],
    }),

    getKycPage: builder.query<KycPage, { userId: string; pageId: string }>({
      query: args => getKycPageArgs(args.userId, args.pageId),
      transformResponse: (r: any) => r.response.page,
    }),

    getAllKycPages: builder.query<
      KycPage[],
      { userId: string; firstPageId: string }
    >({
      queryFn: async (
        { userId, firstPageId },
        api,
        extraOptions,
        baseQuery
      ) => {
        const pages: KycPage[] = [];
        let res: any = await baseQuery(getKycPageArgs(userId, firstPageId));
        let page: KycPage = res.data.response.page;
        pages.push(page);
        while (page.nextId != page.id) {
          res = await baseQuery(getKycPageArgs(userId, page.nextId.toString()));
          page = res.data.response.page;
          pages.push(page);
        }
        return { data: pages };
      },
    }),

    getClientBankAccount: builder.query<BankAccount[], string>({
      query: userId => ({
        url: "/admin/azimut/user/getAzimutClientBankAccounts",
        method: "POST",
        body: { id: userId },
      }),
      transformResponse: (r: any) => r.response.azAccount.clientBankAccounts,
    }),

    getAllReviews: builder.query<Review[], string>({
      query: userId => ({
        url: "/admin/kyc/getAllReviews",
        method: "POST",
        body: { id: userId },
      }),
      transformResponse: (r: any) => r.response.data,
    }),

    submitReviews: builder.mutation<
      string,
      { kycPages: ReviewPage[]; bankAccountId?: number; user: AzimutUser }
    >({
      queryFn: async (
        { kycPages, bankAccountId, user },
        api,
        extraOptions,
        baseQuery
      ) => {
        for (const page of kycPages) {
          if (page.reviews.length > 0) {
            const res: any = await baseQuery({
              url: "/admin/kyc/submitReviews",
              method: "POST",
              body: page,
            });
            if (res.error) return { data: res.error.data.message };
          }
          if (bankAccountId) {
            const res: any = await baseQuery({
              url: "/admin/kyc/submitReviews",
              method: "POST",
              body: {
                pageId: 0,
                appUserId: kycPages[0].appUserId,
                reviews: [{ bankAccountId, status: 1 }],
              },
            });
            if (res.error) return { data: res.error.data.message };
          }
        }
        const res: any = await baseQuery({
          url: "/admin/users/editUserAndSubmitReview",
          method: "POST",
          body: { ...user, reviews: [{ status: 1 }] },
        });
        return {
          data: res.error ? res.error.data.message : "true",
        };
      },
      invalidatesTags: ["Clients"],
    }),

    editUserOnly: builder.mutation<any, AzimutUser>({
      query: user => ({
        url: "/admin/users/editUserOnly",
        method: "POST",
        body: { ...user, reviews: [{ status: 1 }] },
      }),
      invalidatesTags: ["Clients"],
    }),

    getClientBalanceAndTransactions: builder.query<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/getAzimutClientBalanceAndTransactions",
          method: "POST",
          body: { id: userId },
        });
      },
    }),

    getClientFunds: builder.query<
      any,
      {
        userId: string;
        pageNumber?: number;
        sorting?: string;
        teacomputerId?: string;
      }
    >({
      queryFn: async (
        { userId, pageNumber, sorting, teacomputerId },
        api,
        extraOptions,
        baseQuery
      ) => {
        return await baseQuery({
          url: "/admin/users/getClientFunds",
          method: "POST",
          body: { id: userId, pageNumber, sorting, teacomputerId },
        });
      },
    }),

    approveCso: builder.mutation<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/approveCso",
          method: "POST",
          body: { id: userId },
        });
      },
      invalidatesTags: ["Clients"],
    }),

    userHistory: builder.query<string[], string>({
      query: userId => {
        return {
          url: "/admin/users/userHistoryDates",
          method: "POST",
          body: { id: userId },
        };
      },
      transformResponse: (r: any) => r.response.String,
    }),

    getComplianceLog: builder.query<ComplianceLog[], string>({
      query: userId => {
        return {
          url: "/admin/users/complianceLogs?userId=" + userId,
          method: "GET",
        };
      },
      transformResponse: (r: any) => r.response.logs,
    }),

    checkCso: builder.mutation<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/recheckCso",
          method: "POST",
          body: { id: userId },
        });
      },
      invalidatesTags: ["Clients"],
    }),

    markCsoFailed: builder.mutation<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/markCsoFailed",
          method: "POST",
          body: { id: userId },
        });
      },
      invalidatesTags: ["Clients"],
    }),

    checkNtra: builder.mutation<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/recheckNtra",
          method: "POST",
          body: { id: userId },
        });
      },
      invalidatesTags: ["Clients"],
    }),

    checkAml: builder.mutation<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/recheckAml",
          method: "POST",
          body: { id: userId },
        });
      },
      invalidatesTags: ["Clients"],
    }),

    bypassAml: builder.mutation<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/bypassAml",
          method: "POST",
          body: { id: userId },
        });
      },
      invalidatesTags: ["Clients"],
    }),

    sendToFits: builder.mutation<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/sendToFits",
          method: "POST",
          body: { id: userId },
        });
      },
      invalidatesTags: ["Clients"],
    }),

    unblockUser: builder.mutation<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/unblockUser",
          method: "POST",
          body: { id: userId },
        });
      },
      invalidatesTags: ["Clients"],
    }),

    resetChangePhoneAttempts: builder.mutation<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/resetChangePhoneAttempts",
          method: "POST",
          body: { id: userId },
        });
      },
      invalidatesTags: ["Clients"],
    }),

    resyncUser: builder.mutation<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/resyncUser",
          method: "POST",
          body: { id: userId },
        });
      },
      invalidatesTags: ["Clients"],
    }),

    sendOfflineContract: builder.mutation<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/sendOfflineContract",
          method: "POST",
          body: { id: userId },
        });
      },
      invalidatesTags: ["Clients"],
    }),

    approveOfflineContract: builder.mutation<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/approveOfflineContract",
          method: "POST",
          body: { id: userId },
        });
      },
      invalidatesTags: ["Clients"],
    }),

    sendToFra: builder.mutation<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/sendToFra",
          method: "POST",
          body: { id: userId },
        });
      },
      invalidatesTags: ["Clients"],
    }),

    sendCompanyContractToFra: builder.mutation<any, { userId: string }>({
      queryFn: async ({ userId }, api, extraOptions, baseQuery) => {
        return await baseQuery({
          url: "/admin/users/sendCompanyContractToFra",
          method: "POST",
          body: { id: userId },
        });
      },
      invalidatesTags: ["Clients"],
    }),

    signContract: builder.mutation<any, { file: Blob; userId: string }>({
      queryFn: async ({ file, userId }, api, extraOptions, baseQuery) => {
        const body = new FormData();
        body.append("userId", userId);
        body.append("file", file, "file.pdf");

        return await baseQuery({
          url: "/admin/users/signUserContract",
          method: "POST",
          body,
        });
      },
      invalidatesTags: ["Clients"],
    }),

    getNotifications: builder.query<ListNotifications, GetNotificationsFilter>({
      query: args => {
        const { sent, page, rowsPerPage, title } = args;
        const searchesAndFilters: any = [];
        if (sent == "1")
          searchesAndFilters.push({
            key: "sent",
            operation: "IS_NOT_NULL",
          });
        else if (sent == "2")
          searchesAndFilters.push({
            key: "sent",
            operation: "IS_NULL",
          });

        if (title)
          searchesAndFilters.push({
            key: "title",
            operation: "SEARCH",
            value: title,
          });

        const data = {
          pageNumber: page + 1,
          pageSize: rowsPerPage,
          sortingParam: "createdAt",
          asc: false,
          searchesAndFilters,
        };
        return {
          url: "/admin/notifications/list",
          method: "POST",
          body: data,
        };
      },
      transformResponse: (r: any) => r.response.response,
      providesTags: ["Notifications"],
    }),

    deleteNotification: builder.mutation<any, NotificationTemplate>({
      query: notification => ({
        url: "/admin/notifications/deleteNotification",
        method: "POST",
        body: { notification },
      }),
      invalidatesTags: ["Notifications"],
    }),

    editNotification: builder.mutation<any, NotificationTemplate>({
      query: notification => ({
        url: "/admin/notifications/editNotification",
        method: "POST",
        body: { notification },
      }),
      invalidatesTags: ["Notifications"],
    }),

    addNotification: builder.mutation<any, NotificationTemplate>({
      query: notification => ({
        url: "/admin/notifications/addNotification",
        method: "POST",
        body: { notification },
      }),
      invalidatesTags: ["Notifications"],
    }),

    getFunds: builder.query<Fund[], void>({
      query: () => {
        return {
          url: "/admin/dividends/listFunds",
          method: "GET",
        };
      },
      transformResponse: (r: any) => r.response.response,
    }),

    getDividends: builder.query<ListDividends, GetDividendsFilter>({
      query: args => {
        const { fundId, page, rowsPerPage } = args;
        const searchesAndFilters: any = [];
        if (fundId)
          searchesAndFilters.push({
            key: "fundId",
            values: [fundId.toString()],
            operation: "FILTER",
          });

        const data = {
          pageNumber: page + 1,
          pageSize: rowsPerPage,
          sortingParam: "dividendDate",
          asc: false,
          searchesAndFilters,
        };
        return {
          url: "/admin/dividends/list",
          method: "POST",
          body: data,
        };
      },
      transformResponse: (r: any) => r.response.response,
      providesTags: ["Dividends"],
    }),

    deleteDividend: builder.mutation<any, Dividend>({
      query: dividend => ({
        url: "/admin/dividends/deleteDividend",
        method: "POST",
        body: { dividend },
      }),
      invalidatesTags: ["Dividends"],
    }),

    editDividend: builder.mutation<any, Dividend>({
      query: dividend => ({
        url: "/admin/dividends/editDividend",
        method: "POST",
        body: { dividend },
      }),
      invalidatesTags: ["Dividends"],
    }),

    addDividend: builder.mutation<any, Dividend>({
      query: dividend => ({
        url: "/admin/dividends/addDividend",
        method: "POST",
        body: { dividend },
      }),
      invalidatesTags: ["Dividends"],
    }),

    sendNotification: builder.mutation<
      any,
      {
        id: number;
        target?: string;
        users?: number[];
        userNationalIds?: string[];
      }
    >({
      query: ({ id, target, users, userNationalIds }) => ({
        url: "/admin/notifications/send",
        method: "POST",
        body: { id, target, users, userNationalIds },
      }),
      invalidatesTags: ["Notifications"],
    }),

    getPopups: builder.query<ListPopups, GetPopupsFilter>({
      query: args => {
        const { sent, page, rowsPerPage, title } = args;
        const searchesAndFilters: any = [];
        if (sent == "1")
          searchesAndFilters.push({
            key: "sent",
            operation: "IS_NOT_NULL",
          });
        else if (sent == "2")
          searchesAndFilters.push({
            key: "sent",
            operation: "IS_NULL",
          });

        if (title)
          searchesAndFilters.push({
            key: "title",
            operation: "SEARCH",
            value: title,
          });

        const data = {
          pageNumber: page + 1,
          pageSize: rowsPerPage,
          sortingParam: "createdAt",
          asc: false,
          searchesAndFilters,
        };
        return {
          url: "/admin/popups/list",
          method: "POST",
          body: data,
        };
      },
      transformResponse: (r: any) => r.response.response,
      providesTags: ["Popups"],
    }),

    deletePopup: builder.mutation<any, PopupTemplate>({
      query: popup => ({
        url: "/admin/popups/deletePopup",
        method: "POST",
        body: { popup },
      }),
      invalidatesTags: ["Popups"],
    }),

    editPopup: builder.mutation<any, PopupTemplate>({
      query: popup => ({
        url: "/admin/popups/editPopup",
        method: "POST",
        body: { popup },
      }),
      invalidatesTags: ["Popups"],
    }),

    addPopup: builder.mutation<any, PopupTemplate>({
      query: popup => ({
        url: "/admin/popups/addPopup",
        method: "POST",
        body: { popup },
      }),
      invalidatesTags: ["Popups"],
    }),

    sendPopup: builder.mutation<
      any,
      {
        id: number;
        target?: string;
        users?: number[];
        userNationalIds?: string[];
      }
    >({
      query: ({ id, target, users, userNationalIds }) => ({
        url: "/admin/popups/send",
        method: "POST",
        body: { id, target, users, userNationalIds },
      }),
      invalidatesTags: ["Popups"],
    }),

    changePassword: builder.mutation<
      any,
      { password: string; oldPassword: string }
    >({
      query: ({ password, oldPassword }) => ({
        url: "/admin/changePassword",
        method: "POST",
        body: { password, oldPassword },
      }),
    }),

    editProfile: builder.mutation<any, { email: string; name: string }>({
      query: ({ email, name }) => ({
        url: "/admin/changeAdmin",
        method: "POST",
        body: { email, name },
      }),
    }),

    // Negative List endpoints
    getNegativeListStatistics: builder.query<NegativeListStatistics, void>({
      query: () => ({
        url: "/admin/negativelist/statistics",
        method: "GET",
      }),
      transformResponse: (r: any) => r.response.NegativeListStatistics,
      providesTags: ["NegativeList"],
    }),

    searchNegativeList: builder.query<
      NegativeListResult[],
      NegativeListSearchFilter
    >({
      query: searchFilter => ({
        url: "/admin/negativelist/search",
        method: "POST",
        body: searchFilter,
      }),
      transformResponse: (r: any) => r.response.matches,
      providesTags: ["NegativeList"],
    }),

    uploadNegativeListByUrl: builder.mutation<any, void>({
      query: () => ({
        url: "/admin/negativelist/addNegativeList",
        method: "POST",
        body: {},
      }),
      invalidatesTags: ["NegativeList"],
    }),

    uploadNegativeListFile: builder.mutation<
      any,
      {
        file: File;
      }
    >({
      query: ({ file }) => {
        const formData = new FormData();
        formData.append("file", file);

        return {
          url: "/admin/negativelist/uploadHtmlFile",
          method: "POST",
          body: formData,
        };
      },
      invalidatesTags: ["NegativeList"],
    }),

    uploadFile: builder.mutation<any, { file: File }>({
      query: ({ file }) => {
        const formData = new FormData();
        formData.append("file", file);

        return {
          url: "/admin/popups/uploadFile",
          method: "POST",
          body: formData,
        };
      },
    }),
  }),
});

async function blobToBase64(blob: Blob) {
  return new Promise<string>((onSuccess, onError) => {
    try {
      const reader = new FileReader();
      reader.onload = function () {
        onSuccess(this.result as string);
      };
      reader.readAsDataURL(blob);
    } catch (e) {
      onError(e);
    }
  });
}
