import { createApi } from "@reduxjs/toolkit/query/react";

import { baseQueryWithReauth } from "./azimutApi";
import {
  AdminUserMapping,
  CreateUserMappingRequest,
  GetMappingsByAdminFilter,
  ListUserMappings,
  Role,
  User,
} from "./types";

export const azimutAdminApi = createApi({
  reducerPath: "azimutAdminApi",
  tagTypes: ["Users", "Roles", "UserMappings"],
  baseQuery: baseQueryWithReauth,
  endpoints: builder => ({
    getUsers: builder.query<User[], void>({
      query: () => ({
        url: "/admin/listAdmins",
        method: "GET",
      }),
      transformResponse: (r: any) => r.response.admins,
      providesTags: ["Users"],
    }),

    userStats: builder.query<any, { startDate?: string; endDate?: string }>({
      query: data => ({
        url: "/admin/users/stats",
        method: "GET",
        params: data,
      }),
      transformResponse: (r: any) => {
        const res = {
          newUser: 0,
          pending: 0,
          accepted: 0,
          autoAccepted: 0,
          old: 0,
          signed: 0,
          rejected: 0,
        };
        r.response.stats.forEach((rec: any) => {
          if (rec.kycStatus == 0 || rec.kycStatus == 4)
            res.pending += rec.count;
          if (rec.kycStatus == 1 && !rec.isVerified) res.accepted += rec.count;
          if (rec.kycStatus == 1 && rec.isVerified) {
            if (rec.signedPdf) res.signed += rec.count;
            else if (rec.isOld) res.old += rec.count;
            else res.autoAccepted += rec.count;
          }
          if (rec.kycStatus == 2) res.rejected += rec.count;
          if (rec.kycStatus == 5) res.newUser += rec.count;
        });
        return res;
      },
    }),

    userKycStats: builder.query<any, { startDate?: string; endDate?: string }>({
      query: data => ({
        url: "/admin/users/kycStats",
        method: "GET",
        params: data,
      }),
      transformResponse: (r: any) => {
        const res = {
          accepted: 0,
          signed: 0,
          rejected: 0,
        };
        res.accepted = r.response.Integer[0];
        res.rejected = r.response.Integer[1];
        res.signed = r.response.Integer[2];
        return res;
      },
    }),

    addUser: builder.mutation<string, User>({
      query: user => ({
        url: "/admin/addUser",
        method: "POST",
        body: {
          ...user,
          email: user.emailAddress,
        },
      }),
      invalidatesTags: ["Users"],
    }),
    editUser: builder.mutation<string, User>({
      query: user => ({
        url: "/admin/editUser",
        method: "POST",
        body: {
          ...user,
          email: user.emailAddress,
        },
      }),
      invalidatesTags: ["Users"],
    }),
    deleteUser: builder.mutation<any, User>({
      query: user => ({
        url: "/admin/deleteUser",
        method: "POST",
        body: user,
      }),
      invalidatesTags: ["Users"],
    }),

    getRoles: builder.query<Role[], void>({
      query: () => ({
        url: "/admin/listRoles",
        method: "GET",
      }),
      transformResponse: (r: any) => r.response.roles,
      providesTags: ["Roles"],
    }),
    addRole: builder.mutation<string, Role>({
      query: role => ({
        url: "/admin/addRole",
        method: "POST",
        body: role,
      }),
      invalidatesTags: ["Roles"],
    }),
    editRole: builder.mutation<string, Role>({
      query: role => ({
        url: "/admin/editRole",
        method: "POST",
        body: role,
      }),
      invalidatesTags: ["Roles"],
    }),
    deleteRole: builder.mutation<any, Role>({
      query: role => ({
        url: "/admin/deleteRole",
        method: "POST",
        body: role,
      }),
      invalidatesTags: ["Roles"],
    }),

    createUserMapping: builder.mutation<
      AdminUserMapping[],
      CreateUserMappingRequest
    >({
      query: body => ({
        url: "/admin/user-mapping/create",
        method: "POST",
        body,
      }),
      transformResponse: (r: any) => r.response,
      invalidatesTags: ["UserMappings"],
    }),

    getMappingsByAdmin: builder.query<
      ListUserMappings,
      { adminUserId: number; filters: GetMappingsByAdminFilter }
    >({
      query: ({ adminUserId, filters }) => ({
        url: `/admin/user-mapping/by-admin/${adminUserId}`,
        method: "POST",
        body: filters,
      }),
      transformResponse: (r: any) => r.response.adminUsers,
      providesTags: ["UserMappings"],
    }),

    deleteUserMapping: builder.mutation<any, number>({
      query: id => ({
        url: `/admin/user-mapping/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["UserMappings"],
    }),
    bulkDeleteUserMapping: builder.mutation<any, CreateUserMappingRequest>({
      query: body => ({
        url: "/admin/user-mapping/bulk-delete",
        method: "DELETE",
        body,
      }),
      invalidatesTags: ["UserMappings"],
    }),
  }),
});
