import { configureStore } from "@reduxjs/toolkit";
import { setupListeners } from "@reduxjs/toolkit/query";

import { azimutAdminApi } from "../hooks/adminUsersApi";
import { azimutApi } from "../hooks/azimutApi";

export const store = configureStore({
  reducer: {
    // Add the generated reducer as a specific top-level slice
    [azimutApi.reducerPath]: azimutApi.reducer,
    [azimutAdminApi.reducerPath]: azimutAdminApi.reducer,
  },
  // Adding the api middleware enables caching, invalidation, polling,
  // and other useful features of `rtk-query`.
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware()
      .concat(azimutApi.middleware)
      .concat(azimutAdminApi.middleware),
});

// optional, but required for refetchOnFocus/refetchOnReconnect behaviors
// see `setupListeners` docs - takes an optional callback as the 2nd arg for customization
setupListeners(store.dispatch);
