import loadable from "@loadable/component";
import pMinDelay from "p-min-delay";
import { createBrowserRouter, RouterProvider } from "react-router-dom";

import { PageLoader } from "@/components/loaders/PageLoader";
import RestrictedLayout from "@/layouts/RestrictedLayout";

import { AdminsList } from "../pages/admins/AdminsList";
import { AdminUserDetails } from "../pages/admins/AdminUserDetails";
import ChangePassword from "../pages/auth/ChangePassword";
import EditProfile from "../pages/auth/EditProfile";
import { TokenExpiring } from "../pages/auth/TokenExpiring";
import { Client } from "../pages/clients/Client";
import { ClientsList } from "../pages/clients/ClientsList";
import EditClientData from "../pages/clients/EditClientData";
import Dashboard from "../pages/dashboard/Dashboard";
import { Dividends } from "../pages/dividends/Dividends";
import FitsUsersList from "../pages/fitsUsers/FitsUsersList";
import FitsUserView from "../pages/fitsUsers/FitsUserView";
import ManualAccountCreation from "../pages/fitsUsers/ManualAccountCreation";
import { Injections } from "../pages/injections/Injections";
import { NegativeList } from "../pages/negativelist/NegativeList";
import { Notifications } from "../pages/notifications/Notifications";
import { Popups } from "../pages/popups/Popups";
import { ReasonsList } from "../pages/reasons/ReasonsList";
import { RolesList } from "../pages/roles/RolesList";

const timeout = 1;

const App = loadable(() => pMinDelay(import("@/pages/Layout"), timeout), {
  fallback: <PageLoader />,
});
const LoginPage = loadable(
  () => pMinDelay(import("@/pages/auth/Login"), timeout),
  {
    fallback: <PageLoader />,
  }
);

const ErrorBoundary = loadable(() => import("@/pages/ErrorBoundary"));

const authPath = "/auth";

export const routes = createBrowserRouter([
  {
    path: "/",
    errorElement: <ErrorBoundary />,
    children: [
      {
        element: <App />,
        children: [
          {
            index: true,
            element: <RestrictedLayout />,
          },
          {
            element: <RestrictedLayout />,
            children: [
              {
                path: "/home",
                element: <ClientsList />,
              },
              {
                path: "/clients",
                element: <ClientsList />,
              },
              {
                path: "/client/:id",
                element: <Client />,
              },
              {
                path: "/editClient/:id",
                element: <EditClientData />,
              },
              {
                path: "/reasons",
                element: <ReasonsList />,
              },
              {
                path: "/changePassword",
                element: <ChangePassword />,
              },
              {
                path: "/editProfile",
                element: <EditProfile />,
              },
              {
                path: "/users",
                element: <AdminsList />,
              },
              {
                path: "/users/:id",
                element: <AdminUserDetails />,
              },
              {
                path: "/roles",
                element: <RolesList />,
              },
              {
                path: "/dashboard",
                element: <Dashboard />,
              },
              {
                path: "/injections",
                element: <Injections />,
              },
              {
                path: "/notifications",
                element: <Notifications />,
              },
              {
                path: "/popups",
                element: <Popups />,
              },
              {
                path: "/dividends",
                element: <Dividends />,
              },
              {
                path: "/negative-list",
                element: <NegativeList />,
              },
              {
                path: "/fits-users",
                element: <FitsUsersList />,
              },
              {
                path: "/fits-users/create",
                element: <ManualAccountCreation />,
              },
              {
                path: "/fits-users/:id",
                element: <FitsUserView />,
              },
            ],
          },
        ],
      },
      {
        element: <LoginPage />,
        path: `${authPath}/login`,
      },
    ],
  },
]);

function Router() {
  return (
    <div>
      <TokenExpiring />
      <RouterProvider router={routes} />
    </div>
  );
}

export default Router;
