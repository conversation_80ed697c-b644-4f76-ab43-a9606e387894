import { AuthUser, AzimutAuthRes, UserCredentials } from "../../hooks/types";

export const refreshAuthToken = async () => {
  const backendUrl = import.meta.env.VITE_APP_BACKEND_URL;
  const user: AuthUser = JSON.parse(localStorage.getItem("user")!);
  const token = user.auth;
  const refreshToken = user.refreshToken;
  var headers = new Headers();
  headers.set("Accept", "application/json");
  headers.set("Content-type", "application/json");
  headers.set("isadmin", "1");
  headers.set("Authorization", "Bearer " + token);
  const body = { refreshToken };
  const res: AzimutAuthRes = await fetch(`${backendUrl}/admin/refreshToken`, {
    body: JSON.stringify(body),
    method: "POST",
    headers: headers,
  }).then(response => response.json());
  console.log("Refreshing");
  return {
    auth: res.response.credentials.token.tokenString,
    refreshToken: res.response.credentials.token.refreshTokenString,
    user: res.response.credentials.user,
  };
};

export async function signIn({
  email,
  password,
}: UserCredentials): Promise<AuthUser> {
  const backendUrl = import.meta.env.VITE_APP_BACKEND_URL;
  const options = {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ email: email, password: password }),
  };

  const res: AzimutAuthRes = await fetch(
    `${backendUrl}/admin/authenticate`,
    options
  ).then(response => response.json());

  if (res.status !== 0) {
    throw res;
  }
  return {
    auth: res.response.credentials.token.tokenString,
    refreshToken: res.response.credentials.token.refreshTokenString,
    user: res.response.credentials.user,
  };
}
