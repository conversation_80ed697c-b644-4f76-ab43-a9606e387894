import { AuthUser } from "../../hooks/types";

const optoSignerLogin = async () => {
  const backendUrl = import.meta.env.VITE_APP_BACKEND_URL;
  const user: AuthUser = JSON.parse(localStorage.getItem("user")!);
  const token = user.auth;
  try {
    const response = await fetch(`${backendUrl}/api/optoSigner/token`, {
      headers: {
        "Content-Type": "application/json",
        authorization: `Bearer ${token}`,
        isadmin: "1",
      },
    });

    const user = await response.json();
    return user;
  } catch (error) {
    console.error("Login error:", error);
    throw error;
  }
};

const optoSignerListCertificates = async () => {
  try {
    const response = await fetch(
      "https://localhost.optomaticasigner.com:7575/certificate/list",
      {
        method: "GET",
        headers: {
          accept: "*/*",
          "accept-language": "en-GB,en;q=0.9,en-US;q=0.8",
        },
      }
    );
    if (!response.ok) {
      return null;
    } else {
      const data = await response.json();
      return {
        certificateDetailsArray: data.certificates.map((e: any) => ({
          commonname: e.subjectName.match(/CN=([^,]+)/)?.[1],
          ...e,
        })),
      };
    }
  } catch (error: any) {
    return null;
  }
};

export function getFileFromBase64(string64: string) {
  const imageContent = atob(string64);
  const buffer = new ArrayBuffer(imageContent.length);
  const view = new Uint8Array(buffer);

  for (let n = 0; n < imageContent.length; n++) {
    view[n] = imageContent.charCodeAt(n);
  }

  const type = "application/pdf";
  const blob = new Blob([buffer], { type });
  return blob;
}

export const optoSignContract = async (
  contractData: string,
  certificatethumbprint: string
) => {
  const user = await optoSignerLogin();

  return fetch("https://localhost.optomaticasigner.com:7575/certificate/sign", {
    method: "POST",
    headers: {
      accept: "*/*",
      "accept-language": "en-GB,en;q=0.9,en-US;q=0.8",
      "content-type": "application/json",
      Authorization: `Bearer ${user.response.String}`,
    },
    body: JSON.stringify({
      originalpdf: contractData,
      tsaurl: "",
      reason: "",
      licenseusername: "",
      licensepassword: "",
      certificatethumbprint,
      signer: "company",
      signaturepositions: [
        {
          page: 6,
          bottom: 390,
          left: 325,
          top: 460,
          right: 545,
          fontsize: 10,
        },
      ],
    }),
  }).then(response => {
    console.log(response);
    return response.json();
  });
};

export const signContract = async (
  contractData: string,
  certificatethumbprint: string
) => {
  return fetch("https://localhost.deltawebsigner.com:7777/signpdfbase64", {
    method: "POST",
    headers: {
      accept: "*/*",
      "accept-language": "en-GB,en;q=0.9,en-US;q=0.8",
      "content-type": "multipart/form-data",
    },
    body: JSON.stringify({
      originalpdf: contractData,
      tsaurl: "",
      reason: "",
      licenseusername: "",
      licensepassword: "",
      certificatethumbprint,
      signer: "company",
      signaturepositions: [
        {
          page: 6,
          bottom: 390,
          left: 325,
          top: 440,
          right: 545,
          fontsize: 10,
        },
      ],
    }),
  }).then(response => {
    console.log(response);
    return response.json();
  });
};

export const listCertificates = async () => {
  const optoSignerRes = await optoSignerListCertificates();
  if (optoSignerRes) return optoSignerRes;
  return fetch("https://localhost.deltawebsigner.com:7777/listcertificates", {
    method: "POST",
    headers: {
      accept: "*/*",
      "accept-language": "en-GB,en;q=0.9,en-US;q=0.8",
    },
    body: JSON.stringify({
      function: "list certificates",
    }),
  }).then(response => {
    console.log(response);
    return response.json();
  });
};
