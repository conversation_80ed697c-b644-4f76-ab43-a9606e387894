import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import CalendarTodayIcon from "@mui/icons-material/CalendarToday";
import ContentCopyIcon from "@mui/icons-material/ContentCopy";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MoneyIcon from "@mui/icons-material/Money";
import React, { Suspense, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";

import Loader from "../../components/shared/Spinner/Loader";
import { azimutApi } from "../../hooks/azimutApi";
import { UserFund } from "../../hooks/types";

const FundDetails = React.lazy(() => import("./FundDetails"));

const Funds: React.FC = () => {
  const params = useParams();
  const { t } = useTranslation("common");
  const [funds, setFunds] = useState<UserFund[]>([]);
  const [page, setPage] = useState(1);
  const [numberOfPages, setNumberOfPages] = useState(0);
  const [totalPosition, setTotalPosition] = useState("");
  const [currentRevenue, setCurrentRevenue] = useState("");
  const [currentRevenuePercent, setCurrentRevenuePercent] = useState("");
  const [balance, setBalance] = useState("");
  const [expandedFund, setExpandedFund] = useState<string | null>(null);

  const { data: fundsRes, isLoading } = azimutApi.useGetClientFundsQuery({
    userId: params.id!,
    pageNumber: page,
  });

  useEffect(() => {
    if (fundsRes) {
      const res = fundsRes;
      if (res.status === 0) {
        const formattedFunds =
          res.response.azAccount.paginatedBusinessClientFunds.pageList.map(
            (item: any) => ({
              ...item,
              totalAmount: new Intl.NumberFormat().format(item.totalAmount),
              quantity: new Intl.NumberFormat().format(item.quantity),
            })
          );
        setFunds(formattedFunds);
        setTotalPosition(
          new Intl.NumberFormat().format(res.response.azAccount.totalPosition)
        );
        setBalance(
          new Intl.NumberFormat().format(res.response.azAccount.balance)
        );
        setCurrentRevenue(
          new Intl.NumberFormat().format(res.response.azAccount.currentRevenue)
        );
        setCurrentRevenuePercent(
          new Intl.NumberFormat().format(
            res.response.azAccount.currentRevenuePercent
          )
        );
        setNumberOfPages(
          Math.max(
            res.response.azAccount.paginatedBusinessClientFunds.numberOfPages,
            1
          )
        );
      }
    }
  }, [fundsRes]);

  const paginateFunds = (page: number) => {
    setPage(page);
  };

  return (
    <div>
      {isLoading && <Loader />}

      <div className="flex justify-between items-center mb-6 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
        <div className="flex items-center gap-4">
          <p className="font-medium">{t("Total Position")}:</p>
          <p
            className="text-yellow-500 font-bold"
            style={{ color: "#fcd555" }}
          >
            {totalPosition} EGP
          </p>
          {currentRevenue && (
            <span
              style={{
                borderLeft: ".5px solid #a2d7ff45",
                paddingLeft: 30,
                marginLeft: 30,
                color: "white",
                fontSize: "medium",
                textAlign: "center",
                minWidth: 200,
              }}
            >
              {parseFloat(currentRevenue) > 0 && (
                <ArrowUpwardIcon color="success" />
              )}
              {parseFloat(currentRevenue) < 0 && (
                <ArrowDownwardIcon color="error" />
              )}
              <span style={{ marginLeft: 10 }}>
                {currentRevenue} ({currentRevenuePercent})%)
              </span>
            </span>
          )}
        </div>
        <div className="flex items-center gap-4">
          <p className="font-medium">{t("Available Cash")}:</p>
          <p className="text-yellow-500 font-bold">{balance} EGP</p>
        </div>
      </div>

      {funds.length === 0 ? (
        <div>
          <p>{t("AZ Funds in User Account")}</p>
        </div>
      ) : (
        <div>
          {funds.map((fund, index) => (
            <div key={index}>
              <div
                className="fund-card flex items-center cursor-pointer relative"
                onClick={() =>
                  setExpandedFund(
                    expandedFund === fund.teacomputerId.toString()
                      ? null
                      : fund.teacomputerId.toString()
                  )
                }
              >
                <img
                  alt="funds-img"
                  className="funds-img"
                  src={fund.logo}
                />
                <div className="funds-details">
                  <ExpandMoreIcon
                    className={`absolute right-4 text-2xl transition-transform duration-200 ${
                      expandedFund === fund.teacomputerId.toString()
                        ? "rotate-180"
                        : ""
                    }`}
                  />
                  <div className="funds-title-contain">
                    <h5 className="funds-title"> {fund.fundName}</h5>
                  </div>
                  <span className="font-type">{fund.fundType}</span>
                  <div className="funds-price-data" />
                  <div className="my-funds-data-contain">
                    <div className="funds-data">
                      <p className="fund-price">
                        {fund.tradePrice} {fund.currencyName}{" "}
                      </p>
                    </div>
                    <div className="funds-data">
                      <CalendarTodayIcon />
                      <span className="ml-1">{fund.lastPriceUpdateDate}</span>
                    </div>
                    <div className="funds-data">
                      <ContentCopyIcon />
                      <span className="noOfUnits">{fund.quantity}</span>
                      <span>Units</span>
                    </div>
                    <div className="funds-data-last">
                      <MoneyIcon />
                      <span className="moneyAmount">{fund.totalAmount}</span>
                      <span className="currencyName">{fund.currencyName}</span>
                    </div>
                  </div>
                </div>
              </div>
              {expandedFund === fund.teacomputerId.toString() && (
                <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <Suspense fallback={<div>Loading details...</div>}>
                    <FundDetails
                      teacomputerId={fund.teacomputerId.toString()}
                    />
                  </Suspense>
                </div>
              )}
            </div>
          ))}
          <div className="flex justify-end items-center gap-4 mt-4 p-2">
            <button
              className={`px-4 py-2 rounded border ${
                page === 1
                  ? "border-gray-600 bg-gray-700 text-gray-500 cursor-not-allowed"
                  : "border-gray-400 dark:border-gray-500 bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
              }`}
              onClick={() => paginateFunds(page - 1)}
              disabled={page === 1}
            >
              {t("Previous")}
            </button>
            <span className="mx-2 dark:text-gray-300">
              {t("page")} {page} of {numberOfPages}
            </span>
            <button
              className={`px-4 py-2 rounded border ${
                page >= numberOfPages
                  ? "border-gray-600 bg-gray-700 text-gray-500 cursor-not-allowed"
                  : "border-gray-400 dark:border-gray-500 bg-white dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
              }`}
              onClick={() => paginateFunds(page + 1)}
              disabled={page >= numberOfPages}
            >
              {t("Next")}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default Funds;
