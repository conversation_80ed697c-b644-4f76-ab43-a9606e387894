import { Box } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";

import Loader from "../../components/shared/Spinner/Loader";
import { azimutApi } from "../../hooks/azimutApi";

export const BankAccounts = () => {
  const params = useParams();
  const { t } = useTranslation("common");
  const { i18n } = useTranslation();

  const { data: bankAccounts, isLoading } =
    azimutApi.useGetClientBankAccountQuery(params.id!);

  if (isLoading) return <Loader />;

  return (
    <div>
      {bankAccounts?.map(e => (
        <Box
          key={e.accountNumber}
          py={2}
          px={5}
          mx={10}
          className="rounded"
          sx={{ border: "2px solid grey" }}
        >
          <div className="grid grid-cols-2 gap-4 my-1">
            <div className="font-semibold leading-7 text-base">
              {t("Bank Name")}
            </div>
            <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
              {i18n.language == "en" ? e.englishBankName : e.arabicBankName}
            </div>
            <div className="font-semibold leading-7 text-base">
              {t("Branch Name")}
            </div>
            <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
              {i18n.language == "en" ? e.englishBranchName : e.arabicBranchName}
            </div>
            <div className="font-semibold leading-7 text-base">
              {t("Bank Account")}
            </div>
            <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
              {e.accountNumber}
            </div>
            <div className="font-semibold leading-7 text-base">{t("IBAN")}</div>
            <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
              {e.iban}
            </div>
            <div className="font-semibold leading-7 text-base">
              {t("Currency")}
            </div>
            <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
              {i18n.language == "en"
                ? e.englishCurrencyName
                : e.arabicCurrencyName}
            </div>
          </div>
        </Box>
      ))}
    </div>
  );
};
