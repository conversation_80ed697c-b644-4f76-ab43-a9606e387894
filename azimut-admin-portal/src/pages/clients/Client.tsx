import "../../assets/styles/client.css";

import EditIcon from "@mui/icons-material/Edit";
import {
  Box,
  Breadcrumbs,
  IconButton,
  Tab,
  Tabs,
  Typography,
} from "@mui/material";
import { useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { Link, useNavigate, useParams } from "react-router-dom";

import { getFileFromBase64 } from "../../api/services/sign-contract";
import Button from "../../components/shared/Button/Button";
import ChooserDialog from "../../components/shared/Dialog/ChooserDialogue";
import ConfirmationDialog from "../../components/shared/Dialog/ConfirmationDialog";
import Loader from "../../components/shared/Spinner/Loader";
import { azimutApi } from "../../hooks/azimutApi";
import { AzimutUser } from "../../hooks/types";
import { useAuth } from "../../hooks/useAuth";
import { BankAccounts } from "./BankAccounts";
import { ClientData, isEgyptId, isEgyptNtra, isFraValid } from "./ClientData";
import { getStatusDisplay, kycStatuses } from "./ClientsList";
import Funds from "./Funds";
import { Kyc } from "./Kyc";
import Wallet from "./Wallet";

function downloadFile(blob: Blob) {
  const a = document.createElement("a");
  a.hidden = true;
  document.body.appendChild(a);
  const url = window.URL.createObjectURL(blob);
  a.href = url;
  a.download = "file.pdf";
  a.click();
  window.URL.revokeObjectURL(url);
}

const isBlocked = (clientData: AzimutUser) => {
  return clientData?.blockageCount >= 5 || clientData?.failedLogins >= 5;
};

export const Client = () => {
  const params = useParams();
  const [loading, setLoading] = useState(false);
  const { user } = useAuth()!;
  const [toSignUsers, setToSignUsers] = useState<string[]>([]);
  const [tabValue, setTabValue] = useState(0);

  // Confirmation dialog states
  const [showRecheckCsoDialog, setShowRecheckCsoDialog] = useState(false);
  const [showRescanIdDialog, setShowRescanIdDialog] = useState(false);
  const [showRecheckNtraDialog, setShowRecheckNtraDialog] = useState(false);
  const [showRecheckNegativeListDialog, setShowRecheckNegativeListDialog] =
    useState(false);
  const [showAcceptNegativeListDialog, setShowAcceptNegativeListDialog] =
    useState(false);
  const [showSendToFraDialog, setShowSendToFraDialog] = useState(false);
  const [showSendCompanyContractDialog, setShowSendCompanyContractDialog] =
    useState(false);
  const [showUnblockUserDialog, setShowUnblockUserDialog] = useState(false);
  const [showMakeUserScanIdDialog, setShowMakeUserScanIdDialog] =
    useState(false);
  const [showSendOfflineContractDialog, setShowSendOfflineContractDialog] =
    useState(false);
  const [
    showSendOfflineContractEgyptDialog,
    setShowSendOfflineContractEgyptDialog,
  ] = useState(false);
  const [
    showApproveOfflineContractDialog,
    setShowApproveOfflineContractDialog,
  ] = useState(false);

  const { t } = useTranslation("common");
  const navigate = useNavigate();

  const { data: clientData, isLoading: clientDataLoading } =
    azimutApi.useGetClientDetailsQuery(params.id!);
  const [getContract, { data: contractBase64, isLoading }] =
    azimutApi.useLazyGetContractQuery();

  const [sendToFra, { isLoading: isSubmitting }] =
    azimutApi.useSendToFraMutation();

  const [sendCompanyContractToFra, { isLoading: isSubmittingCompanyContract }] =
    azimutApi.useSendCompanyContractToFraMutation();

  const [unblockUSer, { isLoading: isUnblocking }] =
    azimutApi.useUnblockUserMutation();

  const [resyncUser, { isLoading: isResyncingUser }] =
    azimutApi.useResyncUserMutation();
  const [sendOfflineContract, { isLoading: isSendingOffineContract }] =
    azimutApi.useSendOfflineContractMutation();
  const [approveOfflineContract, { isLoading: isApprovingOfflineContract }] =
    azimutApi.useApproveOfflineContractMutation();

  const [checkCso, { isLoading: isSubmittingCso }] =
    azimutApi.useCheckCsoMutation();
  const [markCsoFailed, { isLoading: isMarkingCsoFailed }] =
    azimutApi.useMarkCsoFailedMutation();
  const [checkNtra, { isLoading: isSubmittingNtra }] =
    azimutApi.useCheckNtraMutation();
  const [bypassAml, { isLoading: isSubmittingAml }] =
    azimutApi.useBypassAmlMutation();

  const [checkAml, { isLoading: isSubmittingCheckAml }] =
    azimutApi.useCheckAmlMutation();

  const [sentToFits, { isLoading: isSubmittingSendToFits }] =
    azimutApi.useSendToFitsMutation();
  const submitSendToFits = async (userId: string) => {
    const res = await sentToFits({ userId })
      .unwrap()
      .catch(e => toast.error(e.data.message || "Error send to Fits"));
    if (res.status == 0) toast.success("submitted successfully");
    else toast.error(res.message);
  };

  const submitToFra = async (userId: string) => {
    const res = await sendToFra({ userId })
      .unwrap()
      .catch(e => toast.error(e.data.message || "Error send to FRA"));
    if (res.status == 0) toast.success("submitted successfully");
    else toast.error(res.message);
  };

  const submitCompanyContractToFra = async (userId: string) => {
    const res = await sendCompanyContractToFra({ userId })
      .unwrap()
      .catch(e => toast.error(e.data.message || "Error send to FRA"));
    if (res.status == 0) toast.success("submitted successfully");
    else toast.error(res.message);
  };

  const submitCheckCso = async (userId: string) => {
    const res = await checkCso({ userId })
      .unwrap()
      .catch(e => toast.error(e.data.message || "Error checking CSO"));
    if (res.status == 0) toast.success("submitted successfully");
    else toast.error(res.message);
  };
  const submitMarkCsoFailed = async (userId: string) => {
    const res = await markCsoFailed({ userId })
      .unwrap()
      .catch(e => toast.error(e.data.message || "Error Rescaning ID"));
    if (res.status == 0) toast.success("submitted successfully");
    else toast.error(res.message);
  };
  const submitCheckNtra = async (userId: string) => {
    const res = await checkNtra({ userId })
      .unwrap()
      .catch(e => toast.error(e.data.message || "Error checking NTRA"));
    if (res.status == 0) toast.success("submitted successfully");
    else toast.error(res.message);
  };
  const submitBypassAml = async (userId: string) => {
    const res = await bypassAml({ userId }).unwrap();
    if (res.status == 0) toast.success("submitted successfully");
    else toast.error(res.message);
  };

  const submitCheckAml = async (userId: string) => {
    const res = await checkAml({ userId })
      .unwrap()
      .catch(e => toast.error(e.data.message || "Error checking AML"));
    if (res.status == 0) toast.success("submitted successfully");
    else toast.error(res.message);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const downloadContract = async () => {
    const contract = contractBase64 || (await getContract(params.id!).unwrap());
    const blob = getFileFromBase64(contract);
    downloadFile(blob);
  };

  if (clientDataLoading) return <Loader />;

  const cso = clientData && clientData.cso && JSON.parse(clientData.cso);
  const ntra = clientData && clientData.ntra && JSON.parse(clientData.ntra);
  const aml = clientData && clientData.aml && JSON.parse(clientData.aml);

  if (!clientData)
    return (
      <div
        style={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
        }}
      >
        Error retrieving client data
      </div>
    );

  return (
    <div style={{ margin: 25 }}>
      {isLoading && <Loader />}
      {isBlocked(clientData) && (
        <button
          className="printBtn"
          style={{
            position: "absolute",
            marginLeft: "66%",
            border: "none",
            color: "black",
            fontWeight: "bold",
            background: "red",
          }}
        >
          {t("Client Blocked")}
        </button>
      )}
      {clientData?.azimutAccount?.clientAML === 0 && (
        <button
          className="printBtn"
          style={{
            position: "absolute",
            marginLeft: "44%",
            border: "none",
            color: "black",
            fontWeight: "bold",
            background:
              aml?.data?.resultColor == "Yellow" &&
              !clientData?.azimutAmlMatches
                ? "yellow"
                : "red",
          }}
        >
          {t("Match Negative List")}
        </button>
      )}
      {clientData?.kycStatus != 5 && clientData.isOld ? (
        <button className="cursor-pointer printBtn float-right">
          {t("Physical Contract")}
        </button>
      ) : (
        <button
          className="cursor-pointer printBtn float-right"
          onClick={downloadContract}
        >
          {t("Download Contract")}
        </button>
      )}

      <Breadcrumbs
        aria-label="breadcrumb"
        separator="›"
        sx={{ margin: 2 }}
      >
        <Link to={"/home"}>{t("Clients List")}</Link>
        <Typography color="text.primary">{t("Clients Details")}</Typography>
      </Breadcrumbs>

      {(!clientData?.isVerified ||
        !clientData?.signedPdf ||
        clientData?.kycStatus == 2 ||
        clientData?.kycStatus == 4) &&
        (!clientData?.isOld || (cso && !cso?.isValid)) &&
        clientData?.kycStatus != 5 &&
        user.user.permissions?.includes("editClient") && (
          <div className="float-right">
            <IconButton
              aria-label="edit"
              onClick={() => {
                navigate("/editClient/" + params.id);
              }}
            >
              <EditIcon />
            </IconButton>
          </div>
        )}
      <Box sx={{ borderBottom: 1, borderColor: "divider" }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          aria-label="basic tabs example"
        >
          <Tab
            label={t("Client Data")}
            {...a11yProps(0)}
          />
          <Tab
            label={t("Bank Accounts")}
            {...a11yProps(1)}
          />
          <Tab
            label={t("KYC")}
            {...a11yProps(2)}
          />
          {user.user.permissions?.includes("showWallet") && (
            <Tab
              label={t("Wallet")}
              {...a11yProps(3)}
            />
          )}
          {user.user.permissions?.includes("showFunds") && (
            <Tab
              label={t("Funds")}
              {...a11yProps(4)}
            />
          )}
        </Tabs>
      </Box>
      <CustomTabPanel
        value={tabValue}
        index={0}
      >
        <ClientData />
      </CustomTabPanel>
      <CustomTabPanel
        value={tabValue}
        index={1}
      >
        <BankAccounts />
      </CustomTabPanel>
      <CustomTabPanel
        value={tabValue}
        index={2}
      >
        <Kyc />
      </CustomTabPanel>

      <CustomTabPanel
        value={tabValue}
        index={3}
      >
        <Wallet />
      </CustomTabPanel>
      <CustomTabPanel
        value={tabValue}
        index={4}
      >
        <Funds />
      </CustomTabPanel>

      <div className="flex justify-between">
        <div />
        <div className="flex">
          <div className="mr-2">{t("Status")}: </div>
          <div className={kycStatuses[getStatusDisplay(clientData)].class}>
            {t(kycStatuses[getStatusDisplay(clientData)].label)}
          </div>
        </div>
        {isEgyptId(clientData) &&
        !cso?.isValid &&
        (!clientData?.signedPdf ||
          clientData?.signedPdf == "offline" ||
          clientData?.kycStatus == 2 ||
          clientData?.kycStatus == 4) &&
        (!clientData?.isOld || (cso && !cso?.isValid)) &&
        (clientData?.userStep > 10 ||
          (clientData?.userStep == 9 &&
            (clientData?.kycStatus == 2 ||
              clientData?.kycStatus == 0 ||
              clientData?.kycStatus == 4))) &&
        user.user.permissions?.includes("checkCSO") ? (
          <div>
            <Button
              type="button"
              className="ml-2"
              loading={isSubmittingCso}
              onClick={() => setShowRecheckCsoDialog(true)}
            >
              Recheck CSO
            </Button>
            {!cso && (
              <Button
                className="ml-2"
                type="button"
                loading={isMarkingCsoFailed}
                onClick={() => setShowRescanIdDialog(true)}
              >
                ID Not Clear (Rescan)
              </Button>
            )}
          </div>
        ) : (
          <div />
        )}
        {isEgyptNtra(clientData) &&
        (!clientData?.signedPdf || clientData?.signedPdf == "offline") &&
        !clientData?.isOld &&
        (clientData?.userStep > 10 ||
          (clientData?.userStep == 9 &&
            (clientData?.kycStatus == 2 ||
              clientData?.kycStatus == 0 ||
              clientData?.kycStatus == 4))) &&
        !ntra?.isMatched &&
        user.user.permissions?.includes("checkNTRA") ? (
          <Button
            type="button"
            loading={isSubmittingNtra}
            onClick={() => setShowRecheckNtraDialog(true)}
          >
            Recheck NTRA
          </Button>
        ) : (
          <div />
        )}

        {(!clientData?.signedPdf || clientData?.signedPdf == "offline") &&
        !clientData?.isOld &&
        (clientData?.userStep > 10 ||
          (clientData?.userStep == 9 &&
            (clientData?.kycStatus == 2 ||
              clientData?.kycStatus == 0 ||
              clientData?.kycStatus == 4))) &&
        !aml &&
        isEgyptId(clientData) &&
        user.user.permissions?.includes("checkNegativeList") ? (
          <Button
            type="button"
            loading={isSubmittingCheckAml}
            onClick={() => setShowRecheckNegativeListDialog(true)}
          >
            Recheck Negative List
          </Button>
        ) : (
          <div />
        )}

        {clientData?.azimutAccount?.clientAML === 0 &&
        user.user.permissions?.includes("signContract") ? (
          <Button
            type="button"
            loading={isSubmittingAml}
            onClick={() => setShowAcceptNegativeListDialog(true)}
          >
            Accept Negative List
          </Button>
        ) : (
          <div />
        )}
        {
          // (isFraValid(clientData) || !isEgyptNtra(clientData)) &&
          // clientData?.kycStatus == 1 &&
          // !clientData?.isVerified &&
          // !clientData?.isOld &&
          // clientData?.userStep == 13 &&
          user.user.permissions?.includes("signContract") ? (
            <Button
              type="button"
              loading={isSubmittingSendToFits}
              onClick={() => submitSendToFits(params.id!)}
            >
              Send to Fits
            </Button>
          ) : (
            <div />
          )
        }
        {clientData?.isVerified &&
        !clientData?.signedPdf &&
        !clientData?.isOld &&
        clientData?.kycStatus == 1 &&
        isFraValid(clientData) &&
        user.user.permissions?.includes("signContract") ? (
          !clientData?.fraStoreId ? ( // only allow sent to Fra to be signed
            <Button
              type="button"
              loading={isSubmitting}
              onClick={() => setShowSendToFraDialog(true)}
            >
              {t("Send to Fra")}
            </Button>
          ) : (
            <Button
              type="button"
              loading={loading}
              disabled={
                clientData?.reviewCount == 0 || !clientData?.hasInjected
              }
              onClick={() => setToSignUsers([params.id!])}
            >
              {t("Sign Contract")}
            </Button>
          )
        ) : (
          <div />
        )}

        {clientData?.isVerified &&
        clientData?.signedPdf &&
        !clientData?.isOld &&
        clientData?.kycStatus == 1 &&
        isFraValid(clientData) &&
        clientData.fraStoreId &&
        !clientData.fraCompanyStoreId &&
        user.user.permissions?.includes("signContract") ? (
          <Button
            type="button"
            loading={isSubmittingCompanyContract}
            onClick={() => setShowSendCompanyContractDialog(true)}
          >
            {t("Send Company Contract to Fra")}
          </Button>
        ) : (
          <div />
        )}
      </div>
      {user.user.permissions?.includes("unblock") && isBlocked(clientData) && (
        <div className="flex justify-between mx-20 mt-2">
          <Button
            type="button"
            loading={isUnblocking}
            onClick={() => setShowUnblockUserDialog(true)}
          >
            {t("Unblock User")}
          </Button>
        </div>
      )}
      {(user.user.permissions?.includes("offline") ||
        user.user.permissions?.includes("rescanId") ||
        user.user.permissions?.includes("signContract")) && (
        <div className="flex justify-between mx-20 mt-2">
          {user.user.permissions?.includes("offline") &&
          !isEgyptId(clientData) &&
          ((clientData?.kycStatus == 1 &&
            clientData?.signedPdf != "offline" &&
            !clientData?.isOld &&
            clientData?.userStep == 13) ||
            (clientData?.kycStatus == 0 && clientData?.userStep > 10)) ? (
            <Button
              type="button"
              loading={isResyncingUser}
              onClick={() => setShowMakeUserScanIdDialog(true)}
            >
              {t("Make User Scan ID")}
            </Button>
          ) : (
            <div />
          )}
          {user.user.permissions?.includes("offline") &&
          !isEgyptNtra(clientData) &&
          clientData?.signedPdf != "offline" &&
          !clientData?.isOld &&
          clientData?.userStep >= 9 &&
          clientData?.kycStatus != 5 ? (
            <Button
              type="button"
              loading={isSendingOffineContract}
              onClick={() => setShowSendOfflineContractDialog(true)}
            >
              {t("Send Offline Contract")}
            </Button>
          ) : (
            <div />
          )}
          {user.user.permissions?.includes("egyptOffline") &&
          isEgyptNtra(clientData) &&
          !clientData?.signedPdf &&
          !clientData?.isOld &&
          clientData?.userStep >= 9 &&
          clientData?.kycStatus != 5 &&
          clientData?.kycStatus != 1 ? (
            <Button
              type="button"
              loading={isSendingOffineContract}
              onClick={() => setShowSendOfflineContractEgyptDialog(true)}
            >
              {t("Send Offline Contract (Egypt)")}
            </Button>
          ) : (
            <div />
          )}
          {user.user.permissions?.includes("approveContract") &&
          ((isEgyptNtra(clientData)
            ? !isFraValid(clientData)
            : clientData?.isVerified) ||
            clientData?.signedPdf == "offline") &&
          !clientData?.isOld &&
          clientData?.userStep >= 9 &&
          !clientData?.pdfSignedAt &&
          clientData?.kycStatus != 5 ? (
            <Button
              type="button"
              loading={isApprovingOfflineContract}
              onClick={() => setShowApproveOfflineContractDialog(true)}
            >
              {t("Approve Offline Contract (User Signed)")}
            </Button>
          ) : (
            <div />
          )}
        </div>
      )}
      <ChooserDialog
        toSignUsers={toSignUsers}
        setLoading={setLoading}
      />
      <ConfirmationDialog
        open={showRecheckCsoDialog}
        title={t("Confirm Recheck CSO")}
        message={t("Are you sure you want to recheck CSO for this client?")}
        onClose={() => setShowRecheckCsoDialog(false)}
        onConfirm={() => {
          setShowRecheckCsoDialog(false);
          submitCheckCso(params.id!);
        }}
      />
      <ConfirmationDialog
        open={showRescanIdDialog}
        title={t("Confirm Rescan ID")}
        message={t(
          "Are you sure you want to mark this ID as not clear and request a rescan?"
        )}
        onClose={() => setShowRescanIdDialog(false)}
        onConfirm={() => {
          setShowRescanIdDialog(false);
          submitMarkCsoFailed(params.id!);
        }}
      />
      <ConfirmationDialog
        open={showRecheckNtraDialog}
        title={t("Confirm Recheck NTRA")}
        message={t("Are you sure you want to recheck NTRA for this client?")}
        onClose={() => setShowRecheckNtraDialog(false)}
        onConfirm={() => {
          setShowRecheckNtraDialog(false);
          submitCheckNtra(params.id!);
        }}
      />
      <ConfirmationDialog
        open={showRecheckNegativeListDialog}
        title={t("Confirm Recheck Negative List")}
        message={t(
          "Are you sure you want to recheck the negative list for this client?"
        )}
        onClose={() => setShowRecheckNegativeListDialog(false)}
        onConfirm={() => {
          setShowRecheckNegativeListDialog(false);
          submitCheckAml(params.id!);
        }}
      />
      <ConfirmationDialog
        open={showAcceptNegativeListDialog}
        title={t("Confirm Accept Negative List")}
        message={t(
          "Are you sure you want to accept this client from the negative list?"
        )}
        onClose={() => setShowAcceptNegativeListDialog(false)}
        onConfirm={() => {
          setShowAcceptNegativeListDialog(false);
          submitBypassAml(params.id!);
        }}
        confirmColor="error"
      />
      <ConfirmationDialog
        open={showSendToFraDialog}
        title={t("Confirm Send to FRA")}
        message={t(
          "Are you sure you want to send this contract to FRA for signing?"
        )}
        onClose={() => setShowSendToFraDialog(false)}
        onConfirm={() => {
          setShowSendToFraDialog(false);
          submitToFra(params.id!);
        }}
      />
      <ConfirmationDialog
        open={showSendCompanyContractDialog}
        title={t("Confirm Send Company Contract to FRA")}
        message={t(
          "Are you sure you want to send the company contract to FRA for signing?"
        )}
        onClose={() => setShowSendCompanyContractDialog(false)}
        onConfirm={() => {
          setShowSendCompanyContractDialog(false);
          submitCompanyContractToFra(params.id!);
        }}
      />
      <ConfirmationDialog
        open={showUnblockUserDialog}
        title={t("Confirm Unblock User")}
        message={t("Are you sure you want to unblock this user?")}
        onClose={() => setShowUnblockUserDialog(false)}
        onConfirm={() => {
          setShowUnblockUserDialog(false);
          unblockUSer({ userId: params.id! });
        }}
      />
      <ConfirmationDialog
        open={showMakeUserScanIdDialog}
        title={t("Confirm Make User Scan ID")}
        message={t(
          "Are you sure you want to make this user scan their ID again?"
        )}
        onClose={() => setShowMakeUserScanIdDialog(false)}
        onConfirm={() => {
          setShowMakeUserScanIdDialog(false);
          resyncUser({ userId: params.id! });
        }}
      />
      <ConfirmationDialog
        open={showSendOfflineContractDialog}
        title={t("Confirm Send Offline Contract")}
        message={t(
          "Are you sure you want to send an offline contract to this client?"
        )}
        onClose={() => setShowSendOfflineContractDialog(false)}
        onConfirm={() => {
          setShowSendOfflineContractDialog(false);
          sendOfflineContract({ userId: params.id! });
        }}
      />
      <ConfirmationDialog
        open={showSendOfflineContractEgyptDialog}
        title={t("Confirm Send Offline Contract (Egypt)")}
        message={t(
          "Are you sure you want to send an offline contract to this Egyptian client?"
        )}
        onClose={() => setShowSendOfflineContractEgyptDialog(false)}
        onConfirm={() => {
          setShowSendOfflineContractEgyptDialog(false);
          sendOfflineContract({ userId: params.id! });
        }}
      />
      <ConfirmationDialog
        open={showApproveOfflineContractDialog}
        title={t("Confirm Approve Offline Contract")}
        message={t(
          "Are you sure you want to approve this offline contract as signed by the user?"
        )}
        onClose={() => setShowApproveOfflineContractDialog(false)}
        onConfirm={() => {
          setShowApproveOfflineContractDialog(false);
          approveOfflineContract({ userId: params.id! });
        }}
      />
    </div>
  );
};

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}
