import {
  <PERSON>,
  Fade,
  Grid,
  ImageList,
  ImageList<PERSON>tem,
  Modal,
  TextField,
  Typography,
} from "@mui/material";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";

import Button from "../../components/shared/Button/Button";
import Loader from "../../components/shared/Spinner/Loader";
import { azimutApi } from "../../hooks/azimutApi";
import { modalStyle } from "./ClientData";

const EditClientData = () => {
  const params = useParams();
  const { t } = useTranslation("common");
  const navigate = useNavigate();
  const { data: clientData, isLoading } = azimutApi.useGetClientDetailsQuery(
    params.id!
  );
  const [editUser, { isLoading: isEditing }] =
    azimutApi.useEditUserOnlyMutation();

  const [userData, setUserData] = useState({
    userId: clientData?.userId || "",
    firstName: clientData?.firstName || "",
    lastName: clientData?.lastName || "",
    address: clientData?.azimutAccount.addressEn || "",
    dateOfBirth: clientData?.dateOfBirth || "",
    dateOfRelease: clientData?.dateOfRelease || "",
    dateOfIdExpiry: clientData?.dateOfIdExpiry || "",
    fcn: JSON.parse(clientData?.idData || "{}").fcn || "",
  });

  useEffect(() => {
    if (clientData) {
      setUserData({
        userId: clientData.userId,
        firstName: clientData.firstName,
        lastName: clientData.lastName,
        address: clientData.azimutAccount.addressEn,
        dateOfBirth: clientData.dateOfBirth,
        dateOfRelease: clientData.dateOfRelease,
        dateOfIdExpiry: clientData.dateOfIdExpiry,
        fcn: JSON.parse(clientData.idData || "{}").fcn || "",
      });
    }
  }, [clientData]);

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setUserData(prevData => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    const idData = JSON.parse(clientData!.idData || "{}");
    idData.fcn = userData.fcn;
    const data = {
      ...userData,
      idData: JSON.stringify(idData),
      azimutAccount: {
        ...clientData!.azimutAccount,
        addressEn: userData.address,
        addressAr: userData.address,
      },
    };
    await editUser({ ...clientData!, ...data });
    navigate("/client/" + params.id);
  };

  let images = clientData?.userImages || [];

  const [open, setOpen] = useState(false);
  const [image, setImage] = useState("false");
  const handleClose = () => setOpen(false);

  const handleImageClick = (value: string) => {
    setImage(value);
    setOpen(true);
  };

  if (isLoading) return <Loader />;

  return (
    <Box sx={{ maxWidth: 800, mx: "auto", p: 2 }}>
      {images.length > 0 && (
        <ImageList
          sx={{ width: "100%", height: "250px" }}
          cols={2}
          rowHeight={250}
        >
          {images
            .filter(e => e.imageType != 1)
            .map(item => (
              <ImageListItem key={item.id}>
                <img
                  src={item.imageUrl}
                  alt={item.imageType.toString()}
                  onClick={() => handleImageClick(item.imageUrl)}
                  loading="lazy"
                  style={{ maxHeight: 250, objectFit: "contain" }}
                />
              </ImageListItem>
            ))}
        </ImageList>
      )}
      <Typography
        variant="h6"
        className="mb-6 mt-2"
        gutterBottom
      >
        {t("Edit Client Data")}
      </Typography>
      <form onSubmit={handleSubmit}>
        <Grid
          container
          spacing={2}
        >
          <Grid
            item
            xs={12}
            sm={12}
          >
            <TextField
              fullWidth
              label={t("ID Number")}
              name="userId"
              value={userData.userId}
              onChange={handleChange}
              variant="outlined"
              required
            />
          </Grid>
          <Grid
            item
            xs={12}
            sm={6}
          >
            <TextField
              fullWidth
              label={t("First Name")}
              name="firstName"
              value={userData.firstName}
              onChange={handleChange}
              variant="outlined"
              required
            />
          </Grid>
          <Grid
            item
            xs={12}
            sm={6}
          >
            <TextField
              fullWidth
              label={t("Last Name")}
              name="lastName"
              value={userData.lastName}
              onChange={handleChange}
              variant="outlined"
              required
            />
          </Grid>
          <Grid
            item
            xs={12}
          >
            <TextField
              fullWidth
              label={t("Address")}
              name="address"
              value={userData.address}
              onChange={handleChange}
              variant="outlined"
              required
            />
          </Grid>
          <Grid
            item
            xs={12}
            sm={6}
          >
            <TextField
              fullWidth
              label={t("Date of Birth")}
              name="dateOfBirth"
              value={userData.dateOfBirth}
              onChange={handleChange}
              variant="outlined"
              required
            />
          </Grid>
          <Grid
            item
            xs={12}
            sm={6}
          >
            <TextField
              fullWidth
              label={t("Issue date")}
              name="dateOfRelease"
              value={userData.dateOfRelease}
              onChange={handleChange}
              variant="outlined"
              required
            />
          </Grid>
          <Grid
            item
            xs={12}
            sm={6}
          >
            <TextField
              fullWidth
              label={t("Expiration Date")}
              name="dateOfIdExpiry"
              value={userData.dateOfIdExpiry}
              onChange={handleChange}
              variant="outlined"
              required
            />
          </Grid>
          <Grid
            item
            xs={12}
            sm={6}
          >
            <TextField
              fullWidth
              label={t("Factory Number")}
              name="fcn"
              value={userData.fcn}
              onChange={handleChange}
              variant="outlined"
              required
            />
          </Grid>
          <Grid
            item
            xs={12}
            sm={6}
          />
          <Grid
            item
            xs={12}
            className="text-right"
          >
            <Button
              loading={isEditing}
              variant="text"
              className="mr-2"
              onClick={() => navigate("/client/" + params.id)}
            >
              {t("Cancel")}
            </Button>
            <Button
              type="submit"
              loading={isEditing}
              variant="contained"
              color="primary"
            >
              {t("Save Changes")}
            </Button>
          </Grid>
        </Grid>
      </form>

      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={modalStyle}>
          <Fade
            in={open}
            timeout={500}
          >
            <img
              src={image}
              alt="asd"
              style={{ maxHeight: "90%", maxWidth: "90%" }}
            />
          </Fade>
        </Box>
      </Modal>
    </Box>
  );
};

export default EditClientData;
