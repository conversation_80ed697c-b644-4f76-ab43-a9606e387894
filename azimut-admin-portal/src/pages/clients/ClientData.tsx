import {
  Assignment,
  CheckBoxRounded,
  SquareRounded,
} from "@mui/icons-material";
import {
  Box,
  Chip,
  Fade,
  ImageList,
  ImageListItem,
  Modal,
  Tooltip,
  Typography,
} from "@mui/material";
import { keyBy } from "lodash";
import { Fragment, useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";

import Button from "../../components/shared/Button/Button";
import ConfirmationDialog from "../../components/shared/Dialog/ConfirmationDialog";
import HtmlTooltip from "../../components/shared/HtmlTooltip";
import Loader from "../../components/shared/Spinner/Loader";
import { azimutAdminApi } from "../../hooks/adminUsersApi";
import { azimutApi } from "../../hooks/azimutApi";
import { AzimutUser } from "../../hooks/types";
import { useAuth } from "../../hooks/useAuth";
import SimilarCasesDialog from "./AmlMatchesDialog";

export const isEgyptId = (clientData?: AzimutUser) =>
  clientData && clientData?.userIdType == "National Id";

export const isEgyptNtra = (clientData?: AzimutUser) =>
  clientData &&
  clientData?.userIdType == "National Id" &&
  clientData.countryPhoneCode == "+20";

export const isInEgypt = (lat: number, lon: number) => {
  return lat >= 22.0 && lat <= 31.7 && lon >= 24.7 && lon <= 35;
};

export const isFraValid = (clientData?: AzimutUser) => {
  if (!isEgyptNtra(clientData)) return false;
  const cso = clientData && clientData.cso && JSON.parse(clientData.cso);
  // if (!isEgyptNtra(clientData))
  //   return cso?.isValid && clientData?.azimutAccount?.clientAML == 1;
  const ntra = clientData && clientData.ntra && JSON.parse(clientData.ntra);
  const aml = clientData && clientData.aml && JSON.parse(clientData.aml);
  return (
    cso?.isValid &&
    ntra?.isMatched &&
    aml?.data?.resultColor &&
    clientData?.azimutAccount?.clientAML == 1
  ); // aml not black list & cso is valid & ntra matched
};

export const modalStyle = {
  position: "absolute" as "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: "60%",
  bgcolor: "background.paper",
  border: "2px solid #000",
  textAlign: "center",
  boxShadow: 24,
  p: 4,
};

export const ClientData = () => {
  const params = useParams();
  const { user } = useAuth()!;
  const { t } = useTranslation("common");
  const { i18n } = useTranslation();
  const [open, setOpen] = useState(false);
  const [image, setImage] = useState("false");
  const [openAml, setOpenAml] = useState(false);
  const [openAzimutAml, setOpenAzimutAml] = useState(false);
  const [openApproveCsoDialog, setOpenApproveCsoDialog] = useState(false);
  const [openResetPhoneDialog, setOpenResetPhoneDialog] = useState(false);
  const { data: portalUsers } = azimutAdminApi.useGetUsersQuery();
  const portalUsersDict = useMemo(() => {
    return keyBy(portalUsers, "id");
  }, [portalUsers]);
  const { data: clientHistoryDates } = azimutApi.useUserHistoryQuery(
    params.id!
  );
  const { data: complianceLogs } = azimutApi.useGetComplianceLogQuery(
    params.id!
  );

  const csoLogs = complianceLogs?.filter(e => e.checkType == "CSO");
  const ntraLogs = complianceLogs?.filter(e => e.checkType == "NTRA");
  const amlLogs = complianceLogs?.filter(e => e.checkType == "AML");
  const userContractLogs = complianceLogs?.filter(
    e => e.checkType == "USER_CONTRACT"
  );
  const companyContractLogs = complianceLogs?.filter(
    e => e.checkType == "COMPANY_CONTRACT"
  );

  const handleClose = () => setOpen(false);

  const handleImageClick = (value: string) => {
    setImage(value);
    setOpen(true);
  };

  const { data: clientData, isLoading } = azimutApi.useGetClientDetailsQuery(
    params.id!
  );

  const { data: searchData } = azimutApi.useSearchNegativeListQuery(
    {
      searchTerm:
        clientData?.firstName != null
          ? (
              clientData?.firstName +
              " " +
              (clientData?.lastName != null ? clientData?.lastName : "")
            ).trim()
          : clientData?.nickName,
      nationalId: clientData?.idType == 1 ? clientData?.userId : undefined,
      passportNumber: clientData?.idType == 3 ? clientData?.userId : undefined,
    },
    {
      skip: !clientData?.azimutAmlMatches,
    }
  );

  const [resetChangePhoneAttempts, { isLoading: isResetting }] =
    azimutApi.useResetChangePhoneAttemptsMutation();

  const [approveCso, { isLoading: isApprovingCso }] =
    azimutApi.useApproveCsoMutation();

  if (isLoading) return <Loader />;

  const aml = clientData && clientData.aml && JSON.parse(clientData.aml);
  const cso = clientData && clientData.cso && JSON.parse(clientData.cso);
  const ntra = clientData && clientData.ntra && JSON.parse(clientData.ntra);

  let images = clientData?.userImages || [];
  if (!user.user.permissions?.includes("viewClient"))
    images = images.filter(e => e.imageType != 1) || [];

  return (
    <div>
      <div className="pageSemiTitle mb-5">
        {t(clientData?.userIdType!)} : {clientData?.userId}
        {/* className="ml-1" */}
        {cso?.isValid && !cso?.errorMessage ? (
          <CheckBoxRounded color="success" />
        ) : (
          <Tooltip title={cso?.errorMessage || ""}>
            <SquareRounded
              sx={{
                color: cso ? "red" : isEgyptId(clientData) ? "gray" : "yellow",
              }}
            />
          </Tooltip>
        )}{" "}
        CSO
        {cso &&
          !cso.isValid &&
          cso?.errorMessage?.includes("البيان سرى") &&
          user.user.permissions?.includes("approveCSO") && (
            <Button
              className="ml-2"
              type="button"
              loading={isApprovingCso}
              onClick={() => setOpenApproveCsoDialog(true)}
            >
              {t("Approve CSO")}
            </Button>
          )}
        {cso && cso.isValid && cso?.errorMessage?.includes("البيان سرى") && (
          <div
            className="accepted ml-2"
            style={{ display: "inline-flex" }}
          >
            AZ accepted
          </div>
        )}
        {csoLogs && csoLogs.length > 0 && (
          <span
            className="ml-2"
            style={{ verticalAlign: "middle" }}
          >
            <HtmlTooltip
              placement="bottom-start"
              title={
                <Fragment>
                  <Typography
                    color="inherit"
                    className="text-center"
                  >
                    CSO Check Dates
                  </Typography>
                  {csoLogs.map((q, id) => (
                    <div key={id}>
                      {q.createdAt} by{" "}
                      <b>{portalUsersDict[q.adminUserId!]?.fullName}</b>
                    </div>
                  ))}
                </Fragment>
              }
            >
              <Assignment />
            </HtmlTooltip>
          </span>
        )}
      </div>

      {images.length > 0 && (
        <ImageList
          sx={{ width: "100%", height: "250px" }}
          cols={3}
          rowHeight={250}
        >
          {images.map(item => (
            <ImageListItem key={item.id}>
              <img
                src={item.imageUrl}
                alt={item.imageType.toString()}
                onClick={() => handleImageClick(item.imageUrl)}
                loading="lazy"
                style={{ maxHeight: 250, objectFit: "contain" }}
              />
            </ImageListItem>
          ))}
        </ImageList>
      )}

      <div className="pageSemiTitle mb-5">{t("Personal Details")}</div>

      <div className="grid grid-cols-2 gap-4 my-1">
        <div>
          <div className="font-semibold leading-7 text-base">
            {t("First Name")}
          </div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.firstName || "-"}
          </div>
        </div>
        <div className="my-1">
          <div className="font-semibold leading-7 text-base">
            {t("Last Name")}
          </div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.lastName || "-"}
          </div>
        </div>

        <div>
          <div className="font-semibold leading-7 text-base">
            {t("Client Name")}
          </div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.nickName || "-"}
          </div>
        </div>
        <div className="my-1">
          <div className="font-semibold leading-7 text-base">{t("Email")}</div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.emailAddress || "-"}
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4 my-1">
        <div>
          <div className="font-semibold leading-7 text-base mt-2">
            {t("Address")}
          </div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300 mb-4">
            {(i18n.language == "en"
              ? clientData?.azimutAccount.addressEn
              : clientData?.azimutAccount.addressAr) || "-"}
          </div>
        </div>

        <div className="grid grid-cols-2">
          <div>
            <div className="font-semibold leading-7 text-base">
              FRA Negative List{" "}
              {amlLogs && amlLogs.length > 0 && (
                <span
                  className="ml-2"
                  style={{ verticalAlign: "middle" }}
                >
                  <HtmlTooltip
                    placement="bottom-start"
                    title={
                      <Fragment>
                        <Typography
                          color="inherit"
                          className="text-center"
                        >
                          Negative List Check Dates
                        </Typography>
                        {amlLogs?.map((q, id) => (
                          <div key={id}>
                            {q.createdAt} by{" "}
                            <b>{portalUsersDict[q.adminUserId!]?.fullName}</b>
                          </div>
                        ))}
                      </Fragment>
                    }
                  >
                    <Assignment />
                  </HtmlTooltip>
                </span>
              )}
              {(aml?.data?.resultColor == "Yellow" ||
                aml?.data?.resultColor == "Red") && (
                <a
                  onClick={() => setOpenAml(true)}
                  style={{ cursor: "pointer" }}
                >
                  Show Matches
                </a>
              )}
            </div>

            <div className="text-sm leading-6 max-w-2xl text-gray-700">
              {aml?.data?.resultColor == "Green" ? (
                <CheckBoxRounded color="success" />
              ) : (
                <SquareRounded sx={{ color: "grey" }} />
              )}{" "}
              Green
              {aml?.data?.resultColor == "Yellow" ? (
                <CheckBoxRounded sx={{ color: "Yellow" }} />
              ) : (
                <SquareRounded sx={{ color: "grey" }} />
              )}{" "}
              Yellow
              {aml?.data?.resultColor == "Red" ? (
                <CheckBoxRounded sx={{ color: "red" }} />
              ) : (
                <SquareRounded sx={{ color: "grey" }} />
              )}{" "}
              Red
            </div>
            {(aml?.data?.resultColor == "Red" ||
              aml?.data?.resultColor == "Yellow") &&
              clientData?.azimutAccount?.clientAML === 1 && (
                <div className="accepted absolute">AZ accepted</div>
              )}
          </div>
          <div>
            <div className="font-semibold leading-7 text-base">
              Azimut Negative List{" "}
              {(clientData?.azimutAmlMatches || 0) > 0 && (
                <a
                  style={{ cursor: "pointer" }}
                  onClick={() => setOpenAzimutAml(true)}
                >
                  Show Matches
                </a>
              )}
            </div>

            <div className="text-sm leading-6 max-w-2xl text-gray-700">
              {clientData?.azimutAmlMatches ? (
                <CheckBoxRounded sx={{ color: "red" }} />
              ) : (
                <SquareRounded sx={{ color: "grey" }} />
              )}{" "}
              Matched
              {clientData?.azimutAmlMatches === 0 ? (
                <CheckBoxRounded color="success" />
              ) : (
                <SquareRounded sx={{ color: "grey" }} />
              )}{" "}
              Not Matched
            </div>
            {!!clientData?.azimutAmlMatches &&
              clientData?.azimutAccount?.clientAML === 1 && (
                <div className="accepted absolute">AZ accepted</div>
              )}
          </div>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4 my-1">
        <div>
          <div className="font-semibold leading-7 text-base">
            {t("Country")}
          </div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.country || "-"}
          </div>
        </div>
        <div>
          <div className="font-semibold leading-7 text-base">{t("Risk")}</div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.risk ? <Chip label={clientData?.risk} /> : "-"}
          </div>
        </div>
        <div>
          <div className="font-semibold leading-7 text-base">{t("City")}</div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.city || "-"}
          </div>
        </div>
        <div>
          <div className="font-semibold leading-7 text-base">
            {t("Phone Number")}
            {user.user.permissions?.includes("resetChangePhone") &&
              clientData &&
              clientData.changePhoneCount > 0 && (
                <Button
                  className="ml-2"
                  type="button"
                  loading={isResetting}
                  onClick={() => setOpenResetPhoneDialog(true)}
                >
                  {t("Reset Change Phone Attempts")}
                </Button>
              )}
          </div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.userPhone}
            {ntra?.isMatched ? (
              <CheckBoxRounded color="success" />
            ) : (
              <Tooltip title={ntra?.errorMessage || ""}>
                <SquareRounded
                  sx={{
                    color: ntra
                      ? "red"
                      : isEgyptNtra(clientData)
                      ? "gray"
                      : "yellow",
                  }}
                />
              </Tooltip>
            )}{" "}
            NTRA
            {ntraLogs && ntraLogs.length > 0 && (
              <span
                className="ml-2"
                style={{ verticalAlign: "middle" }}
              >
                <HtmlTooltip
                  placement="bottom-start"
                  title={
                    <Fragment>
                      <Typography
                        color="inherit"
                        className="text-center"
                      >
                        NTRA Check Dates
                      </Typography>
                      {ntraLogs.map((q, id) => (
                        <div key={id}>
                          {q.createdAt} by{" "}
                          <b>{portalUsersDict[q.adminUserId!]?.fullName}</b>
                        </div>
                      ))}
                    </Fragment>
                  }
                >
                  <Assignment />
                </HtmlTooltip>
              </span>
            )}
          </div>
        </div>
        <div>
          <div className="font-semibold leading-7 text-base">
            {t("Date of Birth")}
          </div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.dateOfBirth || "-"}
          </div>
        </div>
        <div>
          <div className="font-semibold leading-7 text-base">
            {t("Issue date")}
          </div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.dateOfRelease || "-"}
          </div>
        </div>
        <div>
          <div className="font-semibold leading-7 text-base">
            {t("Expiration Date")}
          </div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.dateOfIdExpiry || "-"}
          </div>
        </div>

        <div>
          <div className="font-semibold leading-7 text-base">
            {t("Created date")}
            {clientHistoryDates && clientHistoryDates.length > 0 && (
              <span
                className="ml-2"
                style={{ verticalAlign: "middle" }}
              >
                <HtmlTooltip
                  placement="bottom-start"
                  title={
                    <Fragment>
                      <Typography
                        color="inherit"
                        className="text-center"
                      >
                        Old Account Dates
                      </Typography>
                      {clientHistoryDates.map((q, id) => (
                        <div key={id}>{q}</div>
                      ))}
                    </Fragment>
                  }
                >
                  <Assignment />
                </HtmlTooltip>
              </span>
            )}
          </div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.createdAt || "-"}
          </div>
        </div>
        <div>
          <div className="font-semibold leading-7 text-base">
            {t("Updated at")}
          </div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.updatedAt || "-"}
          </div>
        </div>

        <div>
          <div className="font-semibold leading-7 text-base">
            {t("Contract Signed at")}
            {(clientData?.pdfSignedAt ||
              userContractLogs?.length! > 0 ||
              companyContractLogs?.length! > 0) && (
              <span
                className="ml-2"
                style={{ verticalAlign: "middle" }}
              >
                <HtmlTooltip
                  placement="bottom-start"
                  title={
                    <Fragment>
                      <Typography
                        color="inherit"
                        className="text-center"
                      >
                        {clientData?.pdfSignedAt && (
                          <div>
                            <div>Contract Signed by Azimut: </div>
                            <div>{clientData?.pdfSignedAt}</div>
                          </div>
                        )}
                        {userContractLogs?.length! > 0 && (
                          <div>
                            <div>User Contract Sent to FRA:</div>
                            {userContractLogs?.map((q, id) => (
                              <div key={id}>{q.createdAt}</div>
                            ))}
                          </div>
                        )}
                        {companyContractLogs?.length! > 0 && (
                          <div className="mt-2">
                            <div>Company Contract Sent to Fra:</div>
                            {companyContractLogs?.map((q, id) => (
                              <div key={id}>{q.createdAt}</div>
                            ))}
                          </div>
                        )}
                      </Typography>
                    </Fragment>
                  }
                >
                  <Assignment />
                </HtmlTooltip>
              </span>
            )}
          </div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.contractSignedAt
              ? new Date(clientData.contractSignedAt).toLocaleString()
              : clientData?.isOld
              ? "Physical Contract"
              : "-"}
          </div>
        </div>

        <div>
          <div className="font-semibold leading-7 text-base">
            {t("Location In Egypt ?")}
          </div>
          <div className="text-sm leading-6 mt-1 max-w-2xl text-gray-300">
            {clientData?.userLocation
              ? isInEgypt(
                  parseFloat(clientData.userLocation.lat),
                  parseFloat(clientData.userLocation.longt)
                )
                ? "Yes"
                : "No"
              : "-"}
            {clientData?.userLocation && (
              <a
                target="_blank"
                href={`https://maps.google.com/?q=${clientData.userLocation.lat},${clientData.userLocation.longt}`}
                rel="noreferrer"
                className="text-blue-500 hover:underline ml-2"
              >
                show
              </a>
            )}
          </div>
        </div>
      </div>

      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={modalStyle}>
          <Fade
            in={open}
            timeout={500}
          >
            <img
              src={image}
              alt="asd"
              style={{ maxHeight: "90%", maxWidth: "90%" }}
            />
          </Fade>
        </Box>
      </Modal>
      <ConfirmationDialog
        open={openApproveCsoDialog}
        title={t("Confirm Approve CSO")}
        message={t("Are you sure you want to approve CSO for this client?")}
        onClose={() => setOpenApproveCsoDialog(false)}
        onConfirm={() => {
          approveCso({ userId: params.id! });
          setOpenApproveCsoDialog(false);
        }}
        loading={isApprovingCso}
      />
      <ConfirmationDialog
        open={openResetPhoneDialog}
        title={t("Confirm Reset Change Phone Attempts")}
        message={t(
          "Are you sure you want to reset change phone attempts for this client?"
        )}
        onClose={() => setOpenResetPhoneDialog(false)}
        onConfirm={() => {
          resetChangePhoneAttempts({ userId: params.id! });
          setOpenResetPhoneDialog(false);
        }}
        loading={isResetting}
      />
      <SimilarCasesDialog
        open={openAml}
        onClose={() => setOpenAml(false)}
        cases={aml?.data?.output?.data || []}
      />
      <SimilarCasesDialog
        open={openAzimutAml}
        onClose={() => setOpenAzimutAml(false)}
        cases={searchData || []}
        isAzimut
      />
    </div>
  );
};
