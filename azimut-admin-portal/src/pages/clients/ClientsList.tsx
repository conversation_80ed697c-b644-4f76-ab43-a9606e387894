import "../../assets/styles/main.css";

import CheckBoxIcon from "@mui/icons-material/CheckBox";
import FilterListIcon from "@mui/icons-material/FilterList";
import {
  Autocomplete,
  Box,
  Chip,
  DialogActions,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TablePagination,
  TableRow,
  TextField,
} from "@mui/material";
import { GridColDef } from "@mui/x-data-grid";
import { skipToken } from "@reduxjs/toolkit/query";
import dayjs, { Dayjs } from "dayjs";
import jsonToCsvExport from "json-to-csv-export";
import { Fragment, useEffect, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useDispatch } from "react-redux";
import { useLocation, useNavigate } from "react-router-dom";
import { useDebouncedCallback } from "use-debounce";

import Button from "../../components/shared/Button/Button";
import ChooserDialog from "../../components/shared/Dialog/ChooserDialogue";
import Dialog from "../../components/shared/Dialog/Dialog";
import { FilterDrawer } from "../../components/shared/FilterDrawer/FilterDrawer";
import Spinner from "../../components/shared/Spinner/Spinner";
import { EnhancedTableHead } from "../../components/Table/EnhancedTableHead";
import { azimutAdminApi } from "../../hooks/adminUsersApi";
import { azimutApi } from "../../hooks/azimutApi";
import {
  AzimutUser,
  GetUsersFilter,
  NotificationTemplate,
  Order,
  PopupTemplate,
} from "../../hooks/types";
import { useAuth } from "../../hooks/useAuth";
import { isFraValid } from "./ClientData";

export const getStatusDisplay = (client: AzimutUser | undefined) => {
  if (!client) return "";
  if (client.kycStatus == 1 && client.reviewCount == 0 && !client.isOld)
    return "0.1";
  else return client.kycStatus.toString();
};

export const kycStatuses: Record<string, any> = {
  "-1": {
    label: "Show all",
  },
  "0": {
    label: "Pending",
    class: "pending",
  },
  "0.1": {
    label: "Auto Accepted",
    class: "auto-accepted",
  },
  "1": {
    label: "Accepted",
    class: "accepted",
  },
  "2": {
    label: "Rejected",
    class: "rejected",
  },
  "4": {
    label: "Updated",
    class: "pending",
  },
  "5": {
    label: "New User",
    class: "new-user",
  },
  "2.1": {
    label: "Ready to Sign",
    class: "accepted",
  },
};

export const negativeListTypes: Record<string, any> = {
  "-1": {
    label: "Show All",
  },
  "1": {
    label: "Azimut Match",
  },
  "2": {
    label: "FRA Match",
  },
};

export const validations: Record<string, any> = {
  "-1": {
    label: "Show all",
  },
  "1": {
    label: "CSO First Name",
  },
  "2": {
    label: "CSO Other Name",
  },
  "3": {
    label: "CSO Factory Number",
  },
  "4": {
    label: "CSO Expiration Date",
  },
  "5": {
    label: "CSO Error ( بيان سري )",
  },
  "51": {
    label: "CSO Not Clear",
  },
  "6": {
    label: "NTRA Invalid",
  },
  "7": {
    label: "NTRA Service Error",
  },
  "8": {
    label: "Negative List",
  },
  "9": {
    label: "Rejected",
  },
  "10": {
    label: "Accepted",
  },
  "11": {
    label: "Not done",
  },
};

export const signatures: Record<string, any> = {
  "-1": {
    label: "Show all",
  },
  "1": {
    label: "Digital",
  },
  "2": {
    label: "Sent Offline",
  },
  "3": {
    label: "International Phone/Passport",
  },
};

export const nationalities: Record<string, any> = {
  "-1": {
    label: "Show all",
  },
  "1": {
    label: "Egyptian",
  },
  "2": {
    label: "Non Egyptian",
  },
};

export const isVerifiedStates: Record<string, any> = {
  "-1": {
    label: "Show all",
  },
  "1": {
    label: "Active",
  },
  "2": {
    label: "Not Active",
  },
};

export const sentToFraStates: Record<string, any> = {
  "-1": {
    label: "Show all",
  },
  "1": {
    label: "Sent",
  },
  "2": {
    label: "Not Sent",
  },
};

export const contractSignedStates: Record<string, any> = {
  "-1": {
    label: "Show all",
  },
  "0": {
    label: "Signed by Azimut",
  },
  "1": {
    label: "Signed by Azimut & FRA Service Error",
  },
  "2": {
    label: "Not Signed by Azimut",
  },
  "3": {
    label: "Offline Contract Approved",
  },
  "4": {
    label: "Offline Contract Not Approved",
  },
  "5": {
    label: "Physical Signature",
  },
};

export const digitalContractSignedStates: Record<string, any> = {
  "-1": {
    label: "Show all",
  },
  "0": {
    label: "Signed by Azimut",
  },
  "2": {
    label: "Not Signed by Azimut",
  },
};

export const investmentAmountStates: Record<string, any> = {
  "-1": {
    label: "Show all",
  },
  "73": {
    label: "Less than EGP 100,000",
  },
  "74": {
    label: "EGP 100,000 to EGP 250,000",
  },
  "75": {
    label: "EGP 250,000 to EGP 500,000",
  },
  "76": {
    label: "More than EGP 500,000",
  },
};

export const annualIncomeStates: Record<string, any> = {
  "-1": {
    label: "Show all",
  },
  "122": {
    label: "Less than EGP 100,000",
  },
  "123": {
    label: "EGP 100,000 to EGP 250,000",
  },
  "124": {
    label: "EGP 250,000 to EGP 500,000",
  },
  "125": {
    label: "More than EGP 500,000",
  },
};

export const fatcaStates: Record<string, any> = {
  "-1": {
    label: "Show all",
  },
  "192": {
    label: "Yes",
  },
  "193": {
    label: "No",
  },
};

export const userStepTypes: Record<string, any> = {
  "-1": {
    label: "Show all",
  },
  "0": {
    label: "Signup",
  },
  "1": {
    label: "Signup",
  },
  "2": {
    label: "Signup",
  },
  "3": {
    label: "eKYC",
  },
  "6": {
    label: "Client Data",
  },
  "7": {
    label: "Client Data",
  },
  "8": {
    label: "Bank Account",
  },
  "9": {
    label: "KYC",
  },
  "10": {
    label: "View Contract",
  },
  "11": {
    label: "User Signed Contract",
  },
  "12": {
    label: "Rejected",
  },
  "13": {
    label: "Completed",
  },
};

const columns: GridColDef[] = [
  { field: "id", headerName: "ID" },
  { field: "nickName", headerName: "Client Name" },
  { field: "emailAddress", headerName: "Email" },
  { field: "userPhone", headerName: "Phone Number" },
  { field: "userId", headerName: "NID/Passport" },
  {
    field: "kycStatus",
    headerName: "Status",
  },
  {
    field: "contractMap",
    headerName: "Signature",
  },
  {
    field: "isVerified",
    headerName: "Active (APP)",
  },
  {
    field: "isSynchronized",
    headerName: "Sent to FITS",
  },
  {
    field: "signedPdf",
    headerName: "Contract Signed",
  },
  { field: "userStep", headerName: "User Step" },
  { field: "createdAt", headerName: "Created date" },
  { field: "updatedAt", headerName: "Updated date" },
  { field: "referralCode", headerName: "Referral Code" },
  { field: "countryId", headerName: "Country" },
  { field: "cityId", headerName: "City" },
];

export const ClientsList = () => {
  const location = useLocation();
  const isDigital = location.pathname.includes("clients");
  useEffect(() => {
    if (isDigital) {
      setNationality("-1");
      setContractMap("-1");
      columns[4].headerName = "National ID";
    } else {
      columns[4].headerName = "NID/Passport";
    }
  }, [isDigital]);
  const args: GetUsersFilter = JSON.parse(
    localStorage.getItem("filters") || "{}"
  );
  const [name, setName] = useState(args.name || "");
  const [kycStatus, setKycStatus] = useState<string>(args.kycStatus || "-1");
  const [userStep, setUserStep] = useState<string>(args.userStep || "-1");
  const [contractMap, setContractMap] = useState<string>(
    args.contractMap || "-1"
  );
  const [validation, setValidation] = useState<string>(args.validation || "-1");
  const [negativeListType, setNegativeListType] = useState<string>(
    args.negativeListType || "-1"
  );
  const [contractSigned, setContractSigned] = useState<string>(
    args.contractSigned || "-1"
  );
  const [nationality, setNationality] = useState<string>(
    args.nationality || "-1"
  );
  const [investmentAmount, setInvestmentAmount] = useState<string>(
    args.investmentAmount || "-1"
  );
  const [annualIncome, setAnnualIncome] = useState<string>(
    args.annualIncome || "-1"
  );
  const [fatca, setFatca] = useState<string>(args.fatca || "-1");
  const [isVerified, setIsVerified] = useState<string>(args.isVerified || "-1");
  const [isSynchronized, setIsSynchronized] = useState<string>(
    args.isSynchronized || "-1"
  );
  const [sentToFra, setSentToFra] = useState<string>(args.sentToFra || "-1");
  const [userId, setUserId] = useState<string>(args.userId || "");
  const [email, setEmail] = useState<string>(args.email || "");
  const [startDate, setStartDate] = useState<Dayjs | null>(
    args.startDate ? dayjs(args.startDate) : null
  );
  const [endDate, setEndDate] = useState<Dayjs | null>(
    args.endDate ? dayjs(args.endDate) : null
  );
  const [phone, setPhone] = useState<string>(args.phone || "");
  const [referralCode, setReferralCode] = useState<string>(
    args.referralCode || ""
  );
  const [countryId, setCountryId] = useState<number | undefined>(
    args.countryId
  );
  const [cityId, setCityId] = useState<number | undefined>(args.cityId);
  const [id, setId] = useState<number | undefined>(args.id || undefined);
  const [order, setOrder] = useState<Order>(args.order || "desc");
  const [orderBy, setOrderBy] = useState<string>(args.orderBy || "id");
  const [page, setPage] = useState(args.page ? args.page - 1 : 0);
  const [rowsPerPage, setRowsPerPage] = useState(args.rowsPerPage || 10);
  const [minTopup, setMinTopup] = useState<number | undefined>(
    args.minTopup || undefined
  );
  const [maxTopup, setMaxTopup] = useState<number | undefined>(
    args.maxTopup || undefined
  );
  const [minWithdrawal, setMinWithdrawal] = useState<number | undefined>(
    args.minWithdrawal || undefined
  );
  const [maxWithdrawal, setMaxWithdrawal] = useState<number | undefined>(
    args.maxWithdrawal || undefined
  );
  const [minBuy, setMinBuy] = useState<number | undefined>(
    args.minBuy || undefined
  );
  const [maxBuy, setMaxBuy] = useState<number | undefined>(
    args.maxBuy || undefined
  );
  const [minSell, setMinSell] = useState<number | undefined>(
    args.minSell || undefined
  );
  const [maxSell, setMaxSell] = useState<number | undefined>(
    args.maxSell || undefined
  );
  const [fundId, setFundId] = useState<number | undefined>(
    args.fundId || undefined
  );
  const [minBalance, setMinBalance] = useState<number | undefined>(
    args.minBalance || undefined
  );
  const [maxBalance, setMaxBalance] = useState<number | undefined>(
    args.maxBalance || undefined
  );
  const [adminUserId, setAdminUserId] = useState<number | undefined>(
    args.adminUserId || undefined
  );
  const { t } = useTranslation("common");

  const [bulkActionLoading, setBulkActionLoading] = useState(false);
  const [toSignUsers, setToSignUsers] = useState<string[]>([]);
  const [openBulkActions, setOpenBulkActions] = useState(false);
  const [selectedBulkAction, setSelectedBulkAction] = useState<string>("");
  const [notificationTitle, setNotificationTitle] = useState("");
  const [popupTitle, setPopupTitle] = useState("");
  const [selectedNotification, setSelectedNotification] =
    useState<NotificationTemplate | null>(null);
  const [selectedPopup, setSelectedPopup] = useState<PopupTemplate | null>(
    null
  );
  const [openNotification, setOpenNotification] = useState(false);
  const [filterDrawerOpen, setFilterDrawerOpen] = useState(false);
  const [openAdminMapping, setOpenAdminMapping] = useState(false);
  const [selectedAdminId, setSelectedAdminId] = useState<number | null>(null);
  const [adminMappingLoading, setAdminMappingLoading] = useState(false);
  const [openUnassignConfirm, setOpenUnassignConfirm] = useState(false);
  const [sendToFra] = azimutApi.useSendToFraMutation();
  const [sendCompanyToFra] = azimutApi.useSendCompanyContractToFraMutation();
  const [checkCSO] = azimutApi.useCheckCsoMutation();
  const [checkNTRA] = azimutApi.useCheckNtraMutation();
  const [checkNegativeList] = azimutApi.useCheckAmlMutation();
  const [sendNotification] = azimutApi.useSendNotificationMutation();
  const [sendPopup] = azimutApi.useSendPopupMutation();
  const { data: adminUsers } = azimutAdminApi.useGetUsersQuery();
  const [createUserMapping] = azimutAdminApi.useCreateUserMappingMutation();
  const [bulkDeleteMapping] = azimutAdminApi.useBulkDeleteUserMappingMutation();

  const dispatch = useDispatch();

  const { data: referralCodes } = azimutApi.useGetReferralCodesQuery();
  const { data: funds } = azimutApi.useGetFundsQuery();

  const { data: azimutUsers, isFetching } = azimutApi.useGetUsersQuery({
    name,
    kycStatus,
    userStep,
    email,
    phone,
    id,
    userId,
    contractMap,
    contractSigned,
    nationality,
    countryId,
    cityId,
    validation,
    negativeListType,
    referralCode,
    startDate: startDate ? startDate.toDate().toISOString() : undefined,
    endDate: endDate ? endDate.toDate().toISOString() : undefined,
    isVerified,
    isSynchronized,
    sentToFra,
    isDigital,
    order,
    orderBy,
    page: page + 1,
    rowsPerPage,
    investmentAmount,
    minTopup,
    maxTopup,
    minWithdrawal,
    maxWithdrawal,
    minBuy,
    maxBuy,
    minSell,
    maxSell,
    fundId,
    minBalance,
    maxBalance,
    annualIncome,
    fatca,
    adminUserId,
  });
  const navigate = useNavigate();
  const { user } = useAuth()!;
  const [getUsers, { isFetching: downloadLoading }] =
    azimutApi.useLazyGetUsersQuery();

  const { data: notificationList } = azimutApi.useGetNotificationsQuery({
    title: notificationTitle,
    page: 0,
    rowsPerPage: 10,
  });

  const { data: popupList } = azimutApi.useGetPopupsQuery({
    title: popupTitle,
    page: 0,
    rowsPerPage: 10,
  });

  // Lookups
  const { data: countries } = azimutApi.useGetLookupDataQuery({
    entityTypeId: 1,
  });

  // Cities depend on country
  const { data: cities, isFetching: loadingCities } =
    azimutApi.useGetLookupDataQuery(
      countryId ? { entityTypeId: 2, countryId: Number(countryId) } : skipToken
    );

  const { data: allCities } = azimutApi.useGetLookupDataQuery({
    entityTypeId: 2,
    countryId: -1,
  });

  const getAllUsers = async (page = 1) => {
    const data = await getUsers({
      name,
      kycStatus,
      userStep,
      email,
      phone,
      id,
      userId,
      contractMap,
      validation,
      negativeListType,
      referralCode,
      isDigital,
      countryId,
      cityId,
      contractSigned,
      nationality,
      startDate: startDate ? startDate.toDate().toISOString() : undefined,
      endDate: endDate ? endDate.toDate().toISOString() : undefined,
      isVerified,
      isSynchronized,
      sentToFra,
      order,
      orderBy,
      page,
      rowsPerPage: 100,
      investmentAmount,
      minTopup,
      maxTopup,
      minWithdrawal,
      maxWithdrawal,
      minBuy,
      maxBuy,
      minSell,
      maxSell,
      fundId,
      minBalance,
      maxBalance,
      annualIncome,
      fatca,
      adminUserId,
    }).unwrap();
    return data;
  };

  const handleCreateAdminMapping = async () => {
    if (!selectedAdminId) {
      toast.error(t("Please select a financial advisor"));
      return;
    }

    setAdminMappingLoading(true);
    try {
      // Get all users from the current filtered list
      let allUsers: AzimutUser[] = [];
      for (let i = 1; i <= Math.ceil(azimutUsers!.numberOfItems / 100); i++) {
        const newData = await getAllUsers(i);
        allUsers = [...allUsers, ...newData.dataList];
      }

      const userIds = allUsers.map(u => u.userId);

      await createUserMapping({
        adminUserId: selectedAdminId,
        userIds,
      }).unwrap();

      toast.success(`Successfully assigned ${userIds.length} clients`);
      setOpenAdminMapping(false);
      setSelectedAdminId(null);
      dispatch(azimutApi.util.invalidateTags(["ClientsList"]));
    } catch (error: any) {
      toast.error(error?.data?.message || t("Failed to assign clients"));
    } finally {
      setAdminMappingLoading(false);
    }
  };

  const handleUnassignAdminMapping = async () => {
    if (!selectedAdminId) {
      toast.error(t("Please select a financial advisor"));
      return;
    }

    setAdminMappingLoading(true);
    try {
      // Get all users from the current filtered list
      let allUsers: AzimutUser[] = [];
      for (let i = 1; i <= Math.ceil(azimutUsers!.numberOfItems / 100); i++) {
        const newData = await getAllUsers(i);
        allUsers = [...allUsers, ...newData.dataList];
      }

      const userIds = allUsers.map(u => u.userId);

      await bulkDeleteMapping({
        adminUserId: selectedAdminId,
        userIds,
      }).unwrap();

      toast.success(`Successfully unassigned ${userIds.length} clients`);
      setOpenUnassignConfirm(false);
      setOpenAdminMapping(false);
      setSelectedAdminId(null);
      dispatch(azimutApi.util.invalidateTags(["ClientsList"]));
    } catch (error: any) {
      toast.error(error?.data?.message || t("Failed to unassign clients"));
    } finally {
      setAdminMappingLoading(false);
    }
  };

  const handleDownload = async () => {
    let data: AzimutUser[] = [];
    for (let i = 1; i <= Math.ceil(azimutUsers!.numberOfItems / 100); i++) {
      const newData = await getAllUsers(i);
      data = [...data, ...newData.dataList];
    }
    const newData = data
      .filter(e => e)
      .map(e => ({
        ...e,
        userPhone: e.userPhone,
        "Nid/Passport": e.userId,
        Signature: e.contractMap == 1 ? "Digital" : "",
        kycStatus: kycStatuses[e.kycStatus].label,
        userStep: userStepTypes[e.userStep]?.label,
        createdAt: e.createdAt.substring(0, 10),
        updatedAt: e.updatedAt.substring(0, 10),
        Country: countries.countries.find(
          (c: any) => c.systemCountryCode === e.azimutAccount.countryId
        )?.englishCountryName,
        City: allCities.cities.find(
          (c: any) => c.systemCityCode === e.azimutAccount.cityId
        )?.englishCityName,
        ActiveApp: e.isVerified,
        ActiveFits: e.isSynchronized,
        sentToFra: e.fraStoreId != null ? "Yes" : "No",
        "Contract signed": e.signedPdf && (e.signedPdf != "offline" || e.isOld),
      }));
    jsonToCsvExport({
      data: newData,
      headers: [
        "id",
        "createdAt",
        "updatedAt",
        "nickName",
        "emailAddress",
        "userPhone",
        "Nid/Passport",
        "referralCode",
        "Country",
        "City",
        "Signature",
        "kycStatus",
        "userStep",
        "Contract signed",
      ],
    });
  };

  const handleSearchId = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setId(Number(e.target.value));
      setPage(0);
    },
    1000
  );

  const handleMaxTopup = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setMaxTopup(Number(e.target.value));
      setPage(0);
    },
    1000
  );

  const handleMinTopup = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setMinTopup(Number(e.target.value));
      setPage(0);
    },
    1000
  );

  const handleMaxWithdrawal = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setMaxWithdrawal(Number(e.target.value));
      setPage(0);
    },
    1000
  );

  const handleMinWithdrawal = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setMinWithdrawal(Number(e.target.value));
      setPage(0);
    },
    1000
  );

  const handleMaxBuy = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setMaxBuy(Number(e.target.value));
      setPage(0);
    },
    1000
  );

  const handleMinBuy = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setMinBuy(Number(e.target.value));
      setPage(0);
    },
    1000
  );

  const handleMaxSell = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setMaxSell(Number(e.target.value));
      setPage(0);
    },
    1000
  );

  const handleMinSell = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setMinSell(Number(e.target.value));
      setPage(0);
    },
    1000
  );

  const handleMaxBalance = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setMaxBalance(Number(e.target.value));
      setPage(0);
    },
    1000
  );

  const handleMinBalance = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setMinBalance(Number(e.target.value));
      setPage(0);
    },
    1000
  );

  const handleSearchName = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setName(e.target.value);
      setPage(0);
    },
    1000
  );

  const handleSearchEmail = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setEmail(e.target.value);
      setPage(0);
    },
    1000
  );

  const handleSearchUserId = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setUserId(e.target.value);
      setPage(0);
    },
    1000
  );

  const handleNotificationSearch = useDebouncedCallback((e: string) => {
    setNotificationTitle(e);
  }, 1000);

  const handlePopupSearch = useDebouncedCallback((e: string) => {
    setPopupTitle(e);
  }, 1000);

  const handleSearchPhone = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setPhone(e.target.value);
      setPage(0);
    },
    1000
  );

  const handleRequestSort = (
    event: React.MouseEvent<unknown>,
    property: any
  ) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
    setPage(0);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleClick = (event: React.MouseEvent<unknown>, id: number) => {
    navigate(`/client/${id}`);
  };

  const rows = azimutUsers?.dataList.filter(e => e) || [];

  // Avoid a layout jump when reaching the last page with empty rows.
  const emptyRows = isFetching
    ? rowsPerPage
    : Math.max(0, rowsPerPage - rows.length);

  const sendToFraUsers = async (users: string[]) => {
    setBulkActionLoading(true);
    for (const userId of users) {
      try {
        const res = await sendToFra({ userId }).unwrap();
        if (res.status == 0)
          toast.success(`User ${userId} Send To FRA Successfully`);
        else toast.error(`User ${userId} Error: ${res.message}`);
      } catch (error: any) {
        toast.error(`User ${userId} Error: ${error?.data?.message}`);
      }
    }
    dispatch(azimutApi.util.invalidateTags(["ClientsList"]));
    setBulkActionLoading(false);
  };

  const sendCompanyToFraUsers = async (users: string[]) => {
    setBulkActionLoading(true);
    for (const userId of users) {
      try {
        const res = await sendCompanyToFra({ userId }).unwrap();
        if (res.status == 0)
          toast.success(`User ${userId} Send To FRA Successfully`);
        else toast.error(`User ${userId} Error: ${res.message}`);
      } catch (error: any) {
        toast.error(`User ${userId} Error: ${error?.data?.message}`);
      }
    }
    dispatch(azimutApi.util.invalidateTags(["ClientsList"]));
    setBulkActionLoading(false);
  };

  const checkCsoUsers = async (users: string[]) => {
    setBulkActionLoading(true);
    for (const userId of users) {
      try {
        const res = await checkCSO({ userId }).unwrap();
        if (res.status == 0)
          toast.success(`User ${userId} Checked CSO Successfully`);
        else toast.error(`User ${userId} Error: ${res.message}`);
      } catch (error: any) {
        toast.error(`User ${userId} Error: ${error?.data?.message}`);
      }
    }
    dispatch(azimutApi.util.invalidateTags(["ClientsList"]));
    setBulkActionLoading(false);
  };

  const checkNtraUsers = async (users: string[]) => {
    setBulkActionLoading(true);
    for (const userId of users) {
      try {
        const res = await checkNTRA({ userId }).unwrap();
        if (res.status == 0)
          toast.success(`User ${userId} Checked NTRA Successfully`);
        else toast.error(`User ${userId} Error: ${res.message}`);
      } catch (error: any) {
        toast.error(`User ${userId} Error: ${error?.data?.message}`);
      }
    }
    dispatch(azimutApi.util.invalidateTags(["ClientsList"]));
    setBulkActionLoading(false);
  };

  const checkNegativeListUsers = async (users: string[]) => {
    setBulkActionLoading(true);
    for (const userId of users) {
      try {
        const res = await checkNegativeList({ userId }).unwrap();
        if (res.status == 0)
          toast.success(`User ${userId} Checked Negative List Successfully`);
        else toast.error(`User ${userId} Error: ${res.message}`);
      } catch (error: any) {
        toast.error(`User ${userId} Error: ${error?.data?.message}`);
      }
    }
    dispatch(azimutApi.util.invalidateTags(["ClientsList"]));
    setBulkActionLoading(false);
  };

  const sendNotificationToUsers = async () => {
    let data: AzimutUser[] = [];
    for (let i = 1; i <= Math.ceil(azimutUsers!.numberOfItems / 100); i++) {
      const newData = await getAllUsers(i);
      data = [...data, ...newData.dataList];
    }
    const userIds = data?.filter(e => e).map(e => e.id);

    // Send notification if selected
    if (selectedNotification) {
      await sendNotification({
        id: selectedNotification.id!,
        users: userIds,
      });
      toast.success(t("Notification sent successfully"));
    }

    // Send popup if selected
    if (selectedPopup) {
      await sendPopup({
        id: selectedPopup.id!,
        users: userIds,
      });
      toast.success(t("Popup sent successfully"));
    }

    // Clear selections
    setSelectedNotification(null);
    setSelectedPopup(null);
    setOpenNotification(false);
  };

  return (
    <div style={{ maxWidth: "100vw" }}>
      <div className="container">
        <div className="flex items-baseline justify-between">
          <h1>{isDigital ? t("Digital Clients List") : t("Clients List")}</h1>
          <div className="flex gap-2">
            <Button
              className="mx-2"
              variant="outlined"
              onClick={() => setFilterDrawerOpen(true)}
            >
              <FilterListIcon sx={{ mr: 1 }} />
              {t("Advanced Filters")}
            </Button>
            {user.user.permissions?.includes("signContract") &&
              (kycStatus == "1" ||
                kycStatus == "-1" ||
                kycStatus == "0.1" ||
                kycStatus == "2.1" ||
                kycStatus == "0") && (
                <Button
                  type="button"
                  className="mx-2"
                  onClick={() => setOpenBulkActions(true)}
                >
                  {t("Bulk Actions")}
                </Button>
              )}
            {user.user.permissions?.includes("notifications") && (
              <Button
                className="mx-2"
                onClick={() => setOpenNotification(true)}
              >
                {t("Send Notification")}
              </Button>
            )}
            {user.user.permissions?.includes("download") && (
              <Button
                loading={downloadLoading}
                className="mx-2"
                onClick={handleDownload}
              >
                {t("download")}
              </Button>
            )}
            {user.user.permissions?.includes("manageFA") && (
              <Button
                className="mx-2"
                onClick={() => {
                  if (adminUserId) setSelectedAdminId(adminUserId);
                  setOpenAdminMapping(true);
                }}
              >
                {t("Manage FA")}
              </Button>
            )}
          </div>
        </div>
        <TextField
          className="mb-3"
          label={t("ID")}
          sx={{ width: 120 }}
          type="number"
          size="small"
          defaultValue={id}
          onChange={handleSearchId}
        />
        <TextField
          className="mb-3"
          label={t("Client Name")}
          size="small"
          defaultValue={name}
          onChange={handleSearchName}
        />
        <TextField
          className="mb-3"
          label={t("Email")}
          size="small"
          defaultValue={email}
          onChange={handleSearchEmail}
        />
        <TextField
          className="mb-3"
          label={t("Phone Number")}
          size="small"
          defaultValue={phone}
          onChange={handleSearchPhone}
        />
        <TextField
          className="mb-3"
          label={isDigital ? t("National ID") : t("NID/Passport")}
          size="small"
          defaultValue={userId}
          onChange={handleSearchUserId}
        />
        <Autocomplete
          disablePortal
          size="small"
          options={referralCodes || []}
          onChange={(event: any, newValue: string | null) => {
            setReferralCode(newValue || "");
          }}
          sx={{ width: 215, display: "inline-flex" }}
          renderInput={params => (
            <TextField
              {...params}
              label={t("Referral Code")}
            />
          )}
        />
        {user.user.permissions?.includes("manageFA") && (
          <Autocomplete
            disablePortal
            size="small"
            options={adminUsers || []}
            getOptionLabel={option =>
              `${option.fullName} (${option.emailAddress})`
            }
            value={adminUsers?.find(u => u.id === adminUserId) || null}
            onChange={(_event: any, newValue: any) => {
              setAdminUserId(newValue?.id || undefined);
              setPage(0);
            }}
            sx={{ width: 215, display: "inline-flex" }}
            renderInput={params => (
              <TextField
                {...params}
                label={t("Portal User/FA")}
              />
            )}
          />
        )}
        <TextField
          className="mb-3"
          style={{ minWidth: 100 }}
          select
          label={t("User Step")}
          size="small"
          value={userStep}
          // helperText="Select to filter"
          onChange={e => {
            setUserStep(e.target.value);
            if (parseInt(e.target.value) < 11) {
              setContractMap("-1");
              setIsVerified("-1");
              setSentToFra("-1");
              setContractSigned("-1");
            }
            setPage(0);
          }}
        >
          {["1", "3", "7", "8", "9", "10", "11", "12", "13", "-1"].map(
            option => (
              <MenuItem
                key={option}
                value={option}
              >
                {t(userStepTypes[option].label)}
              </MenuItem>
            )
          )}
        </TextField>
        <TextField
          className="mb-3"
          select
          label={t("KYC Status")}
          size="small"
          value={kycStatus}
          // helperText="Select to filter"
          onChange={e => {
            setKycStatus(e.target.value);
            if (e.target.value == "5") {
              setContractMap("-1");
            }
            if (
              !(
                e.target.value == "1" ||
                e.target.value == "-1" ||
                e.target.value == "0.1"
              )
            ) {
              setIsVerified("-1");
              setSentToFra("-1");
              setContractSigned("-1");
            }
            setPage(0);
          }}
        >
          {["0", "0.1", "1", "2", "4", "5", "2.1", "-1"].map(option => (
            <MenuItem
              key={option}
              value={option}
            >
              {t(kycStatuses[option].label)}
            </MenuItem>
          ))}
        </TextField>
        {kycStatus != "5" && (
          <TextField
            className="mb-3"
            style={{ minWidth: 100 }}
            select
            label={t("Validation")}
            size="small"
            value={validation}
            onChange={e => {
              setValidation(e.target.value);
              setPage(0);
            }}
          >
            {Object.entries(validations).map(option => (
              <MenuItem
                key={option[0]}
                value={option[0]}
              >
                {t(option[1].label)}
              </MenuItem>
            ))}
          </TextField>
        )}
        {kycStatus != "5" && (
          <TextField
            className="mb-3"
            style={{ minWidth: 100 }}
            select
            label={t("NegativeList")}
            size="small"
            value={negativeListType}
            onChange={e => {
              setNegativeListType(e.target.value);
              setPage(0);
            }}
          >
            {Object.entries(negativeListTypes).map(option => (
              <MenuItem
                key={option[0]}
                value={option[0]}
              >
                {t(option[1].label)}
              </MenuItem>
            ))}
          </TextField>
        )}
        {!isDigital && kycStatus != "5" && (
          <TextField
            className="mb-3"
            style={{ minWidth: 100 }}
            select
            label={t("Nationality")}
            size="small"
            value={nationality}
            onChange={e => {
              setNationality(e.target.value);
              setPage(0);
            }}
          >
            {Object.entries(nationalities).map(option => (
              <MenuItem
                key={option[0]}
                value={option[0]}
              >
                {t(option[1].label)}
              </MenuItem>
            ))}
          </TextField>
        )}
        {!isDigital && kycStatus != "5" && (
          <TextField
            className="mb-3"
            select
            label={t("Signature")}
            size="small"
            value={contractMap}
            onChange={e => {
              setContractMap(e.target.value);
              setPage(0);
            }}
          >
            {Object.entries(signatures).map(option => (
              <MenuItem
                key={option[0]}
                value={option[0]}
              >
                {t(option[1].label)}
              </MenuItem>
            ))}
          </TextField>
        )}
        {(kycStatus == "1" || kycStatus == "-1" || kycStatus == "0.1") && (
          <TextField
            className="mb-3"
            select
            label={t("Active (APP)")}
            size="small"
            value={isVerified}
            onChange={e => {
              setIsVerified(e.target.value);
              setPage(0);
            }}
          >
            {Object.entries(isVerifiedStates).map(option => (
              <MenuItem
                key={option[0]}
                value={option[0]}
              >
                {t(option[1].label)}
              </MenuItem>
            ))}
          </TextField>
        )}
        {(kycStatus == "1" || kycStatus == "-1" || kycStatus == "0.1") && (
          <TextField
            className="mb-3"
            select
            label={t("Sent to FITS")}
            size="small"
            value={isSynchronized}
            onChange={e => {
              setIsSynchronized(e.target.value);
              setPage(0);
            }}
          >
            {Object.entries(sentToFraStates).map(option => (
              <MenuItem
                key={option[0]}
                value={option[0]}
              >
                {t(option[1].label)}
              </MenuItem>
            ))}
          </TextField>
        )}
        {(kycStatus == "1" ||
          kycStatus == "-1" ||
          kycStatus == "0.1" ||
          kycStatus == "2.1") && (
          <TextField
            className="mb-3"
            style={{ minWidth: 100 }}
            select
            label={t("Sent to FRA")}
            size="small"
            value={sentToFra}
            onChange={e => {
              setSentToFra(e.target.value);
              setPage(0);
            }}
          >
            {Object.entries(sentToFraStates).map(option => (
              <MenuItem
                key={option[0]}
                value={option[0]}
              >
                {t(option[1].label)}
              </MenuItem>
            ))}
          </TextField>
        )}
        {(kycStatus == "1" || kycStatus == "-1" || kycStatus == "0.1") && (
          <TextField
            className="mb-3"
            select
            label={t("Contract Signed")}
            size="small"
            value={contractSigned}
            onChange={e => {
              setContractSigned(e.target.value);
              setPage(0);
            }}
          >
            {(isDigital
              ? Object.entries(digitalContractSignedStates)
              : Object.entries(contractSignedStates)
            ).map(option => (
              <MenuItem
                key={option[0]}
                value={option[0]}
              >
                {t(option[1].label)}
              </MenuItem>
            ))}
          </TextField>
        )}
        <br />
        <Box sx={{ width: "100%", marginTop: "10px" }}>
          <Paper sx={{ width: "100%", mb: 2 }}>
            <TableContainer>
              <Table
                sx={{ minWidth: 750 }}
                aria-labelledby="ClientsList"
                size="small"
              >
                <EnhancedTableHead
                  order={order}
                  orderBy={orderBy}
                  onRequestSort={handleRequestSort}
                  headCells={columns}
                />
                <TableBody>
                  {isFetching ? (
                    <TableRow>
                      <TableCell
                        colSpan={7}
                        style={{ border: "none" }}
                      >
                        <div
                          style={{
                            position: "absolute",
                            marginLeft: "40vw",
                            marginTop: 100,
                          }}
                        >
                          <Spinner />
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    rows.map(row => {
                      return (
                        <TableRow
                          hover
                          onClick={event => handleClick(event, row.id)}
                          key={row.id}
                          sx={{ cursor: "pointer" }}
                        >
                          <TableCell>{row.id}</TableCell>
                          <TableCell>{row.nickName}</TableCell>
                          <TableCell>{row.emailAddress}</TableCell>
                          <TableCell>{row.userPhone}</TableCell>
                          <TableCell>{row.userId}</TableCell>
                          <TableCell>
                            <div
                              className={
                                kycStatuses[getStatusDisplay(row)].class
                              }
                            >
                              {t(kycStatuses[getStatusDisplay(row)].label)}
                            </div>
                          </TableCell>
                          <TableCell>
                            {row.isOld ? (
                              <Chip label={t("Physical")} />
                            ) : row.contractMap == 1 ? (
                              <Chip label={t("Digital")} />
                            ) : (
                              "-"
                            )}
                          </TableCell>
                          <TableCell>
                            {row.isVerified ? (
                              <CheckBoxIcon color="primary" />
                            ) : (
                              ""
                            )}
                          </TableCell>
                          <TableCell>
                            {row.isSynchronized ? (
                              <CheckBoxIcon color="primary" />
                            ) : (
                              ""
                            )}
                          </TableCell>
                          <TableCell>
                            {row.signedPdf &&
                            (row.signedPdf != "offline" || row.isOld) ? (
                              <CheckBoxIcon color="primary" />
                            ) : (
                              ""
                            )}
                          </TableCell>
                          <TableCell>
                            <div className={userStepTypes[row.userStep]?.class}>
                              {t(userStepTypes[row.userStep]?.label)}
                            </div>
                          </TableCell>
                          <TableCell>
                            {row.createdAt.substring(0, 10)}
                          </TableCell>
                          <TableCell>
                            {row.updatedAt.substring(0, 10)}
                          </TableCell>
                          <TableCell>{row.referralCode}</TableCell>
                          <TableCell>
                            {
                              countries?.countries.find(
                                (c: any) =>
                                  c.systemCountryCode ===
                                  row.azimutAccount.countryId
                              )?.englishCountryName
                            }
                          </TableCell>
                          <TableCell>
                            {
                              allCities?.cities.find(
                                (c: any) =>
                                  c.systemCityCode === row.azimutAccount.cityId
                              )?.englishCityName
                            }
                          </TableCell>
                        </TableRow>
                      );
                    })
                  )}
                  {emptyRows > 0 && (
                    <TableRow
                      style={{
                        height: 45 * emptyRows,
                      }}
                    >
                      <TableCell colSpan={7} />
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
            <TablePagination
              rowsPerPageOptions={[5, 10, 25]}
              component="div"
              count={azimutUsers?.numberOfItems || 0}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
            />
          </Paper>
        </Box>
      </div>
      <ChooserDialog
        toSignUsers={toSignUsers}
        setLoading={setBulkActionLoading}
      />
      <Dialog
        open={openBulkActions}
        onClose={() => {
          setOpenBulkActions(false);
          setSelectedBulkAction("");
        }}
        title={t("Bulk Actions")}
        actions={
          <DialogActions>
            {selectedBulkAction ? (
              <Fragment>
                <Button
                  className="popup-btn"
                  loading={bulkActionLoading}
                  onClick={async () => {
                    if (selectedBulkAction === "sign") {
                      await setToSignUsers(
                        rows
                          .filter(
                            e =>
                              !e.signedPdf &&
                              e.fraStoreId &&
                              e.kycStatus == 1 &&
                              isFraValid(e) &&
                              !e.isOld
                          )
                          .map(e => e.id.toString())
                      );
                    } else if (selectedBulkAction === "sendToFra") {
                      await sendToFraUsers(
                        rows
                          .filter(
                            e =>
                              !e.fraStoreId &&
                              e.isVerified &&
                              !e.signedPdf &&
                              e.kycStatus == 1 &&
                              !e.isOld &&
                              isFraValid(e)
                          )
                          .map(e => e.id.toString())
                      );
                    } else if (selectedBulkAction === "sendCompanyToFra") {
                      await sendCompanyToFraUsers(
                        rows
                          .filter(
                            e =>
                              e.fraStoreId &&
                              e.isVerified &&
                              e.signedPdf &&
                              e.kycStatus == 1 &&
                              !e.isOld &&
                              !e.fraCompanyStoreId &&
                              isFraValid(e)
                          )
                          .map(e => e.id.toString())
                      );
                    } else if (selectedBulkAction === "checkCSO") {
                      await checkCsoUsers(rows.map(e => e.id.toString()));
                    } else if (selectedBulkAction === "checkNTRA") {
                      await checkNtraUsers(rows.map(e => e.id.toString()));
                    } else if (selectedBulkAction === "checkNegativeList") {
                      await checkNegativeListUsers(
                        rows.map(e => e.id.toString())
                      );
                    }
                    setBulkActionLoading(false);
                    setOpenBulkActions(false);
                    setSelectedBulkAction("");
                  }}
                >
                  {t("Confirm")}
                </Button>
                <Button
                  variant="text"
                  onClick={() => {
                    setOpenBulkActions(false);
                    setSelectedBulkAction("");
                  }}
                >
                  {t("Cancel")}
                </Button>
              </Fragment>
            ) : (
              <Button
                variant="text"
                onClick={() => {
                  setOpenBulkActions(false);
                  setSelectedBulkAction("");
                }}
              >
                {t("Cancel")}
              </Button>
            )}
          </DialogActions>
        }
      >
        <div className="flex gap-4">
          <div className="w-1/3 flex flex-col gap-4">
            {kycStatus != "0" && (
              <Button
                variant={
                  selectedBulkAction === "sign" ? "contained" : "outlined"
                }
                onClick={() => {
                  setSelectedBulkAction("sign");
                }}
              >
                {t("Bulk Sign Contracts")}
              </Button>
            )}
            {kycStatus != "0" && (
              <Button
                variant={
                  selectedBulkAction === "sendToFra" ? "contained" : "outlined"
                }
                onClick={() => {
                  setSelectedBulkAction("sendToFra");
                }}
              >
                {t("Bulk Submit to FRA")}
              </Button>
            )}
            {kycStatus != "0" && (
              <Button
                variant={
                  selectedBulkAction === "sendCompanyToFra"
                    ? "contained"
                    : "outlined"
                }
                onClick={() => {
                  setSelectedBulkAction("sendCompanyToFra");
                }}
              >
                {t("Bulk Submit Signed Contracts to FRA")}
              </Button>
            )}
            <Button
              variant={
                selectedBulkAction === "checkCSO" ? "contained" : "outlined"
              }
              onClick={() => {
                setSelectedBulkAction("checkCSO");
              }}
            >
              {t("Bulk Check CSO")}
            </Button>
            <Button
              variant={
                selectedBulkAction === "checkNTRA" ? "contained" : "outlined"
              }
              onClick={() => {
                setSelectedBulkAction("checkNTRA");
              }}
            >
              {t("Bulk Check NTRA")}
            </Button>
            <Button
              variant={
                selectedBulkAction === "checkNegativeList"
                  ? "contained"
                  : "outlined"
              }
              onClick={() => {
                setSelectedBulkAction("checkNegativeList");
              }}
            >
              {t("Bulk Check Negative List")}
            </Button>
          </div>
          {selectedBulkAction && (
            <div className="w-2/3 pl-4 border-l">
              {selectedBulkAction === "sign" && (
                <div>
                  <div>
                    {t(
                      "Are you sure you want to bulk sign all selected users contracts?"
                    )}
                  </div>
                  <div>
                    {t("This will sign all accepted users in the list below")}
                  </div>
                </div>
              )}
              {selectedBulkAction === "sendToFra" && (
                <div>
                  <div>
                    {t(
                      "Are you sure you want to bulk send all selected users contracts to the FRA?"
                    )}
                  </div>
                  <div>
                    {t(
                      "This will send all accepted users in the list below to the FRA"
                    )}
                  </div>
                </div>
              )}
              {selectedBulkAction === "sendCompanyToFra" && (
                <div>
                  <div>
                    {t(
                      "Are you sure you want to bulk send all selected Signed users contracts to the FRA?"
                    )}
                  </div>
                  <div>
                    {t(
                      "This will send all signed users contracts in the list below to the FRA"
                    )}
                  </div>
                </div>
              )}
              {selectedBulkAction === "checkCSO" && (
                <div>
                  <div>
                    {t(
                      "Are you sure you want to bulk check CSO for all selected users?"
                    )}
                  </div>
                  <div>{t("This will check CSO for all selected users")}</div>
                </div>
              )}
              {selectedBulkAction === "checkNTRA" && (
                <div>
                  <div>
                    {t(
                      "Are you sure you want to bulk check NTRA for all selected users?"
                    )}
                  </div>
                  <div>{t("This will check NTRA for all selected users")}</div>
                </div>
              )}
              {selectedBulkAction === "checkNegativeList" && (
                <div>
                  <div>
                    {t(
                      "Are you sure you want to bulk check Negative List for all selected users?"
                    )}
                  </div>
                  <div>
                    {t("This will check Negative List for all selected users")}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </Dialog>

      <Dialog
        open={openNotification}
        onClose={() => {
          setOpenNotification(false);
          setSelectedNotification(null);
          setSelectedPopup(null);
        }}
        title={t("Select Notification/Popup")}
        actions={
          <DialogActions>
            <Button
              onClick={() => {
                setOpenNotification(false);
                setSelectedNotification(null);
                setSelectedPopup(null);
              }}
            >
              Cancel
            </Button>
            <Button
              loading={downloadLoading}
              disabled={!selectedNotification && !selectedPopup}
              onClick={sendNotificationToUsers}
            >
              Submit
            </Button>
          </DialogActions>
        }
      >
        <div className="mb-4">
          <div className="font-bold mb-2">{t("Notifications")}</div>
          <Autocomplete
            options={notificationList?.dataList.filter(e => e) || []}
            onInputChange={(e, value) => handleNotificationSearch(value)}
            getOptionLabel={option => option.title + ": " + option.body}
            value={selectedNotification}
            onChange={(e, value) => setSelectedNotification(value)}
            renderInput={params => (
              <TextField
                {...params}
                label="Notifications"
              />
            )}
          />
        </div>
        <div className="mb-4">
          <div className="font-bold mb-2">{t("Popups")}</div>
          <Autocomplete
            options={popupList?.dataList.filter(e => e) || []}
            onInputChange={(e, value) => handlePopupSearch(value)}
            getOptionLabel={option => option.title + ": " + option.body}
            value={selectedPopup}
            onChange={(e, value) => setSelectedPopup(value)}
            renderInput={params => (
              <TextField
                {...params}
                label="Popups"
              />
            )}
          />
        </div>
        <div className="mb-2 mt-4">
          Note: This will send the selected notification and/or popup to all the
          filtered users here. You can select both a notification and a popup to
          send them together.
        </div>
      </Dialog>

      <Dialog
        open={openAdminMapping}
        onClose={() => {
          setOpenAdminMapping(false);
          setSelectedAdminId(null);
        }}
        title={t("Manage Financial Advisor Assignment")}
        actions={
          <DialogActions>
            <Button
              onClick={() => {
                setOpenAdminMapping(false);
                setSelectedAdminId(null);
              }}
            >
              {t("Cancel")}
            </Button>
            <Button
              variant="outlined"
              color="error"
              disabled={!selectedAdminId}
              onClick={() => setOpenUnassignConfirm(true)}
            >
              {t("Unassign All")}
            </Button>
            <Button
              loading={adminMappingLoading}
              disabled={!selectedAdminId}
              onClick={handleCreateAdminMapping}
            >
              {t("Assign All")}
            </Button>
          </DialogActions>
        }
      >
        <div className="mb-4">
          <div className="mb-2">
            {`Select a financial advisor to manage assignments for ${
              azimutUsers?.numberOfItems || 0
            } filtered clients`}
          </div>
          <Autocomplete
            options={adminUsers || []}
            getOptionLabel={option =>
              `${option.fullName} (${option.emailAddress})`
            }
            value={adminUsers?.find(u => u.id === selectedAdminId) || null}
            onChange={(_, value) => setSelectedAdminId(value?.id || null)}
            renderInput={params => (
              <TextField
                {...params}
                label={t("Portal User / Financial Advisor")}
              />
            )}
          />
        </div>
      </Dialog>

      <Dialog
        open={openUnassignConfirm}
        onClose={() => setOpenUnassignConfirm(false)}
        title={t("Confirm Unassign")}
        actions={
          <DialogActions>
            <Button
              variant="text"
              onClick={() => setOpenUnassignConfirm(false)}
            >
              {t("Cancel")}
            </Button>
            <Button
              loading={adminMappingLoading}
              color="error"
              onClick={handleUnassignAdminMapping}
            >
              {t("Confirm Unassign")}
            </Button>
          </DialogActions>
        }
      >
        <div>
          {t(
            `Are you sure you want to unassign all ${
              azimutUsers?.numberOfItems || 0
            } filtered clients from the selected financial advisor?`
          )}
        </div>
      </Dialog>

      <FilterDrawer
        open={filterDrawerOpen}
        onClose={() => setFilterDrawerOpen(false)}
        investmentAmount={investmentAmount}
        setInvestmentAmount={setInvestmentAmount}
        investmentAmountStates={investmentAmountStates}
        annualIncome={annualIncome}
        setAnnualIncome={setAnnualIncome}
        annualIncomeStates={annualIncomeStates}
        fatca={fatca}
        setFatca={setFatca}
        fatcaStates={fatcaStates}
        showKycFilters={
          kycStatus == "1" ||
          kycStatus == "-1" ||
          kycStatus == "0.1" ||
          kycStatus == "2.1"
        }
        minTopup={minTopup}
        handleMinTopup={handleMinTopup}
        maxTopup={maxTopup}
        handleMaxTopup={handleMaxTopup}
        minWithdrawal={minWithdrawal}
        handleMinWithdrawal={handleMinWithdrawal}
        maxWithdrawal={maxWithdrawal}
        handleMaxWithdrawal={handleMaxWithdrawal}
        minBuy={minBuy}
        handleMinBuy={handleMinBuy}
        maxBuy={maxBuy}
        handleMaxBuy={handleMaxBuy}
        minSell={minSell}
        handleMinSell={handleMinSell}
        maxSell={maxSell}
        handleMaxSell={handleMaxSell}
        minBalance={minBalance}
        handleMinBalance={handleMinBalance}
        maxBalance={maxBalance}
        handleMaxBalance={handleMaxBalance}
        fundId={fundId}
        setFundId={setFundId}
        funds={funds || []}
        showFinancialFilters={
          user.user.permissions?.includes("financialFilter") || false
        }
        countryId={countryId}
        setCountryId={setCountryId}
        countries={countries}
        cityId={cityId}
        setCityId={setCityId}
        cities={cities}
        loadingCities={loadingCities}
        startDate={startDate}
        setStartDate={setStartDate}
        endDate={endDate}
        setEndDate={setEndDate}
        setPage={setPage}
      />
    </div>
  );
};
