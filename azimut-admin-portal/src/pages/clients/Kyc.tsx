import {
  Assignment,
  CheckCircle,
  ChevronRight,
  HighlightOff,
  Replay,
} from "@mui/icons-material";
import {
  Checkbox,
  FormControl,
  InputLabel,
  MenuItem,
  Radio,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import { cloneDeep, Dictionary, groupBy, keyBy, orderBy } from "lodash";
import { Fragment, useEffect, useMemo, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";

import Button from "../../components/shared/Button/Button";
import HtmlTooltip from "../../components/shared/HtmlTooltip";
import Loader from "../../components/shared/Spinner/Loader";
import { azimutAdminApi } from "../../hooks/adminUsersApi";
import { azimutApi } from "../../hooks/azimutApi";
import {
  Answer,
  AnswerType,
  Question,
  Review,
  ReviewPage,
  UserAnswer,
} from "../../hooks/types";
import { useAuth } from "../../hooks/useAuth";
import { isEgyptNtra } from "./ClientData";

export const Kyc = () => {
  const params = useParams();
  const { user } = useAuth()!;
  const { t } = useTranslation("common");

  const [reviewPages, setReviewPages] = useState<Dictionary<ReviewPage>>({});
  const [reviews, setReviews] = useState<Dictionary<Review>>({});
  const [isAutoApproved, setIsAutoApproved] = useState(false);

  const { data: kycPages, isLoading } = azimutApi.useGetAllKycPagesQuery({
    userId: params.id!,
    firstPageId: "5",
  });

  const { data: allReviews } = azimutApi.useGetAllReviewsQuery(params.id!);

  const groupedAllReviews = useMemo(() => {
    const orderedAllReviews = orderBy(allReviews, "id", "desc");
    const groupedReviews = groupBy(orderedAllReviews, "questionId");
    return groupedReviews;
  }, [allReviews]);

  const [submitReviews, { isLoading: isSubmitting }] =
    azimutApi.useSubmitReviewsMutation();

  const { data: bankAccounts, isLoading: isBankLoading } =
    azimutApi.useGetClientBankAccountQuery(params.id!);

  const { data: clientData } = azimutApi.useGetClientDetailsQuery(params.id!);

  const cso = clientData && clientData.cso && JSON.parse(clientData.cso);
  const ntra = clientData && clientData.ntra && JSON.parse(clientData.ntra);

  useEffect(() => {
    if (kycPages && clientData && Object.keys(reviews).length == 0) {
      const beReviewPages: Dictionary<ReviewPage> = {};
      const beReviews: Dictionary<Review> = {};
      kycPages.forEach(page => {
        beReviewPages[page.id] = {
          pageId: page.id,
          nextPageId: page.nextId,
          appUserId: params.id!,
          pageOrder: page.pageOrder,
          reviews: page.questions.map(q => q.review).filter(r => r),
        };
        page.questions.forEach(question => {
          beReviews[question.id] = question.review || {
            questionId: question.id,
            status: 0,
            pageId: page.id,
          };
        });
      });
      const autoAccepted =
        Object.values(beReviews).filter(e => e.status != 0).length == 0 &&
        clientData?.kycStatus == 1;
      setIsAutoApproved(autoAccepted);
      if (autoAccepted) {
        Object.keys(beReviews).forEach(key => {
          beReviews[key] = { ...beReviews[key], status: 1 };
        });
      }

      setReviews(beReviews);
      setReviewPages(beReviewPages);
    }
  }, [kycPages, clientData]);

  const updateReview = (
    questionId: number,
    status: number,
    reason?: number,
    comment?: string
  ) => {
    const newReview: Dictionary<Review> = cloneDeep(reviews);
    newReview[questionId].status = status;
    newReview[questionId].updated = true;
    if (reason) newReview[questionId].reasonId = reason;
    if (comment != undefined) newReview[questionId].comment = comment;
    setReviews(newReview);
  };
  const acceptAll = () => {
    const newReview: Dictionary<Review> = cloneDeep(reviews);
    Object.keys(newReview).forEach(questionId => {
      newReview[questionId] = {
        questionId: parseInt(questionId),
        status: 1,
        updated: true,
        pageId: newReview[questionId].pageId,
      };
    });
    setReviews(newReview);
  };

  const submitAnswers = async () => {
    const newReviewPages: Dictionary<ReviewPage> = cloneDeep(reviewPages);
    const newReviewPagesToSet: Dictionary<ReviewPage> = cloneDeep(reviewPages);
    let hasErrors = false;
    let incomplete = false;
    const groupedReviews = groupBy(reviews, "pageId");
    for (const reviewGroup of Object.entries(groupedReviews)) {
      const filteredReviews = reviewGroup[1].filter(
        e =>
          e.status != 0 &&
          (e.status !=
            reviewPages[reviewGroup[0]].reviews.find(
              r => e.questionId == r.questionId
            )?.status ||
            e.comment !=
              reviewPages[reviewGroup[0]].reviews.find(
                r => e.questionId == r.questionId
              )?.comment)
      );

      if (filteredReviews.length > 0)
        newReviewPages[reviewGroup[0]].reviews = reviewGroup[1];
      else newReviewPages[reviewGroup[0]].reviews = filteredReviews;
      newReviewPagesToSet[reviewGroup[0]].reviews = reviewGroup[1];
      newReviewPages[reviewGroup[0]].reviews.forEach(review => {
        if (review.status == 0) incomplete = true;
        if (review.status == 2 && !review.reasonId) hasErrors = true;
      });
    }
    if (hasErrors)
      toast.error(
        <div>
          Submission Failed <br />
          Please choose reason for every rejected answer
        </div>
      );
    else if (incomplete)
      toast.error(
        <div>
          Submission Failed <br />
          Missing review: Please make sure to accept or reject all questions
        </div>
      );
    else {
      setReviewPages(newReviewPagesToSet);
      const res = await submitReviews({
        kycPages: Object.values(newReviewPages),
        bankAccountId: bankAccounts ? bankAccounts[0]?.accountId : undefined,
        user: clientData!,
      }).unwrap();
      if (res == "true") toast.success("KYC submitted successfully");
      else toast.error(res);
    }
  };

  if (isLoading || isBankLoading) return <Loader />;

  const isRejection = Object.values(reviews).some(r => r.status == 2);

  return (
    <div>
      {isSubmitting && <Loader />}
      {kycPages?.map(page => (
        <div key={page.id}>
          <h2>{page.title}</h2>
          {page.questions.map(question => (
            <QuestionComponent
              question={question}
              key={question.id}
              isAutoApproved={isAutoApproved}
              review={
                clientData?.isVerified && clientData?.signedPdf
                  ? undefined
                  : reviews[question.id]
              }
              updateReview={updateReview}
              questionReviews={groupedAllReviews[question.id]}
            />
          ))}
        </div>
      ))}
      {(!clientData?.isVerified || !clientData?.signedPdf) &&
        !clientData?.isOld &&
        clientData?.kycStatus != 5 &&
        user.user.permissions?.includes("submitKyc") && (
          <div className="acceptAllHolder">
            <button
              className="acceptAllBtn"
              onClick={acceptAll}
            >
              {t("Accept All")}
            </button>
            {isRejection ? (
              <Button
                type="button"
                color="error"
                sx={{ width: "180px" }}
                loading={isSubmitting}
                onClick={submitAnswers}
              >
                {t("Reject")} <ChevronRight />
              </Button>
            ) : (
              <Button
                type="button"
                sx={{ width: "180px" }}
                loading={isSubmitting}
                disabled={
                  isEgyptNtra()
                    ? !cso ||
                      !cso.isValid ||
                      !ntra ||
                      !ntra.isMatched ||
                      clientData?.azimutAccount?.clientAML == 0
                    : clientData?.azimutAccount?.clientAML == 0
                }
                onClick={submitAnswers}
              >
                {t("Submit")} <ChevronRight />
              </Button>
            )}
          </div>
        )}
    </div>
  );
};

const QuestionOption = ({
  answer,
  userAnswer,
}: {
  answer: Answer;
  userAnswer: UserAnswer;
}) => {
  const { t } = useTranslation("common");

  switch (answer.answerType) {
    case AnswerType.RADIO:
      return (
        <div>
          <Radio
            checked={userAnswer?.answerId === answer.id}
            disabled
            sx={{
              // "&.Mui-disabled": {
              //   color: "white",
              // },
              "&.Mui-checked": {
                color: "#0092ff",
              },
            }}
            value={answer.id}
            name={answer.id.toString()}
          />
          {answer.answerOption}
        </div>
      );
    case AnswerType.CHECK:
      return (
        <div>
          <Checkbox
            checked={userAnswer?.answerId === answer.id}
            disabled
            sx={{
              // "&.Mui-disabled": {
              //   color: "white",
              // },
              "&.Mui-checked": {
                color: "#0092ff",
              },
            }}
          />
          {answer.answerOption}
        </div>
      );
    case AnswerType.DROP:
      if (userAnswer?.answerId === answer.id)
        return (
          <Select
            disabled
            value={answer.id}
            sx={{
              "&& .Mui-disabled": {
                color: "white",
                WebkitTextFillColor: "white",
              },
            }}
          >
            <MenuItem value={answer.id}>{answer.answerOption}</MenuItem>
          </Select>
        );
      else if (!userAnswer && answer.answerPlaceHolder) {
        return (
          <Select
            disabled
            value={answer.id}
          >
            <MenuItem value={answer.id}>{answer.answerPlaceHolder}</MenuItem>
          </Select>
        );
      } else return null;
    case AnswerType.TEXT:
    case AnswerType.RICH:
    case AnswerType.EMAIL:
    case AnswerType.PHONE:
    case AnswerType.CALENDER:
      return (
        <TextField
          multiline={answer.answerType == AnswerType.RICH}
          disabled
          sx={{
            "&& .Mui-disabled": {
              color: "white",
              WebkitTextFillColor: "white",
            },
          }}
          value={userAnswer?.answerValue}
          placeholder={answer.answerPlaceHolder}
        />
      );
    case AnswerType.DOCUMENT:
      return userAnswer?.documentURL ? (
        <a
          href={userAnswer.documentURL}
          target="_blank"
          rel="noreferrer"
        >
          {t("View Document")}
        </a>
      ) : (
        <div style={{ color: "gray" }}> {t("Not submitted")}</div>
      );

    default:
      return null;
  }
};

const QuestionComponent = ({
  question,
  review,
  updateReview,
  questionReviews,
  isAutoApproved,
}: {
  question: Question;
  review: Review | undefined; // no review means account is verified
  updateReview: (
    questionId: number,
    status: number,
    reason?: number,
    comment?: string
  ) => void;
  questionReviews: Review[];
  isAutoApproved: boolean;
}) => {
  const { data: rejectionReasons } = azimutApi.useGetReasonsQuery();
  const { i18n } = useTranslation();
  const { user } = useAuth()!;
  const { t } = useTranslation("common");
  const params = useParams();
  const { data: clientData } = azimutApi.useGetClientDetailsQuery(params.id!);
  const { data: portalUsers } = azimutAdminApi.useGetUsersQuery();
  const portalUsersDict = useMemo(() => {
    return keyBy(portalUsers, "id");
  }, [portalUsers]);
  const [comment, setComment] = useState("");

  const cso = clientData && clientData.cso && JSON.parse(clientData.cso);
  const ntra = clientData && clientData.ntra && JSON.parse(clientData.ntra);

  const userChoice =
    question?.userAnswers &&
    question?.userAnswers[0] &&
    question.answers.find(
      answer => question.userAnswers[0].answerId === answer.id
    );
  return (
    <div className="questionHolder">
      {question.id == 50 &&
        clientData?.azimutAccount.occupation &&
        clientData?.azimutAccount.occupation != "NULL" && (
          <div className="text-center">
            {t("ID Job")}: {clientData?.azimutAccount.occupation}
          </div>
        )}
      {questionReviews && (
        <div className="float-right">
          <HtmlTooltip
            placement="bottom-start"
            title={
              <Fragment>
                <Typography
                  color="inherit"
                  className="text-center"
                >
                  Review Logs
                </Typography>
                {questionReviews
                  .filter(
                    (e, i) =>
                      i == questionReviews.length - 1 ||
                      questionReviews[i + 1].status != e.status ||
                      questionReviews[i + 1].comment != e.comment
                  )
                  .map(q => (
                    <div key={q.id}>
                      <span>{q.createdAt?.substring(0, 10)}: </span>
                      {q.status == 1 ? (
                        <span className="acceptSpn">{t("Accept")}</span>
                      ) : (
                        <span className="rejectSpn">{t("Reject")}</span>
                      )}
                      <span> by </span>
                      <b>{portalUsersDict[q.actionMaker!]?.fullName}</b>
                      {q.comment && <div>{q.comment}</div>}
                    </div>
                  ))}
              </Fragment>
            }
          >
            <Assignment />
          </HtmlTooltip>
        </div>
      )}
      <p>
        {question.questionOrder}. {question.questionText}
        {!question.isAnswerMandatory && (
          <span className="optionalText">(Optional)</span>
        )}
      </p>
      <div className="grid grid-cols-2 gap-4">
        {question.answers.map(answer => (
          <QuestionOption
            key={answer.id}
            answer={answer}
            userAnswer={question?.userAnswers && question?.userAnswers[0]}
          />
        ))}
      </div>
      {userChoice &&
        userChoice.relatedAnswers &&
        userChoice.relatedAnswers.map((relatedAnswer, index) => (
          <div key={relatedAnswer.id}>
            <p>
              {relatedAnswer.relatedQuestionText}
              {!relatedAnswer.isAnswerMandatory && (
                <span className="optionalText">(Optional)</span>
              )}
            </p>
            <QuestionOption
              answer={relatedAnswer}
              userAnswer={question.userAnswers[0].relatedUserAnswers[index]}
            />
          </div>
        ))}
      {review?.status == 2 ? (
        <Fragment>
          <div>
            <div className="buttonsHolder1">
              <button className="rejectedBtn">
                <HighlightOff />
                {t("Rejected")}
              </button>
              {user.user.permissions?.includes("submitKyc") && (
                <div
                  className="retakeHolder"
                  onClick={() => updateReview(question.id, 0)}
                >
                  <Replay />
                  <p className="undoText">{t("Undo")}</p>
                </div>
              )}
            </div>
            <FormControl>
              <InputLabel id={question.id + "-select-label"}>
                {t("reason")}
              </InputLabel>
              <Select
                sx={{ minWidth: "200px" }}
                defaultValue={review?.reasonId}
                label={t("reason")}
                id={question.id + "-select"}
                onChange={event =>
                  updateReview(
                    question.id,
                    2,
                    parseInt(event.target.value.toString())
                  )
                }
              >
                {rejectionReasons?.map(reason => (
                  <MenuItem
                    key={reason.id}
                    value={reason.id}
                  >
                    {i18n.language == "en" ? reason.reason : reason.reasonAr}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </div>
          {review.comment && (
            <div style={{ whiteSpace: "pre-wrap" }}>
              <b>
                Notes:
                <br />
              </b>
              {review.comment}
            </div>
          )}
        </Fragment>
      ) : review?.status == 1 || (isAutoApproved && !review?.updated) ? (
        <Fragment>
          <div className="buttonsHolder1">
            <button
              className={
                "acceptedBtn " +
                (isAutoApproved && !review?.updated ? "autoAcceptedBtn" : "")
              }
            >
              <CheckCircle />
              {isAutoApproved && !review?.updated
                ? t("Auto Accepted")
                : t("Accepted")}
            </button>
            {user.user.permissions?.includes("submitKyc") && (
              <div
                className="retakeHolder"
                onClick={() => updateReview(question.id, 0)}
              >
                <Replay />
                <p className="undoText">{t("Undo")}</p>
              </div>
            )}
          </div>
          {(!isAutoApproved || review?.updated) && review!.comment && (
            <div style={{ whiteSpace: "pre-wrap" }}>
              <b>
                Notes:
                <br />
              </b>
              {review!.comment}
            </div>
          )}
        </Fragment>
      ) : review?.status == 0 &&
        (!clientData?.isVerified || !clientData?.signedPdf) &&
        !clientData?.isOld &&
        clientData?.kycStatus != 5 &&
        user.user.permissions?.includes("submitKyc") ? (
        <Fragment>
          <div style={{ float: "right", marginTop: -20 }}>
            Notes:
            <br />
            <textarea
              style={{ width: "300px", height: 85 }}
              value={comment}
              onChange={e => {
                setComment(e.target.value);
              }}
            />
          </div>
          <div className="buttonsHolder">
            <button
              onClick={() => updateReview(question.id, 1, undefined, comment)}
              className="acceptBtn"
              disabled={
                (cso && !cso.isValid) ||
                (ntra && !ntra.isMatched) ||
                clientData?.azimutAccount?.clientAML == 0
              }
            >
              {t("Accept")}
            </button>
            <button
              className="rejectBtn"
              onClick={() => updateReview(question.id, 2, undefined, comment)}
            >
              {t("Reject")}
            </button>
          </div>
        </Fragment>
      ) : null}
    </div>
  );
};
