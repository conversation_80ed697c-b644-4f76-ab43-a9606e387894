import ArrowDownwardIcon from "@mui/icons-material/ArrowDownward";
import ArrowUpwardIcon from "@mui/icons-material/ArrowUpward";
import NorthEastIcon from "@mui/icons-material/NorthEast";
import SouthWestIcon from "@mui/icons-material/SouthWest";
import { clone } from "lodash";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";
import { PuffLoader } from "react-spinners";

import { azimutApi } from "../../hooks/azimutApi";

const FundDetails = ({ teacomputerId }: { teacomputerId: string }) => {
  const params = useParams();
  const { t } = useTranslation("common");
  const [transactions, setTransactions] = useState<any>(null);
  const [fundDetails, setFundDetails] = useState<any>(null);
  const [currentRevenue, setCurrentRevenue] = useState<any>(null);
  const [currentRevenuePercent, setCurrentRevenuePercent] = useState<any>(null);

  const { data: fundsRes, isLoading } = azimutApi.useGetClientFundsQuery({
    userId: params.id!,
    sorting: "DESC",
    teacomputerId: teacomputerId,
  });

  useEffect(() => {
    if (fundsRes) {
      const res = fundsRes;
      if (res.status === 0) {
        const fundDetails = clone(
          res.response.azAccount.businessClientFunds[0]
        );
        setTransactions(
          res.response.azAccount.businessClientFunds[0].fundTransactions
        );

        fundDetails.totalAmount = new Intl.NumberFormat().format(
          fundDetails.totalAmount
        );
        fundDetails.quantity = new Intl.NumberFormat().format(
          fundDetails.quantity
        );
        setFundDetails(fundDetails);
        setCurrentRevenue(
          new Intl.NumberFormat().format(res.response.azAccount.currentRevenue)
        );
        setCurrentRevenuePercent(
          new Intl.NumberFormat().format(
            res.response.azAccount.currentRevenuePercent
          )
        );
      }
    }
  }, [fundsRes]);

  if (isLoading || !fundDetails || !transactions) {
    return (
      <div className="m-auto w-full flex justify-center items-center">
        <PuffLoader color="white" />
      </div>
    );
  }

  return (
    <div>
      {fundDetails && (
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded">
            {fundDetails.quantity} Units
          </div>
          <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded">
            {fundDetails.totalAmount} {fundDetails.currencyName}
          </div>
          <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded">
            {currentRevenue > 0 && <ArrowUpwardIcon color="success" />}
            {currentRevenue < 0 && <ArrowDownwardIcon color="error" />}
            <span className="ml-1">
              {currentRevenue} ({currentRevenuePercent})%)
            </span>
          </div>
        </div>
      )}
      {/* Transaction list */}
      <ul>
        {transactions.map((transaction: any) => (
          <li
            key={transaction.id}
            className="transactions-card"
          >
            <div className="transaction-data">
              {transaction?.orderTypeId == 1 && (
                <SouthWestIcon
                  color="success"
                  style={{ marginRight: "5px", marginLeft: "5px" }}
                />
              )}
              {transaction?.orderTypeId == 2 && (
                <NorthEastIcon
                  color="error"
                  style={{ marginRight: "5px", marginLeft: "5px" }}
                />
              )}
              <div>
                <p className="trans-type">
                  {transaction?.orderTypeId == 1 ? t("Buy") : t("Sell")}
                </p>
                <p className="trans-date">
                  {transaction.orderValue} {fundDetails?.currencyName} |{" "}
                  {transaction.quantity} {t("unit")}
                </p>
              </div>
            </div>
            <div>
              {transaction?.orderStatusId == 1 && (
                <p className="approved">{t("Completed")}</p>
              )}
              {transaction?.orderStatusId == 2 && (
                <p className="pending">{t("Processing")}...</p>
              )}
              {transaction?.orderStatusId == 3 && (
                <p className="rejected">{t("Canceled")}</p>
              )}
              <p className="trans-date">{transaction.orderDate}</p>
            </div>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default FundDetails;
