import CloseIcon from "@mui/icons-material/Close";
import NorthEastIcon from "@mui/icons-material/NorthEast";
import PriorityHighIcon from "@mui/icons-material/PriorityHigh";
import SouthWestIcon from "@mui/icons-material/SouthWest";
import SwapHorizIcon from "@mui/icons-material/SwapHoriz";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";

import Loader from "../../components/shared/Spinner/Loader";
import { azimutApi } from "../../hooks/azimutApi";

const Wallet: React.FC = () => {
  const params = useParams();
  const { t } = useTranslation("common");
  const [myAzAcc, setMyAzAcc] = useState<any>(null);

  const { data: myAzAccRes, isLoading } =
    azimutApi.useGetClientBalanceAndTransactionsQuery({
      userId: params.id!,
    });

  useEffect(() => {
    if (myAzAccRes) {
      setMyAzAcc(myAzAccRes.response.azAccount);
    }
  }, [myAzAccRes]);

  const getTransactionStatus = (status: number) => {
    switch (status) {
      case 1:
        return t("Pending");
      case 2:
        return t("Approved");
      case 3:
        return t("Rejected");
      case 4:
        return t("Other");
      case 6:
        return t("Unpaid");
      case 7:
        return t("Failed");
      case 8:
        return t("Cancel");
      default:
        return t("Unknown");
    }
  };

  return (
    <div>
      {isLoading && <Loader />}

      {myAzAcc?.businessClientCashBalances?.length === 0 ? (
        <div className="p-4 bg-gray-800 rounded-lg">
          <p className="text-gray-300">{t("AZ-Account Is Empty!")}</p>
        </div>
      ) : (
        <div className="bg-gray-800 shadow-lg rounded-lg p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-100 mb-4 mt-0">
            {t("Balances")}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {myAzAcc?.businessClientCashBalances?.map((balance: any) => (
              <div
                key={balance.currencyName}
                className="bg-gray-700 rounded-lg p-4"
              >
                <div className="flex justify-between items-center mb-3">
                  <span className="font-medium text-gray-100">
                    {balance.currencyName}
                  </span>
                  <span className="text-xl font-semibold text-primary-400">
                    {new Intl.NumberFormat().format(balance.balance)}
                  </span>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">{t("Pending")}:</span>
                    <span className="text-gray-300">
                      {balance.pendingTransferFormatted}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">{t("On hold")}:</span>
                    <span className="text-gray-300">
                      {balance.totalBuyValue || 0}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <h4 style={{ marginBottom: 0 }}> {t("Transactions")} </h4>
      {myAzAcc?.businessTransactions?.map((transaction: any, index: any) => (
        <div
          key={index}
          className="transactions-card"
        >
          <div className="transaction-data">
            {transaction?.type == 2 &&
              (transaction?.status == 2 || transaction?.status == 3) && (
                <SouthWestIcon
                  color="success"
                  style={{ marginRight: "5px", marginLeft: "5px" }}
                />
              )}
            {transaction?.type == 1 &&
              (transaction?.status == 2 || transaction?.status == 3) && (
                <NorthEastIcon
                  color="error"
                  style={{ marginRight: "5px", marginLeft: "5px" }}
                />
              )}
            {transaction?.status == 1 && (
              <SwapHorizIcon
                color="primary"
                style={{ marginRight: "5px", marginLeft: "5px" }}
              />
            )}
            {transaction?.status == 7 && (
              <CloseIcon
                color="error"
                style={{ marginRight: "5px", marginLeft: "5px" }}
              />
            )}
            {transaction?.status == 6 && (
              <PriorityHighIcon
                color="error"
                style={{ marginRight: "5px", marginLeft: "5px" }}
              />
            )}
            <div>
              <p className="trans-type">
                {transaction?.type == 1
                  ? t("Withdrawal")
                  : transaction?.type == 2
                  ? t("Top-up")
                  : t("other")}
              </p>
              <p className="trans-date">
                {transaction.amount} {transaction.currency} |{" "}
                {transaction.trxDate}{" "}
              </p>
            </div>
          </div>
          <p className={getTransactionStatus(transaction?.status) + "-trx"}>
            {getTransactionStatus(transaction?.status)}
          </p>
        </div>
      ))}
    </div>
  );
};

export default Wallet;
