import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
} from "@mui/material";

const listTypes: any = {
  TERRORISM_LOCAL: "قوائم محلية",
  TERRORISM_UN: "UN",
  SANCTION: "اوامر منع",
};

/**
 * SimilarCasesDialog
 *
 * Props:
 *  open: boolean - whether the dialog is open
 *  onClose: function - callback to close the dialog
 *  cases: array of objects with keys:
 *    score, caseNo, caseYear, fullName, idNumber, recordDate, dateOfBirth
 */
export default function AmlMatchesDialog({
  open,
  onClose,
  cases,
  isAzimut,
}: any) {
  if (isAzimut)
    cases = cases.map((c: any) => ({
      score: `${c.confidenceScore * 100}%`,
      ...c.negativeList,
    }));
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>Matching Cases</DialogTitle>
      <DialogContent>
        {cases && cases.length > 0 ? (
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Score</TableCell>
                  <TableCell>Full Name</TableCell>
                  <TableCell>Date of Birth</TableCell>
                  <TableCell>{isAzimut ? "Case Info" : "Case No."}</TableCell>
                  <TableCell>{isAzimut ? "Type" : "Case Year"}</TableCell>
                  <TableCell>ID Number</TableCell>
                  <TableCell>
                    {isAzimut ? "File name" : "Record Date"}
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {cases.map((c: any, idx: any) => (
                  <TableRow key={idx}>
                    <TableCell>{c.score}</TableCell>
                    <TableCell>{c.fullName}</TableCell>
                    <TableCell>
                      {c.dateOfBirth
                        ? new Date(c.dateOfBirth).toLocaleDateString()
                        : "-"}
                    </TableCell>
                    <TableCell>{c.caseNo || c.additionalInfo || "-"}</TableCell>
                    <TableCell>
                      {c.caseYear || listTypes[c.listType] || "-"}
                    </TableCell>
                    <TableCell>
                      {c.idNumber || c.nationalId || c.passportNumber || "-"}
                    </TableCell>
                    <TableCell>
                      {c.recordDate
                        ? new Date(c.recordDate).toLocaleDateString()
                        : c.listSource
                        ? c.listSource.substr(0, c.listSource.length - 4)
                        : "-"}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <Typography>No Matches found.</Typography>
        )}
      </DialogContent>
      <DialogActions>
        <Button
          onClick={onClose}
          color="primary"
        >
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
}
