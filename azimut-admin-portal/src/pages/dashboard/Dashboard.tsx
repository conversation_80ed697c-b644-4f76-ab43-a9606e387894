import {
  <PERSON><PERSON><PERSON><PERSON>,
  CalendarToday,
  DateRange,
  Event,
  History,
  Today,
} from "@mui/icons-material";
import {
  Box,
  Button,
  ButtonGroup,
  Card,
  CardContent,
  Grid,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from "@mui/material";
import { DatePicker, LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import isSameOrBefore from "dayjs/plugin/isSameOrBefore";
import isToday from "dayjs/plugin/isToday";
import weekday from "dayjs/plugin/weekday";
import { Fragment, useState } from "react";

import Loader from "../../components/shared/Spinner/Loader";
import { azimutAdminApi } from "../../hooks/adminUsersApi";

dayjs.extend(isToday);
dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);
dayjs.extend(weekday);

const Dashboard = () => {
  const [dateFilter, setDateFilter] = useState("All Time");
  const [startDate, setStartDate] = useState<Dayjs | undefined>();
  const [endDate, setEndDate] = useState<Dayjs | undefined>();
  const [showing, setShowing] = useState("operations");
  const {
    data: clientsData,
    isFetching,
    error,
  } = azimutAdminApi.useUserStatsQuery({
    startDate: startDate?.toISOString(),
    endDate: endDate?.toISOString(),
  });

  const {
    data: clientsKycData,
    isFetching: isKycFetching,
    error: isKycError,
  } = azimutAdminApi.useUserKycStatsQuery({
    startDate: startDate?.toISOString(),
    endDate: endDate?.toISOString(),
  });

  const clients = error
    ? {
        newUser: 0,
        pending: 0,
        accepted: 0,
        signed: 0,
        autoAccepted: 0,
        old: 0,
        rejected: 0,
      }
    : clientsData;

  const clientsKyc = isKycError
    ? {
        accepted: 0,
        signed: 0,
        rejected: 0,
      }
    : clientsKycData;

  const handleDateFilterChange = (filter: any) => {
    setDateFilter(filter);

    let start, end;
    switch (filter) {
      case "Today":
        start = dayjs().startOf("day");
        end = dayjs().endOf("day");
        break;
      case "This Week":
        start = dayjs().weekday(0).startOf("day"); // Start of the week (Sunday)
        end = dayjs().weekday(6).endOf("day"); // End of the week (Saturday)
        break;
      case "This Month":
        start = dayjs().startOf("month");
        end = dayjs().endOf("month");
        break;
      case "Last Month":
        start = dayjs().subtract(1, "month").startOf("month");
        end = dayjs().subtract(1, "month").endOf("month");
        break;
      default:
        start = undefined;
        end = undefined;
    }

    if (filter !== "Custom") {
      setStartDate(start);
      setEndDate(end);
    } else {
      setStartDate(dayjs().subtract(1, "month").startOf("day"));
      setEndDate(dayjs().endOf("day"));
    }
  };

  const handleChangeShowing = (_: any, newShowing: any) => {
    setShowing(newShowing);
  };

  return (
    <Box p={2}>
      <ToggleButtonGroup
        color="primary"
        value={showing}
        sx={{ float: "right" }}
        exclusive
        onChange={handleChangeShowing}
        aria-label="Show"
      >
        <ToggleButton value="clients">Clients</ToggleButton>
        <ToggleButton value="operations">Operations</ToggleButton>
      </ToggleButtonGroup>

      <Typography
        variant="h4"
        gutterBottom
      >
        Client Dashboard
      </Typography>

      <ButtonGroup
        variant="outlined"
        color="primary"
        aria-label="outlined primary button group"
      >
        <Button
          startIcon={<Today />}
          onClick={() => handleDateFilterChange("Today")}
          variant={dateFilter === "Today" ? "contained" : "outlined"}
          color={dateFilter === "Today" ? "primary" : "neutral"}
        >
          Today
        </Button>
        <Button
          startIcon={<CalendarToday />}
          onClick={() => handleDateFilterChange("This Week")}
          variant={dateFilter === "This Week" ? "contained" : "outlined"}
          color={dateFilter === "This Week" ? "primary" : "neutral"}
        >
          This Week
        </Button>
        <Button
          startIcon={<CalendarMonth />}
          onClick={() => handleDateFilterChange("This Month")}
          variant={dateFilter === "This Month" ? "contained" : "outlined"}
          color={dateFilter === "This Month" ? "primary" : "neutral"}
        >
          This Month
        </Button>
        <Button
          startIcon={<History />}
          onClick={() => handleDateFilterChange("Last Month")}
          variant={dateFilter === "Last Month" ? "contained" : "outlined"}
          color={dateFilter === "Last Month" ? "primary" : "neutral"}
        >
          Last Month
        </Button>
        <Button
          startIcon={<DateRange />}
          onClick={() => handleDateFilterChange("All Time")}
          variant={dateFilter === "All Time" ? "contained" : "outlined"}
          color={dateFilter === "All Time" ? "primary" : "neutral"}
        >
          All Time
        </Button>
        <Button
          startIcon={<Event />}
          onClick={() => handleDateFilterChange("Custom")}
          variant={dateFilter === "Custom" ? "contained" : "outlined"}
          color={dateFilter === "Custom" ? "primary" : "neutral"}
        >
          Custom
        </Button>
      </ButtonGroup>

      {dateFilter === "Custom" && (
        <Box
          mt={2}
          display="flex"
          gap={2}
        >
          <LocalizationProvider dateAdapter={AdapterDayjs}>
            <DatePicker
              label="Start Date"
              value={startDate}
              onChange={newValue => setStartDate(newValue || undefined)}
            />
            <DatePicker
              label="End Date"
              value={endDate}
              onChange={newValue => {
                if (newValue) {
                  // newValue = newValue.add(1, "day");
                }
                setEndDate(newValue || undefined);
              }}
            />
          </LocalizationProvider>
        </Box>
      )}

      {isFetching ? (
        <Loader />
      ) : (
        showing == "clients" && (
          <Grid
            container
            spacing={2}
            mt={2}
          >
            <Grid
              item
              xs={12}
            >
              <Card className="text-center">
                <CardContent>
                  <Typography
                    variant="h5"
                    component="div"
                  >
                    All Users
                  </Typography>
                  <Typography
                    variant="h2"
                    color="primary"
                  >
                    {clients.newUser +
                      clients.pending +
                      clients.accepted +
                      clients.autoAccepted +
                      clients.old +
                      clients.signed +
                      clients.rejected}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
            >
              <Card>
                <CardContent>
                  <Typography
                    variant="h5"
                    component="div"
                  >
                    New Users
                  </Typography>
                  <Typography
                    variant="h2"
                    color="primary"
                  >
                    {clients.newUser}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
            >
              <Card>
                <CardContent>
                  <Typography
                    variant="h5"
                    component="div"
                  >
                    Old Users
                  </Typography>
                  <Typography
                    variant="h2"
                    color="primary"
                  >
                    {clients.old}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
            >
              <Card>
                <CardContent>
                  <Typography
                    variant="h5"
                    component="div"
                  >
                    Pending
                  </Typography>
                  <Typography
                    variant="h2"
                    color="primary"
                  >
                    {clients.pending}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
            >
              <Card>
                <CardContent>
                  <Typography
                    variant="h5"
                    component="div"
                  >
                    Accepted
                  </Typography>
                  <Typography
                    variant="h2"
                    color="primary"
                  >
                    {clients.accepted}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
            >
              <Card>
                <CardContent>
                  <Typography
                    variant="h5"
                    component="div"
                  >
                    Rejected
                  </Typography>
                  <Typography
                    variant="h2"
                    color="primary"
                  >
                    {clients.rejected}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
            >
              <Card>
                <CardContent>
                  <Typography
                    variant="h5"
                    component="div"
                  >
                    Auto Accepted
                  </Typography>
                  <Typography
                    variant="h2"
                    color="primary"
                  >
                    {clients.autoAccepted}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid
              item
              xs={12}
              sm={6}
              md={4}
            >
              <Card>
                <CardContent>
                  <Typography
                    variant="h5"
                    component="div"
                  >
                    Signed Contract
                  </Typography>
                  <Typography
                    variant="h2"
                    color="primary"
                  >
                    {clients.signed}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        )
      )}

      {isKycFetching ? (
        <Loader />
      ) : (
        showing == "operations" && (
          <Fragment>
            <Typography
              variant="h4"
              gutterBottom
              className="text-center mt-4"
            >
              Operations Updates
            </Typography>
            <Grid
              container
              spacing={2}
              mt={2}
            >
              <Grid
                item
                xs={12}
                sm={6}
                md={4}
              >
                <Card>
                  <CardContent>
                    <Typography
                      variant="h5"
                      component="div"
                    >
                      Accepted Users
                    </Typography>
                    <Typography
                      variant="h2"
                      color="primary"
                    >
                      {clientsKyc.accepted}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid
                item
                xs={12}
                sm={6}
                md={4}
              >
                <Card>
                  <CardContent>
                    <Typography
                      variant="h5"
                      component="div"
                    >
                      Rejected Users
                    </Typography>
                    <Typography
                      variant="h2"
                      color="primary"
                    >
                      {clientsKyc.rejected}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid
                item
                xs={12}
                sm={6}
                md={4}
              >
                <Card>
                  <CardContent>
                    <Typography
                      variant="h5"
                      component="div"
                    >
                      Signed Contract (incl. Accepted)
                    </Typography>
                    <Typography
                      variant="h2"
                      color="primary"
                    >
                      {clientsKyc.signed}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Fragment>
        )
      )}
    </Box>
  );
};

export default Dashboard;
