import "../../assets/styles/reason.css";

import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import SearchIcon from "@mui/icons-material/Search";
import {
  Card,
  CardContent,
  DialogActions,
  Grid,
  TextField,
  Typography,
} from "@mui/material";
import { DataGrid, GridColDef } from "@mui/x-data-grid";
import { useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";

import Button from "../../components/shared/Button/Button";
import Dialog from "../../components/shared/Dialog/Dialog";
import { azimutApi } from "../../hooks/azimutApi";
import { NegativeListSearchFilter } from "../../hooks/types";

export const NegativeList = () => {
  const [loading, setLoading] = useState(false);
  const [openUploadUrl, setOpenUploadUrl] = useState(false);
  const [openUploadFile, setOpenUploadFile] = useState(false);
  const [openSearch, setOpenSearch] = useState(false);

  // Upload file states
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Search states
  const [searchFilter, setSearchFilter] = useState<NegativeListSearchFilter>({
    searchTerm: "",
    searchType: "FUZZY",
  });
  const [showSearchResults, setShowSearchResults] = useState(false);

  const { t } = useTranslation("common");

  // API hooks
  const { data: statistics } = azimutApi.useGetNegativeListStatisticsQuery();
  const [uploadByUrl] = azimutApi.useUploadNegativeListByUrlMutation();
  const [uploadFile] = azimutApi.useUploadNegativeListFileMutation();
  const { data: searchData, isFetching: isSearching } =
    azimutApi.useSearchNegativeListQuery(searchFilter, {
      skip: !showSearchResults,
    });

  const handleUploadByUrl = async () => {
    setLoading(true);
    try {
      await uploadByUrl().unwrap();

      toast.success(t("Negative list updated successfully"));
      setOpenUploadUrl(false);
    } catch (error: any) {
      toast.error(
        error?.data?.message ||
          t("Negative list update complete. System now scanning all users")
      );
    } finally {
      setLoading(false);
    }
  };

  const handleUploadFile = async () => {
    if (!selectedFile) {
      toast.error(t("Please select a file"));
      return;
    }

    setLoading(true);
    try {
      await uploadFile({
        file: selectedFile,
      }).unwrap();

      toast.success(t("Negative list file uploaded successfully"));
      setOpenUploadFile(false);
      setSelectedFile(null);
    } catch (error: any) {
      toast.error(
        error?.data?.message || t("Failed to upload negative list file")
      );
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    if (!searchFilter.searchTerm?.trim()) {
      toast.error(t("Please enter a search term"));
      return;
    }
    setShowSearchResults(true);
    setOpenSearch(false);
  };

  const searchColumns: GridColDef[] = [
    {
      field: "fullName",
      headerName: t("Full Name"),
      width: 200,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: "nationality",
      headerName: t("Nationality"),
      width: 150,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: "dateOfBirth",
      headerName: t("Date of Birth"),
      width: 150,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: "nationalId",
      headerName: t("National ID"),
      width: 150,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: "passportNumber",
      headerName: t("Passport Number"),
      width: 150,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: "listSource",
      headerName: t("List Source"),
      width: 150,
      disableColumnMenu: true,
      sortable: false,
    },
    {
      field: "listType",
      headerName: t("List Type"),
      width: 120,
      disableColumnMenu: true,
      sortable: false,
    },
  ];

  console.log(statistics);
  return (
    <div className="container">
      <h1>{t("Negative List Management")}</h1>

      {/* Statistics Cards */}
      <Grid
        container
        spacing={3}
        className="mb-6"
      >
        <Grid
          item
          xs={12}
          md={6}
        >
          <Card>
            <CardContent>
              <Typography
                variant="h6"
                component="div"
              >
                {t("Total Records")}
              </Typography>
              <Typography
                variant="h4"
                color="primary"
              >
                {statistics?.totalRecords || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid
          item
          xs={12}
          md={6}
        >
          <Card>
            <CardContent>
              <Typography
                variant="h6"
                component="div"
              >
                {t("Total Files Added")}
              </Typography>
              <Typography
                variant="h4"
                color="primary"
              >
                {statistics?.totalFiles || 0}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Action Buttons */}
      <div className="mb-4 space-x-2">
        <Button
          onClick={() => setOpenUploadUrl(true)}
          startIcon={<CloudUploadIcon />}
        >
          {t("Update Terrorism List from public list")}
        </Button>
        <Button
          onClick={() => setOpenUploadFile(true)}
          startIcon={<CloudUploadIcon />}
          variant="outlined"
        >
          {t("Upload HTML File")}
        </Button>
        <Button
          onClick={() => setOpenSearch(true)}
          startIcon={<SearchIcon />}
          variant="outlined"
        >
          {t("Search Matches")}
        </Button>
      </div>

      {/* Search Results */}
      {showSearchResults && (
        <div className="mt-6">
          <Typography
            variant="h6"
            className="mb-3"
          >
            {t("Search Results")} ({searchData?.length} {t("matches found")})
          </Typography>
          {searchData?.length && searchData?.length > 0 ? (
            <DataGrid
              rows={searchData.map(e => e.negativeList)}
              columns={searchColumns}
              disableRowSelectionOnClick
              autoHeight
              loading={isSearching}
              pageSizeOptions={[10, 25, 50]}
              initialState={{
                pagination: { paginationModel: { pageSize: 10 } },
              }}
            />
          ) : (
            <Typography
              variant="body1"
              color="textSecondary"
            >
              {t("No matches found")}
            </Typography>
          )}
        </div>
      )}

      {/* Upload by URL Dialog */}
      <Dialog
        open={openUploadUrl}
        onClose={() => setOpenUploadUrl(false)}
        title={t("Update Negative List")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              onClick={handleUploadByUrl}
            >
              {t("Yes, update")}
            </Button>
            <Button
              variant="text"
              onClick={() => setOpenUploadUrl(false)}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        <div className="space-y-4">
          This will update any new data from وحدة مكافحة غسل الأموال وتمويل
          الإرهاب المصرية
        </div>
      </Dialog>

      {/* Upload File Dialog */}
      <Dialog
        open={openUploadFile}
        onClose={() => setOpenUploadFile(false)}
        title={t("Upload HTML Negative List File")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              onClick={handleUploadFile}
            >
              {t("Upload")}
            </Button>
            <Button
              variant="text"
              onClick={() => setOpenUploadFile(false)}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        <div className="space-y-4">
          <div>
            <input
              type="file"
              accept=".html,.htm"
              onChange={e => setSelectedFile(e.target.files?.[0] || null)}
              className="mb-2"
            />
            <Typography
              variant="caption"
              color="textSecondary"
            >
              {t(
                "Please select an HTML file containing the negative list data"
              )}
            </Typography>
          </div>
        </div>
      </Dialog>

      {/* Search Dialog */}
      <Dialog
        open={openSearch}
        onClose={() => setOpenSearch(false)}
        title={t("Search Negative List")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              onClick={handleSearch}
            >
              {t("Search")}
            </Button>
            <Button
              variant="text"
              onClick={() => setOpenSearch(false)}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        <div className="space-y-4">
          <TextField
            fullWidth
            label={t("Search Term")}
            value={searchFilter.searchTerm}
            onChange={e =>
              setSearchFilter({ ...searchFilter, searchTerm: e.target.value })
            }
            placeholder={t("Enter name, ID, passport number...")}
          />

          <TextField
            fullWidth
            label={t("Nationality (Optional)")}
            value={searchFilter.nationality || ""}
            onChange={e =>
              setSearchFilter({ ...searchFilter, nationality: e.target.value })
            }
          />

          <TextField
            fullWidth
            label={t("Date of Birth (Optional)")}
            value={searchFilter.dateOfBirth || ""}
            onChange={e =>
              setSearchFilter({ ...searchFilter, dateOfBirth: e.target.value })
            }
            placeholder="YYYY-MM-DD"
          />

          <TextField
            fullWidth
            label={t("National ID (Optional)")}
            value={searchFilter.nationalId || ""}
            onChange={e =>
              setSearchFilter({ ...searchFilter, nationalId: e.target.value })
            }
          />

          <TextField
            fullWidth
            label={t("Passport Number (Optional)")}
            value={searchFilter.passportNumber || ""}
            onChange={e =>
              setSearchFilter({
                ...searchFilter,
                passportNumber: e.target.value,
              })
            }
          />
        </div>
      </Dialog>
    </div>
  );
};
