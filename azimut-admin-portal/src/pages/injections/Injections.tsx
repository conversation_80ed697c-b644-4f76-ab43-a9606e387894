import "../../assets/styles/reason.css";

import { CheckBox } from "@mui/icons-material";
import { DialogActions, IconButton, MenuItem, TextField } from "@mui/material";
import { DataGrid, GridColDef } from "@mui/x-data-grid";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { Dayjs } from "dayjs";
import { useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

import Button from "../../components/shared/Button/Button";
import Dialog from "../../components/shared/Dialog/Dialog";
import { azimutApi } from "../../hooks/azimutApi";

const syncedStates: Record<number, any> = {
  "-1": {
    label: "Show all",
  },
  "1": {
    label: "Yes",
  },
  "2": {
    label: "No",
  },
};

export const Injections = () => {
  const [openSubmit, setOpenSubmit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentTransactionId, setCurrentTransactionId] = useState<number>();
  const [submitTransaction] = azimutApi.useRetryInjectMutation();

  const [startDate, setStartDate] = useState<Dayjs | null>(null);
  const [endDate, setEndDate] = useState<Dayjs | null>(null);
  const [synced, setSynced] = useState<string>("2");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const { t } = useTranslation("common");

  const { data: transactions, isFetching } = azimutApi.useGetTransactionsQuery({
    startDate: startDate ? startDate.toDate().toISOString() : undefined,
    endDate: endDate ? endDate.toDate().toISOString() : undefined,
    synced,
    page,
    rowsPerPage,
  });

  const rowCountRef = useRef(transactions?.numberOfItems || 0);

  const rowCount = useMemo(() => {
    if (transactions?.numberOfItems !== undefined) {
      rowCountRef.current = transactions.numberOfItems;
    }
    return rowCountRef.current;
  }, [transactions?.numberOfItems]);

  const columns: GridColDef[] = [
    // { field: "user.id", headerName: "User ID" },
    {
      field: "userPhone",
      headerName: "Phone Number",
      disableColumnMenu: true,
      sortable: false,
      width: 150,
    },
    {
      field: "emailAddress",
      headerName: "Email",
      disableColumnMenu: true,
      sortable: false,
      width: 250,
    },
    {
      field: "orderValue",
      headerName: "Amount",
      disableColumnMenu: true,
      sortable: false,
      width: 100,
    },
    {
      field: "createdAt",
      headerName: "Date",
      width: 100,
      disableColumnMenu: true,
      sortable: false,
      valueGetter: (e: any) => e.substring(0, 10),
    },
    {
      field: "referenceTransactionId",
      headerName: "Paytabs Reference",
      disableColumnMenu: true,
      sortable: false,
      width: 200,
    },
    {
      field: "id",
      headerName: "",
      disableColumnMenu: true,
      disableReorder: true,
      sortable: false,
      renderCell: a => (
        <div className="absolute right-0">
          {a.row.transactionStatus && (
            <IconButton
              aria-label="Resubmit"
              onClick={() => {
                setCurrentTransactionId(a.value);
                setOpenSubmit(true);
              }}
            >
              <CheckBox color="success" />
            </IconButton>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="container">
      <h1>{t("Injections")}</h1>
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <DatePicker
          label="Start Date"
          value={startDate}
          onChange={(e: any) => {
            setStartDate(e);
            setPage(0);
          }}
        />
        <DatePicker
          label="End Date"
          value={endDate}
          onChange={(e: any) => {
            setEndDate(e);
            setPage(0);
          }}
        />
      </LocalizationProvider>
      <TextField
        className="mb-3"
        sx={{ width: 250 }}
        select
        label={t("Sent to FITS")}
        size="small"
        value={synced}
        onChange={e => {
          setSynced(e.target.value);
          setPage(0);
        }}
      >
        {Object.entries(syncedStates).map(option => (
          <MenuItem
            key={option[0]}
            value={option[0]}
          >
            {t(option[1].label)}
          </MenuItem>
        ))}
      </TextField>
      <div className="mt-5">
        <DataGrid
          rows={transactions?.dataList.filter(e => e) || []}
          columns={columns}
          disableRowSelectionOnClick
          pageSizeOptions={[5, 10, 25]}
          rowCount={rowCount}
          loading={isFetching}
          paginationModel={{
            page,
            pageSize: rowsPerPage,
          }}
          paginationMode="server"
          onPaginationModelChange={e => {
            setPage(e.page);
            setRowsPerPage(e.pageSize);
          }}
        />
      </div>
      <Dialog
        open={openSubmit}
        onClose={() => setOpenSubmit(false)}
        title={t("Retry Injection")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              onClick={async () => {
                setLoading(true);
                await submitTransaction(currentTransactionId!);
                setLoading(false);
                setOpenSubmit(false);
              }}
            >
              {t("Yes, Submit")}
            </Button>
            <Button
              variant="text"
              onClick={() => {
                setOpenSubmit(false);
              }}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        {t("Are you sure you want to resend this transaction to FITS now ?")}
      </Dialog>
    </div>
  );
};
