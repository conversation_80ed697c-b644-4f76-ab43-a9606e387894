import "../../assets/styles/reason.css";

import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import { DialogActions, IconButton, TextField } from "@mui/material";
import { DataGrid, GridColDef } from "@mui/x-data-grid";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useDebouncedCallback } from "use-debounce";

import Button from "../../components/shared/Button/Button";
import Dialog from "../../components/shared/Dialog/Dialog";
import { azimutApi } from "../../hooks/azimutApi";

export const ReasonsList = () => {
  const [search, setSearch] = useState("");
  const [openEdit, setOpenEdit] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentReasonId, setCurrentReasonId] = useState<number>();
  const [deleteReason] = azimutApi.useDeleteReasonMutation();
  const [editReason] = azimutApi.useEditReasonMutation();
  const [addReason] = azimutApi.useAddReasonMutation();
  const [english, setEnglish] = useState("");
  const [arabic, setArabic] = useState("");
  const { t } = useTranslation("common");
  const { i18n } = useTranslation();

  const { data: rejectionReasons } = azimutApi.useGetReasonsQuery(search);

  const handleSearch = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setSearch(e.target.value);
    },
    1000
  );

  const handleChangeArabic = (e: React.ChangeEvent<HTMLInputElement>) =>
    setArabic(e.target.value);

  const handleChangeEnglish = (e: React.ChangeEvent<HTMLInputElement>) =>
    setEnglish(e.target.value);

  const columns: GridColDef[] = [
    {
      field: i18n.language == "en" ? "reason" : "reasonAr",
      headerName: t("reason"),
      disableColumnMenu: true,
      width: 800,
    },
    {
      field: "reasonType",
      headerName: t("reason Case"),
      disableColumnMenu: true,
      width: 150,
    },
    {
      field: "id",
      headerName: "",
      disableColumnMenu: true,
      disableReorder: true,
      sortable: false,
      renderCell: a => (
        <div className="absolute right-0">
          <IconButton
            aria-label="edit"
            onClick={() => {
              setCurrentReasonId(a.value);
              setArabic(a.row.reasonAr);
              setEnglish(a.row.reason);
              setIsEdit(true);
              setOpenEdit(true);
            }}
          >
            <EditIcon />
          </IconButton>
          <IconButton
            aria-label="delete"
            onClick={() => {
              setCurrentReasonId(a.value);
              setOpenDelete(true);
            }}
          >
            <DeleteIcon color="error" />
          </IconButton>
        </div>
      ),
    },
  ];

  return (
    <div className="container">
      <h1>{t("KYC Rejection Reasons")}</h1>
      <Button
        className="float-right"
        onClick={() => {
          setIsEdit(false);
          setArabic("");
          setEnglish("");
          setOpenEdit(true);
        }}
      >
        {t("Add new Reason")}
      </Button>
      <TextField
        label={t("Search Reason")}
        size="small"
        onChange={handleSearch}
      />
      <div className="mt-5">
        <DataGrid
          rows={rejectionReasons}
          columns={columns}
          disableRowSelectionOnClick
          pageSizeOptions={[5, 10, 25]}
        />
      </div>
      <Dialog
        open={openDelete}
        onClose={() => setOpenDelete(false)}
        title={t("Delete Reason")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              onClick={async () => {
                setLoading(true);
                await deleteReason(
                  rejectionReasons?.find(e => e.id == currentReasonId)!
                );
                setLoading(false);
                setOpenDelete(false);
              }}
            >
              {t("Yes, Delete")}
            </Button>
            <Button
              variant="text"
              onClick={() => {
                setOpenDelete(false);
              }}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        {t("Are you sure you want to delete this reason ?")}
      </Dialog>
      <Dialog
        open={openEdit}
        onClose={() => setOpenEdit(false)}
        title={isEdit ? t("Edit Reason") : t("Add Reason")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              onClick={async () => {
                setLoading(true);
                await (isEdit
                  ? editReason({
                      id: currentReasonId!,
                      reason: english,
                      reasonAr: arabic,
                    })
                  : addReason({
                      reason: english,
                      reasonAr: arabic,
                    }));
                setLoading(false);
                setOpenEdit(false);
              }}
            >
              {t("Submit")}
            </Button>
            <Button
              variant="text"
              onClick={() => {
                setOpenEdit(false);
              }}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        <div className="mb-3">English</div>
        <TextField
          label={t("Reason")}
          value={english}
          onChange={handleChangeEnglish}
        />
        <div className="my-3">Arabic</div>
        <TextField
          label={t("Reason")}
          value={arabic}
          onChange={handleChangeArabic}
        />
      </Dialog>
    </div>
  );
};
