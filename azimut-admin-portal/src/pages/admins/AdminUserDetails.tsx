import "../../assets/styles/reason.css";

import DeleteIcon from "@mui/icons-material/Delete";
import UploadFileIcon from "@mui/icons-material/UploadFile";
import {
  Breadcrumbs,
  DialogActions,
  IconButton,
  TextField,
  Typography,
} from "@mui/material";
import { DataGrid, GridColDef } from "@mui/x-data-grid";
import Papa from "papaparse";
import { useRef, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { Link, useParams } from "react-router-dom";
import { read, utils } from "xlsx";

import Button from "../../components/shared/Button/Button";
import Dialog from "../../components/shared/Dialog/Dialog";
import Loader from "../../components/shared/Spinner/Loader";
import { azimutAdminApi } from "../../hooks/adminUsersApi";

export const AdminUserDetails = () => {
  const params = useParams();
  const adminUserId = parseInt(params.id!);
  const { t } = useTranslation("common");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [openDelete, setOpenDelete] = useState(false);
  const [openUpload, setOpenUpload] = useState(false);
  const [currentMappingId, setCurrentMappingId] = useState<number>();
  const [userPhoneFilter, setUserPhoneFilter] = useState("");
  const [userIdFilter, setUserIdFilter] = useState("");
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [uploadedUserIds, setUploadedUserIds] = useState<string[]>([]);
  const [pageNumber, setPageNumber] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [loading, setLoading] = useState(false);

  const { data: adminUsers } = azimutAdminApi.useGetUsersQuery();
  const currentAdmin = adminUsers?.find(user => user.id === adminUserId);

  const searchesAndFilters = [
    ...(userPhoneFilter
      ? [
          {
            key: "userPhone",
            value: userPhoneFilter,
            operation: "search",
            parentColumn: "user",
          },
        ]
      : []),
    ...(userIdFilter
      ? [
          {
            key: "userId",
            value: userIdFilter,
            operation: "search",
            parentColumn: "user",
          },
        ]
      : []),
  ];

  const { data: mappingsData, isLoading } =
    azimutAdminApi.useGetMappingsByAdminQuery({
      adminUserId,
      filters: {
        pageNumber,
        pageSize,
        sortingParam: "createdAt",
        asc: false,
        searchesAndFilters,
      },
    });

  const [deleteMapping] = azimutAdminApi.useDeleteUserMappingMutation();
  const [createMappings] = azimutAdminApi.useCreateUserMappingMutation();

  const handleDeleteMapping = async () => {
    if (!currentMappingId) return;

    setLoading(true);
    try {
      await deleteMapping(currentMappingId).unwrap();
      toast.success(t("Assignment deleted successfully"));
      setOpenDelete(false);
    } catch (error: any) {
      toast.error(error?.data?.message || t("Failed to delete assignment"));
    } finally {
      setLoading(false);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (
        !file.name.endsWith(".csv") &&
        !file.name.endsWith(".xlsx") &&
        file.type !== "text/csv"
      ) {
        toast.error(t("Please upload a CSV or XLSX file"));
        return;
      }
      setCsvFile(file);

      // Parse the file immediately when selected
      try {
        // Parse XLSX using xlsx
        if (file.name.endsWith(".xlsx")) {
          const data = await file.arrayBuffer();
          const workbook = read(data, { type: "buffer" });
          const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
          const csv = utils.sheet_to_csv(firstSheet);

          // Parse CSV using papaparse
          Papa.parse(csv as string, {
            header: true,
            skipEmptyLines: true,
            complete: results => {
              const userIds = results.data
                .filter((row: any) => row["Nid/Passport"])
                .map((row: any) => row["Nid/Passport"]);
              setUploadedUserIds(userIds);
            },
          });
        } else if (file.name.endsWith(".csv")) {
          const text = await file.text();

          // Parse CSV using papaparse
          Papa.parse(text, {
            header: true,
            skipEmptyLines: true,
            complete: results => {
              const userIds = results.data
                .filter((row: any) => row["Nid/Passport"])
                .map((row: any) => row["Nid/Passport"]);
              setUploadedUserIds(userIds);
            },
          });
        }
      } catch (error) {
        toast.error(t("Failed to parse file"));
        setCsvFile(null);
        setUploadedUserIds([]);
      }
    }
  };

  const handleUploadCsv = async () => {
    if (!csvFile) {
      toast.error(t("Please select a CSV or XLSX file"));
      return;
    }

    if (uploadedUserIds.length === 0) {
      toast.error(t("No valid clients found in file"));
      return;
    }

    setLoading(true);
    try {
      await createMappings({
        adminUserId,
        userIds: uploadedUserIds,
      }).unwrap();

      toast.success(t("Assignment created successfully"));
      setOpenUpload(false);
      setCsvFile(null);
      setUploadedUserIds([]);
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error: any) {
      toast.error(error?.data?.message || t("Failed to create assignment"));
    } finally {
      setLoading(false);
    }
  };

  const columns: GridColDef[] = [
    {
      field: "a",
      headerName: t("Client Name"),
      disableColumnMenu: true,
      width: 150,
      renderCell: params => params.row.user?.nickName || params.value,
    },
    {
      field: "b",
      headerName: t("Email"),
      disableColumnMenu: true,
      width: 200,
      renderCell: params => params.row.user?.emailAddress || "-",
    },
    {
      field: "userPhone",
      headerName: t("Phone Number"),
      disableColumnMenu: true,
      width: 200,
      renderCell: params => params.row.user?.userPhone || "-",
    },
    {
      field: "userId",
      headerName: t("NID/Passport"),
      disableColumnMenu: true,
      width: 150,
      renderCell: params => params.row.user?.userId || params.value,
    },
    {
      field: "createdBy",
      headerName: t("Created By"),
      disableColumnMenu: true,
      width: 200,
      renderCell: params => params.row.createdBy?.fullName || "-",
    },
    {
      field: "createdAt",
      headerName: t("Created At"),
      disableColumnMenu: true,
      width: 200,
      renderCell: params =>
        params.value ? new Date(params.value).toLocaleString() : "-",
    },
    {
      field: "id",
      headerName: "",
      disableColumnMenu: true,
      disableReorder: true,
      sortable: false,
      width: 100,
      renderCell: params => (
        <div className="absolute right-0">
          <IconButton
            aria-label="delete"
            onClick={() => {
              setCurrentMappingId(params.value);
              setOpenDelete(true);
            }}
          >
            <DeleteIcon color="error" />
          </IconButton>
        </div>
      ),
    },
  ];

  if (isLoading) {
    return <Loader />;
  }

  return (
    <div className="container">
      <Breadcrumbs
        aria-label="breadcrumb"
        separator="›"
        sx={{ margin: 2 }}
      >
        <Link to={"/users"}>{t("Portal Users")}</Link>
        <Typography color="text.primary">{currentAdmin?.fullName}</Typography>
      </Breadcrumbs>

      <div className="flex justify-between items-center mb-4">
        <h1>
          {t("Client Assignments for")} {currentAdmin?.fullName}
        </h1>
        <Button
          startIcon={<UploadFileIcon />}
          onClick={() => setOpenUpload(true)}
        >
          {t("Upload CSV")}
        </Button>
      </div>

      <div className="flex gap-4 mb-4">
        <TextField
          label={t("Client Phone")}
          value={userPhoneFilter}
          onChange={e => setUserPhoneFilter(e.target.value)}
          size="small"
        />
        <TextField
          label={t("Client ID")}
          value={userIdFilter}
          onChange={e => setUserIdFilter(e.target.value)}
          size="small"
        />
      </div>

      <div className="mt-5">
        <DataGrid
          rows={mappingsData?.dataList || []}
          columns={columns}
          disableRowSelectionOnClick
          paginationMode="server"
          rowCount={mappingsData?.numberOfItems || 0}
          paginationModel={{
            page: pageNumber - 1,
            pageSize,
          }}
          onPaginationModelChange={model => {
            setPageNumber(model.page + 1);
            setPageSize(model.pageSize);
          }}
          pageSizeOptions={[5, 10, 25]}
        />
      </div>

      <Dialog
        open={openDelete}
        onClose={() => setOpenDelete(false)}
        title={t("Delete Assignment")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              onClick={handleDeleteMapping}
            >
              {t("Yes, Delete")}
            </Button>
            <Button
              variant="text"
              onClick={() => setOpenDelete(false)}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        {t("Are you sure you want to delete this assignment?")}
      </Dialog>

      <Dialog
        open={openUpload}
        onClose={() => {
          setOpenUpload(false);
          setCsvFile(null);
          setUploadedUserIds([]);
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        }}
        title={t("Upload CSV/XLSX File")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              disabled={!csvFile || uploadedUserIds.length === 0}
              onClick={handleUploadCsv}
            >
              {t("Submit")}
            </Button>
            <Button
              variant="text"
              onClick={() => {
                setOpenUpload(false);
                setCsvFile(null);
                setUploadedUserIds([]);
                if (fileInputRef.current) {
                  fileInputRef.current.value = "";
                }
              }}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        <div className="mb-3">
          {t("Upload a CSV or XLSX file containing Nid/Passport column")}
        </div>
        <input
          ref={fileInputRef}
          type="file"
          accept=".csv,.xlsx"
          onChange={handleFileChange}
          className="mb-3"
        />
        {csvFile && (
          <div className="text-sm text-gray-600 mb-2">
            {t("Selected file")}: {csvFile.name}
          </div>
        )}
        {uploadedUserIds.length > 0 && (
          <div className="text-sm text-green-600 font-semibold">
            {`Found ${uploadedUserIds.length} clients in file`}
          </div>
        )}
      </Dialog>
    </div>
  );
};
