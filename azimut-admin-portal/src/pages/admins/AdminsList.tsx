import "../../assets/styles/reason.css";

import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import {
  Checkbox,
  DialogActions,
  FormControlLabel,
  IconButton,
  MenuItem,
  Select,
  TextField,
} from "@mui/material";
import { DataGrid, GridColDef } from "@mui/x-data-grid";
import { Fragment, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

import Button from "../../components/shared/Button/Button";
import Dialog from "../../components/shared/Dialog/Dialog";
import { azimutAdminApi } from "../../hooks/adminUsersApi";

export const AdminsList = () => {
  const navigate = useNavigate();
  const [openEdit, setOpenEdit] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentAdminId, setCurrentAdminId] = useState<number>();
  const [deleteUser] = azimutAdminApi.useDeleteUserMutation();
  const [editUser] = azimutAdminApi.useEditUserMutation();
  const [addUser] = azimutAdminApi.useAddUserMutation();
  const { data: roles } = azimutAdminApi.useGetRolesQuery();
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [roleId, setRoleId] = useState<number>();
  const [isFA, setIsFA] = useState(false);
  const { t } = useTranslation("common");

  const { data: adminUsers } = azimutAdminApi.useGetUsersQuery();
  const handleChangeEmail = (e: React.ChangeEvent<HTMLInputElement>) =>
    setEmail(e.target.value);

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleChangeName = (e: React.ChangeEvent<HTMLInputElement>) =>
    setName(e.target.value);

  const columns: GridColDef[] = [
    {
      field: "fullName",
      headerName: t("Name"),
      disableColumnMenu: true,
      width: 200,
    },
    {
      field: "emailAddress",
      headerName: t("Email"),
      disableColumnMenu: true,
      width: 200,
    },
    {
      field: "roleId",
      headerName: t("Role"),
      disableColumnMenu: true,
      renderCell: a => <div>{roles?.find(e => e.id == a.value)?.name}</div>,
      width: 200,
    },
    {
      field: "id",
      headerName: "",
      disableColumnMenu: true,
      disableReorder: true,
      sortable: false,
      renderCell: a => (
        <div className="absolute right-0">
          <IconButton
            aria-label="edit"
            onClick={e => {
              e.stopPropagation();
              setCurrentAdminId(a.value);
              console.log(a);
              setEmail(a.row.emailAddress);
              setName(a.row.fullName);
              setRoleId(a.row.roleId);
              setIsFA(a.row.isFA || false);
              setIsEdit(true);
              setOpenEdit(true);
            }}
          >
            <EditIcon />
          </IconButton>
          <IconButton
            aria-label="delete"
            onClick={e => {
              e.stopPropagation();
              setCurrentAdminId(a.value);
              setOpenDelete(true);
            }}
          >
            <DeleteIcon color="error" />
          </IconButton>
        </div>
      ),
    },
  ];

  return (
    <div className="container">
      <Button
        className="float-right"
        onClick={() => {
          setIsEdit(false);
          setEmail("");
          setName("");
          setIsFA(false);
          setOpenEdit(true);
        }}
      >
        {t("Add new User")}
      </Button>
      <h1>{t("Portal Users")}</h1>
      <div className="mt-5">
        <DataGrid
          rows={adminUsers}
          columns={columns}
          disableRowSelectionOnClick
          pageSizeOptions={[5, 10, 25]}
          onRowClick={params => navigate(`/users/${params.id}`)}
          sx={{
            "& .MuiDataGrid-row": {
              cursor: "pointer",
            },
          }}
        />
      </div>
      <Dialog
        open={openDelete}
        onClose={() => setOpenDelete(false)}
        title={t("Delete User")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              onClick={async () => {
                setLoading(true);
                await deleteUser(
                  adminUsers?.find(e => e.id == currentAdminId)!
                );
                setLoading(false);
                setOpenDelete(false);
              }}
            >
              {t("Yes, Delete")}
            </Button>
            <Button
              variant="text"
              onClick={() => {
                setOpenDelete(false);
              }}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        {t("Are you sure you want to delete this user ?")}
      </Dialog>
      <Dialog
        open={openEdit}
        onClose={() => setOpenEdit(false)}
        title={isEdit ? t("Edit User") : t("Add USer")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              onClick={async () => {
                if (!name) {
                  toast.error("Name is required");
                  return;
                }
                if (!email) {
                  toast.error("Email is required");
                  return;
                }
                if (!isEdit && !password) {
                  toast.error("Password is required");
                  return;
                }
                if (!validateEmail(email)) {
                  toast.error("Please enter a valid email address.");
                  return;
                }
                setLoading(true);
                const res: any = await (isEdit
                  ? editUser({
                      id: currentAdminId!,
                      fullName: name,
                      emailAddress: email,
                      roleId,
                      isFA,
                    })
                  : addUser({
                      fullName: name,
                      emailAddress: email,
                      password,
                      roleId,
                      isFA,
                    }));
                if (res.error) {
                  toast.error(res.error.data.message);
                } else {
                  setOpenEdit(false);
                }
                setLoading(false);
              }}
            >
              {t("Submit")}
            </Button>
            <Button
              variant="text"
              onClick={() => {
                setOpenEdit(false);
              }}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        <div className="mb-3">{t("Name")}</div>
        <TextField
          value={name}
          onChange={handleChangeName}
        />
        <div className="my-3">{t("Email")}</div>
        <TextField
          value={email}
          onChange={handleChangeEmail}
        />
        <div className="my-3">{t("Role")}</div>
        <Select
          sx={{ minWidth: "200px" }}
          defaultValue={roleId}
          onChange={event => setRoleId(parseInt(event.target.value.toString()))}
        >
          {roles?.map(role => (
            <MenuItem
              key={role.id}
              value={role.id}
            >
              {role.name}
            </MenuItem>
          ))}
        </Select>
        <div className="my-3">
          <FormControlLabel
            control={
              <Checkbox
                checked={isFA}
                onChange={e => setIsFA(e.target.checked)}
              />
            }
            label={t("Financial Advisor")}
          />
        </div>
        {!isEdit && (
          <Fragment>
            {" "}
            <div className="my-3">{t("Password")}</div>
            <TextField
              type="password"
              value={password}
              onChange={e => setPassword(e.target.value)}
            />
          </Fragment>
        )}
      </Dialog>
    </div>
  );
};
