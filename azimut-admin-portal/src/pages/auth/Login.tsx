import { Alert } from "@mui/material";
import { Formik } from "formik";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import * as Yup from "yup";

import { signIn } from "@/api/services/auth";
import Button from "@/components/shared/Button/Button";
import InputField from "@/components/shared/InputField/InputField";
import { handleFormikSubmission } from "@/helpers/forms";
import AuthLayout from "@/layouts/AuthLayout";

import { UserCredentials } from "../../hooks/types";
import { useAuth } from "../../hooks/useAuth";

function LoginPage() {
  const navigate = useNavigate();
  const [t] = useTranslation("common");
  const { setUser } = useAuth()!;
  const [error, setError] = useState<string | null>(null);

  async function handleLogin({ email, password }: UserCredentials) {
    setError(null);

    await signIn({ email, password })
      .then(signedInUser => {
        signedInUser.user.permissions =
          (signedInUser.user.permissions as string)?.split(",") || [];
        setUser(signedInUser);
        navigate("/home", {
          replace: true,
        });
      })
      .catch(err => {
        setError(err.message);
      });
  }

  return (
    <AuthLayout
      title={t("Log in")}
      description={t("Welcome to Azimut Dashboard")}
    >
      {error && (
        <Alert
          className="mb-8"
          severity="error"
        >
          {error}
        </Alert>
      )}

      <Formik
        initialValues={{
          email: "",
          password: "",
        }}
        validationSchema={Yup.object({
          email: Yup.string().email().required().label(t("email")),
          password: Yup.string().min(6).required().label(t("password")),
        })}
        onSubmit={() => {}}
      >
        {formik => (
          <form
            className="grid w-full grid-cols-1 gap-4"
            noValidate
            onSubmit={async e => {
              e.preventDefault();

              handleFormikSubmission(e.currentTarget, formik, async () => {
                await handleLogin(formik.values);
              });
            }}
          >
            <InputField
              name="email"
              label={t("Email")}
              type="email"
              required
            />
            <InputField
              name="password"
              label={t("Password")}
              type="password"
              required
            />
            <Button
              size="large"
              type="submit"
              className="h-[56px] text-base"
              // loading={!useIsAuthenticated()}
            >
              {t("Log in")}
            </Button>
          </form>
        )}
      </Formik>
    </AuthLayout>
  );
}

export default LoginPage;
