import { <PERSON>, Container, <PERSON><PERSON>ield, Typography } from "@mui/material";
import { cloneDeep } from "lodash";
import { useState } from "react";

import Button from "../../components/shared/Button/Button";
import { azimutApi } from "../../hooks/azimutApi";
import { useAuth } from "../../hooks/useAuth";

const EditProfile = () => {
  const { user, setUser } = useAuth()!;
  const [username, setUsername] = useState(user.user.fullName);
  const [email, setEmail] = useState(user.user.emailAddress);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  const [editProfileApi, { isLoading }] = azimutApi.useEditProfileMutation();

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    setErrorMessage("");
    setSuccessMessage("");

    if (!validateEmail(email)) {
      setErrorMessage("Please enter a valid email address.");
      return;
    }

    const res: any = await editProfileApi({ email, name: username });
    if (res.error) {
      setErrorMessage(res.error.data.message);
    } else {
      const newUser = cloneDeep(user);
      newUser.user.emailAddress = email;
      newUser.user.fullName = username;
      setUser(newUser);
      setSuccessMessage("User information updated successfully!");
    }
  };

  return (
    <Container maxWidth="sm">
      <Box sx={{ mt: 4 }}>
        <Typography
          variant="h4"
          component="h2"
          gutterBottom
        >
          Change User Information
        </Typography>
        <form onSubmit={handleSubmit}>
          <TextField
            label="Name"
            variant="outlined"
            fullWidth
            margin="normal"
            value={username}
            onChange={e => setUsername(e.target.value)}
            required
          />
          <TextField
            label="Email"
            variant="outlined"
            fullWidth
            margin="normal"
            value={email}
            onChange={e => setEmail(e.target.value)}
            required
          />
          {errorMessage && (
            <Typography
              color="error"
              variant="body2"
            >
              {errorMessage}
            </Typography>
          )}
          {successMessage && (
            <Typography
              color="success"
              variant="body2"
            >
              {successMessage}
            </Typography>
          )}
          <Button
            type="submit"
            loading={isLoading}
            variant="contained"
            color="primary"
            fullWidth
            sx={{ mt: 2 }}
          >
            Update Information
          </Button>
        </form>
      </Box>
    </Container>
  );
};

export default EditProfile;
