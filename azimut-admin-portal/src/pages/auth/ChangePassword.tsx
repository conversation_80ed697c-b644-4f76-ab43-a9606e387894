import { Box, Container, TextField, Typography } from "@mui/material";
import { useState } from "react";

import Button from "../../components/shared/Button/Button";
import { azimutApi } from "../../hooks/azimutApi";

const ChangePassword = () => {
  const [oldPassword, setOldPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  const [changePasswordApi, { isLoading }] =
    azimutApi.useChangePasswordMutation();

  const validatePassword = (password: string) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumber = /[0-9]/.test(password);

    return (
      password.length >= minLength && hasUpperCase && hasLowerCase && hasNumber
    );
  };

  const handleSubmit = async (e: any) => {
    e.preventDefault();
    setErrorMessage("");
    setSuccessMessage("");

    if (newPassword !== confirmNewPassword) {
      setErrorMessage("New passwords do not match.");
      return;
    }

    if (!validatePassword(newPassword)) {
      setErrorMessage("New password does not meet the criteria.");
      return;
    }

    const res: any = await changePasswordApi({
      oldPassword,
      password: newPassword,
    });
    if (res.error) {
      setErrorMessage(res.error.data.message);
    } else {
      setSuccessMessage("Password changed successfully!");
    }
  };

  return (
    <Container maxWidth="sm">
      <Box sx={{ mt: 4 }}>
        <Typography
          variant="h4"
          component="h2"
          gutterBottom
        >
          Change Password
        </Typography>
        <form onSubmit={handleSubmit}>
          <TextField
            label="Old Password"
            variant="outlined"
            type="password"
            fullWidth
            margin="normal"
            value={oldPassword}
            onChange={e => setOldPassword(e.target.value)}
            required
          />
          <TextField
            label="New Password"
            variant="outlined"
            type="password"
            fullWidth
            margin="normal"
            value={newPassword}
            onChange={e => setNewPassword(e.target.value)}
            required
          />
          <p> Contain at least 8 characters </p>
          <p> Contain at least 1 uppercase </p>
          <p> Contain at least 1 number </p>
          <p> Contain at least 1 lowercase </p>
          <TextField
            label="Confirm New Password"
            variant="outlined"
            type="password"
            fullWidth
            margin="normal"
            value={confirmNewPassword}
            onChange={e => setConfirmNewPassword(e.target.value)}
            required
          />
          {errorMessage && (
            <Typography
              color="error"
              variant="body2"
            >
              {errorMessage}
            </Typography>
          )}
          {successMessage && (
            <Typography
              color="success"
              variant="body2"
            >
              {successMessage}
            </Typography>
          )}
          <Button
            type="submit"
            loading={isLoading}
            variant="contained"
            color="primary"
            fullWidth
            sx={{ mt: 2 }}
          >
            Change Password
          </Button>
        </form>
      </Box>
    </Container>
  );
};

export default ChangePassword;
