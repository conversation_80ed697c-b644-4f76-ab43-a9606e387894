import {
  Button,
  <PERSON>alog,
  <PERSON>alogA<PERSON>,
  <PERSON>alogContent,
  DialogTitle,
  Typography,
} from "@mui/material";
import { jwtDecode } from "jwt-decode";
import { useEffect, useState } from "react";

import { refreshAuthToken } from "../../api/services/auth";
import { useAuth } from "../../hooks/useAuth";

export const TokenExpiring = () => {
  const { user, setUser } = useAuth()!;
  const [open, setOpen] = useState(false);
  const [remainingTime, setRemainingTime] = useState(60);
  const [intervalPointer, setIntervalPointer] = useState<NodeJS.Timeout>();

  useEffect(() => {
    if (user) {
      console.log("initializing interval");
      clearInterval(intervalPointer);
      const interval = setInterval(async () => {
        if (user) {
          const decoded = jwtDecode(user.auth);
          const remaining = decoded.exp! - Date.now() / 1000;
          setRemainingTime(Math.floor(remaining));
          const lastActivity = localStorage.getItem("lastActivity")
            ? new Date(localStorage.getItem("lastActivity")!)
            : new Date();
          const now = new Date();
          if (
            remaining < 120 &&
            (now.getTime() - lastActivity.getTime()) / 1000 < 120 &&
            remaining > 0
          ) {
            const res = await refreshAuthToken();
            setUser({
              ...user,
              auth: res.auth,
              refreshToken: res.refreshToken,
            });
          } else {
            if (remaining < 60 && remaining > 0) {
              setOpen(true);
            } else if (remaining <= 0) {
              setUser(null);
              setOpen(false);
            }
          }
        }
      }, 10 * 1000);
      setIntervalPointer(interval);
    } else {
      clearInterval(intervalPointer);
    }
  }, [user]);

  useEffect(() => {
    if (open && user)
      if (remainingTime <= 0) {
        setUser(null);
        setOpen(false);
      } else {
        setTimeout(() => setRemainingTime(remainingTime - 1), 1000);
      }
  }, [remainingTime, user, open]);
  return (
    <Dialog
      keepMounted
      sx={{ "& .MuiDialog-paper": { width: "80%", maxHeight: 435 } }}
      open={open}
      maxWidth="xs"
    >
      <DialogTitle>Session will expire</DialogTitle>
      <DialogContent dividers>
        <Typography
          id="modal-modal-description"
          sx={{ mt: 2 }}
        >
          For your own protection, your session will expire in {remainingTime}
        </Typography>
      </DialogContent>
      <DialogActions>
        <Button
          className="popup-btn"
          onClick={async () => {
            const res = await refreshAuthToken();
            setUser({
              ...user,
              auth: res.auth,
              refreshToken: res.refreshToken,
            });
            setOpen(false);
          }}
        >
          Extend
        </Button>
        <Button
          onClick={() => {
            setUser(null);
            setOpen(false);
          }}
        >
          Logout
        </Button>
      </DialogActions>
    </Dialog>
  );
};
