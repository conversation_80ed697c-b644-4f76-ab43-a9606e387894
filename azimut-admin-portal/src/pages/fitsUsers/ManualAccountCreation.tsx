import {
  Autocomplete,
  Button,
  FormControl,
  FormControlLabel,
  FormHelperText,
  FormLabel,
  Radio,
  RadioGroup,
  TextField,
} from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { skipToken } from "@reduxjs/toolkit/query";
import { Dayjs } from "dayjs";
import { useFormik } from "formik";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import * as Yup from "yup";

import { azimutApi } from "../../hooks/azimutApi";

// interface LookupItem {
//   id: number;
//   englishName: string;
// }

const validationSchema = Yup.object().shape({
  firstName: Yup.string().required("Required"),
  lastName: Yup.string().required("Required"),
  address: Yup.string().required("Required"),
  email: Yup.string().email("Invalid email").required("Required"),
  idNumber: Yup.string().required("Required"),
  mobile: Yup.string().required("Required"),
  countryId: Yup.string().required("Required"),
  cityId: Yup.number().required("Required").positive("Required"),
  nationalityId: Yup.number().required("Required").positive("Required"),
  idTypeId: Yup.number().required("Required").positive("Required"),
  birthDate: Yup.date().required("Birth date is required"),
  idIssueDate: Yup.date().required("ID issue date is required"),
  idMaturityDate: Yup.date().required("ID maturity date is required"),
  gender: Yup.string().oneOf(["Male", "Female"]).required("Required"),
  occupation: Yup.string().required("Required"),
});

export default function ManualAccountCreation() {
  const { t } = useTranslation();
  const [contractFile, setContractFile] = useState<File | null>(null);

  // Mutations
  const [createUser] = azimutApi.useCreateFitsUserMutation();
  const [uploadContract] = azimutApi.useUploadFitsContractMutation();

  const navigate = useNavigate();

  // Lookups
  const { data: countries } = azimutApi.useGetLookupDataQuery({
    entityTypeId: 1,
  });
  const { data: nationalities } = azimutApi.useGetLookupDataQuery({
    entityTypeId: 4,
  });
  const { data: idTypes } = azimutApi.useGetLookupDataQuery({
    entityTypeId: 10,
  });
  const { data: referralCodesData } = azimutApi.useGetAllReferralCodesQuery();

  const referralCodes: string[] = referralCodesData?.map((r: any) => r.code);

  const formik = useFormik({
    initialValues: {
      firstName: "",
      lastName: "",
      address: "",
      email: "",
      idTypeId: "",
      idNumber: "",
      mobile: "",
      gender: "",
      occupation: "",
      countryId: "",
      cityId: "",
      nationalityId: "",
      birthDate: null,
      idIssueDate: null,
      idMaturityDate: null,
      contactName: "",
    },
    validationSchema,
    onSubmit: async values => {
      try {
        // Fetch lookup English names
        const country = countries?.countries.find(
          (c: any) => c.systemCountryCode === values.countryId
        )?.englishCountryName;
        const city = cities?.cities?.find(
          (c: any) => c.id === Number(values.cityId)
        )?.englishCityName;
        const nationality = nationalities?.nationalities.find(
          (n: any) => n.id === Number(values.nationalityId)
        )?.englishNationalityName;
        const idType = idTypes?.userTypes.find(
          (i: any) => i.id === Number(values.idTypeId)
        )?.idType;

        const payload = {
          firstName: values.firstName,
          lastName: values.lastName,
          address: values.address,
          email: values.email,
          idNumber: values.idNumber,
          mobile: values.mobile,
          occupation: values.occupation,
          country,
          city,
          nationality,
          idType,
          gender: values.gender,
          birthDate: formatDate(values.birthDate),
          idIssueDate: formatDate(values.idIssueDate),
          idMaturityDate: formatDate(values.idMaturityDate),
          contactName: values.contactName,
        };

        const userRes = await createUser(payload).unwrap();

        // Only upload contract if a file was selected
        if (contractFile) {
          await uploadContract({
            id: userRes.clientId,
            file: contractFile,
          }).unwrap();
        }

        toast.success(t("Account Created"));
      } catch (error: any) {
        toast.error(t("Creation Failed"));
      }
    },
  });

  // Cities depend on country
  const { data: cities, isFetching: loadingCities } =
    azimutApi.useGetLookupDataQuery(
      formik.values.countryId
        ? { entityTypeId: 2, countryId: Number(formik.values.countryId) }
        : skipToken
    );

  const formatDate = (date: Dayjs | null) => {
    if (!date) return "";
    return date.format("DD-MM-YYYY");
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size > 50 * 1024 * 1024) {
        toast.error(t("File Too Large"));
        return;
      }
      if (file.type !== "application/pdf") {
        toast.error(t("Must be PDF file"));
        return;
      }
      setContractFile(file);
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <div className="container mx-auto p-4 max-w-2xl">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold">
            {t("Manual Account Creation")}
          </h1>
          <Button
            variant="outlined"
            onClick={() => navigate("/fits-users")}
          >
            {t("Back")}
          </Button>
        </div>

        <form
          onSubmit={formik.handleSubmit}
          className="grid grid-cols-1 gap-4"
        >
          {/* Personal Info */}
          <div className="grid grid-cols-2 gap-4">
            <TextField
              label={t("First Name")}
              required
              {...formik.getFieldProps("firstName")}
              error={formik.touched.firstName && !!formik.errors.firstName}
              helperText={
                formik.touched.firstName && t(formik.errors.firstName as string)
              }
            />
            <TextField
              label={t("Last Name")}
              required
              {...formik.getFieldProps("lastName")}
              error={formik.touched.lastName && !!formik.errors.lastName}
              helperText={
                formik.touched.lastName && t(formik.errors.lastName as string)
              }
            />
          </div>

          <TextField
            label={t("Address")}
            required
            {...formik.getFieldProps("address")}
            error={formik.touched.address && !!formik.errors.address}
            helperText={
              formik.touched.address && t(formik.errors.address as string)
            }
          />

          <TextField
            label={t("Email")}
            required
            {...formik.getFieldProps("email")}
            error={formik.touched.email && !!formik.errors.email}
            helperText={
              formik.touched.email && t(formik.errors.email as string)
            }
          />

          <TextField
            label={t("Mobile")}
            required
            {...formik.getFieldProps("mobile")}
            error={formik.touched.mobile && !!formik.errors.mobile}
            helperText={
              formik.touched.mobile && t(formik.errors.mobile as string)
            }
          />

          <div className="grid grid-cols-2 gap-4">
            <Autocomplete
              disablePortal
              options={idTypes?.userTypes || []}
              getOptionLabel={(option: any) => option.idType}
              value={
                idTypes?.userTypes?.find(
                  (i: any) => i.id === Number(formik.values.idTypeId)
                ) || null
              }
              onChange={(event, value) => {
                formik.setFieldValue("idTypeId", value ? value.id : "");
              }}
              onBlur={formik.handleBlur}
              renderInput={params => (
                <TextField
                  {...params}
                  label={t("ID Type")}
                  name="idTypeId"
                  error={formik.touched.idTypeId && !!formik.errors.idTypeId}
                  helperText={
                    formik.touched.idTypeId &&
                    t(formik.errors.idTypeId as string)
                  }
                />
              )}
            />

            <TextField
              label={t("ID Number")}
              required
              {...formik.getFieldProps("idNumber")}
              error={formik.touched.idNumber && !!formik.errors.idNumber}
              helperText={
                formik.touched.idNumber && t(formik.errors.idNumber as string)
              }
            />
          </div>

          {/* Dates */}
          <div className="grid grid-cols-3 gap-4">
            <DatePicker
              label={t("Birth Date")}
              value={formik.values.birthDate}
              onChange={date => formik.setFieldValue("birthDate", date)}
              slotProps={{
                textField: {
                  onBlur: formik.handleBlur,
                  error: formik.touched.birthDate && !!formik.errors.birthDate,
                  helperText:
                    formik.touched.birthDate &&
                    t(formik.errors.birthDate as string),
                },
              }}
            />
            <DatePicker
              label={t("ID Issue Date")}
              value={formik.values.idIssueDate}
              onChange={date => formik.setFieldValue("idIssueDate", date)}
              slotProps={{
                textField: {
                  onBlur: formik.handleBlur,
                  error:
                    formik.touched.idIssueDate && !!formik.errors.idIssueDate,
                  helperText:
                    formik.touched.idIssueDate &&
                    t(formik.errors.idIssueDate as string),
                },
              }}
            />
            <DatePicker
              label={t("ID Maturity Date")}
              value={formik.values.idMaturityDate}
              onChange={date => formik.setFieldValue("idMaturityDate", date)}
              slotProps={{
                textField: {
                  onBlur: formik.handleBlur,
                  error:
                    formik.touched.idMaturityDate &&
                    !!formik.errors.idMaturityDate,
                  helperText:
                    formik.touched.idMaturityDate &&
                    t(formik.errors.idMaturityDate as string),
                },
              }}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Autocomplete
              disablePortal
              options={countries?.countries || []}
              getOptionLabel={(option: any) => option.englishCountryName}
              value={
                countries?.countries?.find(
                  (c: any) => c.systemCountryCode === formik.values.countryId
                ) || null
              }
              onChange={(event, value) => {
                formik.setFieldValue(
                  "countryId",
                  value ? value.systemCountryCode : ""
                );
                // Reset city when country changes
                formik.setFieldValue("cityId", "");
              }}
              onBlur={formik.handleBlur}
              renderInput={params => (
                <TextField
                  {...params}
                  label={t("Country")}
                  name="countryId"
                  error={formik.touched.countryId && !!formik.errors.countryId}
                  helperText={
                    formik.touched.countryId &&
                    t(formik.errors.countryId as string)
                  }
                />
              )}
            />

            <Autocomplete
              disablePortal
              options={cities?.cities || []}
              getOptionLabel={(option: any) => option.englishCityName}
              value={
                cities?.cities?.find(
                  (c: any) => c.id === Number(formik.values.cityId)
                ) || null
              }
              onChange={(event, value) => {
                formik.setFieldValue("cityId", value ? value.id : "");
              }}
              onBlur={formik.handleBlur}
              disabled={!formik.values.countryId || loadingCities}
              renderInput={params => (
                <TextField
                  {...params}
                  label={t("City")}
                  name="cityId"
                  error={formik.touched.cityId && !!formik.errors.cityId}
                  helperText={
                    formik.touched.cityId && t(formik.errors.cityId as string)
                  }
                />
              )}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Autocomplete
              disablePortal
              options={nationalities?.nationalities || []}
              getOptionLabel={(option: any) => option.englishNationalityName}
              value={
                nationalities?.nationalities?.find(
                  (n: any) => n.id === Number(formik.values.nationalityId)
                ) || null
              }
              onChange={(event, value) => {
                formik.setFieldValue("nationalityId", value ? value.id : "");
              }}
              onBlur={formik.handleBlur}
              renderInput={params => (
                <TextField
                  {...params}
                  label={t("Nationality")}
                  name="nationalityId"
                  error={
                    formik.touched.nationalityId &&
                    !!formik.errors.nationalityId
                  }
                  helperText={
                    formik.touched.nationalityId &&
                    t(formik.errors.nationalityId as string)
                  }
                />
              )}
            />

            <TextField
              label={t("Occupation")}
              {...formik.getFieldProps("occupation")}
              error={formik.touched.occupation && !!formik.errors.occupation}
              helperText={
                formik.touched.occupation &&
                t(formik.errors.occupation as string)
              }
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Autocomplete
              disablePortal
              options={referralCodes || []}
              onChange={(event, value) => {
                formik.setFieldValue("contactName", value || "");
              }}
              renderInput={params => (
                <TextField
                  {...params}
                  label={t("Referral")}
                  name="contactName"
                />
              )}
            />

            {/* Gender */}
            <FormControl
              component="fieldset"
              error={formik.touched.gender && !!formik.errors.gender}
            >
              <FormLabel component="legend">{t("Gender")}</FormLabel>
              <RadioGroup
                name="gender"
                value={formik.values.gender}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                row
              >
                <FormControlLabel
                  value="Male"
                  control={<Radio />}
                  label={t("Male")}
                />
                <FormControlLabel
                  value="Female"
                  control={<Radio />}
                  label={t("Female")}
                />
              </RadioGroup>
              {formik.touched.gender && formik.errors.gender && (
                <FormHelperText>
                  {t(formik.errors.gender as string)}
                </FormHelperText>
              )}
            </FormControl>
          </div>

          {/* File Upload */}
          <div>
            <div className="mb-3">Contract Scanned PDF</div>
            <input
              type="file"
              accept=".pdf"
              onChange={handleFileChange}
              className="block w-full text-sm text-gray-500
              file:mr-4 file:py-2 file:px-4
              file:rounded-full file:border-0
              file:text-sm file:font-semibold
              file:bg-primary-50 file:text-primary-700
              hover:file:bg-primary-100"
            />
          </div>

          <Button
            type="submit"
            variant="contained"
            size="large"
            className="mt-6 w-full"
            disabled={formik.isSubmitting}
          >
            {formik.isSubmitting ? t("Submitting") : t("Create Account")}
          </Button>
        </form>
      </div>
    </LocalizationProvider>
  );
}
