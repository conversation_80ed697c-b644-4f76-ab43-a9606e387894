import {
  Button,
  CircularProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
} from "@mui/material";
import { ChangeEvent, Fragment, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

import { azimutApi } from "../../hooks/azimutApi";

interface FitsUser {
  clientId: string;
  firstName: string;
  lastName: string;
  mobile: string;
  idNumber: string;
  [key: string]: any; // Allow for additional properties
}

export default function FitsUsersList() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useState({
    firstName: "",
    lastName: "",
    mobile: "",
    idNumber: "",
  });
  const [page, setPage] = useState(0); // 0-indexed for MUI
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [users, setUsers] = useState<FitsUser[]>([]);
  const [totalItems, setTotalItems] = useState(0);

  const { data, isFetching } = azimutApi.useFitsUsersSearchQuery({
    ...searchParams,
    page,
    size: rowsPerPage,
  });

  useEffect(() => {
    if (data) {
      // Handle different possible data structures
      if (data.dataList) {
        setUsers(data.dataList);
        setTotalItems(data.numberOfItems || data.dataList.length);
      } else {
        setUsers([]);
        setTotalItems(0);
      }
    }
  }, [data]);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setSearchParams(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleClick = (id: string) => {
    navigate(`/fits-users/${id}`);
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center justify-between">
        <Typography
          variant="h4"
          className="mb-6"
        >
          {t("FITS Users")}
        </Typography>
        <Button
          className="mb-6"
          onClick={() => navigate("/fits-users/create")}
        >
          {t("Create FITS User")}
        </Button>
      </div>

      {/* Search Form */}
      <Paper className="p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          <TextField
            label={t("First Name")}
            name="firstName"
            value={searchParams.firstName}
            onChange={handleInputChange}
            variant="outlined"
            size="small"
          />
          <TextField
            label={t("Last Name")}
            name="lastName"
            value={searchParams.lastName}
            onChange={handleInputChange}
            variant="outlined"
            size="small"
          />
          <TextField
            label={t("Mobile")}
            name="mobile"
            value={searchParams.mobile}
            onChange={handleInputChange}
            variant="outlined"
            size="small"
          />
          <TextField
            label={t("ID Number")}
            name="idNumber"
            value={searchParams.idNumber}
            onChange={handleInputChange}
            variant="outlined"
            size="small"
          />
        </div>
      </Paper>

      {/* Results Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>{t("First Name")}</TableCell>
                <TableCell>{t("Last Name")}</TableCell>
                <TableCell>{t("Mobile")}</TableCell>
                <TableCell>{t("ID Number")}</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isFetching ? (
                <TableRow>
                  <TableCell
                    colSpan={4}
                    align="center"
                  >
                    <CircularProgress className="my-4" />
                  </TableCell>
                </TableRow>
              ) : (
                <Fragment>
                  {users.map((user, index) => (
                    <TableRow
                      key={user.id || index}
                      hover
                      onClick={() =>
                        user.clientId && handleClick(user.clientId)
                      }
                      className="cursor-pointer"
                    >
                      <TableCell>{user.firstName || "-"}</TableCell>
                      <TableCell>{user.lastName || "-"}</TableCell>
                      <TableCell>{user.mobile || "-"}</TableCell>
                      <TableCell>{user.idNumber || "-"}</TableCell>
                    </TableRow>
                  ))}
                  {users.length === 0 && !isFetching && (
                    <TableRow>
                      <TableCell
                        colSpan={4}
                        align="center"
                        className="py-8"
                      >
                        {t("No users found")}
                      </TableCell>
                    </TableRow>
                  )}
                </Fragment>
              )}
            </TableBody>
          </Table>
        </TableContainer>

        {/* Pagination */}
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={totalItems}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          disabled={isFetching}
        />
      </Paper>
    </div>
  );
}
