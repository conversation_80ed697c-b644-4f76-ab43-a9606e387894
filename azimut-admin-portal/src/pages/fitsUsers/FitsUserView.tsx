import {
  Autocomplete,
  Button,
  CircularProgress,
  FormControl,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
} from "@mui/material";
import { ChangeEvent, FormEvent, useRef, useState } from "react";
import { toast } from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";

import { azimutApi } from "../../hooks/azimutApi";
import { UserBankAccount } from "../../hooks/types";

export default function FitsUserView() {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const { data: userData } = azimutApi.useGetFitsUserQuery(id!);

  const { data: currencies } = azimutApi.useGetLookupDataQuery({
    entityTypeId: 11,
  });
  const { data: banks } = azimutApi.useGetLookupDataQuery({
    entityTypeId: 12,
  });
  const { data: walletBanks } = azimutApi.useGetLookupDataQuery({
    entityTypeId: 12,
    bankType: "WALLET",
  });

  // State for contract management
  const [isDownloading, setIsDownloading] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // State for bank account form
  const [bankAccount, setBankAccount] = useState<UserBankAccount>({
    bankId: 0,
    currencyId: 0,
    accountNo: "",
    iban: "",
    bankType: "BANK",
  });
  const [isAddingBank, setIsAddingBank] = useState(false);

  // Hooks for API calls
  const [getFitsContract] = azimutApi.useLazyGetFitsContractQuery();
  const [uploadFitsContract] = azimutApi.useUploadFitsContractMutation();
  const [addFitsBankAccount] = azimutApi.useAddFitsBankAccountMutation();

  // Handle contract download
  const handleDownloadContract = async () => {
    if (!id) return;

    try {
      setIsDownloading(true);
      const result = await getFitsContract(id).unwrap();

      // Create a download link for the contract
      const byteString = atob(result);
      const mimeString = "application/pdf";
      const ab = new ArrayBuffer(byteString.length);
      const ia = new Uint8Array(ab);
      for (let i = 0; i < byteString.length; i++) {
        ia[i] = byteString.charCodeAt(i);
      }
      const blob = new Blob([ab], { type: mimeString });
      const url = URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = url;
      link.download = `fits-contract-${id}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast.success(t("Contract downloaded successfully"));
    } catch (error) {
      toast.error(t("Failed to download contract"));
      console.error("Download error:", error);
    } finally {
      setIsDownloading(false);
    }
  };

  // Handle contract upload
  const handleUploadContract = async (e: ChangeEvent<HTMLInputElement>) => {
    if (!id || !e.target.files || e.target.files.length === 0) return;

    try {
      setIsUploading(true);
      const file = e.target.files[0];

      // Validate file type
      if (file.type !== "application/pdf") {
        toast.error(t("Please upload a PDF file"));
        return;
      }

      // Validate file size (50MB limit)
      if (file.size > 50 * 1024 * 1024) {
        toast.error(t("File size exceeds 50MB limit"));
        return;
      }

      await uploadFitsContract({ id, file }).unwrap();
      toast.success(t("Contract uploaded successfully"));

      // Clear file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      toast.error(t("Failed to upload contract"));
      console.error("Upload error:", error);
    } finally {
      setIsUploading(false);
    }
  };

  // Handle bank account form changes
  const handleBankAccountChange = (
    e:
      | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
      | { target: { name: string; value: string } }
  ) => {
    const { name, value } = e.target;
    setBankAccount(prev => {
      // If bankType changes, reset bankId to avoid invalid selection
      if (name === "bankType") {
        return {
          ...prev,
          [name]: value,
          bankId: 0,
        };
      }
      return {
        ...prev,
        [name]:
          name === "bankId" || name === "currencyId" ? Number(value) : value,
      };
    });
  };

  // Handle bank account submission
  const handleAddBankAccount = async (e: FormEvent) => {
    e.preventDefault();
    if (!id) return;

    try {
      setIsAddingBank(true);

      // Validate required fields
      if (
        !bankAccount.bankId ||
        !bankAccount.currencyId ||
        !bankAccount.accountNo ||
        !bankAccount.iban
      ) {
        toast.error(t("Please fill all required fields"));
        return;
      }

      await addFitsBankAccount({ id, bank: bankAccount }).unwrap();
      toast.success(t("Bank account added successfully"));

      // Reset form
      setBankAccount({
        bankId: 0,
        currencyId: 0,
        accountNo: "",
        iban: "",
        bankType: "BANK",
      });
    } catch (error) {
      toast.error(t("Failed to add bank account"));
      console.error("Add bank account error:", error);
    } finally {
      setIsAddingBank(false);
    }
  };

  // Trigger file input click
  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <div className="container mx-auto p-4">
      <div className="flex items-center justify-between mb-6">
        <Typography variant="h6">{t("FITS User Details")}</Typography>
        <Button
          variant="outlined"
          onClick={() => navigate("/fits-users")}
        >
          {t("Back")}
        </Button>
      </div>

      <Paper className="p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <Typography
              variant="subtitle2"
              className="text-gray-500"
            >
              {t("Name")}
            </Typography>
            <Typography>
              {userData ? `${userData.firstName} ${userData.lastName}` : "-"}
            </Typography>
          </div>
          <div>
            <Typography
              variant="subtitle2"
              className="text-gray-500"
            >
              {t("Mobile")}
            </Typography>
            <Typography>{userData?.mobile || "-"}</Typography>
          </div>
          <div>
            <Typography
              variant="subtitle2"
              className="text-gray-500"
            >
              {t("ID Number")}
            </Typography>
            <Typography>{userData?.idNumber || "-"}</Typography>
          </div>
        </div>
      </Paper>

      {/* Contract Management Section */}
      <Paper className="p-6 mb-6">
        <Typography
          variant="h6"
          className="mb-4"
        >
          {t("Contract Management")}
        </Typography>

        <div className="flex flex-col sm:flex-row gap-4">
          <Button
            variant="contained"
            onClick={handleDownloadContract}
            disabled={isDownloading || !id || !userData?.contractFilePath}
            startIcon={isDownloading ? <CircularProgress size={20} /> : null}
          >
            {isDownloading ? t("Downloading...") : t("Download Contract")}
          </Button>

          <div>
            <input
              type="file"
              ref={fileInputRef}
              onChange={handleUploadContract}
              accept="application/pdf"
              className="hidden"
            />
            <Button
              variant="outlined"
              onClick={triggerFileInput}
              disabled={isUploading || !id}
              startIcon={isUploading ? <CircularProgress size={20} /> : null}
            >
              {isUploading ? t("Uploading...") : t("Upload Contract")}
            </Button>
          </div>
        </div>
      </Paper>

      {/* Add Bank Account Section */}
      <Paper className="p-6">
        <div className="flex justify-between items-center mb-4">
          <Typography variant="h6">{t("Add Bank Account")}</Typography>
          <ToggleButtonGroup
            color="primary"
            value={bankAccount.bankType}
            exclusive
            onChange={(event, newValue) => {
              if (newValue !== null) {
                handleBankAccountChange({
                  target: { name: "bankType", value: newValue },
                } as any);
              }
            }}
            aria-label={t("Bank Type")}
          >
            <ToggleButton
              value="BANK"
              aria-label={t("Bank")}
            >
              {t("Bank")}
            </ToggleButton>
            <ToggleButton
              value="WALLET"
              aria-label={t("Wallet")}
            >
              {t("Wallet")}
            </ToggleButton>
          </ToggleButtonGroup>
        </div>

        <form onSubmit={handleAddBankAccount}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <FormControl fullWidth>
              <Autocomplete
                disablePortal
                id="bank-select"
                options={
                  (bankAccount.bankType === "WALLET" ? walletBanks : banks)
                    ?.banks || []
                }
                getOptionLabel={option => option?.englishBankName || ""}
                value={
                  (bankAccount.bankType === "WALLET"
                    ? walletBanks
                    : banks
                  )?.banks?.find(
                    (bank: any) => bank.bankId === bankAccount.bankId
                  ) || null
                }
                onChange={(event, newValue) => {
                  handleBankAccountChange({
                    target: {
                      name: "bankId",
                      value: newValue ? String(newValue.bankId) : "0",
                    },
                  } as any);
                }}
                renderInput={params => (
                  <TextField
                    {...params}
                    label={
                      bankAccount.bankType === "WALLET"
                        ? t("Provider")
                        : t("Bank")
                    }
                    required
                  />
                )}
              />
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>{t("Currency")}</InputLabel>
              <Select
                name="currencyId"
                // value={bankAccount.currencyId}
                onChange={handleBankAccountChange}
                label={t("Currency")}
                required
              >
                <MenuItem value="">
                  <em>{t("Select Currency")}</em>
                </MenuItem>
                {currencies?.currencies.map((currency: any) => (
                  <MenuItem
                    key={currency.currencyId}
                    value={currency.currencyId}
                  >
                    {currency.englishCurrencyName}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <TextField
              label={t("Account Number")}
              name="accountNo"
              value={bankAccount.accountNo}
              onChange={handleBankAccountChange}
              required
              className="md:col-span-2"
            />

            <TextField
              label={t("IBAN")}
              name="iban"
              value={bankAccount.iban}
              onChange={handleBankAccountChange}
              required
              className="md:col-span-2"
            />
          </div>

          <Button
            type="submit"
            variant="contained"
            disabled={isAddingBank}
            startIcon={isAddingBank ? <CircularProgress size={20} /> : null}
          >
            {isAddingBank ? t("Adding...") : t("Add Bank Account")}
          </Button>
        </form>
      </Paper>
    </div>
  );
}
