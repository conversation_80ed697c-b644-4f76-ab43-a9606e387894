import "../../assets/styles/reason.css";

import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import {
  Checkbox,
  DialogActions,
  FormControlLabel,
  IconButton,
  TextField,
} from "@mui/material";
import { DataGrid, GridColDef } from "@mui/x-data-grid";
import { useState } from "react";
import { useTranslation } from "react-i18next";

import Button from "../../components/shared/Button/Button";
import Dialog from "../../components/shared/Dialog/Dialog";
import { azimutAdminApi } from "../../hooks/adminUsersApi";

export const RolesList = () => {
  const [openEdit, setOpenEdit] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentRoleId, setCurrentRoleId] = useState<number>();
  const [deleteRole] = azimutAdminApi.useDeleteRoleMutation();
  const [editRole] = azimutAdminApi.useEditRoleMutation();
  const [addRole] = azimutAdminApi.useAddRoleMutation();
  const [name, setName] = useState("");
  const [permissions, setPermissions] = useState("");
  const { t } = useTranslation("common");

  const { data: roles } = azimutAdminApi.useGetRolesQuery();
  console.log(permissions);
  const permissionsArr = permissions?.split(",") || [];
  const permissionsList = {
    viewClient: "View full client data (incl. image)",
    submitKyc: "Accept/Reject kyc",
    editClient: "Edit Client",
    signContract: "Sign Contract",
    reasons: "Manage Rejection Reasons",
    dashboard: "View Dashboard",
    download: "Download Clients List",
    notifications: "Manage Notifications",
    dividends: "Manage Dividends",
    injections: "Manage Injections",
    offline: "Manage Rescan/Offline Clients",
    egyptOffline: "Send Offline Egypt Clients",
    approveContract: "Approve Offline Contract",
    unblock: "Unblock Clients",
    resetChangePhone: "Reset Change Phone Trials",
    checkCSO: "Recheck CSO",
    checkNTRA: "Recheck NTRA",
    checkNegativeList: "Recheck Negative List",
    approveCSO: "Approve CSO",
    rescanId: "Rescan ID",
    showWallet: "Show Wallet",
    showFunds: "Show Funds",
    negativeList: "Manage Negative List",
    physicalContracts: "Manage Physical Contracts",
    financialFilter: "Financial Filter",
    manageFA: "Manage Financial Advisors",
  };

  const handleChangeName = (e: React.ChangeEvent<HTMLInputElement>) =>
    setName(e.target.value);

  const handleChangePermission = (key: string, checked: boolean) => {
    let curPermissionsArr = permissions?.split(",") || [];
    if (!checked) curPermissionsArr = curPermissionsArr.filter(e => e != key);
    else curPermissionsArr.push(key);
    setPermissions(curPermissionsArr.join(","));
  };

  const columns: GridColDef[] = [
    {
      field: "name",
      headerName: t("Role"),
      disableColumnMenu: true,
      width: 500,
    },
    {
      field: "id",
      headerName: "",
      disableColumnMenu: true,
      disableReorder: true,
      sortable: false,
      renderCell: a => (
        <div className="absolute right-0">
          <IconButton
            aria-label="edit"
            onClick={() => {
              setCurrentRoleId(a.value);
              setPermissions(a.row.permissionsApp);
              setName(a.row.name);
              setIsEdit(true);
              setOpenEdit(true);
            }}
          >
            <EditIcon />
          </IconButton>
          <IconButton
            aria-label="delete"
            onClick={() => {
              setCurrentRoleId(a.value);
              setOpenDelete(true);
            }}
          >
            <DeleteIcon color="error" />
          </IconButton>
        </div>
      ),
    },
  ];

  return (
    <div className="container">
      <Button
        className="float-right"
        onClick={() => {
          setIsEdit(false);
          setPermissions("");
          setName("");
          setOpenEdit(true);
        }}
      >
        {t("Add new Role")}
      </Button>
      <h1>{t("Roles")}</h1>
      <div className="mt-5">
        <DataGrid
          rows={roles}
          columns={columns}
          disableRowSelectionOnClick
          pageSizeOptions={[5, 10, 25]}
        />
      </div>
      <Dialog
        open={openDelete}
        onClose={() => setOpenDelete(false)}
        title={t("Delete Role")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              onClick={async () => {
                setLoading(true);
                await deleteRole(roles?.find(e => e.id == currentRoleId)!);
                setLoading(false);
                setOpenDelete(false);
              }}
            >
              {t("Yes, Delete")}
            </Button>
            <Button
              variant="text"
              onClick={() => {
                setOpenDelete(false);
              }}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        {t("Are you sure you want to delete this role ?")}
      </Dialog>
      <Dialog
        open={openEdit}
        onClose={() => setOpenEdit(false)}
        title={isEdit ? t("Edit Role") : t("Add Role")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              onClick={async () => {
                setLoading(true);
                await (isEdit
                  ? editRole({
                      id: currentRoleId!,
                      name,
                      permissions,
                    })
                  : addRole({
                      name,
                      permissions,
                    }));
                setLoading(false);
                setOpenEdit(false);
              }}
            >
              {t("Submit")}
            </Button>
            <Button
              variant="text"
              onClick={() => {
                setOpenEdit(false);
              }}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        <div className="mb-3">{t("Name")}</div>
        <TextField
          value={name}
          onChange={handleChangeName}
        />
        <div className="my-3">{t("permissions")}</div>
        {Object.entries(permissionsList).map(entry => (
          <FormControlLabel
            key={entry[0]}
            onChange={(e, checked) => handleChangePermission(entry[0], checked)}
            control={
              <Checkbox defaultChecked={permissionsArr.includes(entry[0])} />
            }
            label={entry[1]}
          />
        ))}
      </Dialog>
    </div>
  );
};
