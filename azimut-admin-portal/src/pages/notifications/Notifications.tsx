import "../../assets/styles/reason.css";

import { CheckBox } from "@mui/icons-material";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import { DialogActions, IconButton, MenuItem, TextField } from "@mui/material";
import { styled } from "@mui/material/styles";
import { DataGrid, GridColDef } from "@mui/x-data-grid";
import Papa from "papaparse";
import { useMemo, useRef, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";
import { useDebouncedCallback } from "use-debounce";
import { read, utils } from "xlsx";

import Button from "../../components/shared/Button/Button";
import Dialog from "../../components/shared/Dialog/Dialog";
import { azimutApi } from "../../hooks/azimutApi";

const VisuallyHiddenInput = styled("input")({
  clip: "rect(0 0 0 0)",
  clipPath: "inset(50%)",
  height: 1,
  overflow: "hidden",
  position: "absolute",
  bottom: 0,
  left: 0,
  whiteSpace: "nowrap",
  width: 1,
});

const sentStates: Record<number, any> = {
  "-1": {
    label: "Show all",
  },
  "1": {
    label: "Yes",
  },
  "2": {
    label: "No",
  },
};

export const userTargets: Record<string, any> = {
  selectUsers: {
    label: "Select Users",
  },
  testUsers: {
    label: "Test Users",
  },
  allUsers: {
    label: "All Users",
  },
  newUser: {
    label: "New Users",
  },
  ekyc: {
    label: "Finished eKYC",
  },
  clientData: {
    label: "Finish Client Data",
  },
  bankAccount: {
    label: "Finish Bank Account",
  },
  kyc: {
    label: "Finish KYC Questions",
  },
  signedContract: {
    label: "Finish contract OTP",
  },
  rejected: {
    label: "Rejected",
  },
  active: {
    label: "Contract signed",
  },
};

export const Notifications = () => {
  const [openSubmit, setOpenSubmit] = useState(false);
  const [loading, setLoading] = useState(false);
  const [currentNotificationId, setCurrentNotificationId] = useState<number>();
  const [notificationTarget, setNotificationTarget] = useState("selectUsers");
  const [sendNotification] = azimutApi.useSendNotificationMutation();

  const [deleteNotification] = azimutApi.useDeleteNotificationMutation();
  const [editNotification] = azimutApi.useEditNotificationMutation();
  const [addNotification] = azimutApi.useAddNotificationMutation();

  const [title, setTitle] = useState("");
  const [titleAr, setTitleAr] = useState("");
  const [body, setBody] = useState("");
  const [bodyAr, setBodyAr] = useState("");
  const [link, setLink] = useState<string>();
  const [openEdit, setOpenEdit] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);

  const [sent, setSent] = useState<string>("-1");
  const [titleSearch, setTitleSearch] = useState<string>("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const { t } = useTranslation("common");

  const { data: notifications, isFetching } =
    azimutApi.useGetNotificationsQuery({
      title: titleSearch,
      sent,
      page,
      rowsPerPage,
    });

  const rowCountRef = useRef(notifications?.numberOfItems || 0);

  const rowCount = useMemo(() => {
    if (notifications?.numberOfItems !== undefined) {
      rowCountRef.current = notifications.numberOfItems;
    }
    return rowCountRef.current;
  }, [notifications?.numberOfItems]);

  const handleTitleSearch = useDebouncedCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setTitleSearch(e.target.value);
      setPage(0);
    },
    1000
  );

  const [userIds, setUserIds] = useState<string[] | undefined>();

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Parse XLSX using xlsx
    if (file.name.endsWith(".xlsx")) {
      const reader = new FileReader();
      reader.onload = event => {
        const data = event.target?.result;
        if (!data) return;

        const workbook = read(data, { type: "buffer" });
        const firstSheet = workbook.Sheets[workbook.SheetNames[0]];
        const csv = utils.sheet_to_csv(firstSheet);

        // Parse CSV using papaparse
        Papa.parse(csv as string, {
          header: true,
          skipEmptyLines: true,
          complete: results => {
            const ids = results.data
              .filter((row: any) => row["Nid/Passport"])
              .map((row: any) => row["Nid/Passport"]);
            if (ids.length > 0) setUserIds(ids);
          },
        });
      };

      reader.readAsArrayBuffer(file);
    } else if (file.name.endsWith(".csv")) {
      const reader = new FileReader();
      reader.onload = event => {
        const data = event.target?.result;
        if (!data) return;

        // Parse CSV using papaparse
        Papa.parse(data as string, {
          header: true,
          skipEmptyLines: true,
          complete: results => {
            const ids = results.data
              .filter((row: any) => row["Nid/Passport"])
              .map((row: any) => row["Nid/Passport"]);
            if (ids.length > 0) setUserIds(ids);
          },
        });
      };

      reader.readAsText(file);
    }
  };

  const validateLink = (link: string) => {
    const urlRegex =
      /^(https?:\/\/)?(www\.)?([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}(:\d+)?(\/[^\s]*)?$/;
    return urlRegex.test(link);
  };

  const columns: GridColDef[] = [
    {
      field: "createdAt",
      headerName: "Date",
      width: 120,
      disableColumnMenu: true,
      sortable: false,
      valueGetter: (e: any) => e.substring(0, 10),
    },
    {
      field: "title",
      headerName: "Title",
      disableColumnMenu: true,
      sortable: false,
      width: 150,
    },
    {
      field: "body",
      headerName: "Body",
      disableColumnMenu: true,
      sortable: false,
      width: 350,
    },
    {
      field: "sentGroups",
      headerName: "Sent to",
      disableColumnMenu: true,
      sortable: false,
      width: 350,
      valueFormatter: (value: string[]) =>
        value.map(e => userTargets[e].label).join(", "),
    },
    {
      field: "sentCount",
      headerName: "# Sent Manual",
      disableColumnMenu: true,
      sortable: false,
      width: 150,
    },
    {
      field: "id",
      headerName: "",
      disableColumnMenu: true,
      disableReorder: true,
      sortable: false,
      renderCell: a => (
        <div className="absolute right-0">
          <IconButton
            aria-label="Send"
            onClick={() => {
              setCurrentNotificationId(a.value);
              setOpenSubmit(true);
            }}
          >
            <CheckBox color="success" />
          </IconButton>
          <IconButton
            aria-label="edit"
            onClick={() => {
              setCurrentNotificationId(a.value);
              setTitle(a.row.title);
              setBody(a.row.body);
              setTitleAr(a.row.titleAr);
              setBodyAr(a.row.bodyAr);
              setLink(a.row.link);
              setIsEdit(true);
              setOpenEdit(true);
            }}
          >
            <EditIcon />
          </IconButton>
          <IconButton
            aria-label="delete"
            onClick={() => {
              setCurrentNotificationId(a.value);
              setOpenDelete(true);
            }}
          >
            <DeleteIcon color="error" />
          </IconButton>
        </div>
      ),
    },
  ];

  return (
    <div className="container">
      <Button
        className="float-right"
        onClick={() => {
          setIsEdit(false);
          setTitle("");
          setBody("");
          setTitleAr("");
          setBodyAr("");
          setLink(undefined);
          setOpenEdit(true);
        }}
      >
        {t("Add new Notification")}
      </Button>
      <h1>{t("Notifications")}</h1>
      <TextField
        className="mb-3"
        sx={{ width: 250 }}
        select
        label={t("Sent")}
        size="small"
        value={sent}
        onChange={e => {
          setSent(e.target.value);
          setPage(0);
        }}
      >
        {Object.entries(sentStates).map(option => (
          <MenuItem
            key={option[0]}
            value={option[0]}
          >
            {t(option[1].label)}
          </MenuItem>
        ))}
      </TextField>
      <TextField
        className="ml-2 mb-3"
        label={t("Title")}
        size="small"
        defaultValue={titleSearch}
        onChange={handleTitleSearch}
      />
      <div className="mt-5">
        <DataGrid
          rows={notifications?.dataList.filter(e => e) || []}
          columns={columns}
          disableRowSelectionOnClick
          pageSizeOptions={[5, 10, 25]}
          rowCount={rowCount}
          loading={isFetching}
          paginationModel={{
            page,
            pageSize: rowsPerPage,
          }}
          paginationMode="server"
          onPaginationModelChange={e => {
            setPage(e.page);
            setRowsPerPage(e.pageSize);
          }}
        />
      </div>
      <Dialog
        open={openSubmit}
        onClose={() => setOpenSubmit(false)}
        title={t("Send Notification")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              disabled={notificationTarget == "selectUsers" && !userIds?.length}
              onClick={async () => {
                setLoading(true);
                await sendNotification({
                  id: currentNotificationId!,
                  target:
                    notificationTarget == "selectUsers"
                      ? undefined
                      : notificationTarget,
                  userNationalIds: userIds,
                });
                setLoading(false);
                toast.success(t("Notification sent successfully"));
                setOpenSubmit(false);
              }}
            >
              {t("Yes, Send")}
            </Button>
            <Button
              variant="text"
              onClick={() => {
                setOpenSubmit(false);
              }}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        <div className="mb-4">{t("Select Users to send notification to")}</div>
        <TextField
          className="mb-3"
          select
          size="small"
          value={notificationTarget}
          onChange={e => {
            setNotificationTarget(e.target.value);
            setPage(0);
          }}
        >
          {Object.entries(userTargets).map(option => (
            <MenuItem
              key={option[0]}
              value={option[0]}
            >
              {t(option[1].label)}
            </MenuItem>
          ))}
        </TextField>
        <div className="mb-4">
          {t(
            'Or upload users list (containing "Nid/Passport" column) to manually send notification to '
          )}
          {userIds &&
            userIds.length > 0 &&
            `(${userIds.length} users selected)`}
        </div>
        <Button
          component="label"
          role={undefined}
          variant="contained"
          tabIndex={-1}
          startIcon={<CloudUploadIcon />}
        >
          Upload file
          <VisuallyHiddenInput
            type="file"
            accept=".csv, .xlsx"
            onChange={handleFileUpload}
          />
        </Button>
      </Dialog>
      <Dialog
        open={openDelete}
        onClose={() => setOpenDelete(false)}
        title={t("Delete Notification")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              onClick={async () => {
                setLoading(true);
                await deleteNotification(
                  notifications?.dataList.find(
                    e => e.id == currentNotificationId
                  )!
                );
                setLoading(false);
                setOpenDelete(false);
              }}
            >
              {t("Yes, Delete")}
            </Button>
            <Button
              variant="text"
              onClick={() => {
                setOpenDelete(false);
              }}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        {t("Are you sure you want to delete this notification ?")}
      </Dialog>
      <Dialog
        open={openEdit}
        onClose={() => setOpenEdit(false)}
        title={isEdit ? t("Edit Notification") : t("Add Notification")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              onClick={async () => {
                if (link && !validateLink(link)) {
                  toast.error("Please enter a valid link address.");
                  return;
                }
                if (!link) setLink(undefined);
                setLoading(true);
                await (isEdit
                  ? editNotification({
                      id: currentNotificationId!,
                      title,
                      body,
                      titleAr,
                      bodyAr,
                      link,
                    })
                  : addNotification({
                      title,
                      body,
                      titleAr,
                      bodyAr,
                      link,
                    }));
                setLoading(false);
                setOpenEdit(false);
              }}
            >
              {t("Submit")}
            </Button>
            <Button
              variant="text"
              onClick={() => {
                setOpenEdit(false);
              }}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        <div className="flex flex-row mb-3">
          <div className="basis-1/2">
            <div className="mb-3">{t("Title English")}</div>
            <TextField
              value={title}
              onChange={e => setTitle(e.target.value)}
            />
          </div>
          <div className="basis-1/2">
            <div className="mb-3">{t("Title Arabic")}</div>
            <TextField
              value={titleAr}
              onChange={e => setTitleAr(e.target.value)}
            />
          </div>
        </div>
        <div className="flex flex-row my-4">
          <div className="basis-1/2">
            <div className="mb-3">{t("Body English")}</div>
            <TextField
              className="pe-7"
              multiline
              fullWidth
              rows={4}
              value={body}
              onChange={e => setBody(e.target.value)}
            />
          </div>
          <div className="basis-1/2">
            <div className="mb-3">{t("Body Arabic")}</div>
            <TextField
              className="pe-5"
              multiline
              fullWidth
              rows={4}
              value={bodyAr}
              onChange={e => setBodyAr(e.target.value)}
            />
          </div>
        </div>
        <div className="flex flex-row my-4">
          <div className="mt-2 me-2">{t("Link")}</div>
          <TextField
            className="pe-5"
            fullWidth
            value={link}
            onChange={e => setLink(e.target.value)}
          />
        </div>
      </Dialog>
    </div>
  );
};
