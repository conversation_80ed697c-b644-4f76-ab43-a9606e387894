import "../../assets/styles/reason.css";

import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import {
  Autocomplete,
  DialogActions,
  IconButton,
  TextField,
} from "@mui/material";
import { DataGrid, GridColDef } from "@mui/x-data-grid";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import dayjs, { Dayjs } from "dayjs";
import { useEffect, useMemo, useRef, useState } from "react";
import toast from "react-hot-toast";
import { useTranslation } from "react-i18next";

import Button from "../../components/shared/Button/Button";
import Dialog from "../../components/shared/Dialog/Dialog";
import { azimutApi } from "../../hooks/azimutApi";
import { Fund } from "../../hooks/types";

export const Dividends = () => {
  const [loading, setLoading] = useState(false);
  const [currentDividendId, setCurrentDividendId] = useState<number>();

  const [deleteDividend] = azimutApi.useDeleteDividendMutation();
  const [editDividend, { error: editError }] =
    azimutApi.useEditDividendMutation();
  const [addDividend, { error: addError }] = azimutApi.useAddDividendMutation();
  const { data: funds } = azimutApi.useGetFundsQuery();

  const [date, setDate] = useState<Dayjs | null>(null);
  const [fund, setFund] = useState<Fund>();
  const [amount, setAmount] = useState<number>();
  const [openEdit, setOpenEdit] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [openDelete, setOpenDelete] = useState(false);

  const [filterFundId, setFilterFundId] = useState<number>();
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  const { t } = useTranslation("common");

  const { data: dividends, isFetching } = azimutApi.useGetDividendsQuery({
    fundId: filterFundId,
    page,
    rowsPerPage,
  });

  const rowCountRef = useRef(dividends?.numberOfItems || 0);

  const rowCount = useMemo(() => {
    if (dividends?.numberOfItems !== undefined) {
      rowCountRef.current = dividends.numberOfItems;
    }
    return rowCountRef.current;
  }, [dividends?.numberOfItems]);

  const fundsMap = useMemo(() => {
    const m: Record<number, string> = {};
    if (funds) {
      funds.forEach(fund => {
        m[fund.id] = fund.name;
      });
    }
    return m;
  }, [funds]);

  useEffect(() => {
    if (addError) toast.error((addError as any).data.message);
    if (editError) toast.error((editError as any).data.message);
  }, [addError, editError]);

  const columns: GridColDef[] = [
    {
      field: "fundId",
      headerName: "Fund Id",
      disableColumnMenu: true,
      sortable: false,
      valueGetter: (e: any) => fundsMap[e],
      width: 250,
    },
    {
      field: "dividendDate",
      headerName: "Date",
      width: 150,
      disableColumnMenu: true,
      sortable: false,
      valueGetter: (e: any) => e?.substring(0, 10),
    },
    {
      field: "amount",
      headerName: "Dividend",
      disableColumnMenu: true,
      sortable: false,
      width: 100,
    },
    {
      field: "id",
      headerName: "",
      disableColumnMenu: true,
      disableReorder: true,
      sortable: false,
      renderCell: a => (
        <div className="absolute right-0">
          <IconButton
            aria-label="edit"
            onClick={() => {
              setCurrentDividendId(a.value);
              setDate(dayjs(a.row.dividendDate));
              setFund(funds?.find(e => e.id == a.row.fundId));
              setAmount(a.row.amount);
              setIsEdit(true);
              setOpenEdit(true);
            }}
          >
            <EditIcon />
          </IconButton>
          <IconButton
            aria-label="delete"
            onClick={() => {
              setCurrentDividendId(a.value);
              setOpenDelete(true);
            }}
          >
            <DeleteIcon color="error" />
          </IconButton>
        </div>
      ),
    },
  ];

  return (
    <div className="container">
      <Button
        className="float-right"
        onClick={() => {
          setIsEdit(false);
          setDate(null);
          setFund(undefined);
          setAmount(undefined);
          setOpenEdit(true);
        }}
      >
        {t("Add new Dividend")}
      </Button>
      <h1>{t("Dividends")}</h1>
      <Autocomplete
        className="mb-3"
        options={funds || []}
        getOptionLabel={option => option.name}
        onChange={(event: any, newValue) => {
          setFilterFundId(newValue?.id);
          setPage(0);
        }}
        sx={{ width: 215, display: "inline-flex" }}
        renderInput={params => (
          <TextField
            {...params}
            label={t("Funds")}
          />
        )}
      />
      <div className="mt-5">
        <DataGrid
          rows={dividends?.dataList.filter(e => e) || []}
          columns={columns}
          disableRowSelectionOnClick
          pageSizeOptions={[5, 10, 25]}
          rowCount={rowCount}
          loading={isFetching}
          paginationModel={{
            page,
            pageSize: rowsPerPage,
          }}
          paginationMode="server"
          onPaginationModelChange={e => {
            setPage(e.page);
            setRowsPerPage(e.pageSize);
          }}
        />
      </div>
      <Dialog
        open={openDelete}
        onClose={() => setOpenDelete(false)}
        title={t("Delete Dividend")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              onClick={async () => {
                setLoading(true);
                await deleteDividend(
                  dividends?.dataList.find(e => e.id == currentDividendId)!
                );
                setLoading(false);
                setOpenDelete(false);
              }}
            >
              {t("Yes, Delete")}
            </Button>
            <Button
              variant="text"
              onClick={() => {
                setOpenDelete(false);
              }}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        {t("Are you sure you want to delete this dividend ?")}
      </Dialog>
      <Dialog
        open={openEdit}
        onClose={() => setOpenEdit(false)}
        title={isEdit ? t("Edit Dividend") : t("Add Dividend")}
        actions={
          <DialogActions>
            <Button
              className="popup-btn"
              loading={loading}
              onClick={async () => {
                if (!fund) {
                  toast.error("Fund is required");
                  return;
                }
                if (!date) {
                  toast.error("Date is required");
                  return;
                }
                if (!amount) {
                  toast.error("Amount is required and can't be 0");
                  return;
                }
                setLoading(true);
                await (isEdit
                  ? editDividend({
                      id: currentDividendId!,
                      teacomputerId: fund!.teacomputerId,
                      fundId: fund!.id,
                      dividendDate: date!.toDate().toISOString(),
                      amount: amount!,
                    })
                  : addDividend({
                      teacomputerId: fund!.teacomputerId,
                      fundId: fund!.id,
                      dividendDate: date!.toDate().toISOString(),
                      amount: amount!,
                    }));
                setLoading(false);
                setOpenEdit(false);
              }}
            >
              {t("Submit")}
            </Button>
            <Button
              variant="text"
              onClick={() => {
                setOpenEdit(false);
              }}
            >
              {t("Cancel")}
            </Button>
          </DialogActions>
        }
      >
        <div className="my-3">{t("Fund")}</div>
        <Autocomplete
          className="mb-3"
          options={funds || []}
          defaultValue={funds?.find(e => e.id == fund?.id)}
          getOptionLabel={option => option.name}
          onChange={(event: any, newValue) => {
            setFund(newValue || undefined);
          }}
          sx={{ width: 300, display: "inline-flex" }}
          renderInput={params => <TextField {...params} />}
        />
        <div className="mb-3">{t("Date")}</div>
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <DatePicker
            value={date}
            onChange={(e: Dayjs | null) => {
              if (!e || e?.isValid()) {
                setDate(e);
                setPage(0);
              }
            }}
          />
        </LocalizationProvider>
        <div className="my-3">{t("Amount")}</div>
        <TextField
          className="pe-7"
          fullWidth
          size="small"
          type="number"
          value={amount}
          onChange={e => setAmount(Number(e.target.value))}
        />
      </Dialog>
    </div>
  );
};
