/* input {
  padding: 10px;
  margin: 10px;
} */

.accepted {
  width: 115px;
  height: 24px;
  background: rgba(129, 199, 132, 0.15);
  border-radius: 15px;
  color: #81c784;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.auto-accepted {
  width: 115px;
  height: 24px;
  border-radius: 15px;
  color: #caca4d !important;
  background: rgb(221 219 18 / 15%) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.new-user {
  width: 115px;
  height: 24px;
  background: rgba(0, 146, 255, 0.15);
  border-radius: 15px;
  color: #0092ff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.pending {
  width: 115px;
  height: 24px;
  background: rgba(255, 179, 0, 0.15);
  border-radius: 15px;
  color: #ffb300;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.rejected {
  width: 115px;
  height: 24px;
  background: rgba(255, 85, 82, 0.15);
  border-radius: 15px;
  color: #ff5552;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.MuiOutlinedInput-root:not(.MuiInputBase-multiline) {
  height: 40px;
}
