.pageSemiTitle {
  color: #aebdd5;
  font-size: 18px;
  font-family: Poppins-Medium;
  border-left: 2px solid #ffb300;
  padding-left: 10px;
  margin-top: 23px;
}

.printBtn {
  width: 145px;
  height: 36px;
  box-shadow: 0 3px 6px #0000001a;
  border: 1px solid #0092ff;
  border-radius: 18px;
  background-color: transparent;
  color: #0092ff;
  font-size: 14px;
}

.acceptBtn {
  margin-right: 20px;
  border: 1px solid #81c784;
  color: #81c784;
}

.rejectBtn {
  margin-right: 20px;
  border: 1px solid #ff5552;
  color: #ff5552;
}

.acceptSpn {
  color: #81c784;
}

.rejectSpn {
  color: #ff5552;
}

.fullBtnWidth .acceptBtn,
.fullBtnWidth .rejectBtn {
  width: 49% !important;
}

.autoAcceptedBtn {
  color: #caca4d !important;
  background: rgb(221 219 18 / 15%) !important;
}

.acceptedBtn {
  width: 150px !important;
  height: 40px !important;
  background: rgba(129, 199, 132, 0.15);
  border-radius: 20px !important;
  color: #81c784;
  border: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.undoText {
  color: #0092ff;
  text-decoration: underline;
  font-family: Poppins-SemiBold;
  margin: 0;
}

.rejectedBtn {
  width: 109px !important;
  height: 40px !important;
  background: rgba(255, 85, 82, 0.15) !important;
  border-radius: 20px !important;
  color: #ff5552 !important;
  border: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.fullBtnWidth .acceptedBtn,
.fullBtnWidth .rejectededBtn {
  width: 92% !important;
}

.questionHolder {
  width: 100%;
  background-color: #121923;
  border-radius: 4px;
  padding: 15px 18px;
  margin-bottom: 25px;
}

.optionalText {
  color: #0092ff;
  font-size: 11px;
  margin: 0 3px;
}

.buttonsHolder1 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 25%;
  margin-top: 20px;
  margin-bottom: 20px;
}

.buttonsHolder {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 30%;
  margin-top: 20px;
  margin-bottom: 20px;
}

.retakeHolder {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #0092ff;
}

.MuiSvgIcon-root {
  width: 18px;
  margin-right: 2px;
}

.MuiInputLabel-sizeMedium {
  line-height: 0.75rem;
}

@media (min-width: 1536px) {
  .container {
    max-width: 98%;
  }
}

.buttonsHolder button {
  background-color: transparent;
  width: 150px;
  height: 45px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  font-size: 15px;
  font-family: Poppins-SemiBold;
  cursor: pointer;
}

.acceptAllHolder {
  justify-content: space-between;
  display: flex;
  width: 100%;
}

.acceptAllBtn {
  border: none;
  color: #81c784;
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 45px;
  border-radius: 4px;
  font-size: 15px;
  cursor: pointer;
}

.submit-kyc-btn {
  width: 180px;
  height: 44px;
  background: #0092ff;
  color: #fff;
  box-shadow: 0 3px 6px #0000001a;
  border-radius: 4px;
  border: 1px solid #0092ff;
  transition: 0.5s;
  max-width: 47%;
  text-align: center !important;
  cursor: pointer;
}

/* Funds part */
.fund-card {
  background: #111820;
  border-radius: 8px;
  padding: 12px 18px;
  display: flex;
  margin-bottom: 16px;
}

.funds-img {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 8px;
}

.funds-details {
  width: 100%;
}

.font-type {
  color: #ffb300;
  font-size: 13px;
  margin-bottom: 0px !important;
}

.funds-title-contain {
  display: flex;
  align-items: baseline;
  justify-content: space-between;
}

.funds-title {
  margin-bottom: 3px;
  margin-top: 0;
  font-size: 14px;
}

.border-title {
  font-family: "Poppins-Regular";
  color: #aebdd5;
}

.my-funds-data-contain p {
  margin-bottom: 0;
}

.fund-price {
  color: #0092ff;
  font-size: 14px;
}

.fund-date {
  color: #aebdd5;
  font-size: 12px;
}

.my-funds-data-contain {
  display: flex;
  padding-top: 5px;
}

.noOfUnits {
  color: #fcd555;
  font-size: 13px;
  font-family: "Poppins-Medium";
  margin: 0 5px;
}

.moneyAmount {
  font-size: 13px;
  font-family: "Poppins-Medium";
  margin: 0 5px;
}

.funds-data:first-child {
  justify-content: flex-start;
}

.funds-data {
  width: 25%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.funds-data p {
  margin-bottom: 0;
  margin-top: 0;
}

.funds-data-last {
  width: calc(25% - 16px);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.funds-data-last p {
  margin-bottom: 0;
}

.fuds-data-title {
  color: #aebdd5;
  font-size: 12px;
  margin-bottom: 0;
}

.empty-state-title {
  font-family: "Poppins-SemiBold";
}

.funds-title {
  font-family: "Poppins-Medium";
}

.font-type {
  font-family: "Poppins-Regular";
}

.funds-price-data,
.fuds-data-contain {
  padding-right: 14px;
}

.fund-price {
  font-family: "Poppins-Medium";
}

.funds-img {
  margin-right: 26px;
}

.funds-data {
  border-right: 0.5px solid rgb(162 215 255 / 27%);
}

@media only screen and (max-width: 1160px) {
  .fund-card {
    padding: 12px;
  }

  .funds-data,
  .funds-data-last {
    width: calc(25% - 8px);
    font-size: 10px;
  }

  .funds-icon {
    width: 16px;
  }

  .funds-img {
    width: 45px;
    height: 45px;
  }
}

@media only screen and (max-width: 992px) {
  .my-funds-data-contain {
    display: block;
  }

  .funds-data,
  .funds-data-last {
    width: 50%;
    display: inline-block;
    margin: 0px !important;
  }

  .funds-title-contain .border-title {
    display: none;
  }

  .funds-title-contain .funds-title {
    display: flex;
    flex-direction: column;
  }

  .funds-data,
  .funds-data-last {
    border-bottom: 0.5px solid rgba(162, 215, 255, 0.27);
    border-right: none !important;
    border-left: none !important;
  }

  .funds-data-last {
    border: none !important;
  }

  .funds-data:nth-last-child(2) {
    border: none !important;
  }

  .funds-price-data p {
    margin-bottom: 0px;
  }
}

/* wallet part */
.transactions-card {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #2c425e99;
  align-items: center;
  padding: 0 0 15px 0;
}

.transaction-data {
  display: flex;
  align-items: center;
}

.trans-type {
  font-size: 15px;
  margin-bottom: 3px !important;
}

.trans-date {
  font-size: 12px;
  margin-bottom: 0px !important;
  color: #bac1cf;
  body.rtl & {
    direction: ltr !important;
  }
}

.Pending-trx {
  color: #ffb300;
  font-size: 12px;
  margin-bottom: 0 !important;
}
.Rejected-trx {
  color: #ff5552;
  font-size: 12px;
  margin-bottom: 0 !important;
}
.Unpaid-trx {
  color: #d20000;
  font-size: 12px;
  margin-bottom: 0 !important;
}
.Failed-trx {
  color: #ffa07a;
  font-size: 12px;
  margin-bottom: 0 !important;
}
.Approved-trx {
  color: #81c784;
  font-size: 12px;
  margin-bottom: 0 !important;
}
.Cancel-trx {
  color: #dee3eb;
  font-size: 12px;
  margin-bottom: 0 !important;
}
