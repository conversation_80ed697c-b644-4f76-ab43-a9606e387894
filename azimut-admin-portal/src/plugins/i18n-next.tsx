import i18next from "i18next";
import { FC } from "react";
import { I18nextProvider } from "react-i18next";

import commonAr from "../locales/ar/common.json";
import commonEn from "../locales/en/common.json";

i18next.init({
  interpolation: { escapeValue: false },
  fallbackLng: "en",
  resources: {
    en: {
      common: commonEn,
    },
    ar: {
      common: commonAr,
    },
  },
});

export const I18nCustomProvider: FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  return <I18nextProvider i18n={i18next}>{children}</I18nextProvider>;
};
