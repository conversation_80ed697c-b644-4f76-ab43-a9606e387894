<?xml version="1.0" encoding="UTF-8"?>
<babeledit_project be_version="4.0.3" version="1.3">
	<!--

    BabelEdit project file
    https://www.codeandweb.com/babeledit

    This file contains meta data for all translations, but not the translation texts itself.
    They are stored in framework-specific message files (.json / .vue / .yaml / .properties)

    -->
	<preset_collections/>
	<framework>react-json</framework>
	<filename>i18n-next.babel</filename>
	<source_root_dir>../../</source_root_dir>
	<folder_node>
		<name/>
		<children>
			<package_node>
				<name>main</name>
				<children>
					<concept_node>
						<name>Forgot Password?</name>
						<description/>
						<comment/>
						<default_text/>
						<translations>
							<translation>
								<language>en-US</language>
								<approved>false</approved>
							</translation>
							<translation>
								<language>tr-TR</language>
								<approved>false</approved>
							</translation>
						</translations>
					</concept_node>
					<concept_node>
						<name>dashboard</name>
						<description/>
						<comment/>
						<default_text/>
						<translations>
							<translation>
								<language>en-US</language>
								<approved>false</approved>
							</translation>
							<translation>
								<language>tr-TR</language>
								<approved>false</approved>
							</translation>
						</translations>
					</concept_node>
					<concept_node>
						<name>email</name>
						<description/>
						<comment/>
						<default_text/>
						<translations>
							<translation>
								<language>en-US</language>
								<approved>false</approved>
							</translation>
							<translation>
								<language>tr-TR</language>
								<approved>false</approved>
							</translation>
						</translations>
					</concept_node>
					<concept_node>
						<name>fullName</name>
						<description/>
						<comment/>
						<default_text/>
						<translations>
							<translation>
								<language>en-US</language>
								<approved>false</approved>
							</translation>
							<translation>
								<language>tr-TR</language>
								<approved>false</approved>
							</translation>
						</translations>
					</concept_node>
					<concept_node>
						<name>login</name>
						<description/>
						<comment/>
						<default_text/>
						<translations>
							<translation>
								<language>en-US</language>
								<approved>false</approved>
							</translation>
							<translation>
								<language>tr-TR</language>
								<approved>false</approved>
							</translation>
						</translations>
					</concept_node>
					<concept_node>
						<name>password</name>
						<description/>
						<comment/>
						<default_text/>
						<translations>
							<translation>
								<language>en-US</language>
								<approved>false</approved>
							</translation>
							<translation>
								<language>tr-TR</language>
								<approved>false</approved>
							</translation>
						</translations>
					</concept_node>
					<concept_node>
						<name>register</name>
						<description/>
						<comment/>
						<default_text/>
						<translations>
							<translation>
								<language>en-US</language>
								<approved>false</approved>
							</translation>
							<translation>
								<language>tr-TR</language>
								<approved>false</approved>
							</translation>
						</translations>
					</concept_node>
					<concept_node>
						<name>repeat</name>
						<description/>
						<comment/>
						<default_text/>
						<translations>
							<translation>
								<language>en-US</language>
								<approved>false</approved>
							</translation>
							<translation>
								<language>tr-TR</language>
								<approved>false</approved>
							</translation>
						</translations>
					</concept_node>
					<concept_node>
						<name>settings</name>
						<description/>
						<comment/>
						<default_text/>
						<translations>
							<translation>
								<language>en-US</language>
								<approved>false</approved>
							</translation>
							<translation>
								<language>tr-TR</language>
								<approved>false</approved>
							</translation>
						</translations>
					</concept_node>
					<concept_node>
						<name>submit</name>
						<description/>
						<comment/>
						<default_text/>
						<translations>
							<translation>
								<language>en-US</language>
								<approved>false</approved>
							</translation>
							<translation>
								<language>tr-TR</language>
								<approved>false</approved>
							</translation>
						</translations>
					</concept_node>
				</children>
			</package_node>
		</children>
	</folder_node>
	<isTemplateProject>false</isTemplateProject>
	<languages>
		<language>
			<code>en-US</code>
		</language>
		<language>
			<code>tr-TR</code>
		</language>
	</languages>
	<translation_packages>
		<translation_package>
			<name>main</name>
			<translation_urls>
				<translation_url>
					<path>en/common.json</path>
					<language>en-US</language>
				</translation_url>
				<translation_url>
					<path>tr/common.json</path>
					<language>tr-TR</language>
				</translation_url>
			</translation_urls>
		</translation_package>
	</translation_packages>
	<editor_configuration>
		<save_empty_translations>true</save_empty_translations>
		<translation_order>alphabetically</translation_order>
		<copy_templates>
			<copy_template>'%1'</copy_template>
			<copy_template/>
			<copy_template/>
		</copy_templates>
		<custom_languages/>
	</editor_configuration>
	<primary_language>en-US</primary_language>
	<configuration>
		<indent>space2</indent>
		<format>json</format>
		<support_arrays>true</support_arrays>
	</configuration>
</babeledit_project>
