import { CssBaseline } from "@mui/material";
import { StyledEngineProvider, ThemeProvider } from "@mui/material/styles";
import { Fragment, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useEffectOnce } from "react-use";

import { AuthProvider } from "./hooks/useAuth";
import { darkTheme, lightTheme } from "./plugins/mui";
import Router from "./router";
import { useAppStore } from "./store/appStore";

function Root() {
  const { theme, language, setLanguage, setTheme } = useAppStore();
  // const { init: initAuth } = useAuthStore();
  const { i18n } = useTranslation();

  useEffectOnce(() => {
    // initAuth();

    if (navigator?.language && !localStorage.getItem("language")) {
      const navigatorLanguage = navigator.language;
      setLanguage(navigatorLanguage.split("-")[0].toLowerCase());
    }

    setTheme("dark");
  });

  // check if the theme is changed, and toggle the class
  useEffect(() => {
    const htmlTag = document.documentElement;

    htmlTag.classList.remove("dark", "light");
    htmlTag.classList.add(theme);
  }, [theme]);

  useEffect(() => {
    document.documentElement.setAttribute("lang", language);
    i18n.changeLanguage(language);
  }, [language]);

  return (
    <Fragment>
      <StyledEngineProvider injectFirst>
        <ThemeProvider theme={theme === "dark" ? darkTheme : lightTheme}>
          <CssBaseline />
          <AuthProvider>
            <Router />
          </AuthProvider>
        </ThemeProvider>
      </StyledEngineProvider>
    </Fragment>
  );
}

export default Root;
