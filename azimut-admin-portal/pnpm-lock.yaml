lockfileVersion: "9.0"

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

importers:
  .:
    dependencies:
      "@emotion/react":
        specifier: ^11.10.6
        version: 11.11.4(@types/react@18.3.2)(react@18.3.1)
      "@emotion/styled":
        specifier: ^11.10.6
        version: 11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1)
      "@loadable/component":
        specifier: ^5.15.3
        version: 5.16.4(react@18.3.1)
      "@mui/icons-material":
        specifier: ^5.16.6
        version: 5.16.7(@mui/material@5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@types/react@18.3.2)(react@18.3.1)
      "@mui/material":
        specifier: ^5.16.6
        version: 5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      "@mui/utils":
        specifier: ^5.16.6
        version: 5.16.6(@types/react@18.3.2)(react@18.3.1)
      "@mui/x-data-grid":
        specifier: ^7.14.0
        version: 7.14.0(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@mui/material@5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@types/react@18.3.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      "@mui/x-date-pickers":
        specifier: ^7.14.0
        version: 7.14.0(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@mui/material@5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@types/react@18.3.2)(dayjs@1.11.13)(moment@2.30.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      "@reduxjs/toolkit":
        specifier: ^2.2.7
        version: 2.2.7(react-redux@9.1.2(@types/react@18.3.2)(react@18.3.1)(redux@5.0.1))(react@18.3.1)
      axios:
        specifier: ^1.7.4
        version: 1.7.4
      chroma-js:
        specifier: ^2.4.2
        version: 2.4.2
      clsx:
        specifier: ^1.2.1
        version: 1.2.1
      dayjs:
        specifier: ^1.11.13
        version: 1.11.13
      formik:
        specifier: ^2.2.9
        version: 2.4.6(react@18.3.1)
      i18next:
        specifier: ^23.11.4
        version: 23.11.4
      jotai:
        specifier: ^2.0.3
        version: 2.8.0(@types/react@18.3.2)(react@18.3.1)
      json-to-csv-export:
        specifier: ^3.0.1
        version: 3.0.1
      jwt-decode:
        specifier: ^4.0.0
        version: 4.0.0
      localforage:
        specifier: ^1.10.0
        version: 1.10.0
      lodash:
        specifier: ^4.17.21
        version: 4.17.21
      match-sorter:
        specifier: ^6.3.1
        version: 6.3.4
      papaparse:
        specifier: ^5.5.3
        version: 5.5.3
      react:
        specifier: ^18.2.0
        version: 18.3.1
      react-dom:
        specifier: ^18.2.0
        version: 18.3.1(react@18.3.1)
      react-hot-toast:
        specifier: ^2.4.1
        version: 2.4.1(csstype@3.1.3)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react-i18next:
        specifier: ^12.2.0
        version: 12.3.1(i18next@23.11.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react-redux:
        specifier: ^9.1.2
        version: 9.1.2(@types/react@18.3.2)(react@18.3.1)(redux@5.0.1)
      react-router-dom:
        specifier: ^6.23.1
        version: 6.23.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react-spinners:
        specifier: ^0.13.8
        version: 0.13.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react-use:
        specifier: ^17.4.0
        version: 17.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      sort-by:
        specifier: ^1.2.0
        version: 1.2.0
      twin.macro:
        specifier: ^3.3.1
        version: 3.4.1(tailwindcss@3.4.3(ts-node@10.9.2(@types/node@18.19.33)(typescript@4.9.5)))
      use-debounce:
        specifier: ^10.0.0
        version: 10.0.0(react@18.3.1)
      uuid:
        specifier: ^9.0.0
        version: 9.0.1
      xlsx:
        specifier: file:vendor/xlsx-0.20.3.tgz
        version: file:vendor/xlsx-0.20.3.tgz
      yup:
        specifier: ^1.1.0
        version: 1.4.0
    devDependencies:
      "@babel/plugin-transform-react-jsx":
        specifier: ^7.21.0
        version: 7.23.4(@babel/core@7.24.5)
      "@commitlint/cli":
        specifier: ^17.5.1
        version: 17.8.1
      "@commitlint/config-conventional":
        specifier: ^17.4.4
        version: 17.8.1
      "@emotion/babel-plugin-jsx-pragmatic":
        specifier: ^0.2.0
        version: 0.2.1(@babel/core@7.24.5)
      "@playwright/test":
        specifier: ^1.33.0
        version: 1.44.0
      "@svgr/rollup":
        specifier: ^7.0.0
        version: 7.0.0(rollup@3.29.4)(typescript@4.9.5)
      "@testing-library/jest-dom":
        specifier: ^4.2.4
        version: 4.2.4
      "@testing-library/react":
        specifier: ^14.0.0
        version: 14.3.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      "@types/chroma-js":
        specifier: ^2.4.0
        version: 2.4.4
      "@types/loadable__component":
        specifier: ^5.13.4
        version: 5.13.9
      "@types/lodash":
        specifier: ^4.14.194
        version: 4.17.1
      "@types/node":
        specifier: ^18.15.10
        version: 18.19.33
      "@types/papaparse":
        specifier: ^5.3.16
        version: 5.3.16
      "@types/react":
        specifier: ^18.0.28
        version: 18.3.2
      "@types/react-dom":
        specifier: ^18.0.11
        version: 18.3.0
      "@types/uuid":
        specifier: ^9.0.1
        version: 9.0.8
      "@typescript-eslint/eslint-plugin":
        specifier: ^5.57.0
        version: 5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@4.9.5))(eslint@8.57.0)(typescript@4.9.5)
      "@typescript-eslint/parser":
        specifier: ^5.57.0
        version: 5.62.0(eslint@8.57.0)(typescript@4.9.5)
      "@vitejs/plugin-react":
        specifier: ^3.1.0
        version: 3.1.0(vite@4.5.14(@types/node@18.19.33)(sass@1.77.1))
      "@vitest/coverage-c8":
        specifier: ^0.29.8
        version: 0.29.8(vitest@0.29.8(@vitest/ui@0.29.8)(happy-dom@8.9.0)(jsdom@22.1.0)(playwright@1.44.0)(sass@1.77.1))
      "@vitest/ui":
        specifier: ^0.29.8
        version: 0.29.8
      autoprefixer:
        specifier: ^10.4.14
        version: 10.4.19(postcss@8.4.38)
      babel-plugin-import:
        specifier: ^1.13.6
        version: 1.13.8
      babel-plugin-macros:
        specifier: ^3.1.0
        version: 3.1.0
      dotenv:
        specifier: ^16.0.3
        version: 16.4.5
      env-cmd:
        specifier: ^10.1.0
        version: 10.1.0
      esbuild:
        specifier: ^0.25.9
        version: 0.25.9
      eslint:
        specifier: ^8.36.0
        version: 8.57.0
      eslint-config-prettier:
        specifier: ^8.8.0
        version: 8.10.0(eslint@8.57.0)
      eslint-config-standard:
        specifier: ^17.0.0
        version: 17.1.0(eslint-plugin-import@2.29.1(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@4.9.5))(eslint@8.57.0))(eslint-plugin-n@15.7.0(eslint@8.57.0))(eslint-plugin-promise@6.1.1(eslint@8.57.0))(eslint@8.57.0)
      eslint-plugin-import:
        specifier: ^2.27.5
        version: 2.29.1(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@4.9.5))(eslint@8.57.0)
      eslint-plugin-n:
        specifier: ^15.6.1
        version: 15.7.0(eslint@8.57.0)
      eslint-plugin-prettier:
        specifier: ^4.2.1
        version: 4.2.1(eslint-config-prettier@8.10.0(eslint@8.57.0))(eslint@8.57.0)(prettier@2.8.7)
      eslint-plugin-promise:
        specifier: ^6.1.1
        version: 6.1.1(eslint@8.57.0)
      eslint-plugin-react:
        specifier: ^7.32.2
        version: 7.34.1(eslint@8.57.0)
      eslint-plugin-simple-import-sort:
        specifier: ^10.0.0
        version: 10.0.0(eslint@8.57.0)
      happy-dom:
        specifier: ^8.9.0
        version: 8.9.0
      husky:
        specifier: ^8.0.3
        version: 8.0.3
      jsdom:
        specifier: ^22.0.0
        version: 22.1.0
      lint-staged:
        specifier: ^13.2.0
        version: 13.3.0
      npm-run-all:
        specifier: ^4.1.5
        version: 4.1.5
      p-min-delay:
        specifier: ^4.0.2
        version: 4.0.2
      postcss:
        specifier: ^8.4.21
        version: 8.4.38
      prettier:
        specifier: 2.8.7
        version: 2.8.7
      prettier-eslint:
        specifier: ^15.0.1
        version: 15.0.1
      rimraf:
        specifier: ^5.0.1
        version: 5.0.7
      sass:
        specifier: ^1.60.0
        version: 1.77.1
      svgo:
        specifier: ^3.0.2
        version: 3.3.2
      tailwindcss:
        specifier: ^3.3.0
        version: 3.4.3(ts-node@10.9.2(@types/node@18.19.33)(typescript@4.9.5))
      typescript:
        specifier: ^4.9.3
        version: 4.9.5
      vite:
        specifier: ^4.5.14
        version: 4.5.14(@types/node@18.19.33)(sass@1.77.1)
      vite-plugin-checker:
        specifier: ^0.5.6
        version: 0.5.6(eslint@8.57.0)(optionator@0.9.4)(typescript@4.9.5)(vite@4.5.14(@types/node@18.19.33)(sass@1.77.1))
      vitest:
        specifier: ^0.29.8
        version: 0.29.8(@vitest/ui@0.29.8)(happy-dom@8.9.0)(jsdom@22.1.0)(playwright@1.44.0)(sass@1.77.1)

packages:
  "@alloc/quick-lru@5.2.0":
    resolution:
      {
        integrity: sha512-UrcABB+4bUrFABwbluTIBErXwvbsU/V7TZWfmbgJfbkwiBuziS9gxdODUyuiecfdGQ85jglMW6juS3+z5TsKLw==,
      }
    engines: { node: ">=10" }

  "@ampproject/remapping@2.3.0":
    resolution:
      {
        integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==,
      }
    engines: { node: ">=6.0.0" }

  "@babel/code-frame@7.24.2":
    resolution:
      {
        integrity: sha512-y5+tLQyV8pg3fsiln67BVLD1P13Eg4lh5RW9mF0zUuvLrv9uIQ4MCL+CRT+FTsBlBjcIan6PGsLcBN0m3ClUyQ==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/compat-data@7.24.4":
    resolution:
      {
        integrity: sha512-vg8Gih2MLK+kOkHJp4gBEIkyaIi00jgWot2D9QOmmfLC8jINSOzmCLta6Bvz/JSBCqnegV0L80jhxkol5GWNfQ==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/core@7.24.5":
    resolution:
      {
        integrity: sha512-tVQRucExLQ02Boi4vdPp49svNGcfL2GhdTCT9aldhXgCJVAI21EtRfBettiuLUwce/7r6bFdgs6JFkcdTiFttA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/generator@7.24.5":
    resolution:
      {
        integrity: sha512-x32i4hEXvr+iI0NEoEfDKzlemF8AmtOP8CcrRaEcpzysWuoEb1KknpcvMsHKPONoKZiDuItklgWhB18xEhr9PA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-annotate-as-pure@7.22.5":
    resolution:
      {
        integrity: sha512-LvBTxu8bQSQkcyKOU+a1btnNFQ1dMAd0R6PyW3arXes06F6QLWLIrd681bxRPIXlrMGR3XYnW9JyML7dP3qgxg==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-builder-binary-assignment-operator-visitor@7.22.15":
    resolution:
      {
        integrity: sha512-QkBXwGgaoC2GtGZRoma6kv7Szfv06khvhFav67ZExau2RaXzy8MpHSMO2PNoP2XtmQphJQRHFfg77Bq731Yizw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-compilation-targets@7.23.6":
    resolution:
      {
        integrity: sha512-9JB548GZoQVmzrFgp8o7KxdgkTGm6xs9DW0o/Pim72UDjzr5ObUQ6ZzYPqA+g9OTS2bBQoctLJrky0RDCAWRgQ==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-create-class-features-plugin@7.24.5":
    resolution:
      {
        integrity: sha512-uRc4Cv8UQWnE4NXlYTIIdM7wfFkOqlFztcC/gVXDKohKoVB3OyonfelUBaJzSwpBntZ2KYGF/9S7asCHsXwW6g==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@babel/helper-create-regexp-features-plugin@7.22.15":
    resolution:
      {
        integrity: sha512-29FkPLFjn4TPEa3RE7GpW+qbE8tlsu3jntNYNfcGsc49LphF1PQIiD+vMZ1z1xVOKt+93khA9tc2JBs3kBjA7w==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@babel/helper-define-polyfill-provider@0.6.2":
    resolution:
      {
        integrity: sha512-LV76g+C502biUK6AyZ3LK10vDpDyCzZnhZFXkH1L75zHPj68+qc8Zfpx2th+gzwA2MzyK+1g/3EPl62yFnVttQ==,
      }
    peerDependencies:
      "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0

  "@babel/helper-environment-visitor@7.22.20":
    resolution:
      {
        integrity: sha512-zfedSIzFhat/gFhWfHtgWvlec0nqB9YEIVrpuwjruLlXfUSnA8cJB0miHKwqDnQ7d32aKo2xt88/xZptwxbfhA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-function-name@7.23.0":
    resolution:
      {
        integrity: sha512-OErEqsrxjZTJciZ4Oo+eoZqeW9UIiOcuYKRJA4ZAgV9myA+pOXhhmpfNCKjEH/auVfEYVFJ6y1Tc4r0eIApqiw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-hoist-variables@7.22.5":
    resolution:
      {
        integrity: sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-member-expression-to-functions@7.24.5":
    resolution:
      {
        integrity: sha512-4owRteeihKWKamtqg4JmWSsEZU445xpFRXPEwp44HbgbxdWlUV1b4Agg4lkA806Lil5XM/e+FJyS0vj5T6vmcA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-module-imports@7.24.3":
    resolution:
      {
        integrity: sha512-viKb0F9f2s0BCS22QSF308z/+1YWKV/76mwt61NBzS5izMzDPwdq1pTrzf+Li3npBWX9KdQbkeCt1jSAM7lZqg==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-module-transforms@7.24.5":
    resolution:
      {
        integrity: sha512-9GxeY8c2d2mdQUP1Dye0ks3VDyIMS98kt/llQ2nUId8IsWqTF0l1LkSX0/uP7l7MCDrzXS009Hyhe2gzTiGW8A==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@babel/helper-optimise-call-expression@7.22.5":
    resolution:
      {
        integrity: sha512-HBwaojN0xFRx4yIvpwGqxiV2tUfl7401jlok564NgB9EHS1y6QT17FmKWm4ztqjeVdXLuC4fSvHc5ePpQjoTbw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-plugin-utils@7.24.5":
    resolution:
      {
        integrity: sha512-xjNLDopRzW2o6ba0gKbkZq5YWEBaK3PCyTOY1K2P/O07LGMhMqlMXPxwN4S5/RhWuCobT8z0jrlKGlYmeR1OhQ==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-remap-async-to-generator@7.22.20":
    resolution:
      {
        integrity: sha512-pBGyV4uBqOns+0UvhsTO8qgl8hO89PmiDYv+/COyp1aeMcmfrfruz+/nCMFiYyFF/Knn0yfrC85ZzNFjembFTw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@babel/helper-replace-supers@7.24.1":
    resolution:
      {
        integrity: sha512-QCR1UqC9BzG5vZl8BMicmZ28RuUBnHhAMddD8yHFHDRH9lLTZ9uUPehX8ctVPT8l0TKblJidqcgUUKGVrePleQ==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@babel/helper-simple-access@7.24.5":
    resolution:
      {
        integrity: sha512-uH3Hmf5q5n7n8mz7arjUlDOCbttY/DW4DYhE6FUsjKJ/oYC1kQQUvwEQWxRwUpX9qQKRXeqLwWxrqilMrf32sQ==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-skip-transparent-expression-wrappers@7.22.5":
    resolution:
      {
        integrity: sha512-tK14r66JZKiC43p8Ki33yLBVJKlQDFoA8GYN67lWCDCqoL6EMMSuM9b+Iff2jHaM/RRFYl7K+iiru7hbRqNx8Q==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-split-export-declaration@7.24.5":
    resolution:
      {
        integrity: sha512-5CHncttXohrHk8GWOFCcCl4oRD9fKosWlIRgWm4ql9VYioKm52Mk2xsmoohvm7f3JoiLSM5ZgJuRaf5QZZYd3Q==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-string-parser@7.24.1":
    resolution:
      {
        integrity: sha512-2ofRCjnnA9y+wk8b9IAREroeUP02KHp431N2mhKniy2yKIDKpbrHv9eXwm8cBeWQYcJmzv5qKCu65P47eCF7CQ==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-validator-identifier@7.24.5":
    resolution:
      {
        integrity: sha512-3q93SSKX2TWCG30M2G2kwaKeTYgEUp5Snjuj8qm729SObL6nbtUldAi37qbxkD5gg3xnBio+f9nqpSepGZMvxA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-validator-option@7.23.5":
    resolution:
      {
        integrity: sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helper-wrap-function@7.24.5":
    resolution:
      {
        integrity: sha512-/xxzuNvgRl4/HLNKvnFwdhdgN3cpLxgLROeLDl83Yx0AJ1SGvq1ak0OszTOjDfiB8Vx03eJbeDWh9r+jCCWttw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/helpers@7.24.5":
    resolution:
      {
        integrity: sha512-CiQmBMMpMQHwM5m01YnrM6imUG1ebgYJ+fAIW4FZe6m4qHTPaRHti+R8cggAwkdz4oXhtO4/K9JWlh+8hIfR2Q==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/highlight@7.24.5":
    resolution:
      {
        integrity: sha512-8lLmua6AVh/8SLJRRVD6V8p73Hir9w5mJrhE+IPpILG31KKlI9iz5zmBYKcWPS59qSfgP9RaSBQSHHE81WKuEw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/parser@7.24.5":
    resolution:
      {
        integrity: sha512-EOv5IK8arwh3LI47dz1b0tKUb/1uhHAnHJOrjgtQMIpu1uXd9mlFrJg9IUgGUgZ41Ch0K8REPTYpO7B76b4vJg==,
      }
    engines: { node: ">=6.0.0" }
    hasBin: true

  "@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.24.5":
    resolution:
      {
        integrity: sha512-LdXRi1wEMTrHVR4Zc9F8OewC3vdm5h4QB6L71zy6StmYeqGi1b3ttIO8UC+BfZKcH9jdr4aI249rBkm+3+YvHw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.24.1":
    resolution:
      {
        integrity: sha512-y4HqEnkelJIOQGd+3g1bTeKsA5c6qM7eOn7VggGVbBc0y8MLSKHacwcIE2PplNlQSj0PqS9rrXL/nkPVK+kUNg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.24.1":
    resolution:
      {
        integrity: sha512-Hj791Ii4ci8HqnaKHAlLNs+zaLXb0EzSDhiAWp5VNlyvCNymYfacs64pxTxbH1znW/NcArSmwpmG9IKE/TUVVQ==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.13.0

  "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.24.1":
    resolution:
      {
        integrity: sha512-m9m/fXsXLiHfwdgydIFnpk+7jlVbnvlK5B2EKiPdLUb6WX654ZaaEWJUjk8TftRbZpK0XibovlLWX4KIZhV6jw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2":
    resolution:
      {
        integrity: sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-async-generators@7.8.4":
    resolution:
      {
        integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-class-properties@7.12.13":
    resolution:
      {
        integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-class-static-block@7.14.5":
    resolution:
      {
        integrity: sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-dynamic-import@7.8.3":
    resolution:
      {
        integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-export-namespace-from@7.8.3":
    resolution:
      {
        integrity: sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-import-assertions@7.24.1":
    resolution:
      {
        integrity: sha512-IuwnI5XnuF189t91XbxmXeCDz3qs6iDRO7GJ++wcfgeXNs/8FmIlKcpDSXNVyuLQxlwvskmI3Ct73wUODkJBlQ==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-import-attributes@7.24.1":
    resolution:
      {
        integrity: sha512-zhQTMH0X2nVLnb04tz+s7AMuasX8U0FnpE+nHTOhSOINjWMnopoZTxtIKsd45n4GQ/HIZLyfIpoul8e2m0DnRA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-import-meta@7.10.4":
    resolution:
      {
        integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-json-strings@7.8.3":
    resolution:
      {
        integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-jsx@7.24.1":
    resolution:
      {
        integrity: sha512-2eCtxZXf+kbkMIsXS4poTvT4Yu5rXiRa+9xGVT56raghjmBTKMpFNc9R4IDiB4emao9eO22Ox7CxuJG7BgExqA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-logical-assignment-operators@7.10.4":
    resolution:
      {
        integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-nullish-coalescing-operator@7.8.3":
    resolution:
      {
        integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-numeric-separator@7.10.4":
    resolution:
      {
        integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-object-rest-spread@7.8.3":
    resolution:
      {
        integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-optional-catch-binding@7.8.3":
    resolution:
      {
        integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-optional-chaining@7.8.3":
    resolution:
      {
        integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-private-property-in-object@7.14.5":
    resolution:
      {
        integrity: sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-top-level-await@7.14.5":
    resolution:
      {
        integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-typescript@7.24.1":
    resolution:
      {
        integrity: sha512-Yhnmvy5HZEnHUty6i++gcfH1/l68AHnItFHnaCv6hn9dNh0hQvvQJsxpi4BMBFN5DLeHBuucT/0DgzXif/OyRw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-syntax-unicode-sets-regex@7.18.6":
    resolution:
      {
        integrity: sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@babel/plugin-transform-arrow-functions@7.24.1":
    resolution:
      {
        integrity: sha512-ngT/3NkRhsaep9ck9uj2Xhv9+xB1zShY3tM3g6om4xxCELwCDN4g4Aq5dRn48+0hasAql7s2hdBOysCfNpr4fw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-async-generator-functions@7.24.3":
    resolution:
      {
        integrity: sha512-Qe26CMYVjpQxJ8zxM1340JFNjZaF+ISWpr1Kt/jGo+ZTUzKkfw/pphEWbRCb+lmSM6k/TOgfYLvmbHkUQ0asIg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-async-to-generator@7.24.1":
    resolution:
      {
        integrity: sha512-AawPptitRXp1y0n4ilKcGbRYWfbbzFWz2NqNu7dacYDtFtz0CMjG64b3LQsb3KIgnf4/obcUL78hfaOS7iCUfw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-block-scoped-functions@7.24.1":
    resolution:
      {
        integrity: sha512-TWWC18OShZutrv9C6mye1xwtam+uNi2bnTOCBUd5sZxyHOiWbU6ztSROofIMrK84uweEZC219POICK/sTYwfgg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-block-scoping@7.24.5":
    resolution:
      {
        integrity: sha512-sMfBc3OxghjC95BkYrYocHL3NaOplrcaunblzwXhGmlPwpmfsxr4vK+mBBt49r+S240vahmv+kUxkeKgs+haCw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-class-properties@7.24.1":
    resolution:
      {
        integrity: sha512-OMLCXi0NqvJfORTaPQBwqLXHhb93wkBKZ4aNwMl6WtehO7ar+cmp+89iPEQPqxAnxsOKTaMcs3POz3rKayJ72g==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-class-static-block@7.24.4":
    resolution:
      {
        integrity: sha512-B8q7Pz870Hz/q9UgP8InNpY01CSLDSCyqX7zcRuv3FcPl87A2G17lASroHWaCtbdIcbYzOZ7kWmXFKbijMSmFg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.12.0

  "@babel/plugin-transform-classes@7.24.5":
    resolution:
      {
        integrity: sha512-gWkLP25DFj2dwe9Ck8uwMOpko4YsqyfZJrOmqqcegeDYEbp7rmn4U6UQZNj08UF6MaX39XenSpKRCvpDRBtZ7Q==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-computed-properties@7.24.1":
    resolution:
      {
        integrity: sha512-5pJGVIUfJpOS+pAqBQd+QMaTD2vCL/HcePooON6pDpHgRp4gNRmzyHTPIkXntwKsq3ayUFVfJaIKPw2pOkOcTw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-destructuring@7.24.5":
    resolution:
      {
        integrity: sha512-SZuuLyfxvsm+Ah57I/i1HVjveBENYK9ue8MJ7qkc7ndoNjqquJiElzA7f5yaAXjyW2hKojosOTAQQRX50bPSVg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-dotall-regex@7.24.1":
    resolution:
      {
        integrity: sha512-p7uUxgSoZwZ2lPNMzUkqCts3xlp8n+o05ikjy7gbtFJSt9gdU88jAmtfmOxHM14noQXBxfgzf2yRWECiNVhTCw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-duplicate-keys@7.24.1":
    resolution:
      {
        integrity: sha512-msyzuUnvsjsaSaocV6L7ErfNsa5nDWL1XKNnDePLgmz+WdU4w/J8+AxBMrWfi9m4IxfL5sZQKUPQKDQeeAT6lA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-dynamic-import@7.24.1":
    resolution:
      {
        integrity: sha512-av2gdSTyXcJVdI+8aFZsCAtR29xJt0S5tas+Ef8NvBNmD1a+N/3ecMLeMBgfcK+xzsjdLDT6oHt+DFPyeqUbDA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-exponentiation-operator@7.24.1":
    resolution:
      {
        integrity: sha512-U1yX13dVBSwS23DEAqU+Z/PkwE9/m7QQy8Y9/+Tdb8UWYaGNDYwTLi19wqIAiROr8sXVum9A/rtiH5H0boUcTw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-export-namespace-from@7.24.1":
    resolution:
      {
        integrity: sha512-Ft38m/KFOyzKw2UaJFkWG9QnHPG/Q/2SkOrRk4pNBPg5IPZ+dOxcmkK5IyuBcxiNPyyYowPGUReyBvrvZs7IlQ==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-for-of@7.24.1":
    resolution:
      {
        integrity: sha512-OxBdcnF04bpdQdR3i4giHZNZQn7cm8RQKcSwA17wAAqEELo1ZOwp5FFgeptWUQXFyT9kwHo10aqqauYkRZPCAg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-function-name@7.24.1":
    resolution:
      {
        integrity: sha512-BXmDZpPlh7jwicKArQASrj8n22/w6iymRnvHYYd2zO30DbE277JO20/7yXJT3QxDPtiQiOxQBbZH4TpivNXIxA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-json-strings@7.24.1":
    resolution:
      {
        integrity: sha512-U7RMFmRvoasscrIFy5xA4gIp8iWnWubnKkKuUGJjsuOH7GfbMkB+XZzeslx2kLdEGdOJDamEmCqOks6e8nv8DQ==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-literals@7.24.1":
    resolution:
      {
        integrity: sha512-zn9pwz8U7nCqOYIiBaOxoQOtYmMODXTJnkxG4AtX8fPmnCRYWBOHD0qcpwS9e2VDSp1zNJYpdnFMIKb8jmwu6g==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-logical-assignment-operators@7.24.1":
    resolution:
      {
        integrity: sha512-OhN6J4Bpz+hIBqItTeWJujDOfNP+unqv/NJgyhlpSqgBTPm37KkMmZV6SYcOj+pnDbdcl1qRGV/ZiIjX9Iy34w==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-member-expression-literals@7.24.1":
    resolution:
      {
        integrity: sha512-4ojai0KysTWXzHseJKa1XPNXKRbuUrhkOPY4rEGeR+7ChlJVKxFa3H3Bz+7tWaGKgJAXUWKOGmltN+u9B3+CVg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-modules-amd@7.24.1":
    resolution:
      {
        integrity: sha512-lAxNHi4HVtjnHd5Rxg3D5t99Xm6H7b04hUS7EHIXcUl2EV4yl1gWdqZrNzXnSrHveL9qMdbODlLF55mvgjAfaQ==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-modules-commonjs@7.24.1":
    resolution:
      {
        integrity: sha512-szog8fFTUxBfw0b98gEWPaEqF42ZUD/T3bkynW/wtgx2p/XCP55WEsb+VosKceRSd6njipdZvNogqdtI4Q0chw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-modules-systemjs@7.24.1":
    resolution:
      {
        integrity: sha512-mqQ3Zh9vFO1Tpmlt8QPnbwGHzNz3lpNEMxQb1kAemn/erstyqw1r9KeOlOfo3y6xAnFEcOv2tSyrXfmMk+/YZA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-modules-umd@7.24.1":
    resolution:
      {
        integrity: sha512-tuA3lpPj+5ITfcCluy6nWonSL7RvaG0AOTeAuvXqEKS34lnLzXpDb0dcP6K8jD0zWZFNDVly90AGFJPnm4fOYg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-named-capturing-groups-regex@7.22.5":
    resolution:
      {
        integrity: sha512-YgLLKmS3aUBhHaxp5hi1WJTgOUb/NCuDHzGT9z9WTt3YG+CPRhJs6nprbStx6DnWM4dh6gt7SU3sZodbZ08adQ==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@babel/plugin-transform-new-target@7.24.1":
    resolution:
      {
        integrity: sha512-/rurytBM34hYy0HKZQyA0nHbQgQNFm4Q/BOc9Hflxi2X3twRof7NaE5W46j4kQitm7SvACVRXsa6N/tSZxvPug==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-nullish-coalescing-operator@7.24.1":
    resolution:
      {
        integrity: sha512-iQ+caew8wRrhCikO5DrUYx0mrmdhkaELgFa+7baMcVuhxIkN7oxt06CZ51D65ugIb1UWRQ8oQe+HXAVM6qHFjw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-numeric-separator@7.24.1":
    resolution:
      {
        integrity: sha512-7GAsGlK4cNL2OExJH1DzmDeKnRv/LXq0eLUSvudrehVA5Rgg4bIrqEUW29FbKMBRT0ztSqisv7kjP+XIC4ZMNw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-object-rest-spread@7.24.5":
    resolution:
      {
        integrity: sha512-7EauQHszLGM3ay7a161tTQH7fj+3vVM/gThlz5HpFtnygTxjrlvoeq7MPVA1Vy9Q555OB8SnAOsMkLShNkkrHA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-object-super@7.24.1":
    resolution:
      {
        integrity: sha512-oKJqR3TeI5hSLRxudMjFQ9re9fBVUU0GICqM3J1mi8MqlhVr6hC/ZN4ttAyMuQR6EZZIY6h/exe5swqGNNIkWQ==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-optional-catch-binding@7.24.1":
    resolution:
      {
        integrity: sha512-oBTH7oURV4Y+3EUrf6cWn1OHio3qG/PVwO5J03iSJmBg6m2EhKjkAu/xuaXaYwWW9miYtvbWv4LNf0AmR43LUA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-optional-chaining@7.24.5":
    resolution:
      {
        integrity: sha512-xWCkmwKT+ihmA6l7SSTpk8e4qQl/274iNbSKRRS8mpqFR32ksy36+a+LWY8OXCCEefF8WFlnOHVsaDI2231wBg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-parameters@7.24.5":
    resolution:
      {
        integrity: sha512-9Co00MqZ2aoky+4j2jhofErthm6QVLKbpQrvz20c3CH9KQCLHyNB+t2ya4/UrRpQGR+Wrwjg9foopoeSdnHOkA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-private-methods@7.24.1":
    resolution:
      {
        integrity: sha512-tGvisebwBO5em4PaYNqt4fkw56K2VALsAbAakY0FjTYqJp7gfdrgr7YX76Or8/cpik0W6+tj3rZ0uHU9Oil4tw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-private-property-in-object@7.24.5":
    resolution:
      {
        integrity: sha512-JM4MHZqnWR04jPMujQDTBVRnqxpLLpx2tkn7iPn+Hmsc0Gnb79yvRWOkvqFOx3Z7P7VxiRIR22c4eGSNj87OBQ==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-property-literals@7.24.1":
    resolution:
      {
        integrity: sha512-LetvD7CrHmEx0G442gOomRr66d7q8HzzGGr4PMHGr+5YIm6++Yke+jxj246rpvsbyhJwCLxcTn6zW1P1BSenqA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-react-constant-elements@7.24.1":
    resolution:
      {
        integrity: sha512-QXp1U9x0R7tkiGB0FOk8o74jhnap0FlZ5gNkRIWdG3eP+SvMFg118e1zaWewDzgABb106QSKpVsD3Wgd8t6ifA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-react-display-name@7.24.1":
    resolution:
      {
        integrity: sha512-mvoQg2f9p2qlpDQRBC7M3c3XTr0k7cp/0+kFKKO/7Gtu0LSw16eKB+Fabe2bDT/UpsyasTBBkAnbdsLrkD5XMw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-react-jsx-development@7.22.5":
    resolution:
      {
        integrity: sha512-bDhuzwWMuInwCYeDeMzyi7TaBgRQei6DqxhbyniL7/VG4RSS7HtSL2QbY4eESy1KJqlWt8g3xeEBGPuo+XqC8A==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-react-jsx-self@7.24.5":
    resolution:
      {
        integrity: sha512-RtCJoUO2oYrYwFPtR1/jkoBEcFuI1ae9a9IMxeyAVa3a1Ap4AnxmyIKG2b2FaJKqkidw/0cxRbWN+HOs6ZWd1w==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-react-jsx-source@7.24.1":
    resolution:
      {
        integrity: sha512-1v202n7aUq4uXAieRTKcwPzNyphlCuqHHDcdSNc+vdhoTEZcFMh+L5yZuCmGaIO7bs1nJUNfHB89TZyoL48xNA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-react-jsx@7.23.4":
    resolution:
      {
        integrity: sha512-5xOpoPguCZCRbo/JeHlloSkTA8Bld1J/E1/kLfD1nsuiW1m8tduTA1ERCgIZokDflX/IBzKcqR3l7VlRgiIfHA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-react-pure-annotations@7.24.1":
    resolution:
      {
        integrity: sha512-+pWEAaDJvSm9aFvJNpLiM2+ktl2Sn2U5DdyiWdZBxmLc6+xGt88dvFqsHiAiDS+8WqUwbDfkKz9jRxK3M0k+kA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-regenerator@7.24.1":
    resolution:
      {
        integrity: sha512-sJwZBCzIBE4t+5Q4IGLaaun5ExVMRY0lYwos/jNecjMrVCygCdph3IKv0tkP5Fc87e/1+bebAmEAGBfnRD+cnw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-reserved-words@7.24.1":
    resolution:
      {
        integrity: sha512-JAclqStUfIwKN15HrsQADFgeZt+wexNQ0uLhuqvqAUFoqPMjEcFCYZBhq0LUdz6dZK/mD+rErhW71fbx8RYElg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-shorthand-properties@7.24.1":
    resolution:
      {
        integrity: sha512-LyjVB1nsJ6gTTUKRjRWx9C1s9hE7dLfP/knKdrfeH9UPtAGjYGgxIbFfx7xyLIEWs7Xe1Gnf8EWiUqfjLhInZA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-spread@7.24.1":
    resolution:
      {
        integrity: sha512-KjmcIM+fxgY+KxPVbjelJC6hrH1CgtPmTvdXAfn3/a9CnWGSTY7nH4zm5+cjmWJybdcPSsD0++QssDsjcpe47g==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-sticky-regex@7.24.1":
    resolution:
      {
        integrity: sha512-9v0f1bRXgPVcPrngOQvLXeGNNVLc8UjMVfebo9ka0WF3/7+aVUHmaJVT3sa0XCzEFioPfPHZiOcYG9qOsH63cw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-template-literals@7.24.1":
    resolution:
      {
        integrity: sha512-WRkhROsNzriarqECASCNu/nojeXCDTE/F2HmRgOzi7NGvyfYGq1NEjKBK3ckLfRgGc6/lPAqP0vDOSw3YtG34g==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-typeof-symbol@7.24.5":
    resolution:
      {
        integrity: sha512-UTGnhYVZtTAjdwOTzT+sCyXmTn8AhaxOS/MjG9REclZ6ULHWF9KoCZur0HSGU7hk8PdBFKKbYe6+gqdXWz84Jg==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-typescript@7.24.5":
    resolution:
      {
        integrity: sha512-E0VWu/hk83BIFUWnsKZ4D81KXjN5L3MobvevOHErASk9IPwKHOkTgvqzvNo1yP/ePJWqqK2SpUR5z+KQbl6NVw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-unicode-escapes@7.24.1":
    resolution:
      {
        integrity: sha512-RlkVIcWT4TLI96zM660S877E7beKlQw7Ig+wqkKBiWfj0zH5Q4h50q6er4wzZKRNSYpfo6ILJ+hrJAGSX2qcNw==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-unicode-property-regex@7.24.1":
    resolution:
      {
        integrity: sha512-Ss4VvlfYV5huWApFsF8/Sq0oXnGO+jB+rijFEFugTd3cwSObUSnUi88djgR5528Csl0uKlrI331kRqe56Ov2Ng==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-unicode-regex@7.24.1":
    resolution:
      {
        integrity: sha512-2A/94wgZgxfTsiLaQ2E36XAOdcZmGAaEEgVmxQWwZXWkGhvoHbaqXcKnU8zny4ycpu3vNqg0L/PcCiYtHtA13g==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/plugin-transform-unicode-sets-regex@7.24.1":
    resolution:
      {
        integrity: sha512-fqj4WuzzS+ukpgerpAoOnMfQXwUHFxXUZUE84oL2Kao2N8uSlvcpnAidKASgsNgzZHBsHWvcm8s9FPWUhAb8fA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@babel/preset-env@7.24.5":
    resolution:
      {
        integrity: sha512-UGK2ifKtcC8i5AI4cH+sbLLuLc2ktYSFJgBAXorKAsHUZmrQ1q6aQ6i3BvU24wWs2AAKqQB6kq3N9V9Gw1HiMQ==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/preset-modules@0.1.6-no-external-plugins":
    resolution:
      {
        integrity: sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0-0 || ^8.0.0-0 <8.0.0

  "@babel/preset-react@7.24.1":
    resolution:
      {
        integrity: sha512-eFa8up2/8cZXLIpkafhaADTXSnl7IsUFCYenRWrARBz0/qZwcT0RBXpys0LJU4+WfPoF2ZG6ew6s2V6izMCwRA==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/preset-typescript@7.24.1":
    resolution:
      {
        integrity: sha512-1DBaMmRDpuYQBPWD8Pf/WEwCrtgRHxsZnP4mIy9G/X+hFfbI47Q2G4t1Paakld84+qsk2fSsUPMKg71jkoOOaQ==,
      }
    engines: { node: ">=6.9.0" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@babel/regjsgen@0.8.0":
    resolution:
      {
        integrity: sha512-x/rqGMdzj+fWZvCOYForTghzbtqPDZ5gPwaoNGHdgDfF2QA/XZbCBp4Moo5scrkAMPhB7z26XM/AaHuIJdgauA==,
      }

  "@babel/runtime@7.24.5":
    resolution:
      {
        integrity: sha512-Nms86NXrsaeU9vbBJKni6gXiEXZ4CVpYVzEjDH9Sb8vmZ3UljyA1GSOJl/6LGPO8EHLuSF9H+IxNXHPX8QHJ4g==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/runtime@7.25.0":
    resolution:
      {
        integrity: sha512-7dRy4DwXwtzBrPbZflqxnvfxLF8kdZXPkhymtDeFoFqE6ldzjQFgYTtYIFARcLEYDrqfBfYcZt1WqFxRoyC9Rw==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/template@7.24.0":
    resolution:
      {
        integrity: sha512-Bkf2q8lMB0AFpX0NFEqSbx1OkTHf0f+0j82mkw+ZpzBnkk7e9Ql0891vlfgi+kHwOk8tQjiQHpqh4LaSa0fKEA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/traverse@7.24.5":
    resolution:
      {
        integrity: sha512-7aaBLeDQ4zYcUFDUD41lJc1fG8+5IU9DaNSJAgal866FGvmD5EbWQgnEC6kO1gGLsX0esNkfnJSndbTXA3r7UA==,
      }
    engines: { node: ">=6.9.0" }

  "@babel/types@7.24.5":
    resolution:
      {
        integrity: sha512-6mQNsaLeXTw0nxYUYu+NSa4Hx4BlF1x1x8/PMFbiR+GBSr+2DkECc69b8hgy2frEodNcvPffeH8YfWd3LI6jhQ==,
      }
    engines: { node: ">=6.9.0" }

  "@bcoe/v8-coverage@0.2.3":
    resolution:
      {
        integrity: sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==,
      }

  "@commitlint/cli@17.8.1":
    resolution:
      {
        integrity: sha512-ay+WbzQesE0Rv4EQKfNbSMiJJ12KdKTDzIt0tcK4k11FdsWmtwP0Kp1NWMOUswfIWo6Eb7p7Ln721Nx9FLNBjg==,
      }
    engines: { node: ">=v14" }
    hasBin: true

  "@commitlint/config-conventional@17.8.1":
    resolution:
      {
        integrity: sha512-NxCOHx1kgneig3VLauWJcDWS40DVjg7nKOpBEEK9E5fjJpQqLCilcnKkIIjdBH98kEO1q3NpE5NSrZ2kl/QGJg==,
      }
    engines: { node: ">=v14" }

  "@commitlint/config-validator@17.8.1":
    resolution:
      {
        integrity: sha512-UUgUC+sNiiMwkyiuIFR7JG2cfd9t/7MV8VB4TZ+q02ZFkHoduUS4tJGsCBWvBOGD9Btev6IecPMvlWUfJorkEA==,
      }
    engines: { node: ">=v14" }

  "@commitlint/ensure@17.8.1":
    resolution:
      {
        integrity: sha512-xjafwKxid8s1K23NFpL8JNo6JnY/ysetKo8kegVM7c8vs+kWLP8VrQq+NbhgVlmCojhEDbzQKp4eRXSjVOGsow==,
      }
    engines: { node: ">=v14" }

  "@commitlint/execute-rule@17.8.1":
    resolution:
      {
        integrity: sha512-JHVupQeSdNI6xzA9SqMF+p/JjrHTcrJdI02PwesQIDCIGUrv04hicJgCcws5nzaoZbROapPs0s6zeVHoxpMwFQ==,
      }
    engines: { node: ">=v14" }

  "@commitlint/format@17.8.1":
    resolution:
      {
        integrity: sha512-f3oMTyZ84M9ht7fb93wbCKmWxO5/kKSbwuYvS867duVomoOsgrgljkGGIztmT/srZnaiGbaK8+Wf8Ik2tSr5eg==,
      }
    engines: { node: ">=v14" }

  "@commitlint/is-ignored@17.8.1":
    resolution:
      {
        integrity: sha512-UshMi4Ltb4ZlNn4F7WtSEugFDZmctzFpmbqvpyxD3la510J+PLcnyhf9chs7EryaRFJMdAKwsEKfNK0jL/QM4g==,
      }
    engines: { node: ">=v14" }

  "@commitlint/lint@17.8.1":
    resolution:
      {
        integrity: sha512-aQUlwIR1/VMv2D4GXSk7PfL5hIaFSfy6hSHV94O8Y27T5q+DlDEgd/cZ4KmVI+MWKzFfCTiTuWqjfRSfdRllCA==,
      }
    engines: { node: ">=v14" }

  "@commitlint/load@17.8.1":
    resolution:
      {
        integrity: sha512-iF4CL7KDFstP1kpVUkT8K2Wl17h2yx9VaR1ztTc8vzByWWcbO/WaKwxsnCOqow9tVAlzPfo1ywk9m2oJ9ucMqA==,
      }
    engines: { node: ">=v14" }

  "@commitlint/message@17.8.1":
    resolution:
      {
        integrity: sha512-6bYL1GUQsD6bLhTH3QQty8pVFoETfFQlMn2Nzmz3AOLqRVfNNtXBaSY0dhZ0dM6A2MEq4+2d7L/2LP8TjqGRkA==,
      }
    engines: { node: ">=v14" }

  "@commitlint/parse@17.8.1":
    resolution:
      {
        integrity: sha512-/wLUickTo0rNpQgWwLPavTm7WbwkZoBy3X8PpkUmlSmQJyWQTj0m6bDjiykMaDt41qcUbfeFfaCvXfiR4EGnfw==,
      }
    engines: { node: ">=v14" }

  "@commitlint/read@17.8.1":
    resolution:
      {
        integrity: sha512-Fd55Oaz9irzBESPCdMd8vWWgxsW3OWR99wOntBDHgf9h7Y6OOHjWEdS9Xzen1GFndqgyoaFplQS5y7KZe0kO2w==,
      }
    engines: { node: ">=v14" }

  "@commitlint/resolve-extends@17.8.1":
    resolution:
      {
        integrity: sha512-W/ryRoQ0TSVXqJrx5SGkaYuAaE/BUontL1j1HsKckvM6e5ZaG0M9126zcwL6peKSuIetJi7E87PRQF8O86EW0Q==,
      }
    engines: { node: ">=v14" }

  "@commitlint/rules@17.8.1":
    resolution:
      {
        integrity: sha512-2b7OdVbN7MTAt9U0vKOYKCDsOvESVXxQmrvuVUZ0rGFMCrCPJWWP1GJ7f0lAypbDAhaGb8zqtdOr47192LBrIA==,
      }
    engines: { node: ">=v14" }

  "@commitlint/to-lines@17.8.1":
    resolution:
      {
        integrity: sha512-LE0jb8CuR/mj6xJyrIk8VLz03OEzXFgLdivBytoooKO5xLt5yalc8Ma5guTWobw998sbR3ogDd+2jed03CFmJA==,
      }
    engines: { node: ">=v14" }

  "@commitlint/top-level@17.8.1":
    resolution:
      {
        integrity: sha512-l6+Z6rrNf5p333SHfEte6r+WkOxGlWK4bLuZKbtf/2TXRN+qhrvn1XE63VhD8Oe9oIHQ7F7W1nG2k/TJFhx2yA==,
      }
    engines: { node: ">=v14" }

  "@commitlint/types@17.8.1":
    resolution:
      {
        integrity: sha512-PXDQXkAmiMEG162Bqdh9ChML/GJZo6vU+7F03ALKDK8zYc6SuAr47LjG7hGYRqUOz+WK0dU7bQ0xzuqFMdxzeQ==,
      }
    engines: { node: ">=v14" }

  "@cspotcode/source-map-support@0.8.1":
    resolution:
      {
        integrity: sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==,
      }
    engines: { node: ">=12" }

  "@emotion/babel-plugin-jsx-pragmatic@0.2.1":
    resolution:
      {
        integrity: sha512-xy1SlgEJygAAIvIuC2idkGKJYa6v5iwoyILkvNKgk347bV+IImXrUat5Z86EmLGyWhEoTplVT9EHqTnHZG4HFw==,
      }
    peerDependencies:
      "@babel/core": ^7.0.0

  "@emotion/babel-plugin@11.11.0":
    resolution:
      {
        integrity: sha512-m4HEDZleaaCH+XgDDsPF15Ht6wTLsgDTeR3WYj9Q/k76JtWhrJjcP4+/XlG8LGT/Rol9qUfOIztXeA84ATpqPQ==,
      }

  "@emotion/cache@11.11.0":
    resolution:
      {
        integrity: sha512-P34z9ssTCBi3e9EI1ZsWpNHcfY1r09ZO0rZbRO2ob3ZQMnFI35jB536qoXbkdesr5EUhYi22anuEJuyxifaqAQ==,
      }

  "@emotion/hash@0.9.1":
    resolution:
      {
        integrity: sha512-gJB6HLm5rYwSLI6PQa+X1t5CFGrv1J1TWG+sOyMCeKz2ojaj6Fnl/rZEspogG+cvqbt4AE/2eIyD2QfLKTBNlQ==,
      }

  "@emotion/is-prop-valid@1.2.2":
    resolution:
      {
        integrity: sha512-uNsoYd37AFmaCdXlg6EYD1KaPOaRWRByMCYzbKUX4+hhMfrxdVSelShywL4JVaAeM/eHUOSprYBQls+/neX3pw==,
      }

  "@emotion/memoize@0.8.1":
    resolution:
      {
        integrity: sha512-W2P2c/VRW1/1tLox0mVUalvnWXxavmv/Oum2aPsRcoDJuob75FC3Y8FbpfLwUegRcxINtGUMPq0tFCvYNTBXNA==,
      }

  "@emotion/react@11.11.4":
    resolution:
      {
        integrity: sha512-t8AjMlF0gHpvvxk5mAtCqR4vmxiGHCeJBaQO6gncUSdklELOgtwjerNY2yuJNfwnc6vi16U/+uMF+afIawJ9iw==,
      }
    peerDependencies:
      "@types/react": "*"
      react: ">=16.8.0"
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@emotion/serialize@1.1.4":
    resolution:
      {
        integrity: sha512-RIN04MBT8g+FnDwgvIUi8czvr1LU1alUMI05LekWB5DGyTm8cCBMCRpq3GqaiyEDRptEXOyXnvZ58GZYu4kBxQ==,
      }

  "@emotion/sheet@1.2.2":
    resolution:
      {
        integrity: sha512-0QBtGvaqtWi+nx6doRwDdBIzhNdZrXUppvTM4dtZZWEGTXL/XE/yJxLMGlDT1Gt+UHH5IX1n+jkXyytE/av7OA==,
      }

  "@emotion/styled@11.11.5":
    resolution:
      {
        integrity: sha512-/ZjjnaNKvuMPxcIiUkf/9SHoG4Q196DRl1w82hQ3WCsjo1IUR8uaGWrC6a87CrYAW0Kb/pK7hk8BnLgLRi9KoQ==,
      }
    peerDependencies:
      "@emotion/react": ^11.0.0-rc.0
      "@types/react": "*"
      react: ">=16.8.0"
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@emotion/unitless@0.8.1":
    resolution:
      {
        integrity: sha512-KOEGMu6dmJZtpadb476IsZBclKvILjopjUii3V+7MnXIQCYh8W3NgNcgwo21n9LXZX6EDIKvqfjYxXebDwxKmQ==,
      }

  "@emotion/use-insertion-effect-with-fallbacks@1.0.1":
    resolution:
      {
        integrity: sha512-jT/qyKZ9rzLErtrjGgdkMBn2OP8wl0G3sQlBb3YPryvKHsjvINUhVaPFfP+fpBcOkmrVOVEEHQFJ7nbj2TH2gw==,
      }
    peerDependencies:
      react: ">=16.8.0"

  "@emotion/utils@1.2.1":
    resolution:
      {
        integrity: sha512-Y2tGf3I+XVnajdItskUCn6LX+VUDmP6lTL4fcqsXAv43dnlbZiuW4MWQW38rW/BVWSE7Q/7+XQocmpnRYILUmg==,
      }

  "@emotion/weak-memoize@0.3.1":
    resolution:
      {
        integrity: sha512-EsBwpc7hBUJWAsNPBmJy4hxWx12v6bshQsldrVmjxJoc3isbxhOrF2IcCpaXxfvq03NwkI7sbsOLXbYuqF/8Ww==,
      }

  "@esbuild/aix-ppc64@0.25.9":
    resolution:
      {
        integrity: sha512-OaGtL73Jck6pBKjNIe24BnFE6agGl+6KxDtTfHhy1HmhthfKouEcOhqpSL64K4/0WCtbKFLOdzD/44cJ4k9opA==,
      }
    engines: { node: ">=18" }
    cpu: [ppc64]
    os: [aix]

  "@esbuild/android-arm64@0.18.20":
    resolution:
      {
        integrity: sha512-Nz4rJcchGDtENV0eMKUNa6L12zz2zBDXuhj/Vjh18zGqB44Bi7MBMSXjgunJgjRhCmKOjnPuZp4Mb6OKqtMHLQ==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [android]

  "@esbuild/android-arm64@0.25.9":
    resolution:
      {
        integrity: sha512-IDrddSmpSv51ftWslJMvl3Q2ZT98fUSL2/rlUXuVqRXHCs5EUF1/f+jbjF5+NG9UffUDMCiTyh8iec7u8RlTLg==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [android]

  "@esbuild/android-arm@0.18.20":
    resolution:
      {
        integrity: sha512-fyi7TDI/ijKKNZTUJAQqiG5T7YjJXgnzkURqmGj13C6dCqckZBLdl4h7bkhHt/t0WP+zO9/zwroDvANaOqO5Sw==,
      }
    engines: { node: ">=12" }
    cpu: [arm]
    os: [android]

  "@esbuild/android-arm@0.25.9":
    resolution:
      {
        integrity: sha512-5WNI1DaMtxQ7t7B6xa572XMXpHAaI/9Hnhk8lcxF4zVN4xstUgTlvuGDorBguKEnZO70qwEcLpfifMLoxiPqHQ==,
      }
    engines: { node: ">=18" }
    cpu: [arm]
    os: [android]

  "@esbuild/android-x64@0.18.20":
    resolution:
      {
        integrity: sha512-8GDdlePJA8D6zlZYJV/jnrRAi6rOiNaCC/JclcXpB+KIuvfBN4owLtgzY2bsxnx666XjJx2kDPUmnTtR8qKQUg==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [android]

  "@esbuild/android-x64@0.25.9":
    resolution:
      {
        integrity: sha512-I853iMZ1hWZdNllhVZKm34f4wErd4lMyeV7BLzEExGEIZYsOzqDWDf+y082izYUE8gtJnYHdeDpN/6tUdwvfiw==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [android]

  "@esbuild/darwin-arm64@0.18.20":
    resolution:
      {
        integrity: sha512-bxRHW5kHU38zS2lPTPOyuyTm+S+eobPUnTNkdJEfAddYgEcll4xkT8DB9d2008DtTbl7uJag2HuE5NZAZgnNEA==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [darwin]

  "@esbuild/darwin-arm64@0.25.9":
    resolution:
      {
        integrity: sha512-XIpIDMAjOELi/9PB30vEbVMs3GV1v2zkkPnuyRRURbhqjyzIINwj+nbQATh4H9GxUgH1kFsEyQMxwiLFKUS6Rg==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [darwin]

  "@esbuild/darwin-x64@0.18.20":
    resolution:
      {
        integrity: sha512-pc5gxlMDxzm513qPGbCbDukOdsGtKhfxD1zJKXjCCcU7ju50O7MeAZ8c4krSJcOIJGFR+qx21yMMVYwiQvyTyQ==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [darwin]

  "@esbuild/darwin-x64@0.25.9":
    resolution:
      {
        integrity: sha512-jhHfBzjYTA1IQu8VyrjCX4ApJDnH+ez+IYVEoJHeqJm9VhG9Dh2BYaJritkYK3vMaXrf7Ogr/0MQ8/MeIefsPQ==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [darwin]

  "@esbuild/freebsd-arm64@0.18.20":
    resolution:
      {
        integrity: sha512-yqDQHy4QHevpMAaxhhIwYPMv1NECwOvIpGCZkECn8w2WFHXjEwrBn3CeNIYsibZ/iZEUemj++M26W3cNR5h+Tw==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [freebsd]

  "@esbuild/freebsd-arm64@0.25.9":
    resolution:
      {
        integrity: sha512-z93DmbnY6fX9+KdD4Ue/H6sYs+bhFQJNCPZsi4XWJoYblUqT06MQUdBCpcSfuiN72AbqeBFu5LVQTjfXDE2A6Q==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [freebsd]

  "@esbuild/freebsd-x64@0.18.20":
    resolution:
      {
        integrity: sha512-tgWRPPuQsd3RmBZwarGVHZQvtzfEBOreNuxEMKFcd5DaDn2PbBxfwLcj4+aenoh7ctXcbXmOQIn8HI6mCSw5MQ==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [freebsd]

  "@esbuild/freebsd-x64@0.25.9":
    resolution:
      {
        integrity: sha512-mrKX6H/vOyo5v71YfXWJxLVxgy1kyt1MQaD8wZJgJfG4gq4DpQGpgTB74e5yBeQdyMTbgxp0YtNj7NuHN0PoZg==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [freebsd]

  "@esbuild/linux-arm64@0.18.20":
    resolution:
      {
        integrity: sha512-2YbscF+UL7SQAVIpnWvYwM+3LskyDmPhe31pE7/aoTMFKKzIc9lLbyGUpmmb8a8AixOL61sQ/mFh3jEjHYFvdA==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [linux]

  "@esbuild/linux-arm64@0.25.9":
    resolution:
      {
        integrity: sha512-BlB7bIcLT3G26urh5Dmse7fiLmLXnRlopw4s8DalgZ8ef79Jj4aUcYbk90g8iCa2467HX8SAIidbL7gsqXHdRw==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [linux]

  "@esbuild/linux-arm@0.18.20":
    resolution:
      {
        integrity: sha512-/5bHkMWnq1EgKr1V+Ybz3s1hWXok7mDFUMQ4cG10AfW3wL02PSZi5kFpYKrptDsgb2WAJIvRcDm+qIvXf/apvg==,
      }
    engines: { node: ">=12" }
    cpu: [arm]
    os: [linux]

  "@esbuild/linux-arm@0.25.9":
    resolution:
      {
        integrity: sha512-HBU2Xv78SMgaydBmdor38lg8YDnFKSARg1Q6AT0/y2ezUAKiZvc211RDFHlEZRFNRVhcMamiToo7bDx3VEOYQw==,
      }
    engines: { node: ">=18" }
    cpu: [arm]
    os: [linux]

  "@esbuild/linux-ia32@0.18.20":
    resolution:
      {
        integrity: sha512-P4etWwq6IsReT0E1KHU40bOnzMHoH73aXp96Fs8TIT6z9Hu8G6+0SHSw9i2isWrD2nbx2qo5yUqACgdfVGx7TA==,
      }
    engines: { node: ">=12" }
    cpu: [ia32]
    os: [linux]

  "@esbuild/linux-ia32@0.25.9":
    resolution:
      {
        integrity: sha512-e7S3MOJPZGp2QW6AK6+Ly81rC7oOSerQ+P8L0ta4FhVi+/j/v2yZzx5CqqDaWjtPFfYz21Vi1S0auHrap3Ma3A==,
      }
    engines: { node: ">=18" }
    cpu: [ia32]
    os: [linux]

  "@esbuild/linux-loong64@0.18.20":
    resolution:
      {
        integrity: sha512-nXW8nqBTrOpDLPgPY9uV+/1DjxoQ7DoB2N8eocyq8I9XuqJ7BiAMDMf9n1xZM9TgW0J8zrquIb/A7s3BJv7rjg==,
      }
    engines: { node: ">=12" }
    cpu: [loong64]
    os: [linux]

  "@esbuild/linux-loong64@0.25.9":
    resolution:
      {
        integrity: sha512-Sbe10Bnn0oUAB2AalYztvGcK+o6YFFA/9829PhOCUS9vkJElXGdphz0A3DbMdP8gmKkqPmPcMJmJOrI3VYB1JQ==,
      }
    engines: { node: ">=18" }
    cpu: [loong64]
    os: [linux]

  "@esbuild/linux-mips64el@0.18.20":
    resolution:
      {
        integrity: sha512-d5NeaXZcHp8PzYy5VnXV3VSd2D328Zb+9dEq5HE6bw6+N86JVPExrA6O68OPwobntbNJ0pzCpUFZTo3w0GyetQ==,
      }
    engines: { node: ">=12" }
    cpu: [mips64el]
    os: [linux]

  "@esbuild/linux-mips64el@0.25.9":
    resolution:
      {
        integrity: sha512-YcM5br0mVyZw2jcQeLIkhWtKPeVfAerES5PvOzaDxVtIyZ2NUBZKNLjC5z3/fUlDgT6w89VsxP2qzNipOaaDyA==,
      }
    engines: { node: ">=18" }
    cpu: [mips64el]
    os: [linux]

  "@esbuild/linux-ppc64@0.18.20":
    resolution:
      {
        integrity: sha512-WHPyeScRNcmANnLQkq6AfyXRFr5D6N2sKgkFo2FqguP44Nw2eyDlbTdZwd9GYk98DZG9QItIiTlFLHJHjxP3FA==,
      }
    engines: { node: ">=12" }
    cpu: [ppc64]
    os: [linux]

  "@esbuild/linux-ppc64@0.25.9":
    resolution:
      {
        integrity: sha512-++0HQvasdo20JytyDpFvQtNrEsAgNG2CY1CLMwGXfFTKGBGQT3bOeLSYE2l1fYdvML5KUuwn9Z8L1EWe2tzs1w==,
      }
    engines: { node: ">=18" }
    cpu: [ppc64]
    os: [linux]

  "@esbuild/linux-riscv64@0.18.20":
    resolution:
      {
        integrity: sha512-WSxo6h5ecI5XH34KC7w5veNnKkju3zBRLEQNY7mv5mtBmrP/MjNBCAlsM2u5hDBlS3NGcTQpoBvRzqBcRtpq1A==,
      }
    engines: { node: ">=12" }
    cpu: [riscv64]
    os: [linux]

  "@esbuild/linux-riscv64@0.25.9":
    resolution:
      {
        integrity: sha512-uNIBa279Y3fkjV+2cUjx36xkx7eSjb8IvnL01eXUKXez/CBHNRw5ekCGMPM0BcmqBxBcdgUWuUXmVWwm4CH9kg==,
      }
    engines: { node: ">=18" }
    cpu: [riscv64]
    os: [linux]

  "@esbuild/linux-s390x@0.18.20":
    resolution:
      {
        integrity: sha512-+8231GMs3mAEth6Ja1iK0a1sQ3ohfcpzpRLH8uuc5/KVDFneH6jtAJLFGafpzpMRO6DzJ6AvXKze9LfFMrIHVQ==,
      }
    engines: { node: ">=12" }
    cpu: [s390x]
    os: [linux]

  "@esbuild/linux-s390x@0.25.9":
    resolution:
      {
        integrity: sha512-Mfiphvp3MjC/lctb+7D287Xw1DGzqJPb/J2aHHcHxflUo+8tmN/6d4k6I2yFR7BVo5/g7x2Monq4+Yew0EHRIA==,
      }
    engines: { node: ">=18" }
    cpu: [s390x]
    os: [linux]

  "@esbuild/linux-x64@0.18.20":
    resolution:
      {
        integrity: sha512-UYqiqemphJcNsFEskc73jQ7B9jgwjWrSayxawS6UVFZGWrAAtkzjxSqnoclCXxWtfwLdzU+vTpcNYhpn43uP1w==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [linux]

  "@esbuild/linux-x64@0.25.9":
    resolution:
      {
        integrity: sha512-iSwByxzRe48YVkmpbgoxVzn76BXjlYFXC7NvLYq+b+kDjyyk30J0JY47DIn8z1MO3K0oSl9fZoRmZPQI4Hklzg==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [linux]

  "@esbuild/netbsd-arm64@0.25.9":
    resolution:
      {
        integrity: sha512-9jNJl6FqaUG+COdQMjSCGW4QiMHH88xWbvZ+kRVblZsWrkXlABuGdFJ1E9L7HK+T0Yqd4akKNa/lO0+jDxQD4Q==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [netbsd]

  "@esbuild/netbsd-x64@0.18.20":
    resolution:
      {
        integrity: sha512-iO1c++VP6xUBUmltHZoMtCUdPlnPGdBom6IrO4gyKPFFVBKioIImVooR5I83nTew5UOYrk3gIJhbZh8X44y06A==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [netbsd]

  "@esbuild/netbsd-x64@0.25.9":
    resolution:
      {
        integrity: sha512-RLLdkflmqRG8KanPGOU7Rpg829ZHu8nFy5Pqdi9U01VYtG9Y0zOG6Vr2z4/S+/3zIyOxiK6cCeYNWOFR9QP87g==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [netbsd]

  "@esbuild/openbsd-arm64@0.25.9":
    resolution:
      {
        integrity: sha512-YaFBlPGeDasft5IIM+CQAhJAqS3St3nJzDEgsgFixcfZeyGPCd6eJBWzke5piZuZ7CtL656eOSYKk4Ls2C0FRQ==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [openbsd]

  "@esbuild/openbsd-x64@0.18.20":
    resolution:
      {
        integrity: sha512-e5e4YSsuQfX4cxcygw/UCPIEP6wbIL+se3sxPdCiMbFLBWu0eiZOJ7WoD+ptCLrmjZBK1Wk7I6D/I3NglUGOxg==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [openbsd]

  "@esbuild/openbsd-x64@0.25.9":
    resolution:
      {
        integrity: sha512-1MkgTCuvMGWuqVtAvkpkXFmtL8XhWy+j4jaSO2wxfJtilVCi0ZE37b8uOdMItIHz4I6z1bWWtEX4CJwcKYLcuA==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [openbsd]

  "@esbuild/openharmony-arm64@0.25.9":
    resolution:
      {
        integrity: sha512-4Xd0xNiMVXKh6Fa7HEJQbrpP3m3DDn43jKxMjxLLRjWnRsfxjORYJlXPO4JNcXtOyfajXorRKY9NkOpTHptErg==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [openharmony]

  "@esbuild/sunos-x64@0.18.20":
    resolution:
      {
        integrity: sha512-kDbFRFp0YpTQVVrqUd5FTYmWo45zGaXe0X8E1G/LKFC0v8x0vWrhOWSLITcCn63lmZIxfOMXtCfti/RxN/0wnQ==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [sunos]

  "@esbuild/sunos-x64@0.25.9":
    resolution:
      {
        integrity: sha512-WjH4s6hzo00nNezhp3wFIAfmGZ8U7KtrJNlFMRKxiI9mxEK1scOMAaa9i4crUtu+tBr+0IN6JCuAcSBJZfnphw==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [sunos]

  "@esbuild/win32-arm64@0.18.20":
    resolution:
      {
        integrity: sha512-ddYFR6ItYgoaq4v4JmQQaAI5s7npztfV4Ag6NrhiaW0RrnOXqBkgwZLofVTlq1daVTQNhtI5oieTvkRPfZrePg==,
      }
    engines: { node: ">=12" }
    cpu: [arm64]
    os: [win32]

  "@esbuild/win32-arm64@0.25.9":
    resolution:
      {
        integrity: sha512-mGFrVJHmZiRqmP8xFOc6b84/7xa5y5YvR1x8djzXpJBSv/UsNK6aqec+6JDjConTgvvQefdGhFDAs2DLAds6gQ==,
      }
    engines: { node: ">=18" }
    cpu: [arm64]
    os: [win32]

  "@esbuild/win32-ia32@0.18.20":
    resolution:
      {
        integrity: sha512-Wv7QBi3ID/rROT08SABTS7eV4hX26sVduqDOTe1MvGMjNd3EjOz4b7zeexIR62GTIEKrfJXKL9LFxTYgkyeu7g==,
      }
    engines: { node: ">=12" }
    cpu: [ia32]
    os: [win32]

  "@esbuild/win32-ia32@0.25.9":
    resolution:
      {
        integrity: sha512-b33gLVU2k11nVx1OhX3C8QQP6UHQK4ZtN56oFWvVXvz2VkDoe6fbG8TOgHFxEvqeqohmRnIHe5A1+HADk4OQww==,
      }
    engines: { node: ">=18" }
    cpu: [ia32]
    os: [win32]

  "@esbuild/win32-x64@0.18.20":
    resolution:
      {
        integrity: sha512-kTdfRcSiDfQca/y9QIkng02avJ+NCaQvrMejlsB3RRv5sE9rRoeBPISaZpKxHELzRxZyLvNts1P27W3wV+8geQ==,
      }
    engines: { node: ">=12" }
    cpu: [x64]
    os: [win32]

  "@esbuild/win32-x64@0.25.9":
    resolution:
      {
        integrity: sha512-PPOl1mi6lpLNQxnGoyAfschAodRFYXJ+9fs6WHXz7CSWKbOqiMZsubC+BQsVKuul+3vKLuwTHsS2c2y9EoKwxQ==,
      }
    engines: { node: ">=18" }
    cpu: [x64]
    os: [win32]

  "@eslint-community/eslint-utils@4.4.0":
    resolution:
      {
        integrity: sha512-1/sA4dwrzBAyeUoQ6oxahHKmrZvsnLCg4RfxW3ZFGGmQkSNQPFNLV9CUEFQP1x9EYXHTo5p6xdhZM1Ne9p/AfA==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || >=8.0.0

  "@eslint-community/regexpp@4.10.0":
    resolution:
      {
        integrity: sha512-Cu96Sd2By9mCNTx2iyKOmq10v22jUVQv0lQnlGNy16oE9589yE+QADPbrMGCkA51cKZSg3Pu/aTJVTGfL/qjUA==,
      }
    engines: { node: ^12.0.0 || ^14.0.0 || >=16.0.0 }

  "@eslint/eslintrc@2.1.4":
    resolution:
      {
        integrity: sha512-269Z39MS6wVJtsoUl10L60WdkhJVdPG24Q4eZTH3nnF6lpvSShEK3wQjDX9JRWAUPvPh7COouPpU9IrqaZFvtQ==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  "@eslint/js@8.57.0":
    resolution:
      {
        integrity: sha512-Ys+3g2TaW7gADOJzPt83SJtCDhMjndcDMFVQ/Tj9iA1BfJzFKD9mAUXT3OenpuPHbI6P/myECxRJrofUsDx/5g==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  "@humanwhocodes/config-array@0.11.14":
    resolution:
      {
        integrity: sha512-3T8LkOmg45BV5FICb15QQMsyUSWrQ8AygVfC7ZG32zOalnqrilm018ZVCw0eapXux8FtA33q8PSRSstjee3jSg==,
      }
    engines: { node: ">=10.10.0" }
    deprecated: Use @eslint/config-array instead

  "@humanwhocodes/module-importer@1.0.1":
    resolution:
      {
        integrity: sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA==,
      }
    engines: { node: ">=12.22" }

  "@humanwhocodes/object-schema@2.0.3":
    resolution:
      {
        integrity: sha512-93zYdMES/c1D69yZiKDBj0V24vqNzB/koF26KPaagAfd3P/4gUlh3Dys5ogAK+Exi9QyzlD8x/08Zt7wIKcDcA==,
      }
    deprecated: Use @eslint/object-schema instead

  "@isaacs/cliui@8.0.2":
    resolution:
      {
        integrity: sha512-O8jcjabXaleOG9DQ0+ARXWZBTfnP4WNAqzuiJK7ll44AmxGKv/J2M4TPjxjY3znBCfvBXFzucm1twdyFybFqEA==,
      }
    engines: { node: ">=12" }

  "@istanbuljs/schema@0.1.3":
    resolution:
      {
        integrity: sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==,
      }
    engines: { node: ">=8" }

  "@jest/types@24.9.0":
    resolution:
      {
        integrity: sha512-XKK7ze1apu5JWQ5eZjHITP66AX+QsLlbaJRBGYr8pNzwcAE2JVkwnf0yqjHTsDRcjR0mujy/NmZMXw5kl+kGBw==,
      }
    engines: { node: ">= 6" }

  "@jridgewell/gen-mapping@0.3.5":
    resolution:
      {
        integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==,
      }
    engines: { node: ">=6.0.0" }

  "@jridgewell/resolve-uri@3.1.2":
    resolution:
      {
        integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==,
      }
    engines: { node: ">=6.0.0" }

  "@jridgewell/set-array@1.2.1":
    resolution:
      {
        integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==,
      }
    engines: { node: ">=6.0.0" }

  "@jridgewell/sourcemap-codec@1.4.15":
    resolution:
      {
        integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==,
      }

  "@jridgewell/trace-mapping@0.3.25":
    resolution:
      {
        integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==,
      }

  "@jridgewell/trace-mapping@0.3.9":
    resolution:
      {
        integrity: sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==,
      }

  "@loadable/component@5.16.4":
    resolution:
      {
        integrity: sha512-fJWxx9b5WHX90QKmizo9B+es2so8DnBthI1mbflwCoOyvzEwxiZ/SVDCTtXEnHG72/kGBdzr297SSIekYtzSOQ==,
      }
    engines: { node: ">=8" }
    peerDependencies:
      react: ^16.3.0 || ^17.0.0 || ^18.0.0

  "@mui/core-downloads-tracker@5.16.7":
    resolution:
      {
        integrity: sha512-RtsCt4Geed2/v74sbihWzzRs+HsIQCfclHeORh5Ynu2fS4icIKozcSubwuG7vtzq2uW3fOR1zITSP84TNt2GoQ==,
      }

  "@mui/icons-material@5.16.7":
    resolution:
      {
        integrity: sha512-UrGwDJCXEszbDI7yV047BYU5A28eGJ79keTCP4cc74WyncuVrnurlmIRxaHL8YK+LI1Kzq+/JM52IAkNnv4u+Q==,
      }
    engines: { node: ">=12.0.0" }
    peerDependencies:
      "@mui/material": ^5.0.0
      "@types/react": ^17.0.0 || ^18.0.0
      react: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@mui/material@5.16.7":
    resolution:
      {
        integrity: sha512-cwwVQxBhK60OIOqZOVLFt55t01zmarKJiJUWbk0+8s/Ix5IaUzAShqlJchxsIQ4mSrWqgcKCCXKtIlG5H+/Jmg==,
      }
    engines: { node: ">=12.0.0" }
    peerDependencies:
      "@emotion/react": ^11.5.0
      "@emotion/styled": ^11.3.0
      "@types/react": ^17.0.0 || ^18.0.0
      react: ^17.0.0 || ^18.0.0
      react-dom: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      "@emotion/react":
        optional: true
      "@emotion/styled":
        optional: true
      "@types/react":
        optional: true

  "@mui/private-theming@5.16.6":
    resolution:
      {
        integrity: sha512-rAk+Rh8Clg7Cd7shZhyt2HGTTE5wYKNSJ5sspf28Fqm/PZ69Er9o6KX25g03/FG2dfpg5GCwZh/xOojiTfm3hw==,
      }
    engines: { node: ">=12.0.0" }
    peerDependencies:
      "@types/react": ^17.0.0 || ^18.0.0
      react: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@mui/styled-engine@5.16.6":
    resolution:
      {
        integrity: sha512-zaThmS67ZmtHSWToTiHslbI8jwrmITcN93LQaR2lKArbvS7Z3iLkwRoiikNWutx9MBs8Q6okKvbZq1RQYB3v7g==,
      }
    engines: { node: ">=12.0.0" }
    peerDependencies:
      "@emotion/react": ^11.4.1
      "@emotion/styled": ^11.3.0
      react: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      "@emotion/react":
        optional: true
      "@emotion/styled":
        optional: true

  "@mui/system@5.16.7":
    resolution:
      {
        integrity: sha512-Jncvs/r/d/itkxh7O7opOunTqbbSSzMTHzZkNLM+FjAOg+cYAZHrPDlYe1ZGKUYORwwb2XexlWnpZp0kZ4AHuA==,
      }
    engines: { node: ">=12.0.0" }
    peerDependencies:
      "@emotion/react": ^11.5.0
      "@emotion/styled": ^11.3.0
      "@types/react": ^17.0.0 || ^18.0.0
      react: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      "@emotion/react":
        optional: true
      "@emotion/styled":
        optional: true
      "@types/react":
        optional: true

  "@mui/types@7.2.15":
    resolution:
      {
        integrity: sha512-nbo7yPhtKJkdf9kcVOF8JZHPZTmqXjJ/tI0bdWgHg5tp9AnIN4Y7f7wm9T+0SyGYJk76+GYZ8Q5XaTYAsUHN0Q==,
      }
    peerDependencies:
      "@types/react": ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@mui/utils@5.16.6":
    resolution:
      {
        integrity: sha512-tWiQqlhxAt3KENNiSRL+DIn9H5xNVK6Jjf70x3PnfQPz1MPBdh7yyIcAyVBT9xiw7hP3SomRhPR7hzBMBCjqEA==,
      }
    engines: { node: ">=12.0.0" }
    peerDependencies:
      "@types/react": ^17.0.0 || ^18.0.0
      react: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      "@types/react":
        optional: true

  "@mui/x-data-grid@7.14.0":
    resolution:
      {
        integrity: sha512-ddVtvFXmENHADHzO0TGV2dzUQflexsXMbxEKMq3rBmgJ9QyeiZWBEwzgDps1CzqU5vi9QyDACCcPyoAuL6t3tQ==,
      }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      "@emotion/react": ^11.9.0
      "@emotion/styled": ^11.8.1
      "@mui/material": ^5.15.14
      react: ^17.0.0 || ^18.0.0
      react-dom: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      "@emotion/react":
        optional: true
      "@emotion/styled":
        optional: true

  "@mui/x-date-pickers@7.14.0":
    resolution:
      {
        integrity: sha512-3xI3xYVxqPU4//KfE4FcR+Zs7UT4kkDPvA+IDOcQdRUyVwmcXCjBuJZgKgJMqSCNK/KIJZQQrpmy5XGHOKTbdA==,
      }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      "@emotion/react": ^11.9.0
      "@emotion/styled": ^11.8.1
      "@mui/material": ^5.15.14
      date-fns: ^2.25.0 || ^3.2.0
      date-fns-jalali: ^2.13.0-0 || ^3.2.0-0
      dayjs: ^1.10.7
      luxon: ^3.0.2
      moment: ^2.29.4
      moment-hijri: ^2.1.2
      moment-jalaali: ^0.7.4 || ^0.8.0 || ^0.9.0 || ^0.10.0
      react: ^17.0.0 || ^18.0.0
      react-dom: ^17.0.0 || ^18.0.0
    peerDependenciesMeta:
      "@emotion/react":
        optional: true
      "@emotion/styled":
        optional: true
      date-fns:
        optional: true
      date-fns-jalali:
        optional: true
      dayjs:
        optional: true
      luxon:
        optional: true
      moment:
        optional: true
      moment-hijri:
        optional: true
      moment-jalaali:
        optional: true

  "@mui/x-internals@7.14.0":
    resolution:
      {
        integrity: sha512-+qWIHLgt2vgH6bKmf7IwRvS86UbZRWKAdDY/yTQJaqzCzyesUvQhD+WRxe1kpdCK8UE061S9/Ju7hLkM4kjRNA==,
      }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      react: ^17.0.0 || ^18.0.0

  "@nodelib/fs.scandir@2.1.5":
    resolution:
      {
        integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==,
      }
    engines: { node: ">= 8" }

  "@nodelib/fs.stat@2.0.5":
    resolution:
      {
        integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==,
      }
    engines: { node: ">= 8" }

  "@nodelib/fs.walk@1.2.8":
    resolution:
      {
        integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==,
      }
    engines: { node: ">= 8" }

  "@pkgjs/parseargs@0.11.0":
    resolution:
      {
        integrity: sha512-+1VkjdD0QBLPodGrJUeqarH8VAIvQODIbwh9XpP5Syisf7YoQgsJKPNFoqqLQlu+VQ/tVSshMR6loPMn8U+dPg==,
      }
    engines: { node: ">=14" }

  "@playwright/test@1.44.0":
    resolution:
      {
        integrity: sha512-rNX5lbNidamSUorBhB4XZ9SQTjAqfe5M+p37Z8ic0jPFBMo5iCtQz1kRWkEMg+rYOKSlVycpQmpqjSFq7LXOfg==,
      }
    engines: { node: ">=16" }
    hasBin: true

  "@polka/url@1.0.0-next.25":
    resolution:
      {
        integrity: sha512-j7P6Rgr3mmtdkeDGTe0E/aYyWEWVtc5yFXtHCRHs28/jptDEWfaVOc5T7cblqy1XKPPfCxJc/8DwQ5YgLOZOVQ==,
      }

  "@popperjs/core@2.11.8":
    resolution:
      {
        integrity: sha512-P1st0aksCrn9sGZhp8GMYwBnQsbvAWsZAX44oXNNvLHGqAOcoVxmjZiohstwQ7SqKnbR47akdNi+uleWD8+g6A==,
      }

  "@reduxjs/toolkit@2.2.7":
    resolution:
      {
        integrity: sha512-faI3cZbSdFb8yv9dhDTmGwclW0vk0z5o1cia+kf7gCbaCwHI5e+7tP57mJUv22pNcNbeA62GSrPpfrUfdXcQ6g==,
      }
    peerDependencies:
      react: ^16.9.0 || ^17.0.0 || ^18
      react-redux: ^7.2.1 || ^8.1.3 || ^9.0.0
    peerDependenciesMeta:
      react:
        optional: true
      react-redux:
        optional: true

  "@remix-run/router@1.16.1":
    resolution:
      {
        integrity: sha512-es2g3dq6Nb07iFxGk5GuHN20RwBZOsuDQN7izWIisUcv9r+d2C5jQxqmgkdebXgReWfiyUabcki6Fg77mSNrig==,
      }
    engines: { node: ">=14.0.0" }

  "@rollup/pluginutils@5.1.0":
    resolution:
      {
        integrity: sha512-XTIWOPPcpvyKI6L1NHo0lFlCyznUEyPmPY1mc3KpPVDYulHSTvyeLNVW00QTLIAFNhR3kYnJTQHeGqU4M3n09g==,
      }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      rollup: ^1.20.0||^2.0.0||^3.0.0||^4.0.0
    peerDependenciesMeta:
      rollup:
        optional: true

  "@svgr/babel-plugin-add-jsx-attribute@7.0.0":
    resolution:
      {
        integrity: sha512-khWbXesWIP9v8HuKCl2NU2HNAyqpSQ/vkIl36Nbn4HIwEYSRWL0H7Gs6idJdha2DkpFDWlsqMELvoCE8lfFY6Q==,
      }
    engines: { node: ">=14" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@svgr/babel-plugin-remove-jsx-attribute@7.0.0":
    resolution:
      {
        integrity: sha512-iiZaIvb3H/c7d3TH2HBeK91uI2rMhZNwnsIrvd7ZwGLkFw6mmunOCoVnjdYua662MqGFxlN9xTq4fv9hgR4VXQ==,
      }
    engines: { node: ">=14" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@svgr/babel-plugin-remove-jsx-empty-expression@7.0.0":
    resolution:
      {
        integrity: sha512-sQQmyo+qegBx8DfFc04PFmIO1FP1MHI1/QEpzcIcclo5OAISsOJPW76ZIs0bDyO/DBSJEa/tDa1W26pVtt0FRw==,
      }
    engines: { node: ">=14" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@svgr/babel-plugin-replace-jsx-attribute-value@7.0.0":
    resolution:
      {
        integrity: sha512-i6MaAqIZXDOJeikJuzocByBf8zO+meLwfQ/qMHIjCcvpnfvWf82PFvredEZElErB5glQFJa2KVKk8N2xV6tRRA==,
      }
    engines: { node: ">=14" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@svgr/babel-plugin-svg-dynamic-title@7.0.0":
    resolution:
      {
        integrity: sha512-BoVSh6ge3SLLpKC0pmmN9DFlqgFy4NxNgdZNLPNJWBUU7TQpDWeBuyVuDW88iXydb5Cv0ReC+ffa5h3VrKfk1w==,
      }
    engines: { node: ">=14" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@svgr/babel-plugin-svg-em-dimensions@7.0.0":
    resolution:
      {
        integrity: sha512-tNDcBa+hYn0gO+GkP/AuNKdVtMufVhU9fdzu+vUQsR18RIJ9RWe7h/pSBY338RO08wArntwbDk5WhQBmhf2PaA==,
      }
    engines: { node: ">=14" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@svgr/babel-plugin-transform-react-native-svg@7.0.0":
    resolution:
      {
        integrity: sha512-qw54u8ljCJYL2KtBOjI5z7Nzg8LnSvQOP5hPKj77H4VQL4+HdKbAT5pnkkZLmHKYwzsIHSYKXxHouD8zZamCFQ==,
      }
    engines: { node: ">=14" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@svgr/babel-plugin-transform-svg-component@7.0.0":
    resolution:
      {
        integrity: sha512-CcFECkDj98daOg9jE3Bh3uyD9kzevCAnZ+UtzG6+BQG/jOQ2OA3jHnX6iG4G1MCJkUQFnUvEv33NvQfqrb/F3A==,
      }
    engines: { node: ">=12" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@svgr/babel-preset@7.0.0":
    resolution:
      {
        integrity: sha512-EX/NHeFa30j5UjldQGVQikuuQNHUdGmbh9kEpBKofGUtF0GUPJ4T4rhoYiqDAOmBOxojyot36JIFiDUHUK1ilQ==,
      }
    engines: { node: ">=14" }
    peerDependencies:
      "@babel/core": ^7.0.0-0

  "@svgr/core@7.0.0":
    resolution:
      {
        integrity: sha512-ztAoxkaKhRVloa3XydohgQQCb0/8x9T63yXovpmHzKMkHO6pkjdsIAWKOS4bE95P/2quVh1NtjSKlMRNzSBffw==,
      }
    engines: { node: ">=14" }

  "@svgr/hast-util-to-babel-ast@7.0.0":
    resolution:
      {
        integrity: sha512-42Ej9sDDEmsJKjrfQ1PHmiDiHagh/u9AHO9QWbeNx4KmD9yS5d1XHmXUNINfUcykAU+4431Cn+k6Vn5mWBYimQ==,
      }
    engines: { node: ">=14" }

  "@svgr/plugin-jsx@7.0.0":
    resolution:
      {
        integrity: sha512-SWlTpPQmBUtLKxXWgpv8syzqIU8XgFRvyhfkam2So8b3BE0OS0HPe5UfmlJ2KIC+a7dpuuYovPR2WAQuSyMoPw==,
      }
    engines: { node: ">=14" }

  "@svgr/plugin-svgo@7.0.0":
    resolution:
      {
        integrity: sha512-263znzlu3qTKj71/ot5G9l2vpL4CW+pr2IexBFIwwB+fRAXE9Xnw2rUFgE6P4+37N9siOuC4lKkgBfUCOLFRKQ==,
      }
    engines: { node: ">=14" }
    peerDependencies:
      "@svgr/core": "*"

  "@svgr/rollup@7.0.0":
    resolution:
      {
        integrity: sha512-zlx0lxtxTnrXFF+ISuff+hht2XcWXa6uXEliwQbz+o0/qRIrcqyB9ShalO9ekVWB5icgxCWQ5lDaULJTt/pTlA==,
      }
    engines: { node: ">=14" }

  "@testing-library/dom@9.3.4":
    resolution:
      {
        integrity: sha512-FlS4ZWlp97iiNWig0Muq8p+3rVDjRiYE+YKGbAqXOu9nwJFFOdL00kFpz42M+4huzYi86vAK1sOOfyOG45muIQ==,
      }
    engines: { node: ">=14" }

  "@testing-library/jest-dom@4.2.4":
    resolution:
      {
        integrity: sha512-j31Bn0rQo12fhCWOUWy9fl7wtqkp7In/YP2p5ZFyRuiiB9Qs3g+hS4gAmDWONbAHcRmVooNJ5eOHQDCOmUFXHg==,
      }
    engines: { node: ">=8", npm: ">=6" }

  "@testing-library/react@14.3.1":
    resolution:
      {
        integrity: sha512-H99XjUhWQw0lTgyMN05W3xQG1Nh4lq574D8keFf1dDoNTJgp66VbJozRaczoF+wsiaPJNt/TcnfpLGufGxSrZQ==,
      }
    engines: { node: ">=14" }
    peerDependencies:
      react: ^18.0.0
      react-dom: ^18.0.0

  "@tootallnate/once@2.0.0":
    resolution:
      {
        integrity: sha512-XCuKFP5PS55gnMVu3dty8KPatLqUoy/ZYzDzAGCQ8JNFCkLXzmI7vNHCR+XpbZaMWQK/vQubr7PkYq8g470J/A==,
      }
    engines: { node: ">= 10" }

  "@trysound/sax@0.2.0":
    resolution:
      {
        integrity: sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==,
      }
    engines: { node: ">=10.13.0" }

  "@tsconfig/node10@1.0.11":
    resolution:
      {
        integrity: sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==,
      }

  "@tsconfig/node12@1.0.11":
    resolution:
      {
        integrity: sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==,
      }

  "@tsconfig/node14@1.0.3":
    resolution:
      {
        integrity: sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==,
      }

  "@tsconfig/node16@1.0.4":
    resolution:
      {
        integrity: sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==,
      }

  "@types/aria-query@5.0.4":
    resolution:
      {
        integrity: sha512-rfT93uj5s0PRL7EzccGMs3brplhcrghnDoV26NqKhCAS1hVo+WdNsPvE/yb6ilfr5hi2MEk6d5EWJTKdxg8jVw==,
      }

  "@types/chai-subset@1.3.5":
    resolution:
      {
        integrity: sha512-c2mPnw+xHtXDoHmdtcCXGwyLMiauiAyxWMzhGpqHC4nqI/Y5G2XhTampslK2rb59kpcuHon03UH8W6iYUzw88A==,
      }

  "@types/chai@4.3.16":
    resolution:
      {
        integrity: sha512-PatH4iOdyh3MyWtmHVFXLWCCIhUbopaltqddG9BzB+gMIzee2MJrvd+jouii9Z3wzQJruGWAm7WOMjgfG8hQlQ==,
      }

  "@types/chroma-js@2.4.4":
    resolution:
      {
        integrity: sha512-/DTccpHTaKomqussrn+ciEvfW4k6NAHzNzs/sts1TCqg333qNxOhy8TNIoQCmbGG3Tl8KdEhkGAssb1n3mTXiQ==,
      }

  "@types/eslint@8.56.10":
    resolution:
      {
        integrity: sha512-Shavhk87gCtY2fhXDctcfS3e6FdxWkCx1iUZ9eEUbh7rTqlZT0/IzOkCOVt0fCjcFuZ9FPYfuezTBImfHCDBGQ==,
      }

  "@types/estree@1.0.5":
    resolution:
      {
        integrity: sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==,
      }

  "@types/hoist-non-react-statics@3.3.5":
    resolution:
      {
        integrity: sha512-SbcrWzkKBw2cdwRTwQAswfpB9g9LJWfjtUeW/jvNwbhC8cpmmNYVePa+ncbUe0rGTQ7G3Ff6mYUN2VMfLVr+Sg==,
      }

  "@types/istanbul-lib-coverage@2.0.6":
    resolution:
      {
        integrity: sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==,
      }

  "@types/istanbul-lib-report@3.0.3":
    resolution:
      {
        integrity: sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==,
      }

  "@types/istanbul-reports@1.1.2":
    resolution:
      {
        integrity: sha512-P/W9yOX/3oPZSpaYOCQzGqgCQRXn0FFO/V8bWrCQs+wLmvVVxk6CRBXALEvNs9OHIatlnlFokfhuDo2ug01ciw==,
      }

  "@types/js-cookie@2.2.7":
    resolution:
      {
        integrity: sha512-aLkWa0C0vO5b4Sr798E26QgOkss68Un0bLjs7u9qxzPT5CG+8DuNTffWES58YzJs3hrVAOs1wonycqEBqNJubA==,
      }

  "@types/json-schema@7.0.15":
    resolution:
      {
        integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==,
      }

  "@types/json5@0.0.29":
    resolution:
      {
        integrity: sha512-dRLjCWHYg4oaA77cxO64oO+7JwCwnIzkZPdrrC71jQmQtlhM556pwKo5bUzqvZndkVbeFLIIi+9TC40JNF5hNQ==,
      }

  "@types/loadable__component@5.13.9":
    resolution:
      {
        integrity: sha512-QWOtIkwZqHNdQj3nixQ8oyihQiTMKZLk/DNuvNxMSbTfxf47w+kqcbnxlUeBgAxdOtW0Dh48dTAIp83iJKtnrQ==,
      }

  "@types/lodash@4.17.1":
    resolution:
      {
        integrity: sha512-X+2qazGS3jxLAIz5JDXDzglAF3KpijdhFxlf/V1+hEsOUc+HnWi81L/uv/EvGuV90WY+7mPGFCUDGfQC3Gj95Q==,
      }

  "@types/minimist@1.2.5":
    resolution:
      {
        integrity: sha512-hov8bUuiLiyFPGyFPE1lwWhmzYbirOXQNNo40+y3zow8aFVTeyn3VWL0VFFfdNddA8S4Vf0Tc062rzyNr7Paag==,
      }

  "@types/node@18.19.33":
    resolution:
      {
        integrity: sha512-NR9+KrpSajr2qBVp/Yt5TU/rp+b5Mayi3+OlMlcg2cVCfRmcG5PWZ7S4+MG9PZ5gWBoc9Pd0BKSRViuBCRPu0A==,
      }

  "@types/node@20.5.1":
    resolution:
      {
        integrity: sha512-4tT2UrL5LBqDwoed9wZ6N3umC4Yhz3W3FloMmiiG4JwmUJWpie0c7lcnUNd4gtMKuDEO4wRVS8B6Xa0uMRsMKg==,
      }

  "@types/normalize-package-data@2.4.4":
    resolution:
      {
        integrity: sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA==,
      }

  "@types/papaparse@5.3.16":
    resolution:
      {
        integrity: sha512-T3VuKMC2H0lgsjI9buTB3uuKj3EMD2eap1MOuEQuBQ44EnDx/IkGhU6EwiTf9zG3za4SKlmwKAImdDKdNnCsXg==,
      }

  "@types/parse-json@4.0.2":
    resolution:
      {
        integrity: sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==,
      }

  "@types/prettier@2.7.3":
    resolution:
      {
        integrity: sha512-+68kP9yzs4LMp7VNh8gdzMSPZFL44MLGqiHWvttYJe+6qnuVr4Ek9wSBQoveqY/r+LwjCcU29kNVkidwim+kYA==,
      }

  "@types/prop-types@15.7.12":
    resolution:
      {
        integrity: sha512-5zvhXYtRNRluoE/jAp4GVsSduVUzNWKkOZrCDBWYtE7biZywwdC2AcEzg+cSMLFRfVgeAFqpfNabiPjxFddV1Q==,
      }

  "@types/react-dom@18.3.0":
    resolution:
      {
        integrity: sha512-EhwApuTmMBmXuFOikhQLIBUn6uFg81SwLMOAUgodJF14SOBOCMdU04gDoYi0WOJJHD144TL32z4yDqCW3dnkQg==,
      }

  "@types/react-transition-group@4.4.11":
    resolution:
      {
        integrity: sha512-RM05tAniPZ5DZPzzNFP+DmrcOdD0efDUxMy3145oljWSl3x9ZV5vhme98gTxFrj2lhXvmGNnUiuDyJgY9IKkNA==,
      }

  "@types/react@18.3.2":
    resolution:
      {
        integrity: sha512-Btgg89dAnqD4vV7R3hlwOxgqobUQKgx3MmrQRi0yYbs/P0ym8XozIAlkqVilPqHQwXs4e9Tf63rrCgl58BcO4w==,
      }

  "@types/semver@7.5.8":
    resolution:
      {
        integrity: sha512-I8EUhyrgfLrcTkzV3TSsGyl1tSuPrEDzr0yd5m90UgNxQkyDXULk3b6MlQqTCpZpNtWe1K0hzclnZkTcLBe2UQ==,
      }

  "@types/use-sync-external-store@0.0.3":
    resolution:
      {
        integrity: sha512-EwmlvuaxPNej9+T4v5AuBPJa2x2UOJVdjCtDHgcDqitUeOtjnJKJ+apYjVcAoBEMjKW1VVFGZLUb5+qqa09XFA==,
      }

  "@types/uuid@9.0.8":
    resolution:
      {
        integrity: sha512-jg+97EGIcY9AGHJJRaaPVgetKDsrTgbRjQ5Msgjh/DQKEFl0DtyRr/VCOyD1T2R1MNeWPK/u7JoGhlDZnKBAfA==,
      }

  "@types/yargs-parser@21.0.3":
    resolution:
      {
        integrity: sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==,
      }

  "@types/yargs@13.0.12":
    resolution:
      {
        integrity: sha512-qCxJE1qgz2y0hA4pIxjBR+PelCH0U5CK1XJXFwCNqfmliatKp47UCXXE9Dyk1OXBDLvsCF57TqQEJaeLfDYEOQ==,
      }

  "@typescript-eslint/eslint-plugin@5.62.0":
    resolution:
      {
        integrity: sha512-TiZzBSJja/LbhNPvk6yc0JrX9XqhQ0hdh6M2svYfsHGejaKFIAGd9MQ+ERIMzLGlN/kZoYIgdxFV0PuljTKXag==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
    peerDependencies:
      "@typescript-eslint/parser": ^5.0.0
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      typescript: "*"
    peerDependenciesMeta:
      typescript:
        optional: true

  "@typescript-eslint/parser@5.62.0":
    resolution:
      {
        integrity: sha512-VlJEV0fOQ7BExOsHYAGrgbEiZoi8D+Bl2+f6V2RrXerRSylnp+ZBHmPvaIa8cz0Ajx7WO7Z5RqfgYg7ED1nRhA==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0
      typescript: "*"
    peerDependenciesMeta:
      typescript:
        optional: true

  "@typescript-eslint/scope-manager@5.62.0":
    resolution:
      {
        integrity: sha512-VXuvVvZeQCQb5Zgf4HAxc04q5j+WrNAtNh9OwCsCgpKqESMTu3tF/jhZ3xG6T4NZwWl65Bg8KuS2uEvhSfLl0w==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  "@typescript-eslint/type-utils@5.62.0":
    resolution:
      {
        integrity: sha512-xsSQreu+VnfbqQpW5vnCJdq1Z3Q0U31qiWmRhr98ONQmcp/yhiPJFPq8MXiJVLiksmOKSjIldZzkebzHuCGzew==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
    peerDependencies:
      eslint: "*"
      typescript: "*"
    peerDependenciesMeta:
      typescript:
        optional: true

  "@typescript-eslint/types@5.62.0":
    resolution:
      {
        integrity: sha512-87NVngcbVXUahrRTqIK27gD2t5Cu1yuCXxbLcFtCzZGlfyVWWh8mLHkoxzjsB6DDNnvdL+fW8MiwPEJyGJQDgQ==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  "@typescript-eslint/typescript-estree@5.62.0":
    resolution:
      {
        integrity: sha512-CmcQ6uY7b9y694lKdRB8FEel7JbU/40iSAPomu++SjLMntB+2Leay2LO6i8VnJk58MtE9/nQSFIH6jpyRWyYzA==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
    peerDependencies:
      typescript: "*"
    peerDependenciesMeta:
      typescript:
        optional: true

  "@typescript-eslint/utils@5.62.0":
    resolution:
      {
        integrity: sha512-n8oxjeb5aIbPFEtmQxQYOLI0i9n5ySBEY/ZEHHZqKQSFnxio1rv6dthascc9dLuwrL0RC5mPCxB7vnAVGAYWAQ==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
    peerDependencies:
      eslint: ^6.0.0 || ^7.0.0 || ^8.0.0

  "@typescript-eslint/visitor-keys@5.62.0":
    resolution:
      {
        integrity: sha512-07ny+LHRzQXepkGg6w0mFY41fVUNBrL2Roj/++7V1txKugfjm/Ci/qSND03r2RhlJhJYMcTn9AhhSSqQp0Ysyw==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  "@ungap/structured-clone@1.2.0":
    resolution:
      {
        integrity: sha512-zuVdFrMJiuCDQUMCzQaD6KL28MjnqqN8XnAqiEq9PNm/hCPTSGfrXCOfwj1ow4LFb/tNymJPwsNbVePc1xFqrQ==,
      }

  "@vitejs/plugin-react@3.1.0":
    resolution:
      {
        integrity: sha512-AfgcRL8ZBhAlc3BFdigClmTUMISmmzHn7sB2h9U1odvc5U/MjWXsAaz18b/WoppUTDBzxOJwo2VdClfUcItu9g==,
      }
    engines: { node: ^14.18.0 || >=16.0.0 }
    peerDependencies:
      vite: ^4.1.0-beta.0

  "@vitest/coverage-c8@0.29.8":
    resolution:
      {
        integrity: sha512-y+sEMQMctWokjnSqm3FCQEYFkjLrYaznsxEZHxcx8z2aftpYg3A5tvI1S5himfdEFo7o+OeHzh40bPSWZHW4oQ==,
      }
    deprecated: v8 coverage is moved to @vitest/coverage-v8 package
    peerDependencies:
      vitest: ">=0.29.0 <1"

  "@vitest/expect@0.29.8":
    resolution:
      {
        integrity: sha512-xlcVXn5I5oTq6NiZSY3ykyWixBxr5mG8HYtjvpgg6KaqHm0mvhX18xuwl5YGxIRNt/A5jidd7CWcNHrSvgaQqQ==,
      }

  "@vitest/runner@0.29.8":
    resolution:
      {
        integrity: sha512-FzdhnRDwEr/A3Oo1jtIk/B952BBvP32n1ObMEb23oEJNO+qO5cBet6M2XWIDQmA7BDKGKvmhUf2naXyp/2JEwQ==,
      }

  "@vitest/spy@0.29.8":
    resolution:
      {
        integrity: sha512-VdjBe9w34vOMl5I5mYEzNX8inTxrZ+tYUVk9jxaZJmHFwmDFC/GV3KBFTA/JKswr3XHvZL+FE/yq5EVhb6pSAw==,
      }

  "@vitest/ui@0.29.8":
    resolution:
      {
        integrity: sha512-+vbLd+c1R/XUWfzJsWeyjeiw13fwJ95I5tguxaqXRg61y9iYUKesVljg7Pttp2uo7VK+kAjvY91J41NZ1Vx3vg==,
      }

  "@vitest/utils@0.29.8":
    resolution:
      {
        integrity: sha512-qGzuf3vrTbnoY+RjjVVIBYfuWMjn3UMUqyQtdGNZ6ZIIyte7B37exj6LaVkrZiUTvzSadVvO/tJm8AEgbGCBPg==,
      }

  "@xobotyi/scrollbar-width@1.9.5":
    resolution:
      {
        integrity: sha512-N8tkAACJx2ww8vFMneJmaAgmjAG1tnVBZJRLRcx061tmsLRZHSEZSLuGWnwPtunsSLvSqXQ2wfp7Mgqg1I+2dQ==,
      }

  JSONStream@1.3.5:
    resolution:
      {
        integrity: sha512-E+iruNOY8VV9s4JEbe1aNEm6MiszPRr/UfcHMz0TQh1BXSxHK+ASV1R6W4HpjBhSeS+54PIsAMCBmwD06LLsqQ==,
      }
    hasBin: true

  abab@2.0.6:
    resolution:
      {
        integrity: sha512-j2afSsaIENvHZN2B8GOpF566vZ5WVk5opAiMTvWgaQT8DkbOqsTfvNAvHoRGU2zzP8cPoqys+xHTRDWW8L+/BA==,
      }
    deprecated: Use your platform's native atob() and btoa() methods instead

  acorn-jsx@5.3.2:
    resolution:
      {
        integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==,
      }
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0

  acorn-walk@8.3.2:
    resolution:
      {
        integrity: sha512-cjkyv4OtNCIeqhHrfS81QWXoCBPExR/J62oyEqepVw8WaQeSqpW2uhuLPh1m9eWhDuOo/jUXVTlifvesOWp/4A==,
      }
    engines: { node: ">=0.4.0" }

  acorn@8.11.3:
    resolution:
      {
        integrity: sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg==,
      }
    engines: { node: ">=0.4.0" }
    hasBin: true

  agent-base@6.0.2:
    resolution:
      {
        integrity: sha512-RZNwNclF7+MS/8bDg70amg32dyeZGZxiDuQmZxKLAlQjr3jGyLx+4Kkk58UO7D2QdgFIQCovuSuZESne6RG6XQ==,
      }
    engines: { node: ">= 6.0.0" }

  ajv@6.12.6:
    resolution:
      {
        integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==,
      }

  ajv@8.13.0:
    resolution:
      {
        integrity: sha512-PRA911Blj99jR5RMeTunVbNXMF6Lp4vZXnk5GQjcnUWUTsrXtekg/pnmFFI2u/I36Y/2bITGS30GZCXei6uNkA==,
      }

  ansi-escapes@4.3.2:
    resolution:
      {
        integrity: sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==,
      }
    engines: { node: ">=8" }

  ansi-escapes@5.0.0:
    resolution:
      {
        integrity: sha512-5GFMVX8HqE/TB+FuBJGuO5XG0WrsA6ptUqoODaT/n9mmUaZFkqnBueB4leqGBCmrUHnCnC4PCZTCd0E7QQ83bA==,
      }
    engines: { node: ">=12" }

  ansi-regex@2.1.1:
    resolution:
      {
        integrity: sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==,
      }
    engines: { node: ">=0.10.0" }

  ansi-regex@3.0.1:
    resolution:
      {
        integrity: sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==,
      }
    engines: { node: ">=4" }

  ansi-regex@4.1.1:
    resolution:
      {
        integrity: sha512-ILlv4k/3f6vfQ4OoP2AGvirOktlQ98ZEL1k9FaQjxa3L1abBgbuTDAdPOpvbGncC0BTVQrl+OM8xZGK6tWXt7g==,
      }
    engines: { node: ">=6" }

  ansi-regex@5.0.1:
    resolution:
      {
        integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==,
      }
    engines: { node: ">=8" }

  ansi-regex@6.0.1:
    resolution:
      {
        integrity: sha512-n5M855fKb2SsfMIiFFoVrABHJC8QtHwVx+mHWP3QcEqBHYienj5dHSgjbxtC0WEZXYt4wcD6zrQElDPhFuZgfA==,
      }
    engines: { node: ">=12" }

  ansi-styles@2.2.1:
    resolution:
      {
        integrity: sha512-kmCevFghRiWM7HB5zTPULl4r9bVFSWjz62MhqizDGUrq2NWuNMQyuv4tHHoKJHs69M/MF64lEcHdYIocrdWQYA==,
      }
    engines: { node: ">=0.10.0" }

  ansi-styles@3.2.1:
    resolution:
      {
        integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==,
      }
    engines: { node: ">=4" }

  ansi-styles@4.3.0:
    resolution:
      {
        integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==,
      }
    engines: { node: ">=8" }

  ansi-styles@5.2.0:
    resolution:
      {
        integrity: sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==,
      }
    engines: { node: ">=10" }

  ansi-styles@6.2.1:
    resolution:
      {
        integrity: sha512-bN798gFfQX+viw3R7yrGWRqnrN2oRkEkUjjl4JNn4E8GxxbjtG3FbrEIIY3l8/hrwUwIeCZvi4QuOTP4MErVug==,
      }
    engines: { node: ">=12" }

  any-promise@1.3.0:
    resolution:
      {
        integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==,
      }

  anymatch@3.1.3:
    resolution:
      {
        integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==,
      }
    engines: { node: ">= 8" }

  arg@4.1.3:
    resolution:
      {
        integrity: sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==,
      }

  arg@5.0.2:
    resolution:
      {
        integrity: sha512-PYjyFOLKQ9y57JvQ6QLo8dAgNqswh8M1RMJYdQduT6xbWSgK36P/Z/v+p888pM69jMMfS8Xd8F6I1kQ/I9HUGg==,
      }

  argparse@2.0.1:
    resolution:
      {
        integrity: sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q==,
      }

  aria-query@5.1.3:
    resolution:
      {
        integrity: sha512-R5iJ5lkuHybztUfuOAznmboyjWq8O6sqNqtK7CLOqdydi54VNbORp49mb14KbWgG1QD3JFO9hJdZ+y4KutfdOQ==,
      }

  array-buffer-byte-length@1.0.1:
    resolution:
      {
        integrity: sha512-ahC5W1xgou+KTXix4sAO8Ki12Q+jf4i0+tmk3sC+zgcynshkHxzpXdImBehiUYKKKDwvfFiJl1tZt6ewscS1Mg==,
      }
    engines: { node: ">= 0.4" }

  array-ify@1.0.0:
    resolution:
      {
        integrity: sha512-c5AMf34bKdvPhQ7tBGhqkgKNUzMr4WUs+WDtC2ZUGOUncbxKMTvqxYctiseW3+L4bA8ec+GcZ6/A/FW4m8ukng==,
      }

  array-includes@3.1.8:
    resolution:
      {
        integrity: sha512-itaWrbYbqpGXkGhZPGUulwnhVf5Hpy1xiCFsGqyIGglbBxmG5vSjxQen3/WGOjPpNEv1RtBLKxbmVXm8HpJStQ==,
      }
    engines: { node: ">= 0.4" }

  array-union@2.1.0:
    resolution:
      {
        integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==,
      }
    engines: { node: ">=8" }

  array.prototype.findlast@1.2.5:
    resolution:
      {
        integrity: sha512-CVvd6FHg1Z3POpBLxO6E6zr+rSKEQ9L6rZHAaY7lLfhKsWYUBBOuMs0e9o24oopj6H+geRCX0YJ+TJLBK2eHyQ==,
      }
    engines: { node: ">= 0.4" }

  array.prototype.findlastindex@1.2.5:
    resolution:
      {
        integrity: sha512-zfETvRFA8o7EiNn++N5f/kaCw221hrpGsDmcpndVupkPzEc1Wuf3VgC0qby1BbHs7f5DVYjgtEU2LLh5bqeGfQ==,
      }
    engines: { node: ">= 0.4" }

  array.prototype.flat@1.3.2:
    resolution:
      {
        integrity: sha512-djYB+Zx2vLewY8RWlNCUdHjDXs2XOgm602S9E7P/UpHgfeHL00cRiIF+IN/G/aUJ7kGPb6yO/ErDI5V2s8iycA==,
      }
    engines: { node: ">= 0.4" }

  array.prototype.flatmap@1.3.2:
    resolution:
      {
        integrity: sha512-Ewyx0c9PmpcsByhSW4r+9zDU7sGjFc86qf/kKtuSCRdhfbk0SNLLkaT5qvcHnRGgc5NP/ly/y+qkXkqONX54CQ==,
      }
    engines: { node: ">= 0.4" }

  array.prototype.toreversed@1.1.2:
    resolution:
      {
        integrity: sha512-wwDCoT4Ck4Cz7sLtgUmzR5UV3YF5mFHUlbChCzZBQZ+0m2cl/DH3tKgvphv1nKgFsJ48oCSg6p91q2Vm0I/ZMA==,
      }

  array.prototype.tosorted@1.1.3:
    resolution:
      {
        integrity: sha512-/DdH4TiTmOKzyQbp/eadcCVexiCb36xJg7HshYOYJnNZFDj33GEv0P7GxsynpShhq4OLYJzbGcBDkLsDt7MnNg==,
      }

  arraybuffer.prototype.slice@1.0.3:
    resolution:
      {
        integrity: sha512-bMxMKAjg13EBSVscxTaYA4mRc5t1UAXa2kXiGTNfZ079HIWXEkKmkgFrh/nJqamaLSrXO5H4WFFkPEaLJWbs3A==,
      }
    engines: { node: ">= 0.4" }

  arrify@1.0.1:
    resolution:
      {
        integrity: sha512-3CYzex9M9FGQjCGMGyi6/31c8GJbgb0qGyrx5HWxPd0aCwh4cB2YjMb2Xf9UuoogrMrlO9cTqnB5rI5GHZTcUA==,
      }
    engines: { node: ">=0.10.0" }

  assertion-error@1.1.0:
    resolution:
      {
        integrity: sha512-jgsaNduz+ndvGyFt3uSuWqvy4lCnIJiovtouQN5JZHOKCS2QuhEdbcQHFhVksz2N2U9hXJo8odG7ETyWlEeuDw==,
      }

  asynckit@0.4.0:
    resolution:
      {
        integrity: sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q==,
      }

  atob@2.1.2:
    resolution:
      {
        integrity: sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==,
      }
    engines: { node: ">= 4.5.0" }
    hasBin: true

  autoprefixer@10.4.19:
    resolution:
      {
        integrity: sha512-BaENR2+zBZ8xXhM4pUaKUxlVdxZ0EZhjvbopwnXmxRUfqDmwSpC2lAi/QXvx7NRdPCo1WKEcEF6mV64si1z4Ew==,
      }
    engines: { node: ^10 || ^12 || >=14 }
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0

  available-typed-arrays@1.0.7:
    resolution:
      {
        integrity: sha512-wvUjBtSGN7+7SjNpq/9M2Tg350UZD3q62IFZLbRAR1bSMlCo1ZaeW+BJ+D090e4hIIZLBcTDWe4Mh4jvUDajzQ==,
      }
    engines: { node: ">= 0.4" }

  axios@1.7.4:
    resolution:
      {
        integrity: sha512-DukmaFRnY6AzAALSH4J2M3k6PkaC+MfaAGdEERRWcC9q3/TWQwLpHR8ZRLKTdQ3aBDL64EdluRDjJqKw+BPZEw==,
      }

  babel-plugin-import@1.13.8:
    resolution:
      {
        integrity: sha512-36babpjra5m3gca44V6tSTomeBlPA7cHUynrE2WiQIm3rEGD9xy28MKsx5IdO45EbnpJY7Jrgd00C6Dwt/l/2Q==,
      }

  babel-plugin-macros@3.1.0:
    resolution:
      {
        integrity: sha512-Cg7TFGpIr01vOQNODXOOaGz2NpCU5gl8x1qJFbb6hbZxR7XrcE2vtbAsTAbJ7/xwJtUuJEw8K8Zr/AE0LHlesg==,
      }
    engines: { node: ">=10", npm: ">=6" }

  babel-plugin-polyfill-corejs2@0.4.11:
    resolution:
      {
        integrity: sha512-sMEJ27L0gRHShOh5G54uAAPaiCOygY/5ratXuiyb2G46FmlSpc9eFCzYVyDiPxfNbwzA7mYahmjQc5q+CZQ09Q==,
      }
    peerDependencies:
      "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-corejs3@0.10.4:
    resolution:
      {
        integrity: sha512-25J6I8NGfa5YkCDogHRID3fVCadIR8/pGl1/spvCkzb6lVn6SR3ojpx9nOn9iEBcUsjY24AmdKm5khcfKdylcg==,
      }
    peerDependencies:
      "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0

  babel-plugin-polyfill-regenerator@0.6.2:
    resolution:
      {
        integrity: sha512-2R25rQZWP63nGwaAswvDazbPXfrM3HwVoBXK6HcqeKrSrL/JqcC/rDcf95l4r7LXLyxDXc8uQDa064GubtCABg==,
      }
    peerDependencies:
      "@babel/core": ^7.4.0 || ^8.0.0-0 <8.0.0

  balanced-match@1.0.2:
    resolution:
      {
        integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==,
      }

  binary-extensions@2.3.0:
    resolution:
      {
        integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==,
      }
    engines: { node: ">=8" }

  boolbase@1.0.0:
    resolution:
      {
        integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==,
      }

  brace-expansion@1.1.11:
    resolution:
      {
        integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==,
      }

  brace-expansion@2.0.1:
    resolution:
      {
        integrity: sha512-XnAIvQ8eM+kC6aULx6wuQiwVsnzsi9d3WxzV3FpWTGA19F621kwdbsAcFKXgKUHZWsy+mY6iL1sHTxWEFCytDA==,
      }

  braces@3.0.2:
    resolution:
      {
        integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==,
      }
    engines: { node: ">=8" }

  browserslist@4.23.0:
    resolution:
      {
        integrity: sha512-QW8HiM1shhT2GuzkvklfjcKDiWFXHOeFCIA/huJPwHsslwcydgk7X+z2zXpEijP98UCY7HbubZt5J2Zgvf0CaQ==,
      }
    engines: { node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7 }
    hasBin: true

  builtins@5.1.0:
    resolution:
      {
        integrity: sha512-SW9lzGTLvWTP1AY8xeAMZimqDrIaSdLQUcVr9DMef51niJ022Ri87SwRRKYm4A6iHfkPaiVUu/Duw2Wc4J7kKg==,
      }

  c8@7.14.0:
    resolution:
      {
        integrity: sha512-i04rtkkcNcCf7zsQcSv/T9EbUn4RXQ6mropeMcjFOsQXQ0iGLAr/xT6TImQg4+U9hmNpN9XdvPkjUL1IzbgxJw==,
      }
    engines: { node: ">=10.12.0" }
    hasBin: true

  cac@6.7.14:
    resolution:
      {
        integrity: sha512-b6Ilus+c3RrdDk+JhLKUAQfzzgLEPy6wcXqS7f/xe1EETvsDP6GORG7SFuOs6cID5YkqchW/LXZbX5bc8j7ZcQ==,
      }
    engines: { node: ">=8" }

  call-bind@1.0.7:
    resolution:
      {
        integrity: sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==,
      }
    engines: { node: ">= 0.4" }

  callsites@3.1.0:
    resolution:
      {
        integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==,
      }
    engines: { node: ">=6" }

  camelcase-css@2.0.1:
    resolution:
      {
        integrity: sha512-QOSvevhslijgYwRx6Rv7zKdMF8lbRmx+uQGx2+vDc+KI/eBnsy9kit5aj23AgGu3pa4t9AgwbnXWqS+iOY+2aA==,
      }
    engines: { node: ">= 6" }

  camelcase-keys@6.2.2:
    resolution:
      {
        integrity: sha512-YrwaA0vEKazPBkn0ipTiMpSajYDSe+KjQfrjhcBMxJt/znbvlHd8Pw/Vamaz5EB4Wfhs3SUR3Z9mwRu/P3s3Yg==,
      }
    engines: { node: ">=8" }

  camelcase@5.3.1:
    resolution:
      {
        integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==,
      }
    engines: { node: ">=6" }

  camelcase@6.3.0:
    resolution:
      {
        integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==,
      }
    engines: { node: ">=10" }

  caniuse-lite@1.0.30001718:
    resolution:
      {
        integrity: sha512-AflseV1ahcSunK53NfEs9gFWgOEmzr0f+kaMFA4xiLZlr9Hzt7HxcSpIFcnNCUkz6R6dWKa54rUz3HUmI3nVcw==,
      }

  chai@4.4.1:
    resolution:
      {
        integrity: sha512-13sOfMv2+DWduEU+/xbun3LScLoqN17nBeTLUsmDfKdoiC1fr0n9PU4guu4AhRcOVFk/sW8LyZWHuhWtQZiF+g==,
      }
    engines: { node: ">=4" }

  chalk@1.1.3:
    resolution:
      {
        integrity: sha512-U3lRVLMSlsCfjqYPbLyVv11M9CPW4I728d6TCKMAOJueEeB9/8o+eSsMnxPJD+Q+K909sdESg7C+tIkoH6on1A==,
      }
    engines: { node: ">=0.10.0" }

  chalk@2.4.2:
    resolution:
      {
        integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==,
      }
    engines: { node: ">=4" }

  chalk@4.1.2:
    resolution:
      {
        integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==,
      }
    engines: { node: ">=10" }

  chalk@5.3.0:
    resolution:
      {
        integrity: sha512-dLitG79d+GV1Nb/VYcCDFivJeK1hiukt9QjRNVOsUtTy1rR1YJsmpGGTZ3qJos+uw7WmWF4wUwBd9jxjocFC2w==,
      }
    engines: { node: ^12.17.0 || ^14.13 || >=16.0.0 }

  check-error@1.0.3:
    resolution:
      {
        integrity: sha512-iKEoDYaRmd1mxM90a2OEfWhjsjPpYPuQ+lMYsoxB126+t8fw7ySEO48nmDg5COTjxDI65/Y2OWpeEHk3ZOe8zg==,
      }

  chokidar@3.6.0:
    resolution:
      {
        integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==,
      }
    engines: { node: ">= 8.10.0" }

  chroma-js@2.4.2:
    resolution:
      {
        integrity: sha512-U9eDw6+wt7V8z5NncY2jJfZa+hUH8XEj8FQHgFJTrUFnJfXYf4Ml4adI2vXZOjqRDpFWtYVWypDfZwnJ+HIR4A==,
      }

  cli-cursor@4.0.0:
    resolution:
      {
        integrity: sha512-VGtlMu3x/4DOtIUwEkRezxUZ2lBacNJCHash0N0WeZDBS+7Ux1dm3XWAgWYxLJFMMdOeXMHXorshEFhbMSGelg==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  cli-truncate@3.1.0:
    resolution:
      {
        integrity: sha512-wfOBkjXteqSnI59oPcJkcPl/ZmwvMMOj340qUIY1SKZCv0B9Cf4D4fAucRkIKQmsIuYK3x1rrgU7MeGRruiuiA==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  cliui@7.0.4:
    resolution:
      {
        integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==,
      }

  cliui@8.0.1:
    resolution:
      {
        integrity: sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==,
      }
    engines: { node: ">=12" }

  clsx@1.2.1:
    resolution:
      {
        integrity: sha512-EcR6r5a8bj6pu3ycsa/E/cKVGuTgZJZdsyUYHOksG/UHIiKfjxzRxYJpyVBwYaQeOvghal9fcc4PidlgzugAQg==,
      }
    engines: { node: ">=6" }

  clsx@2.1.1:
    resolution:
      {
        integrity: sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA==,
      }
    engines: { node: ">=6" }

  color-convert@1.9.3:
    resolution:
      {
        integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==,
      }

  color-convert@2.0.1:
    resolution:
      {
        integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==,
      }
    engines: { node: ">=7.0.0" }

  color-name@1.1.3:
    resolution:
      {
        integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==,
      }

  color-name@1.1.4:
    resolution:
      {
        integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==,
      }

  colorette@2.0.20:
    resolution:
      {
        integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==,
      }

  combined-stream@1.0.8:
    resolution:
      {
        integrity: sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg==,
      }
    engines: { node: ">= 0.8" }

  commander@11.0.0:
    resolution:
      {
        integrity: sha512-9HMlXtt/BNoYr8ooyjjNRdIilOTkVJXB+GhxMTtOKwk0R4j4lS4NpjuqmRxroBfnfTSHQIHQB7wryHhXarNjmQ==,
      }
    engines: { node: ">=16" }

  commander@4.1.1:
    resolution:
      {
        integrity: sha512-NOKm8xhkzAjzFx8B2v5OAHT+u5pRQc2UCa2Vq9jYL/31o2wi9mxBA7LIFs3sV5VSC49z6pEhfbMULvShKj26WA==,
      }
    engines: { node: ">= 6" }

  commander@7.2.0:
    resolution:
      {
        integrity: sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==,
      }
    engines: { node: ">= 10" }

  commander@8.3.0:
    resolution:
      {
        integrity: sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==,
      }
    engines: { node: ">= 12" }

  common-tags@1.8.2:
    resolution:
      {
        integrity: sha512-gk/Z852D2Wtb//0I+kRFNKKE9dIIVirjoqPoA1wJU+XePVXZfGeBpk45+A1rKO4Q43prqWBNY/MiIeRLbPWUaA==,
      }
    engines: { node: ">=4.0.0" }

  compare-func@2.0.0:
    resolution:
      {
        integrity: sha512-zHig5N+tPWARooBnb0Zx1MFcdfpyJrfTJ3Y5L+IFvUm8rM74hHz66z0gw0x4tijh5CorKkKUCnW82R2vmpeCRA==,
      }

  concat-map@0.0.1:
    resolution:
      {
        integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==,
      }

  confbox@0.1.7:
    resolution:
      {
        integrity: sha512-uJcB/FKZtBMCJpK8MQji6bJHgu1tixKPxRLeGkNzBoOZzpnZUJm0jm2/sBDWcuBx1dYgxV4JU+g5hmNxCyAmdA==,
      }

  conventional-changelog-angular@6.0.0:
    resolution:
      {
        integrity: sha512-6qLgrBF4gueoC7AFVHu51nHL9pF9FRjXrH+ceVf7WmAfH3gs+gEYOkvxhjMPjZu57I4AGUGoNTY8V7Hrgf1uqg==,
      }
    engines: { node: ">=14" }

  conventional-changelog-conventionalcommits@6.1.0:
    resolution:
      {
        integrity: sha512-3cS3GEtR78zTfMzk0AizXKKIdN4OvSh7ibNz6/DPbhWWQu7LqE/8+/GqSodV+sywUR2gpJAdP/1JFf4XtN7Zpw==,
      }
    engines: { node: ">=14" }

  conventional-commits-parser@4.0.0:
    resolution:
      {
        integrity: sha512-WRv5j1FsVM5FISJkoYMR6tPk07fkKT0UodruX4je86V4owk451yjXAKzKAPOs9l7y59E2viHUS9eQ+dfUA9NSg==,
      }
    engines: { node: ">=14" }
    hasBin: true

  convert-source-map@1.9.0:
    resolution:
      {
        integrity: sha512-ASFBup0Mz1uyiIjANan1jzLQami9z1PoYSZCiiYW2FczPbenXc45FZdBZLzOT+r6+iciuEModtmCti+hjaAk0A==,
      }

  convert-source-map@2.0.0:
    resolution:
      {
        integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==,
      }

  copy-to-clipboard@3.3.3:
    resolution:
      {
        integrity: sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA==,
      }

  core-js-compat@3.37.1:
    resolution:
      {
        integrity: sha512-9TNiImhKvQqSUkOvk/mMRZzOANTiEVC7WaBNhHcKM7x+/5E1l5NvsysR19zuDQScE8k+kfQXWRN3AtS/eOSHpg==,
      }

  cosmiconfig-typescript-loader@4.4.0:
    resolution:
      {
        integrity: sha512-BabizFdC3wBHhbI4kJh0VkQP9GkBfoHPydD0COMce1nJ1kJAB3F2TmJ/I7diULBKtmEWSwEbuN/KDtgnmUUVmw==,
      }
    engines: { node: ">=v14.21.3" }
    peerDependencies:
      "@types/node": "*"
      cosmiconfig: ">=7"
      ts-node: ">=10"
      typescript: ">=4"

  cosmiconfig@7.1.0:
    resolution:
      {
        integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==,
      }
    engines: { node: ">=10" }

  cosmiconfig@8.3.6:
    resolution:
      {
        integrity: sha512-kcZ6+W5QzcJ3P1Mt+83OUv/oHFqZHIx8DuxG6eZ5RGMERoLqp4BuGjhHLYGK+Kf5XVkQvqBSmAy/nGWN3qDgEA==,
      }
    engines: { node: ">=14" }
    peerDependencies:
      typescript: ">=4.9.5"
    peerDependenciesMeta:
      typescript:
        optional: true

  create-require@1.1.1:
    resolution:
      {
        integrity: sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==,
      }

  cross-spawn@6.0.5:
    resolution:
      {
        integrity: sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==,
      }
    engines: { node: ">=4.8" }

  cross-spawn@7.0.3:
    resolution:
      {
        integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==,
      }
    engines: { node: ">= 8" }

  css-in-js-utils@3.1.0:
    resolution:
      {
        integrity: sha512-fJAcud6B3rRu+KHYk+Bwf+WFL2MDCJJ1XG9x137tJQ0xYxor7XziQtuGFbWNdqrvF4Tk26O3H73nfVqXt/fW1A==,
      }

  css-select@5.1.0:
    resolution:
      {
        integrity: sha512-nwoRF1rvRRnnCqqY7updORDsuqKzqYJ28+oSMaJMMgOauh3fvwHqMS7EZpIPqK8GL+g9mKxF1vP/ZjSeNjEVHg==,
      }

  css-tree@1.1.3:
    resolution:
      {
        integrity: sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==,
      }
    engines: { node: ">=8.0.0" }

  css-tree@2.2.1:
    resolution:
      {
        integrity: sha512-OA0mILzGc1kCOCSJerOeqDxDQ4HOh+G8NbOJFOTgOCzpw7fCBubk0fEyxp8AgOL/jvLgYA/uV0cMbe43ElF1JA==,
      }
    engines: { node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0, npm: ">=7.0.0" }

  css-tree@2.3.1:
    resolution:
      {
        integrity: sha512-6Fv1DV/TYw//QF5IzQdqsNDjx/wc8TrMBZsqjL9eW01tWb7R7k/mq+/VXfJCl7SoD5emsJop9cOByJZfs8hYIw==,
      }
    engines: { node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0 }

  css-what@6.1.0:
    resolution:
      {
        integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==,
      }
    engines: { node: ">= 6" }

  css.escape@1.5.1:
    resolution:
      {
        integrity: sha512-YUifsXXuknHlUsmlgyY0PKzgPOr7/FjCePfHNt0jxm83wHZi44VDMQ7/fGNkjY3/jV1MC+1CmZbaHzugyeRtpg==,
      }

  css@2.2.4:
    resolution:
      {
        integrity: sha512-oUnjmWpy0niI3x/mPL8dVEI1l7MnG3+HHyRPHf+YFSbK+svOhXpmSOcDURUh2aOCgl2grzrOPt1nHLuCVFULLw==,
      }

  cssesc@3.0.0:
    resolution:
      {
        integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==,
      }
    engines: { node: ">=4" }
    hasBin: true

  csso@5.0.5:
    resolution:
      {
        integrity: sha512-0LrrStPOdJj+SPCCrGhzryycLjwcgUSHBtxNA8aIDxf0GLsRh1cKYhB00Gd1lDOS4yGH69+SNn13+TWbVHETFQ==,
      }
    engines: { node: ^10 || ^12.20.0 || ^14.13.0 || >=15.0.0, npm: ">=7.0.0" }

  cssstyle@3.0.0:
    resolution:
      {
        integrity: sha512-N4u2ABATi3Qplzf0hWbVCdjenim8F3ojEXpBDF5hBpjzW182MjNGLqfmQ0SkSPeQ+V86ZXgeH8aXj6kayd4jgg==,
      }
    engines: { node: ">=14" }

  csstype@3.1.3:
    resolution:
      {
        integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==,
      }

  dargs@7.0.0:
    resolution:
      {
        integrity: sha512-2iy1EkLdlBzQGvbweYRFxmFath8+K7+AKB0TlhHWkNuH+TmovaMH/Wp7V7R4u7f4SnX3OgLsU9t1NI9ioDnUpg==,
      }
    engines: { node: ">=8" }

  data-urls@4.0.0:
    resolution:
      {
        integrity: sha512-/mMTei/JXPqvFqQtfyTowxmJVwr2PVAeCcDxyFf6LhoOu/09TX2OX3kb2wzi4DMXcfj4OItwDOnhl5oziPnT6g==,
      }
    engines: { node: ">=14" }

  data-view-buffer@1.0.1:
    resolution:
      {
        integrity: sha512-0lht7OugA5x3iJLOWFhWK/5ehONdprk0ISXqVFn/NFrDu+cuc8iADFrGQz5BnRK7LLU3JmkbXSxaqX+/mXYtUA==,
      }
    engines: { node: ">= 0.4" }

  data-view-byte-length@1.0.1:
    resolution:
      {
        integrity: sha512-4J7wRJD3ABAzr8wP+OcIcqq2dlUKp4DVflx++hs5h5ZKydWMI6/D/fAot+yh6g2tHh8fLFTvNOaVN357NvSrOQ==,
      }
    engines: { node: ">= 0.4" }

  data-view-byte-offset@1.0.0:
    resolution:
      {
        integrity: sha512-t/Ygsytq+R995EJ5PZlD4Cu56sWa8InXySaViRzw9apusqsOO2bQP+SbYzAhR0pFKoB+43lYy8rWban9JSuXnA==,
      }
    engines: { node: ">= 0.4" }

  dayjs@1.11.13:
    resolution:
      {
        integrity: sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg==,
      }

  debug@3.2.7:
    resolution:
      {
        integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==,
      }
    peerDependencies:
      supports-color: "*"
    peerDependenciesMeta:
      supports-color:
        optional: true

  debug@4.3.4:
    resolution:
      {
        integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==,
      }
    engines: { node: ">=6.0" }
    peerDependencies:
      supports-color: "*"
    peerDependenciesMeta:
      supports-color:
        optional: true

  decamelize-keys@1.1.1:
    resolution:
      {
        integrity: sha512-WiPxgEirIV0/eIOMcnFBA3/IJZAZqKnwAwWyvvdi4lsr1WCN22nhdf/3db3DoZcUjTV2SqfzIwNyp6y2xs3nmg==,
      }
    engines: { node: ">=0.10.0" }

  decamelize@1.2.0:
    resolution:
      {
        integrity: sha512-z2S+W9X73hAUUki+N+9Za2lBlun89zigOyGrsax+KUQ6wKW4ZoWpEYBkGhQjwAjjDCkWxhY0VKEhk8wzY7F5cA==,
      }
    engines: { node: ">=0.10.0" }

  decimal.js@10.4.3:
    resolution:
      {
        integrity: sha512-VBBaLc1MgL5XpzgIP7ny5Z6Nx3UrRkIViUkPUdtl9aya5amy3De1gsUUSB1g3+3sExYNjCAsAznmukyxCb1GRA==,
      }

  decode-uri-component@0.2.2:
    resolution:
      {
        integrity: sha512-FqUYQ+8o158GyGTrMFJms9qh3CqTKvAqgqsTnkLI8sKu0028orqBhxNMFkFen0zGyg6epACD32pjVk58ngIErQ==,
      }
    engines: { node: ">=0.10" }

  deep-eql@4.1.3:
    resolution:
      {
        integrity: sha512-WaEtAOpRA1MQ0eohqZjpGD8zdI0Ovsm8mmFhaDN8dvDZzyoUMcYDnf5Y6iu7HTXxf8JDS23qWa4a+hKCDyOPzw==,
      }
    engines: { node: ">=6" }

  deep-equal@2.2.3:
    resolution:
      {
        integrity: sha512-ZIwpnevOurS8bpT4192sqAowWM76JDKSHYzMLty3BZGSswgq6pBaH3DhCSW5xVAZICZyKdOBPjwww5wfgT/6PA==,
      }
    engines: { node: ">= 0.4" }

  deep-is@0.1.4:
    resolution:
      {
        integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==,
      }

  deepmerge@2.2.1:
    resolution:
      {
        integrity: sha512-R9hc1Xa/NOBi9WRVUWg19rl1UB7Tt4kuPd+thNJgFZoxXsTz7ncaPaeIm+40oSGuP33DfMb4sZt1QIGiJzC4EA==,
      }
    engines: { node: ">=0.10.0" }

  deepmerge@4.3.1:
    resolution:
      {
        integrity: sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==,
      }
    engines: { node: ">=0.10.0" }

  define-data-property@1.1.4:
    resolution:
      {
        integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==,
      }
    engines: { node: ">= 0.4" }

  define-properties@1.2.1:
    resolution:
      {
        integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==,
      }
    engines: { node: ">= 0.4" }

  delayed-stream@1.0.0:
    resolution:
      {
        integrity: sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ==,
      }
    engines: { node: ">=0.4.0" }

  didyoumean@1.2.2:
    resolution:
      {
        integrity: sha512-gxtyfqMg7GKyhQmb056K7M3xszy/myH8w+B4RT+QXBQsvAOdc3XymqDDPHx1BgPgsdAA5SIifona89YtRATDzw==,
      }

  diff-sequences@24.9.0:
    resolution:
      {
        integrity: sha512-Dj6Wk3tWyTE+Fo1rW8v0Xhwk80um6yFYKbuAxc9c3EZxIHFDYwbi34Uk42u1CdnIiVorvt4RmlSDjIPyzGC2ew==,
      }
    engines: { node: ">= 6" }

  diff@4.0.2:
    resolution:
      {
        integrity: sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==,
      }
    engines: { node: ">=0.3.1" }

  diff@5.2.0:
    resolution:
      {
        integrity: sha512-uIFDxqpRZGZ6ThOk84hEfqWoHx2devRFvpTZcTHur85vImfaxUbTW9Ryh4CpCuDnToOP1CEtXKIgytHBPVff5A==,
      }
    engines: { node: ">=0.3.1" }

  dir-glob@3.0.1:
    resolution:
      {
        integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==,
      }
    engines: { node: ">=8" }

  dlv@1.1.3:
    resolution:
      {
        integrity: sha512-+HlytyjlPKnIG8XuRG8WvmBP8xs8P71y+SKKS6ZXWoEgLuePxtDoUEiH7WkdePWrQ5JBpE6aoVqfZfJUQkjXwA==,
      }

  doctrine@2.1.0:
    resolution:
      {
        integrity: sha512-35mSku4ZXK0vfCuHEDAwt55dg2jNajHZ1odvF+8SSr82EsZY4QmXfuWso8oEd8zRhVObSN18aM0CjSdoBX7zIw==,
      }
    engines: { node: ">=0.10.0" }

  doctrine@3.0.0:
    resolution:
      {
        integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==,
      }
    engines: { node: ">=6.0.0" }

  dom-accessibility-api@0.5.16:
    resolution:
      {
        integrity: sha512-X7BJ2yElsnOJ30pZF4uIIDfBEVgF4XEBxL9Bxhy6dnrm5hkzqmsWHGTiHqRiITNhMyFLyAiWndIJP7Z1NTteDg==,
      }

  dom-helpers@5.2.1:
    resolution:
      {
        integrity: sha512-nRCa7CK3VTrM2NmGkIy4cbK7IZlgBE/PYMn55rrXefr5xXDP0LdtfPnblFDoVdcAfslJ7or6iqAUnx0CCGIWQA==,
      }

  dom-serializer@2.0.0:
    resolution:
      {
        integrity: sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==,
      }

  domelementtype@2.3.0:
    resolution:
      {
        integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==,
      }

  domexception@4.0.0:
    resolution:
      {
        integrity: sha512-A2is4PLG+eeSfoTMA95/s4pvAoSo2mKtiM5jlHkAVewmiO8ISFTFKZjH7UAM1Atli/OT/7JHOrJRJiMKUZKYBw==,
      }
    engines: { node: ">=12" }
    deprecated: Use your platform's native DOMException instead

  domhandler@5.0.3:
    resolution:
      {
        integrity: sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==,
      }
    engines: { node: ">= 4" }

  domutils@3.1.0:
    resolution:
      {
        integrity: sha512-H78uMmQtI2AhgDJjWeQmHwJJ2bLPD3GMmO7Zja/ZZh84wkm+4ut+IUnUdRa8uCGX88DiVx1j6FRe1XfxEgjEZA==,
      }

  dot-prop@5.3.0:
    resolution:
      {
        integrity: sha512-QM8q3zDe58hqUqjraQOmzZ1LIH9SWQJTlEKCH4kJ2oQvLZk7RbQXvtDM2XEq3fwkV9CCvvH4LA0AV+ogFsBM2Q==,
      }
    engines: { node: ">=8" }

  dotenv@16.4.5:
    resolution:
      {
        integrity: sha512-ZmdL2rui+eB2YwhsWzjInR8LldtZHGDoQ1ugH85ppHKwpUHL7j7rN0Ti9NCnGiQbhaZ11FpR+7ao1dNsmduNUg==,
      }
    engines: { node: ">=12" }

  eastasianwidth@0.2.0:
    resolution:
      {
        integrity: sha512-I88TYZWc9XiYHRQ4/3c5rjjfgkjhLyW2luGIheGERbNQ6OY7yTybanSpDXZa8y7VUP9YmDcYa+eyq4ca7iLqWA==,
      }

  electron-to-chromium@1.4.769:
    resolution:
      {
        integrity: sha512-bZu7p623NEA2rHTc9K1vykl57ektSPQYFFqQir8BOYf6EKOB+yIsbFB9Kpm7Cgt6tsLr9sRkqfqSZUw7LP1XxQ==,
      }

  emoji-regex@8.0.0:
    resolution:
      {
        integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==,
      }

  emoji-regex@9.2.2:
    resolution:
      {
        integrity: sha512-L18DaJsXSUk2+42pv8mLs5jJT2hqFkFE4j21wOmgbUqsZ2hL72NsUU785g9RXgo3s0ZNgVl42TiHp3ZtOv/Vyg==,
      }

  entities@4.5.0:
    resolution:
      {
        integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==,
      }
    engines: { node: ">=0.12" }

  env-cmd@10.1.0:
    resolution:
      {
        integrity: sha512-mMdWTT9XKN7yNth/6N6g2GuKuJTsKMDHlQFUDacb/heQRRWOTIZ42t1rMHnQu4jYxU1ajdTeJM+9eEETlqToMA==,
      }
    engines: { node: ">=8.0.0" }
    hasBin: true

  error-ex@1.3.2:
    resolution:
      {
        integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==,
      }

  error-stack-parser@2.1.4:
    resolution:
      {
        integrity: sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==,
      }

  es-abstract@1.23.3:
    resolution:
      {
        integrity: sha512-e+HfNH61Bj1X9/jLc5v1owaLYuHdeHHSQlkhCBiTK8rBvKaULl/beGMxwrMXjpYrv4pz22BlY570vVePA2ho4A==,
      }
    engines: { node: ">= 0.4" }

  es-define-property@1.0.0:
    resolution:
      {
        integrity: sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==,
      }
    engines: { node: ">= 0.4" }

  es-errors@1.3.0:
    resolution:
      {
        integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==,
      }
    engines: { node: ">= 0.4" }

  es-get-iterator@1.1.3:
    resolution:
      {
        integrity: sha512-sPZmqHBe6JIiTfN5q2pEi//TwxmAFHwj/XEuYjTuse78i8KxaqMTTzxPoFKuzRpDpTJ+0NAbpfenkmH2rePtuw==,
      }

  es-iterator-helpers@1.0.19:
    resolution:
      {
        integrity: sha512-zoMwbCcH5hwUkKJkT8kDIBZSz9I6mVG//+lDCinLCGov4+r7NIy0ld8o03M0cJxl2spVf6ESYVS6/gpIfq1FFw==,
      }
    engines: { node: ">= 0.4" }

  es-object-atoms@1.0.0:
    resolution:
      {
        integrity: sha512-MZ4iQ6JwHOBQjahnjwaC1ZtIBH+2ohjamzAO3oaHcXYup7qxjF2fixyH+Q71voWHeOkI2q/TnJao/KfXYIZWbw==,
      }
    engines: { node: ">= 0.4" }

  es-set-tostringtag@2.0.3:
    resolution:
      {
        integrity: sha512-3T8uNMC3OQTHkFUsFq8r/BwAXLHvU/9O9mE0fBc/MY5iq/8H7ncvO947LmYA6ldWw9Uh8Yhf25zu6n7nML5QWQ==,
      }
    engines: { node: ">= 0.4" }

  es-shim-unscopables@1.0.2:
    resolution:
      {
        integrity: sha512-J3yBRXCzDu4ULnQwxyToo/OjdMx6akgVC7K6few0a7F/0wLtmKKN7I73AH5T2836UuXRqN7Qg+IIUw/+YJksRw==,
      }

  es-to-primitive@1.2.1:
    resolution:
      {
        integrity: sha512-QCOllgZJtaUo9miYBcLChTUaHNjJF3PYs1VidD7AwiEj1kYxKeQTctLAezAOH5ZKRH0g2IgPn6KwB4IT8iRpvA==,
      }
    engines: { node: ">= 0.4" }

  esbuild@0.18.20:
    resolution:
      {
        integrity: sha512-ceqxoedUrcayh7Y7ZX6NdbbDzGROiyVBgC4PriJThBKSVPWnnFHZAkfI1lJT8QFkOwH4qOS2SJkS4wvpGl8BpA==,
      }
    engines: { node: ">=12" }
    hasBin: true

  esbuild@0.25.9:
    resolution:
      {
        integrity: sha512-CRbODhYyQx3qp7ZEwzxOk4JBqmD/seJrzPa/cGjY1VtIn5E09Oi9/dB4JwctnfZ8Q8iT7rioVv5k/FNT/uf54g==,
      }
    engines: { node: ">=18" }
    hasBin: true

  escalade@3.1.2:
    resolution:
      {
        integrity: sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==,
      }
    engines: { node: ">=6" }

  escape-string-regexp@1.0.5:
    resolution:
      {
        integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==,
      }
    engines: { node: ">=0.8.0" }

  escape-string-regexp@4.0.0:
    resolution:
      {
        integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==,
      }
    engines: { node: ">=10" }

  eslint-config-prettier@8.10.0:
    resolution:
      {
        integrity: sha512-SM8AMJdeQqRYT9O9zguiruQZaN7+z+E4eAP9oiLNGKMtomwaB1E9dcgUD6ZAn/eQAb52USbvezbiljfZUhbJcg==,
      }
    hasBin: true
    peerDependencies:
      eslint: ">=7.0.0"

  eslint-config-standard@17.1.0:
    resolution:
      {
        integrity: sha512-IwHwmaBNtDK4zDHQukFDW5u/aTb8+meQWZvNFWkiGmbWjD6bqyuSSBxxXKkCftCUzc1zwCH2m/baCNDLGmuO5Q==,
      }
    engines: { node: ">=12.0.0" }
    peerDependencies:
      eslint: ^8.0.1
      eslint-plugin-import: ^2.25.2
      eslint-plugin-n: "^15.0.0 || ^16.0.0 "
      eslint-plugin-promise: ^6.0.0

  eslint-import-resolver-node@0.3.9:
    resolution:
      {
        integrity: sha512-WFj2isz22JahUv+B788TlO3N6zL3nNJGU8CcZbPZvVEkBPaJdCV4vy5wyghty5ROFbCRnm132v8BScu5/1BQ8g==,
      }

  eslint-module-utils@2.8.1:
    resolution:
      {
        integrity: sha512-rXDXR3h7cs7dy9RNpUlQf80nX31XWJEyGq1tRMo+6GsO5VmTe4UTwtmonAD4ZkAsrfMVDA2wlGJ3790Ys+D49Q==,
      }
    engines: { node: ">=4" }
    peerDependencies:
      "@typescript-eslint/parser": "*"
      eslint: "*"
      eslint-import-resolver-node: "*"
      eslint-import-resolver-typescript: "*"
      eslint-import-resolver-webpack: "*"
    peerDependenciesMeta:
      "@typescript-eslint/parser":
        optional: true
      eslint:
        optional: true
      eslint-import-resolver-node:
        optional: true
      eslint-import-resolver-typescript:
        optional: true
      eslint-import-resolver-webpack:
        optional: true

  eslint-plugin-es@4.1.0:
    resolution:
      {
        integrity: sha512-GILhQTnjYE2WorX5Jyi5i4dz5ALWxBIdQECVQavL6s7cI76IZTDWleTHkxz/QT3kvcs2QlGHvKLYsSlPOlPXnQ==,
      }
    engines: { node: ">=8.10.0" }
    peerDependencies:
      eslint: ">=4.19.1"

  eslint-plugin-import@2.29.1:
    resolution:
      {
        integrity: sha512-BbPC0cuExzhiMo4Ff1BTVwHpjjv28C5R+btTOGaCRC7UEz801up0JadwkeSk5Ued6TG34uaczuVuH6qyy5YUxw==,
      }
    engines: { node: ">=4" }
    peerDependencies:
      "@typescript-eslint/parser": "*"
      eslint: ^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8
    peerDependenciesMeta:
      "@typescript-eslint/parser":
        optional: true

  eslint-plugin-n@15.7.0:
    resolution:
      {
        integrity: sha512-jDex9s7D/Qial8AGVIHq4W7NswpUD5DPDL2RH8Lzd9EloWUuvUkHfv4FRLMipH5q2UtyurorBkPeNi1wVWNh3Q==,
      }
    engines: { node: ">=12.22.0" }
    peerDependencies:
      eslint: ">=7.0.0"

  eslint-plugin-prettier@4.2.1:
    resolution:
      {
        integrity: sha512-f/0rXLXUt0oFYs8ra4w49wYZBG5GKZpAYsJSm6rnYL5uVDjd+zowwMwVZHnAjf4edNrKpCDYfXDgmRE/Ak7QyQ==,
      }
    engines: { node: ">=12.0.0" }
    peerDependencies:
      eslint: ">=7.28.0"
      eslint-config-prettier: "*"
      prettier: ">=2.0.0"
    peerDependenciesMeta:
      eslint-config-prettier:
        optional: true

  eslint-plugin-promise@6.1.1:
    resolution:
      {
        integrity: sha512-tjqWDwVZQo7UIPMeDReOpUgHCmCiH+ePnVT+5zVapL0uuHnegBUs2smM13CzOs2Xb5+MHMRFTs9v24yjba4Oig==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0

  eslint-plugin-react@7.34.1:
    resolution:
      {
        integrity: sha512-N97CxlouPT1AHt8Jn0mhhN2RrADlUAsk1/atcT2KyA/l9Q/E6ll7OIGwNumFmWfZ9skV3XXccYS19h80rHtgkw==,
      }
    engines: { node: ">=4" }
    peerDependencies:
      eslint: ^3 || ^4 || ^5 || ^6 || ^7 || ^8

  eslint-plugin-simple-import-sort@10.0.0:
    resolution:
      {
        integrity: sha512-AeTvO9UCMSNzIHRkg8S6c3RPy5YEwKWSQPx3DYghLedo2ZQxowPFLGDN1AZ2evfg6r6mjBSZSLxLFsWSu3acsw==,
      }
    peerDependencies:
      eslint: ">=5.0.0"

  eslint-scope@5.1.1:
    resolution:
      {
        integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==,
      }
    engines: { node: ">=8.0.0" }

  eslint-scope@7.2.2:
    resolution:
      {
        integrity: sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  eslint-utils@2.1.0:
    resolution:
      {
        integrity: sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg==,
      }
    engines: { node: ">=6" }

  eslint-utils@3.0.0:
    resolution:
      {
        integrity: sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA==,
      }
    engines: { node: ^10.0.0 || ^12.0.0 || >= 14.0.0 }
    peerDependencies:
      eslint: ">=5"

  eslint-visitor-keys@1.3.0:
    resolution:
      {
        integrity: sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==,
      }
    engines: { node: ">=4" }

  eslint-visitor-keys@2.1.0:
    resolution:
      {
        integrity: sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==,
      }
    engines: { node: ">=10" }

  eslint-visitor-keys@3.4.3:
    resolution:
      {
        integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  eslint@8.57.0:
    resolution:
      {
        integrity: sha512-dZ6+mexnaTIbSBZWgou51U6OmzIhYM2VcNdtiTtI7qPNZm35Akpr0f6vtw3w1Kmn5PYo+tZVfh13WrhpS6oLqQ==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
    deprecated: This version is no longer supported. Please see https://eslint.org/version-support for other options.
    hasBin: true

  espree@9.6.1:
    resolution:
      {
        integrity: sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }

  esquery@1.5.0:
    resolution:
      {
        integrity: sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==,
      }
    engines: { node: ">=0.10" }

  esrecurse@4.3.0:
    resolution:
      {
        integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==,
      }
    engines: { node: ">=4.0" }

  estraverse@4.3.0:
    resolution:
      {
        integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==,
      }
    engines: { node: ">=4.0" }

  estraverse@5.3.0:
    resolution:
      {
        integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==,
      }
    engines: { node: ">=4.0" }

  estree-walker@2.0.2:
    resolution:
      {
        integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==,
      }

  esutils@2.0.3:
    resolution:
      {
        integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==,
      }
    engines: { node: ">=0.10.0" }

  eventemitter3@5.0.1:
    resolution:
      {
        integrity: sha512-GWkBvjiSZK87ELrYOSESUYeVIc9mvLLf/nXalMOS5dYrgZq9o5OVkbZAVM06CVxYsCwH9BDZFPlQTlPA1j4ahA==,
      }

  execa@5.1.1:
    resolution:
      {
        integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==,
      }
    engines: { node: ">=10" }

  execa@7.2.0:
    resolution:
      {
        integrity: sha512-UduyVP7TLB5IcAQl+OzLyLcS/l32W/GLg+AhHJ+ow40FOk2U3SAllPwR44v4vmdFwIWqpdwxxpQbF1n5ta9seA==,
      }
    engines: { node: ^14.18.0 || ^16.14.0 || >=18.0.0 }

  fast-deep-equal@3.1.3:
    resolution:
      {
        integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==,
      }

  fast-diff@1.3.0:
    resolution:
      {
        integrity: sha512-VxPP4NqbUjj6MaAOafWeUn2cXWLcCtljklUtZf0Ind4XQ+QPtmA0b18zZy0jIQx+ExRVCR/ZQpBmik5lXshNsw==,
      }

  fast-glob@3.3.2:
    resolution:
      {
        integrity: sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==,
      }
    engines: { node: ">=8.6.0" }

  fast-json-stable-stringify@2.1.0:
    resolution:
      {
        integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==,
      }

  fast-levenshtein@2.0.6:
    resolution:
      {
        integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==,
      }

  fast-loops@1.1.3:
    resolution:
      {
        integrity: sha512-8EZzEP0eKkEEVX+drtd9mtuQ+/QrlfW/5MlwcwK5Nds6EkZ/tRzEexkzUY2mIssnAyVLT+TKHuRXmFNNXYUd6g==,
      }

  fast-shallow-equal@1.0.0:
    resolution:
      {
        integrity: sha512-HPtaa38cPgWvaCFmRNhlc6NG7pv6NUHqjPgVAkWGoB9mQMwYB27/K0CvOM5Czy+qpT3e8XJ6Q4aPAnzpNpzNaw==,
      }

  fastest-stable-stringify@2.0.2:
    resolution:
      {
        integrity: sha512-bijHueCGd0LqqNK9b5oCMHc0MluJAx0cwqASgbWMvkO01lCYgIhacVRLcaDz3QnyYIRNJRDwMb41VuT6pHJ91Q==,
      }

  fastq@1.17.1:
    resolution:
      {
        integrity: sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==,
      }

  file-entry-cache@6.0.1:
    resolution:
      {
        integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==,
      }
    engines: { node: ^10.12.0 || >=12.0.0 }

  fill-range@7.0.1:
    resolution:
      {
        integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==,
      }
    engines: { node: ">=8" }

  find-root@1.1.0:
    resolution:
      {
        integrity: sha512-NKfW6bec6GfKc0SGx1e07QZY9PE99u0Bft/0rzSD5k3sO/vwkVUpDUKVm5Gpp5Ue3YfShPFTX2070tDs5kB9Ng==,
      }

  find-up@4.1.0:
    resolution:
      {
        integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==,
      }
    engines: { node: ">=8" }

  find-up@5.0.0:
    resolution:
      {
        integrity: sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng==,
      }
    engines: { node: ">=10" }

  flat-cache@3.2.0:
    resolution:
      {
        integrity: sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==,
      }
    engines: { node: ^10.12.0 || >=12.0.0 }

  flatted@3.3.1:
    resolution:
      {
        integrity: sha512-X8cqMLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw==,
      }

  follow-redirects@1.15.6:
    resolution:
      {
        integrity: sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==,
      }
    engines: { node: ">=4.0" }
    peerDependencies:
      debug: "*"
    peerDependenciesMeta:
      debug:
        optional: true

  for-each@0.3.3:
    resolution:
      {
        integrity: sha512-jqYfLp7mo9vIyQf8ykW2v7A+2N4QjeCeI5+Dz9XraiO1ign81wjiH7Fb9vSOWvQfNtmSa4H2RoQTrrXivdUZmw==,
      }

  foreground-child@2.0.0:
    resolution:
      {
        integrity: sha512-dCIq9FpEcyQyXKCkyzmlPTFNgrCzPudOe+mhvJU5zAtlBnGVy2yKxtfsxK2tQBThwq225jcvBjpw1Gr40uzZCA==,
      }
    engines: { node: ">=8.0.0" }

  foreground-child@3.1.1:
    resolution:
      {
        integrity: sha512-TMKDUnIte6bfb5nWv7V/caI169OHgvwjb7V4WkeUvbQQdjr5rWKqHFiKWb/fcOwB+CzBT+qbWjvj+DVwRskpIg==,
      }
    engines: { node: ">=14" }

  form-data@4.0.0:
    resolution:
      {
        integrity: sha512-ETEklSGi5t0QMZuiXoA/Q6vcnxcLQP5vdugSpuAyi6SVGi2clPPp+xgEhuMaHC+zGgn31Kd235W35f7Hykkaww==,
      }
    engines: { node: ">= 6" }

  formik@2.4.6:
    resolution:
      {
        integrity: sha512-A+2EI7U7aG296q2TLGvNapDNTZp1khVt5Vk0Q/fyfSROss0V/V6+txt2aJnwEos44IxTCW/LYAi/zgWzlevj+g==,
      }
    peerDependencies:
      react: ">=16.8.0"

  fraction.js@4.3.7:
    resolution:
      {
        integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==,
      }

  fs-extra@11.2.0:
    resolution:
      {
        integrity: sha512-PmDi3uwK5nFuXh7XDTlVnS17xJS7vW36is2+w3xcv8SVxiB4NyATf4ctkVY5bkSjX0Y4nbvZCq1/EjtEyr9ktw==,
      }
    engines: { node: ">=14.14" }

  fs.realpath@1.0.0:
    resolution:
      {
        integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==,
      }

  fsevents@2.3.2:
    resolution:
      {
        integrity: sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==,
      }
    engines: { node: ^8.16.0 || ^10.6.0 || >=11.0.0 }
    os: [darwin]

  fsevents@2.3.3:
    resolution:
      {
        integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==,
      }
    engines: { node: ^8.16.0 || ^10.6.0 || >=11.0.0 }
    os: [darwin]

  function-bind@1.1.2:
    resolution:
      {
        integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==,
      }

  function.prototype.name@1.1.6:
    resolution:
      {
        integrity: sha512-Z5kx79swU5P27WEayXM1tBi5Ze/lbIyiNgU3qyXUOf9b2rgXYyF9Dy9Cx+IQv/Lc8WCG6L82zwUPpSS9hGehIg==,
      }
    engines: { node: ">= 0.4" }

  functions-have-names@1.2.3:
    resolution:
      {
        integrity: sha512-xckBUXyTIqT97tq2x2AMb+g163b5JFysYk0x4qxNFwbfQkmNZoiRHb6sPzI9/QV33WeuvVYBUIiD4NzNIyqaRQ==,
      }

  gensync@1.0.0-beta.2:
    resolution:
      {
        integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==,
      }
    engines: { node: ">=6.9.0" }

  get-caller-file@2.0.5:
    resolution:
      {
        integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==,
      }
    engines: { node: 6.* || 8.* || >= 10.* }

  get-func-name@2.0.2:
    resolution:
      {
        integrity: sha512-8vXOvuE167CtIc3OyItco7N/dpRtBbYOsPsXCz7X/PMnlGjYjSGuZJgM1Y7mmew7BKf9BqvLX2tnOVy1BBUsxQ==,
      }

  get-intrinsic@1.2.4:
    resolution:
      {
        integrity: sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==,
      }
    engines: { node: ">= 0.4" }

  get-stream@6.0.1:
    resolution:
      {
        integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==,
      }
    engines: { node: ">=10" }

  get-symbol-description@1.0.2:
    resolution:
      {
        integrity: sha512-g0QYk1dZBxGwk+Ngc+ltRH2IBp2f7zBkBMBJZCDerh6EhlhSR6+9irMCuT/09zD6qkarHUSn529sK/yL4S27mg==,
      }
    engines: { node: ">= 0.4" }

  git-raw-commits@2.0.11:
    resolution:
      {
        integrity: sha512-VnctFhw+xfj8Va1xtfEqCUD2XDrbAPSJx+hSrE5K7fGdjZruW7XV+QOrN7LF/RJyvspRiD2I0asWsxFp0ya26A==,
      }
    engines: { node: ">=10" }
    hasBin: true

  glob-parent@5.1.2:
    resolution:
      {
        integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==,
      }
    engines: { node: ">= 6" }

  glob-parent@6.0.2:
    resolution:
      {
        integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==,
      }
    engines: { node: ">=10.13.0" }

  glob@10.3.15:
    resolution:
      {
        integrity: sha512-0c6RlJt1TICLyvJYIApxb8GsXoai0KUP7AxKKAtsYXdgJR1mGEUa7DgwShbdk1nly0PYoZj01xd4hzbq3fsjpw==,
      }
    engines: { node: ">=16 || 14 >=14.18" }
    hasBin: true

  glob@7.2.3:
    resolution:
      {
        integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==,
      }
    deprecated: Glob versions prior to v9 are no longer supported

  global-dirs@0.1.1:
    resolution:
      {
        integrity: sha512-NknMLn7F2J7aflwFOlGdNIuCDpN3VGoSoB+aap3KABFWbHVn1TCgFC+np23J8W2BiZbjfEw3BFBycSMv1AFblg==,
      }
    engines: { node: ">=4" }

  globals@11.12.0:
    resolution:
      {
        integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==,
      }
    engines: { node: ">=4" }

  globals@13.24.0:
    resolution:
      {
        integrity: sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==,
      }
    engines: { node: ">=8" }

  globalthis@1.0.4:
    resolution:
      {
        integrity: sha512-DpLKbNU4WylpxJykQujfCcwYWiV/Jhm50Goo0wrVILAv5jOr9d+H+UR3PhSCD2rCCEIg0uc+G+muBTwD54JhDQ==,
      }
    engines: { node: ">= 0.4" }

  globby@11.1.0:
    resolution:
      {
        integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==,
      }
    engines: { node: ">=10" }

  goober@2.1.14:
    resolution:
      {
        integrity: sha512-4UpC0NdGyAFqLNPnhCT2iHpza2q+RAY3GV85a/mRPdzyPQMsj0KmMMuetdIkzWRbJ+Hgau1EZztq8ImmiMGhsg==,
      }
    peerDependencies:
      csstype: ^3.0.10

  gopd@1.0.1:
    resolution:
      {
        integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==,
      }

  graceful-fs@4.2.11:
    resolution:
      {
        integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==,
      }

  graphemer@1.4.0:
    resolution:
      {
        integrity: sha512-EtKwoO6kxCL9WO5xipiHTZlSzBm7WLT627TqC/uVRd0HKmq8NXyebnNYxDoBi7wt8eTWrUrKXCOVaFq9x1kgag==,
      }

  happy-dom@8.9.0:
    resolution:
      {
        integrity: sha512-JZwJuGdR7ko8L61136YzmrLv7LgTh5b8XaEM3P709mLjyQuXJ3zHTDXvUtBBahRjGlcYW0zGjIiEWizoTUGKfA==,
      }

  hard-rejection@2.1.0:
    resolution:
      {
        integrity: sha512-VIZB+ibDhx7ObhAe7OVtoEbuP4h/MuOTHJ+J8h/eBXotJYl0fBgR72xDFCKgIh22OJZIOVNxBMWuhAr10r8HdA==,
      }
    engines: { node: ">=6" }

  has-ansi@2.0.0:
    resolution:
      {
        integrity: sha512-C8vBJ8DwUCx19vhm7urhTuUsr4/IyP6l4VzNQDv+ryHQObW3TTTp9yB68WpYgRe2bbaGuZ/se74IqFeVnMnLZg==,
      }
    engines: { node: ">=0.10.0" }

  has-bigints@1.0.2:
    resolution:
      {
        integrity: sha512-tSvCKtBr9lkF0Ex0aQiP9N+OpV4zi2r/Nee5VkRDbaqv35RLYMzbwQfFSZZH0kR+Rd6302UJZ2p/bJCEoR3VoQ==,
      }

  has-flag@3.0.0:
    resolution:
      {
        integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==,
      }
    engines: { node: ">=4" }

  has-flag@4.0.0:
    resolution:
      {
        integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==,
      }
    engines: { node: ">=8" }

  has-property-descriptors@1.0.2:
    resolution:
      {
        integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==,
      }

  has-proto@1.0.3:
    resolution:
      {
        integrity: sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==,
      }
    engines: { node: ">= 0.4" }

  has-symbols@1.0.3:
    resolution:
      {
        integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==,
      }
    engines: { node: ">= 0.4" }

  has-tostringtag@1.0.2:
    resolution:
      {
        integrity: sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw==,
      }
    engines: { node: ">= 0.4" }

  hasown@2.0.2:
    resolution:
      {
        integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==,
      }
    engines: { node: ">= 0.4" }

  he@1.2.0:
    resolution:
      {
        integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==,
      }
    hasBin: true

  hoist-non-react-statics@3.3.2:
    resolution:
      {
        integrity: sha512-/gGivxi8JPKWNm/W0jSmzcMPpfpPLc3dY/6GxhX2hQ9iGj3aDfklV4ET7NjKpSinLpJ5vafa9iiGIEZg10SfBw==,
      }

  hosted-git-info@2.8.9:
    resolution:
      {
        integrity: sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==,
      }

  hosted-git-info@4.1.0:
    resolution:
      {
        integrity: sha512-kyCuEOWjJqZuDbRHzL8V93NzQhwIB71oFWSyzVo+KPZI+pnQPPxucdkrOZvkLRnrf5URsQM+IJ09Dw29cRALIA==,
      }
    engines: { node: ">=10" }

  html-encoding-sniffer@3.0.0:
    resolution:
      {
        integrity: sha512-oWv4T4yJ52iKrufjnyZPkrN0CH3QnrUqdB6In1g5Fe1mia8GmF36gnfNySxoZtxD5+NmYw1EElVXiBk93UeskA==,
      }
    engines: { node: ">=12" }

  html-escaper@2.0.2:
    resolution:
      {
        integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==,
      }

  html-parse-stringify@3.0.1:
    resolution:
      {
        integrity: sha512-KknJ50kTInJ7qIScF3jeaFRpMpE8/lfiTdzf/twXyPBLAGrLRTmkz3AdTnKeh40X8k9L2fdYwEp/42WGXIRGcg==,
      }

  http-proxy-agent@5.0.0:
    resolution:
      {
        integrity: sha512-n2hY8YdoRE1i7r6M0w9DIw5GgZN0G25P8zLCRQ8rjXtTU3vsNFBI/vWK/UIeE6g5MUUz6avwAPXmL6Fy9D/90w==,
      }
    engines: { node: ">= 6" }

  https-proxy-agent@5.0.1:
    resolution:
      {
        integrity: sha512-dFcAjpTQFgoLMzC2VwU+C/CbS7uRL0lWmxDITmqm7C+7F0Odmj6s9l6alZc6AELXhrnggM2CeWSXHGOdX2YtwA==,
      }
    engines: { node: ">= 6" }

  human-signals@2.1.0:
    resolution:
      {
        integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==,
      }
    engines: { node: ">=10.17.0" }

  human-signals@4.3.1:
    resolution:
      {
        integrity: sha512-nZXjEF2nbo7lIw3mgYjItAfgQXog3OjJogSbKa2CQIIvSGWcKgeJnQlNXip6NglNzYH45nSRiEVimMvYL8DDqQ==,
      }
    engines: { node: ">=14.18.0" }

  husky@8.0.3:
    resolution:
      {
        integrity: sha512-+dQSyqPh4x1hlO1swXBiNb2HzTDN1I2IGLQx1GrBuiqFJfoMrnZWwVmatvSiO+Iz8fBUnf+lekwNo4c2LlXItg==,
      }
    engines: { node: ">=14" }
    hasBin: true

  hyphenate-style-name@1.0.5:
    resolution:
      {
        integrity: sha512-fedL7PRwmeVkgyhu9hLeTBaI6wcGk7JGJswdaRsa5aUbkXI1kr1xZwTPBtaYPpwf56878iDek6VbVnuWMebJmw==,
      }

  i18next@23.11.4:
    resolution:
      {
        integrity: sha512-CCUjtd5TfaCl+mLUzAA0uPSN+AVn4fP/kWCYt/hocPUwusTpMVczdrRyOBUwk6N05iH40qiKx6q1DoNJtBIwdg==,
      }

  iconv-lite@0.6.3:
    resolution:
      {
        integrity: sha512-4fCk79wshMdzMp2rH06qWrJE4iolqLhCUH+OiuIgU++RB0+94NlDL81atO7GX55uUKueo0txHNtvEyI6D7WdMw==,
      }
    engines: { node: ">=0.10.0" }

  ignore@5.3.1:
    resolution:
      {
        integrity: sha512-5Fytz/IraMjqpwfd34ke28PTVMjZjJG2MPn5t7OE4eUCUNf8BAa7b5WUS9/Qvr6mwOQS7Mk6vdsMno5he+T8Xw==,
      }
    engines: { node: ">= 4" }

  immediate@3.0.6:
    resolution:
      {
        integrity: sha512-XXOFtyqDjNDAQxVfYxuF7g9Il/IbWmmlQg2MYKOH8ExIT1qg6xc4zyS3HaEEATgs1btfzxq15ciUiY7gjSXRGQ==,
      }

  immer@10.1.1:
    resolution:
      {
        integrity: sha512-s2MPrmjovJcoMaHtx6K11Ra7oD05NT97w1IC5zpMkT6Atjr7H8LjaDd81iIxUYpMKSRRNMJE703M1Fhr/TctHw==,
      }

  immutable@4.3.6:
    resolution:
      {
        integrity: sha512-Ju0+lEMyzMVZarkTn/gqRpdqd5dOPaz1mCZ0SH3JV6iFw81PldE/PEB1hWVEA288HPt4WXW8O7AWxB10M+03QQ==,
      }

  import-fresh@3.3.0:
    resolution:
      {
        integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==,
      }
    engines: { node: ">=6" }

  imurmurhash@0.1.4:
    resolution:
      {
        integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==,
      }
    engines: { node: ">=0.8.19" }

  indent-string@4.0.0:
    resolution:
      {
        integrity: sha512-EdDDZu4A2OyIK7Lr/2zG+w5jmbuk1DVBnEwREQvBzspBJkCEbRa8GxU1lghYcaGJCnRWibjDXlq779X1/y5xwg==,
      }
    engines: { node: ">=8" }

  inflight@1.0.6:
    resolution:
      {
        integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==,
      }

  inherits@2.0.4:
    resolution:
      {
        integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==,
      }

  ini@1.3.8:
    resolution:
      {
        integrity: sha512-JV/yugV2uzW5iMRSiZAyDtQd+nxtUnjeLt0acNdw98kKLrvuRVyB80tsREOE7yvGVgalhZ6RNXCmEHkUKBKxew==,
      }

  inline-style-prefixer@7.0.0:
    resolution:
      {
        integrity: sha512-I7GEdScunP1dQ6IM2mQWh6v0mOYdYmH3Bp31UecKdrcUgcURTcctSe1IECdUznSHKSmsHtjrT3CwCPI1pyxfUQ==,
      }

  internal-slot@1.0.7:
    resolution:
      {
        integrity: sha512-NGnrKwXzSms2qUUih/ILZ5JBqNTSa1+ZmP6flaIp6KmSElgE9qdndzS3cqjrDovwFdmwsGsLdeFgB6suw+1e9g==,
      }
    engines: { node: ">= 0.4" }

  is-arguments@1.1.1:
    resolution:
      {
        integrity: sha512-8Q7EARjzEnKpt/PCD7e1cgUS0a6X8u5tdSiMqXhojOdoV9TsMsiO+9VLC5vAmO8N7/GmXn7yjR8qnA6bVAEzfA==,
      }
    engines: { node: ">= 0.4" }

  is-array-buffer@3.0.4:
    resolution:
      {
        integrity: sha512-wcjaerHw0ydZwfhiKbXJWLDY8A7yV7KhjQOpb83hGgGfId/aQa4TOvwyzn2PuswW2gPCYEL/nEAiSVpdOj1lXw==,
      }
    engines: { node: ">= 0.4" }

  is-arrayish@0.2.1:
    resolution:
      {
        integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==,
      }

  is-async-function@2.0.0:
    resolution:
      {
        integrity: sha512-Y1JXKrfykRJGdlDwdKlLpLyMIiWqWvuSd17TvZk68PLAOGOoF4Xyav1z0Xhoi+gCYjZVeC5SI+hYFOfvXmGRCA==,
      }
    engines: { node: ">= 0.4" }

  is-bigint@1.0.4:
    resolution:
      {
        integrity: sha512-zB9CruMamjym81i2JZ3UMn54PKGsQzsJeo6xvN3HJJ4CAsQNB6iRutp2To77OfCNuoxspsIhzaPoO1zyCEhFOg==,
      }

  is-binary-path@2.1.0:
    resolution:
      {
        integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==,
      }
    engines: { node: ">=8" }

  is-boolean-object@1.1.2:
    resolution:
      {
        integrity: sha512-gDYaKHJmnj4aWxyj6YHyXVpdQawtVLHU5cb+eztPGczf6cjuTdwve5ZIEfgXqH4e57An1D1AKf8CZ3kYrQRqYA==,
      }
    engines: { node: ">= 0.4" }

  is-callable@1.2.7:
    resolution:
      {
        integrity: sha512-1BC0BVFhS/p0qtw6enp8e+8OD0UrK0oFLztSjNzhcKA3WDuJxxAPXzPuPtKkjEY9UUoEWlX/8fgKeu2S8i9JTA==,
      }
    engines: { node: ">= 0.4" }

  is-core-module@2.13.1:
    resolution:
      {
        integrity: sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==,
      }

  is-data-view@1.0.1:
    resolution:
      {
        integrity: sha512-AHkaJrsUVW6wq6JS8y3JnM/GJF/9cf+k20+iDzlSaJrinEo5+7vRiteOSwBhHRiAyQATN1AmY4hwzxJKPmYf+w==,
      }
    engines: { node: ">= 0.4" }

  is-date-object@1.0.5:
    resolution:
      {
        integrity: sha512-9YQaSxsAiSwcvS33MBk3wTCVnWK+HhF8VZR2jRxehM16QcVOdHqPn4VPHmRK4lSr38n9JriurInLcP90xsYNfQ==,
      }
    engines: { node: ">= 0.4" }

  is-extglob@2.1.1:
    resolution:
      {
        integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==,
      }
    engines: { node: ">=0.10.0" }

  is-finalizationregistry@1.0.2:
    resolution:
      {
        integrity: sha512-0by5vtUJs8iFQb5TYUHHPudOR+qXYIMKtiUzvLIZITZUjknFmziyBJuLhVRc+Ds0dREFlskDNJKYIdIzu/9pfw==,
      }

  is-fullwidth-code-point@3.0.0:
    resolution:
      {
        integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==,
      }
    engines: { node: ">=8" }

  is-fullwidth-code-point@4.0.0:
    resolution:
      {
        integrity: sha512-O4L094N2/dZ7xqVdrXhh9r1KODPJpFms8B5sGdJLPy664AgvXsreZUyCQQNItZRDlYug4xStLjNp/sz3HvBowQ==,
      }
    engines: { node: ">=12" }

  is-generator-function@1.0.10:
    resolution:
      {
        integrity: sha512-jsEjy9l3yiXEQ+PsXdmBwEPcOxaXWLspKdplFUVI9vq1iZgIekeC0L167qeu86czQaxed3q/Uzuw0swL0irL8A==,
      }
    engines: { node: ">= 0.4" }

  is-glob@4.0.3:
    resolution:
      {
        integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==,
      }
    engines: { node: ">=0.10.0" }

  is-map@2.0.3:
    resolution:
      {
        integrity: sha512-1Qed0/Hr2m+YqxnM09CjA2d/i6YZNfF6R2oRAOj36eUdS6qIV/huPJNSEpKbupewFs+ZsJlxsjjPbc0/afW6Lw==,
      }
    engines: { node: ">= 0.4" }

  is-negative-zero@2.0.3:
    resolution:
      {
        integrity: sha512-5KoIu2Ngpyek75jXodFvnafB6DJgr3u8uuK0LEZJjrU19DrMD3EVERaR8sjz8CCGgpZvxPl9SuE1GMVPFHx1mw==,
      }
    engines: { node: ">= 0.4" }

  is-number-object@1.0.7:
    resolution:
      {
        integrity: sha512-k1U0IRzLMo7ZlYIfzRu23Oh6MiIFasgpb9X76eqfFZAqwH44UI4KTBvBYIZ1dSL9ZzChTB9ShHfLkR4pdW5krQ==,
      }
    engines: { node: ">= 0.4" }

  is-number@7.0.0:
    resolution:
      {
        integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==,
      }
    engines: { node: ">=0.12.0" }

  is-obj@2.0.0:
    resolution:
      {
        integrity: sha512-drqDG3cbczxxEJRoOXcOjtdp1J/lyp1mNn0xaznRs8+muBhgQcrnbspox5X5fOw0HnMnbfDzvnEMEtqDEJEo8w==,
      }
    engines: { node: ">=8" }

  is-path-inside@3.0.3:
    resolution:
      {
        integrity: sha512-Fd4gABb+ycGAmKou8eMftCupSir5lRxqf4aD/vd0cD2qc4HL07OjCeuHMr8Ro4CoMaeCKDB0/ECBOVWjTwUvPQ==,
      }
    engines: { node: ">=8" }

  is-plain-obj@1.1.0:
    resolution:
      {
        integrity: sha512-yvkRyxmFKEOQ4pNXCmJG5AEQNlXJS5LaONXo5/cLdTZdWvsZ1ioJEonLGAosKlMWE8lwUy/bJzMjcw8az73+Fg==,
      }
    engines: { node: ">=0.10.0" }

  is-potential-custom-element-name@1.0.1:
    resolution:
      {
        integrity: sha512-bCYeRA2rVibKZd+s2625gGnGF/t7DSqDs4dP7CrLA1m7jKWz6pps0LpYLJN8Q64HtmPKJ1hrN3nzPNKFEKOUiQ==,
      }

  is-regex@1.1.4:
    resolution:
      {
        integrity: sha512-kvRdxDsxZjhzUX07ZnLydzS1TU/TJlTUHHY4YLL87e37oUA49DfkLqgy+VjFocowy29cKvcSiu+kIv728jTTVg==,
      }
    engines: { node: ">= 0.4" }

  is-set@2.0.3:
    resolution:
      {
        integrity: sha512-iPAjerrse27/ygGLxw+EBR9agv9Y6uLeYVJMu+QNCoouJ1/1ri0mGrcWpfCqFZuzzx3WjtwxG098X+n4OuRkPg==,
      }
    engines: { node: ">= 0.4" }

  is-shared-array-buffer@1.0.3:
    resolution:
      {
        integrity: sha512-nA2hv5XIhLR3uVzDDfCIknerhx8XUKnstuOERPNNIinXG7v9u+ohXF67vxm4TPTEPU6lm61ZkwP3c9PCB97rhg==,
      }
    engines: { node: ">= 0.4" }

  is-stream@2.0.1:
    resolution:
      {
        integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==,
      }
    engines: { node: ">=8" }

  is-stream@3.0.0:
    resolution:
      {
        integrity: sha512-LnQR4bZ9IADDRSkvpqMGvt/tEJWclzklNgSw48V5EAaAeDd6qGvN8ei6k5p0tvxSR171VmGyHuTiAOfxAbr8kA==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  is-string@1.0.7:
    resolution:
      {
        integrity: sha512-tE2UXzivje6ofPW7l23cjDOMa09gb7xlAqG6jG5ej6uPV32TlWP3NKPigtaGeHNu9fohccRYvIiZMfOOnOYUtg==,
      }
    engines: { node: ">= 0.4" }

  is-symbol@1.0.4:
    resolution:
      {
        integrity: sha512-C/CPBqKWnvdcxqIARxyOh4v1UUEOCHpgDa0WYgpKDFMszcrPcffg5uhwSgPCLD2WWxmq6isisz87tzT01tuGhg==,
      }
    engines: { node: ">= 0.4" }

  is-text-path@1.0.1:
    resolution:
      {
        integrity: sha512-xFuJpne9oFz5qDaodwmmG08e3CawH/2ZV8Qqza1Ko7Sk8POWbkRdwIoAWVhqvq0XeUzANEhKo2n0IXUGBm7A/w==,
      }
    engines: { node: ">=0.10.0" }

  is-typed-array@1.1.13:
    resolution:
      {
        integrity: sha512-uZ25/bUAlUY5fR4OKT4rZQEBrzQWYV9ZJYGGsUmEJ6thodVJ1HX64ePQ6Z0qPWP+m+Uq6e9UugrE38jeYsDSMw==,
      }
    engines: { node: ">= 0.4" }

  is-weakmap@2.0.2:
    resolution:
      {
        integrity: sha512-K5pXYOm9wqY1RgjpL3YTkF39tni1XajUIkawTLUo9EZEVUFga5gSQJF8nNS7ZwJQ02y+1YCNYcMh+HIf1ZqE+w==,
      }
    engines: { node: ">= 0.4" }

  is-weakref@1.0.2:
    resolution:
      {
        integrity: sha512-qctsuLZmIQ0+vSSMfoVvyFe2+GSEvnmZ2ezTup1SBse9+twCCeial6EEi3Nc2KFcf6+qz2FBPnjXsk8xhKSaPQ==,
      }

  is-weakset@2.0.3:
    resolution:
      {
        integrity: sha512-LvIm3/KWzS9oRFHugab7d+M/GcBXuXX5xZkzPmN+NxihdQlZUQ4dWuSV1xR/sq6upL1TJEDrfBgRepHFdBtSNQ==,
      }
    engines: { node: ">= 0.4" }

  isarray@2.0.5:
    resolution:
      {
        integrity: sha512-xHjhDr3cNBK0BzdUJSPXZntQUx/mwMS5Rw4A7lPJ90XGAO6ISP/ePDNuo0vhqOZU+UD5JoodwCAAoZQd3FeAKw==,
      }

  isexe@2.0.0:
    resolution:
      {
        integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==,
      }

  istanbul-lib-coverage@3.2.2:
    resolution:
      {
        integrity: sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==,
      }
    engines: { node: ">=8" }

  istanbul-lib-report@3.0.1:
    resolution:
      {
        integrity: sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==,
      }
    engines: { node: ">=10" }

  istanbul-reports@3.1.7:
    resolution:
      {
        integrity: sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==,
      }
    engines: { node: ">=8" }

  iterator.prototype@1.1.2:
    resolution:
      {
        integrity: sha512-DR33HMMr8EzwuRL8Y9D3u2BMj8+RqSE850jfGu59kS7tbmPLzGkZmVSfyCFSDxuZiEY6Rzt3T2NA/qU+NwVj1w==,
      }

  jackspeak@2.3.6:
    resolution:
      {
        integrity: sha512-N3yCS/NegsOBokc8GAdM8UcmfsKiSS8cipheD/nivzr700H+nsMOxJjQnvwOcRYVuFkdH0wGUvW2WbXGmrZGbQ==,
      }
    engines: { node: ">=14" }

  jest-diff@24.9.0:
    resolution:
      {
        integrity: sha512-qMfrTs8AdJE2iqrTp0hzh7kTd2PQWrsFyj9tORoKmu32xjPjeE4NyjVRDz8ybYwqS2ik8N4hsIpiVTyFeo2lBQ==,
      }
    engines: { node: ">= 6" }

  jest-get-type@24.9.0:
    resolution:
      {
        integrity: sha512-lUseMzAley4LhIcpSP9Jf+fTrQ4a1yHQwLNeeVa2cEmbCGeoZAtYPOIv8JaxLD/sUpKxetKGP+gsHl8f8TSj8Q==,
      }
    engines: { node: ">= 6" }

  jest-matcher-utils@24.9.0:
    resolution:
      {
        integrity: sha512-OZz2IXsu6eaiMAwe67c1T+5tUAtQyQx27/EMEkbFAGiw52tB9em+uGbzpcgYVpA8wl0hlxKPZxrly4CXU/GjHA==,
      }
    engines: { node: ">= 6" }

  jiti@1.21.0:
    resolution:
      {
        integrity: sha512-gFqAIbuKyyso/3G2qhiO2OM6shY6EPP/R0+mkDbyspxKazh8BXDC5FiFsUjlczgdNz/vfra0da2y+aHrusLG/Q==,
      }
    hasBin: true

  jotai@2.8.0:
    resolution:
      {
        integrity: sha512-yZNMC36FdLOksOr8qga0yLf14miCJlEThlp5DeFJNnqzm2+ZG7wLcJzoOyij5K6U6Xlc5ljQqPDlJRgqW0Y18g==,
      }
    engines: { node: ">=12.20.0" }
    peerDependencies:
      "@types/react": ">=17.0.0"
      react: ">=17.0.0"
    peerDependenciesMeta:
      "@types/react":
        optional: true
      react:
        optional: true

  js-cookie@2.2.1:
    resolution:
      {
        integrity: sha512-HvdH2LzI/EAZcUwA8+0nKNtWHqS+ZmijLA30RwZA0bo7ToCckjK5MkGhjED9KoRcXO6BaGI3I9UIzSA1FKFPOQ==,
      }

  js-tokens@4.0.0:
    resolution:
      {
        integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==,
      }

  js-yaml@4.1.0:
    resolution:
      {
        integrity: sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA==,
      }
    hasBin: true

  jsdom@22.1.0:
    resolution:
      {
        integrity: sha512-/9AVW7xNbsBv6GfWho4TTNjEo9fe6Zhf9O7s0Fhhr3u+awPwAJMKwAMXnkk5vBxflqLW9hTHX/0cs+P3gW+cQw==,
      }
    engines: { node: ">=16" }
    peerDependencies:
      canvas: ^2.5.0
    peerDependenciesMeta:
      canvas:
        optional: true

  jsesc@0.5.0:
    resolution:
      {
        integrity: sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA==,
      }
    hasBin: true

  jsesc@2.5.2:
    resolution:
      {
        integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==,
      }
    engines: { node: ">=4" }
    hasBin: true

  json-buffer@3.0.1:
    resolution:
      {
        integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==,
      }

  json-parse-better-errors@1.0.2:
    resolution:
      {
        integrity: sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==,
      }

  json-parse-even-better-errors@2.3.1:
    resolution:
      {
        integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==,
      }

  json-schema-traverse@0.4.1:
    resolution:
      {
        integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==,
      }

  json-schema-traverse@1.0.0:
    resolution:
      {
        integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==,
      }

  json-stable-stringify-without-jsonify@1.0.1:
    resolution:
      {
        integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==,
      }

  json-to-csv-export@3.0.1:
    resolution:
      {
        integrity: sha512-z2k54jiFph55HO5JkmUsiQEFvVahLBLkrJuXgN6b87vrBnZtd7/xacHjPFKkQmtf4DkMrLBJU5DyVwjy+/7s2Q==,
      }
    engines: { node: ">=12.17.0" }

  json5@1.0.2:
    resolution:
      {
        integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==,
      }
    hasBin: true

  json5@2.2.3:
    resolution:
      {
        integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==,
      }
    engines: { node: ">=6" }
    hasBin: true

  jsonfile@6.1.0:
    resolution:
      {
        integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==,
      }

  jsonparse@1.3.1:
    resolution:
      {
        integrity: sha512-POQXvpdL69+CluYsillJ7SUhKvytYjW9vG/GKpnf+xP8UWgYEM/RaMzHHofbALDiKbbP1W8UEYmgGl39WkPZsg==,
      }
    engines: { "0": node >= 0.2.0 }

  jsx-ast-utils@3.3.5:
    resolution:
      {
        integrity: sha512-ZZow9HBI5O6EPgSJLUb8n2NKgmVWTwCvHGwFuJlMjvLFqlGG6pjirPhtdsseaLZjSibD8eegzmYpUZwoIlj2cQ==,
      }
    engines: { node: ">=4.0" }

  jwt-decode@4.0.0:
    resolution:
      {
        integrity: sha512-+KJGIyHgkGuIq3IEBNftfhW/LfWhXUIY6OmyVWjliu5KH1y0fw7VQ8YndE2O4qZdMSd9SqbnC8GOcZEy0Om7sA==,
      }
    engines: { node: ">=18" }

  keyv@4.5.4:
    resolution:
      {
        integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==,
      }

  kind-of@6.0.3:
    resolution:
      {
        integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==,
      }
    engines: { node: ">=0.10.0" }

  levn@0.4.1:
    resolution:
      {
        integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==,
      }
    engines: { node: ">= 0.8.0" }

  lie@3.1.1:
    resolution:
      {
        integrity: sha512-RiNhHysUjhrDQntfYSfY4MU24coXXdEOgw9WGcKHNeEwffDYbF//u87M1EWaMGzuFoSbqW0C9C6lEEhDOAswfw==,
      }

  lilconfig@2.1.0:
    resolution:
      {
        integrity: sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==,
      }
    engines: { node: ">=10" }

  lilconfig@3.1.1:
    resolution:
      {
        integrity: sha512-O18pf7nyvHTckunPWCV1XUNXU1piu01y2b7ATJ0ppkUkk8ocqVWBrYjJBCwHDjD/ZWcfyrA0P4gKhzWGi5EINQ==,
      }
    engines: { node: ">=14" }

  lines-and-columns@1.2.4:
    resolution:
      {
        integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==,
      }

  lint-staged@13.3.0:
    resolution:
      {
        integrity: sha512-mPRtrYnipYYv1FEE134ufbWpeggNTo+O/UPzngoaKzbzHAthvR55am+8GfHTnqNRQVRRrYQLGW9ZyUoD7DsBHQ==,
      }
    engines: { node: ^16.14.0 || >=18.0.0 }
    hasBin: true

  listr2@6.6.1:
    resolution:
      {
        integrity: sha512-+rAXGHh0fkEWdXBmX+L6mmfmXmXvDGEKzkjxO+8mP3+nI/r/CWznVBvsibXdxda9Zz0OW2e2ikphN3OwCT/jSg==,
      }
    engines: { node: ">=16.0.0" }
    peerDependencies:
      enquirer: ">= 2.3.0 < 3"
    peerDependenciesMeta:
      enquirer:
        optional: true

  load-json-file@4.0.0:
    resolution:
      {
        integrity: sha512-Kx8hMakjX03tiGTLAIdJ+lL0htKnXjEZN6hk/tozf/WOuYGdZBJrZ+rCJRbVCugsjB3jMLn9746NsQIf5VjBMw==,
      }
    engines: { node: ">=4" }

  local-pkg@0.4.3:
    resolution:
      {
        integrity: sha512-SFppqq5p42fe2qcZQqqEOiVRXl+WCP1MdT6k7BDEW1j++sp5fIY+/fdRQitvKgB5BrBcmrs5m/L0v2FrU5MY1g==,
      }
    engines: { node: ">=14" }

  localforage@1.10.0:
    resolution:
      {
        integrity: sha512-14/H1aX7hzBBmmh7sGPd+AOMkkIrHM3Z1PAyGgZigA1H1p5O5ANnMyWzvpAETtG68/dC4pC0ncy3+PPGzXZHPg==,
      }

  locate-path@5.0.0:
    resolution:
      {
        integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==,
      }
    engines: { node: ">=8" }

  locate-path@6.0.0:
    resolution:
      {
        integrity: sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw==,
      }
    engines: { node: ">=10" }

  lodash-es@4.17.21:
    resolution:
      {
        integrity: sha512-mKnC+QJ9pWVzv+C4/U3rRsHapFfHvQFoFB92e52xeyGMcX6/OlIl78je1u8vePzYZSkkogMPJ2yjxxsb89cxyw==,
      }

  lodash.camelcase@4.3.0:
    resolution:
      {
        integrity: sha512-TwuEnCnxbc3rAvhf/LbG7tJUDzhqXyFnv3dtzLOPgCG/hODL7WFnsbwktkD7yUV0RrreP/l1PALq/YSg6VvjlA==,
      }

  lodash.debounce@4.0.8:
    resolution:
      {
        integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==,
      }

  lodash.get@4.4.2:
    resolution:
      {
        integrity: sha512-z+Uw/vLuy6gQe8cfaFWD7p0wVv8fJl3mbzXh33RS+0oW2wvUqiRXiQ69gLWSLpgB5/6sU+r6BlQR0MBILadqTQ==,
      }
    deprecated: This package is deprecated. Use the optional chaining (?.) operator instead.

  lodash.isfunction@3.0.9:
    resolution:
      {
        integrity: sha512-AirXNj15uRIMMPihnkInB4i3NHeb4iBtNg9WRWuK2o31S+ePwwNmDPaTL3o7dTJ+VXNZim7rFs4rxN4YU1oUJw==,
      }

  lodash.isplainobject@4.0.6:
    resolution:
      {
        integrity: sha512-oSXzaWypCMHkPC3NvBEaPHf0KsA5mvPrOPgQWDsbg8n7orZ290M0BmC/jgRZ4vcJ6DTAhjrsSYgdsW/F+MFOBA==,
      }

  lodash.kebabcase@4.1.1:
    resolution:
      {
        integrity: sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g==,
      }

  lodash.merge@4.6.2:
    resolution:
      {
        integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==,
      }

  lodash.mergewith@4.6.2:
    resolution:
      {
        integrity: sha512-GK3g5RPZWTRSeLSpgP8Xhra+pnjBC56q9FZYe1d5RN3TJ35dbkGy3YqBSMbyCrlbi+CM9Z3Jk5yTL7RCsqboyQ==,
      }

  lodash.pick@4.4.0:
    resolution:
      {
        integrity: sha512-hXt6Ul/5yWjfklSGvLQl8vM//l3FtyHZeuelpzK6mm99pNvN9yTDruNZPEJZD1oWrqo+izBmB7oUfWgcCX7s4Q==,
      }

  lodash.snakecase@4.1.1:
    resolution:
      {
        integrity: sha512-QZ1d4xoBHYUeuouhEq3lk3Uq7ldgyFXGBhg04+oRLnIz8o9T65Eh+8YdroUwn846zchkA9yDsDl5CVVaV2nqYw==,
      }

  lodash.startcase@4.4.0:
    resolution:
      {
        integrity: sha512-+WKqsK294HMSc2jEbNgpHpd0JfIBhp7rEV4aqXWqFr6AlXov+SlcgB1Fv01y2kGe3Gc8nMW7VA0SrGuSkRfIEg==,
      }

  lodash.uniq@4.5.0:
    resolution:
      {
        integrity: sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ==,
      }

  lodash.upperfirst@4.3.1:
    resolution:
      {
        integrity: sha512-sReKOYJIJf74dhJONhU4e0/shzi1trVbSWDOhKYE5XV2O+H7Sb2Dihwuc7xWxVl+DgFPyTqIN3zMfT9cq5iWDg==,
      }

  lodash@4.17.21:
    resolution:
      {
        integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==,
      }

  log-update@5.0.1:
    resolution:
      {
        integrity: sha512-5UtUDQ/6edw4ofyljDNcOVJQ4c7OjDro4h3y8e1GQL5iYElYclVHJ3zeWchylvMaKnDbDilC8irOVyexnA/Slw==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  loglevel-colored-level-prefix@1.0.0:
    resolution:
      {
        integrity: sha512-u45Wcxxc+SdAlh4yeF/uKlC1SPUPCy0gullSNKXod5I4bmifzk+Q4lSLExNEVn19tGaJipbZ4V4jbFn79/6mVA==,
      }

  loglevel@1.9.1:
    resolution:
      {
        integrity: sha512-hP3I3kCrDIMuRwAwHltphhDM1r8i55H33GgqjXbrisuJhF4kRhW1dNuxsRklp4bXl8DSdLaNLuiL4A/LWRfxvg==,
      }
    engines: { node: ">= 0.6.0" }

  loose-envify@1.4.0:
    resolution:
      {
        integrity: sha512-lyuxPGr/Wfhrlem2CL/UcnUc1zcqKAImBDzukY7Y5F/yQiNdko6+fRLevlw1HgMySw7f611UIY408EtxRSoK3Q==,
      }
    hasBin: true

  loupe@2.3.7:
    resolution:
      {
        integrity: sha512-zSMINGVYkdpYSOBmLi0D1Uo7JU9nVdQKrHxC8eYlV+9YKK9WePqAlL7lSlorG/U2Fw1w0hTBmaa/jrQ3UbPHtA==,
      }

  lru-cache@10.2.2:
    resolution:
      {
        integrity: sha512-9hp3Vp2/hFQUiIwKo8XCeFVnrg8Pk3TYNPIR7tJADKi5YfcF7vEaK7avFHTlSy3kOKYaJQaalfEo6YuXdceBOQ==,
      }
    engines: { node: 14 || >=16.14 }

  lru-cache@5.1.1:
    resolution:
      {
        integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==,
      }

  lru-cache@6.0.0:
    resolution:
      {
        integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==,
      }
    engines: { node: ">=10" }

  lz-string@1.5.0:
    resolution:
      {
        integrity: sha512-h5bgJWpxJNswbU7qCrV0tIKQCaS3blPDrqKWx+QxzuzL1zGUzij9XCWLrSLsJPu5t+eWA/ycetzYAO5IOMcWAQ==,
      }
    hasBin: true

  magic-string@0.27.0:
    resolution:
      {
        integrity: sha512-8UnnX2PeRAPZuN12svgR9j7M1uWMovg/CEnIwIG0LFkXSJJe4PdfUGiTGl8V9bsBHFUtfVINcSyYxd7q+kx9fA==,
      }
    engines: { node: ">=12" }

  make-dir@4.0.0:
    resolution:
      {
        integrity: sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==,
      }
    engines: { node: ">=10" }

  make-error@1.3.6:
    resolution:
      {
        integrity: sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==,
      }

  map-obj@1.0.1:
    resolution:
      {
        integrity: sha512-7N/q3lyZ+LVCp7PzuxrJr4KMbBE2hW7BT7YNia330OFxIf4d3r5zVpicP2650l7CPN6RM9zOJRl3NGpqSiw3Eg==,
      }
    engines: { node: ">=0.10.0" }

  map-obj@4.3.0:
    resolution:
      {
        integrity: sha512-hdN1wVrZbb29eBGiGjJbeP8JbKjq1urkHJ/LIP/NY48MZ1QVXUsQBV1G1zvYFHn1XE06cwjBsOI2K3Ulnj1YXQ==,
      }
    engines: { node: ">=8" }

  match-sorter@6.3.4:
    resolution:
      {
        integrity: sha512-jfZW7cWS5y/1xswZo8VBOdudUiSd9nifYRWphc9M5D/ee4w4AoXLgBEdRbgVaxbMuagBPeUC5y2Hi8DO6o9aDg==,
      }

  mdn-data@2.0.14:
    resolution:
      {
        integrity: sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==,
      }

  mdn-data@2.0.28:
    resolution:
      {
        integrity: sha512-aylIc7Z9y4yzHYAJNuESG3hfhC+0Ibp/MAMiaOZgNv4pmEdFyfZhhhny4MNiAfWdBQ1RQ2mfDWmM1x8SvGyp8g==,
      }

  mdn-data@2.0.30:
    resolution:
      {
        integrity: sha512-GaqWWShW4kv/G9IEucWScBx9G1/vsFZZJUO+tD26M8J8z3Kw5RDQjaoZe03YAClgeS/SWPOcb4nkFBTEi5DUEA==,
      }

  memorystream@0.3.1:
    resolution:
      {
        integrity: sha512-S3UwM3yj5mtUSEfP41UZmt/0SCoVYUcU1rkXv+BQ5Ig8ndL4sPoJNBUJERafdPb5jjHJGuMgytgKvKIf58XNBw==,
      }
    engines: { node: ">= 0.10.0" }

  meow@8.1.2:
    resolution:
      {
        integrity: sha512-r85E3NdZ+mpYk1C6RjPFEMSE+s1iZMuHtsHAqY0DT3jZczl0diWUZ8g6oU7h0M9cD2EL+PzaYghhCLzR0ZNn5Q==,
      }
    engines: { node: ">=10" }

  merge-stream@2.0.0:
    resolution:
      {
        integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==,
      }

  merge2@1.4.1:
    resolution:
      {
        integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==,
      }
    engines: { node: ">= 8" }

  micromatch@4.0.5:
    resolution:
      {
        integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==,
      }
    engines: { node: ">=8.6" }

  mime-db@1.52.0:
    resolution:
      {
        integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==,
      }
    engines: { node: ">= 0.6" }

  mime-types@2.1.35:
    resolution:
      {
        integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==,
      }
    engines: { node: ">= 0.6" }

  mimic-fn@2.1.0:
    resolution:
      {
        integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==,
      }
    engines: { node: ">=6" }

  mimic-fn@4.0.0:
    resolution:
      {
        integrity: sha512-vqiC06CuhBTUdZH+RYl8sFrL096vA45Ok5ISO6sE/Mr1jRbGH4Csnhi8f3wKVl7x8mO4Au7Ir9D3Oyv1VYMFJw==,
      }
    engines: { node: ">=12" }

  min-indent@1.0.1:
    resolution:
      {
        integrity: sha512-I9jwMn07Sy/IwOj3zVkVik2JTvgpaykDZEigL6Rx6N9LbMywwUSMtxET+7lVoDLLd3O3IXwJwvuuns8UB/HeAg==,
      }
    engines: { node: ">=4" }

  minimatch@3.1.2:
    resolution:
      {
        integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==,
      }

  minimatch@9.0.4:
    resolution:
      {
        integrity: sha512-KqWh+VchfxcMNRAJjj2tnsSJdNbHsVgnkBhTNrW7AjVo6OvLtxw8zfT9oLw1JSohlFzJ8jCoTgaoXvJ+kHt6fw==,
      }
    engines: { node: ">=16 || 14 >=14.17" }

  minimist-options@4.1.0:
    resolution:
      {
        integrity: sha512-Q4r8ghd80yhO/0j1O3B2BjweX3fiHg9cdOwjJd2J76Q135c+NDxGCqdYKQ1SKBuFfgWbAUzBfvYjPUEeNgqN1A==,
      }
    engines: { node: ">= 6" }

  minimist@1.2.8:
    resolution:
      {
        integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==,
      }

  minipass@7.1.1:
    resolution:
      {
        integrity: sha512-UZ7eQ+h8ywIRAW1hIEl2AqdwzJucU/Kp59+8kkZeSvafXhZjul247BvIJjEVFVeON6d7lM46XX1HXCduKAS8VA==,
      }
    engines: { node: ">=16 || 14 >=14.17" }

  mlly@1.7.0:
    resolution:
      {
        integrity: sha512-U9SDaXGEREBYQgfejV97coK0UL1r+qnF2SyO9A3qcI8MzKnsIFKHNVEkrDyNncQTKQQumsasmeq84eNMdBfsNQ==,
      }

  moment@2.30.1:
    resolution:
      {
        integrity: sha512-uEmtNhbDOrWPFS+hdjFCBfy9f2YoyzRpwcl+DqpC6taX21FzsTLQVbMV/W7PzNSX6x/bhC1zA3c2UQ5NzH6how==,
      }

  mrmime@2.0.0:
    resolution:
      {
        integrity: sha512-eu38+hdgojoyq63s+yTpN4XMBdt5l8HhMhc4VKLO9KM5caLIBvUm4thi7fFaxyTmCKeNnXZ5pAlBwCUnhA09uw==,
      }
    engines: { node: ">=10" }

  ms@2.1.2:
    resolution:
      {
        integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==,
      }

  ms@2.1.3:
    resolution:
      {
        integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==,
      }

  mz@2.7.0:
    resolution:
      {
        integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==,
      }

  nano-css@5.6.1:
    resolution:
      {
        integrity: sha512-T2Mhc//CepkTa3X4pUhKgbEheJHYAxD0VptuqFhDbGMUWVV2m+lkNiW/Ieuj35wrfC8Zm0l7HvssQh7zcEttSw==,
      }
    peerDependencies:
      react: "*"
      react-dom: "*"

  nanoid@3.3.7:
    resolution:
      {
        integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==,
      }
    engines: { node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1 }
    hasBin: true

  natural-compare-lite@1.4.0:
    resolution:
      {
        integrity: sha512-Tj+HTDSJJKaZnfiuw+iaF9skdPpTo2GtEly5JHnWV/hfv2Qj/9RKsGISQtLh2ox3l5EAGw487hnBee0sIJ6v2g==,
      }

  natural-compare@1.4.0:
    resolution:
      {
        integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==,
      }

  nice-try@1.0.5:
    resolution:
      {
        integrity: sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==,
      }

  node-fetch@2.7.0:
    resolution:
      {
        integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==,
      }
    engines: { node: 4.x || >=6.0.0 }
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true

  node-releases@2.0.14:
    resolution:
      {
        integrity: sha512-y10wOWt8yZpqXmOgRo77WaHEmhYQYGNA6y421PKsKYWEK8aW+cqAphborZDhqfyKrbZEN92CN1X2KbafY2s7Yw==,
      }

  normalize-package-data@2.5.0:
    resolution:
      {
        integrity: sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==,
      }

  normalize-package-data@3.0.3:
    resolution:
      {
        integrity: sha512-p2W1sgqij3zMMyRC067Dg16bfzVH+w7hyegmpIvZ4JNjqtGOVAIvLmjBx3yP7YTe9vKJgkoNOPjwQGogDoMXFA==,
      }
    engines: { node: ">=10" }

  normalize-path@3.0.0:
    resolution:
      {
        integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==,
      }
    engines: { node: ">=0.10.0" }

  normalize-range@0.1.2:
    resolution:
      {
        integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==,
      }
    engines: { node: ">=0.10.0" }

  npm-run-all@4.1.5:
    resolution:
      {
        integrity: sha512-Oo82gJDAVcaMdi3nuoKFavkIHBRVqQ1qvMb+9LHk/cF4P6B2m8aP04hGf7oL6wZ9BuGwX1onlLhpuoofSyoQDQ==,
      }
    engines: { node: ">= 4" }
    hasBin: true

  npm-run-path@4.0.1:
    resolution:
      {
        integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==,
      }
    engines: { node: ">=8" }

  npm-run-path@5.3.0:
    resolution:
      {
        integrity: sha512-ppwTtiJZq0O/ai0z7yfudtBpWIoxM8yE6nHi1X47eFR2EWORqfbu6CnPlNsjeN683eT0qG6H/Pyf9fCcvjnnnQ==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  nth-check@2.1.1:
    resolution:
      {
        integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==,
      }

  nwsapi@2.2.10:
    resolution:
      {
        integrity: sha512-QK0sRs7MKv0tKe1+5uZIQk/C8XGza4DAnztJG8iD+TpJIORARrCxczA738awHrZoHeTjSSoHqao2teO0dC/gFQ==,
      }

  object-assign@4.1.1:
    resolution:
      {
        integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==,
      }
    engines: { node: ">=0.10.0" }

  object-hash@3.0.0:
    resolution:
      {
        integrity: sha512-RSn9F68PjH9HqtltsSnqYC1XXoWe9Bju5+213R98cNGttag9q9yAOTzdbsqvIa7aNm5WffBZFpWYr2aWrklWAw==,
      }
    engines: { node: ">= 6" }

  object-inspect@1.13.1:
    resolution:
      {
        integrity: sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ==,
      }

  object-is@1.1.6:
    resolution:
      {
        integrity: sha512-F8cZ+KfGlSGi09lJT7/Nd6KJZ9ygtvYC0/UYYLI9nmQKLMnydpB9yvbv9K1uSkEu7FU9vYPmVwLg328tX+ot3Q==,
      }
    engines: { node: ">= 0.4" }

  object-keys@1.1.1:
    resolution:
      {
        integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==,
      }
    engines: { node: ">= 0.4" }

  object-path@0.6.0:
    resolution:
      {
        integrity: sha512-fxrwsCFi3/p+LeLOAwo/wyRMODZxdGBtUlWRzsEpsUVrisZbEfZ21arxLGfaWfcnqb8oHPNihIb4XPE8CQPN5A==,
      }
    engines: { node: ">=0.8.0" }

  object.assign@4.1.5:
    resolution:
      {
        integrity: sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==,
      }
    engines: { node: ">= 0.4" }

  object.entries@1.1.8:
    resolution:
      {
        integrity: sha512-cmopxi8VwRIAw/fkijJohSfpef5PdN0pMQJN6VC/ZKvn0LIknWD8KtgY6KlQdEc4tIjcQ3HxSMmnvtzIscdaYQ==,
      }
    engines: { node: ">= 0.4" }

  object.fromentries@2.0.8:
    resolution:
      {
        integrity: sha512-k6E21FzySsSK5a21KRADBd/NGneRegFO5pLHfdQLpRDETUNJueLXs3WCzyQ3tFRDYgbq3KHGXfTbi2bs8WQ6rQ==,
      }
    engines: { node: ">= 0.4" }

  object.groupby@1.0.3:
    resolution:
      {
        integrity: sha512-+Lhy3TQTuzXI5hevh8sBGqbmurHbbIjAi0Z4S63nthVLmLxfbj4T54a4CfZrXIrt9iP4mVAPYMo/v99taj3wjQ==,
      }
    engines: { node: ">= 0.4" }

  object.hasown@1.1.4:
    resolution:
      {
        integrity: sha512-FZ9LZt9/RHzGySlBARE3VF+gE26TxR38SdmqOqliuTnl9wrKulaQs+4dee1V+Io8VfxqzAfHu6YuRgUy8OHoTg==,
      }
    engines: { node: ">= 0.4" }

  object.values@1.2.0:
    resolution:
      {
        integrity: sha512-yBYjY9QX2hnRmZHAjG/f13MzmBzxzYgQhFrke06TTyKY5zSTEqkOeukBzIdVA3j3ulu8Qa3MbVFShV7T2RmGtQ==,
      }
    engines: { node: ">= 0.4" }

  once@1.4.0:
    resolution:
      {
        integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==,
      }

  onetime@5.1.2:
    resolution:
      {
        integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==,
      }
    engines: { node: ">=6" }

  onetime@6.0.0:
    resolution:
      {
        integrity: sha512-1FlR+gjXK7X+AsAHso35MnyN5KqGwJRi/31ft6x0M194ht7S+rWAvd7PHss9xSKMzE0asv1pyIHaJYq+BbacAQ==,
      }
    engines: { node: ">=12" }

  optionator@0.9.4:
    resolution:
      {
        integrity: sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g==,
      }
    engines: { node: ">= 0.8.0" }

  p-limit@2.3.0:
    resolution:
      {
        integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==,
      }
    engines: { node: ">=6" }

  p-limit@3.1.0:
    resolution:
      {
        integrity: sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==,
      }
    engines: { node: ">=10" }

  p-limit@4.0.0:
    resolution:
      {
        integrity: sha512-5b0R4txpzjPWVw/cXXUResoD4hb6U/x9BH08L7nw+GN1sezDzPdxeRvpc9c433fZhBan/wusjbCsqwqm4EIBIQ==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  p-locate@4.1.0:
    resolution:
      {
        integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==,
      }
    engines: { node: ">=8" }

  p-locate@5.0.0:
    resolution:
      {
        integrity: sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw==,
      }
    engines: { node: ">=10" }

  p-min-delay@4.0.2:
    resolution:
      {
        integrity: sha512-7hJcTq/MGF5pNHbQ2akrpPy1N43YYlB4RPECDSbPRn4xP/dsgP0I6ls7NvSUQ5k88o+CyATMOrQiZ/PK4aQR9w==,
      }
    engines: { node: ">=12" }

  p-try@2.2.0:
    resolution:
      {
        integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==,
      }
    engines: { node: ">=6" }

  papaparse@5.5.3:
    resolution:
      {
        integrity: sha512-5QvjGxYVjxO59MGU2lHVYpRWBBtKHnlIAcSe1uNFCkkptUh63NFRj0FJQm7nR67puEruUci/ZkjmEFrjCAyP4A==,
      }

  parent-module@1.0.1:
    resolution:
      {
        integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==,
      }
    engines: { node: ">=6" }

  parse-json@4.0.0:
    resolution:
      {
        integrity: sha512-aOIos8bujGN93/8Ox/jPLh7RwVnPEysynVFE+fQZyg6jKELEHwzgKdLRFHUgXJL6kylijVSBC4BvN9OmsB48Rw==,
      }
    engines: { node: ">=4" }

  parse-json@5.2.0:
    resolution:
      {
        integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==,
      }
    engines: { node: ">=8" }

  parse5@7.1.2:
    resolution:
      {
        integrity: sha512-Czj1WaSVpaoj0wbhMzLmWD69anp2WH7FXMB9n1Sy8/ZFF9jolSQVMu1Ij5WIyGmcBmhk7EOndpO4mIpihVqAXw==,
      }

  path-exists@4.0.0:
    resolution:
      {
        integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==,
      }
    engines: { node: ">=8" }

  path-is-absolute@1.0.1:
    resolution:
      {
        integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==,
      }
    engines: { node: ">=0.10.0" }

  path-key@2.0.1:
    resolution:
      {
        integrity: sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==,
      }
    engines: { node: ">=4" }

  path-key@3.1.1:
    resolution:
      {
        integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==,
      }
    engines: { node: ">=8" }

  path-key@4.0.0:
    resolution:
      {
        integrity: sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==,
      }
    engines: { node: ">=12" }

  path-parse@1.0.7:
    resolution:
      {
        integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==,
      }

  path-scurry@1.11.1:
    resolution:
      {
        integrity: sha512-Xa4Nw17FS9ApQFJ9umLiJS4orGjm7ZzwUrwamcGQuHSzDyth9boKDaycYdDcZDuqYATXw4HFXgaqWTctW/v1HA==,
      }
    engines: { node: ">=16 || 14 >=14.18" }

  path-type@3.0.0:
    resolution:
      {
        integrity: sha512-T2ZUsdZFHgA3u4e5PfPbjd7HDDpxPnQb5jN0SrDsjNSuVXHJqtwTnWqG0B1jZrgmJ/7lj1EmVIByWt1gxGkWvg==,
      }
    engines: { node: ">=4" }

  path-type@4.0.0:
    resolution:
      {
        integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==,
      }
    engines: { node: ">=8" }

  pathe@1.1.2:
    resolution:
      {
        integrity: sha512-whLdWMYL2TwI08hn8/ZqAbrVemu0LNaNNJZX73O6qaIdCTfXutsLhMkjdENX0qhsQ9uIimo4/aQOmXkoon2nDQ==,
      }

  pathval@1.1.1:
    resolution:
      {
        integrity: sha512-Dp6zGqpTdETdR63lehJYPeIOqpiNBNtc7BpWSLrOje7UaIsE5aY92r/AunQA7rsXvet3lrJ3JnZX29UPTKXyKQ==,
      }

  picocolors@1.0.1:
    resolution:
      {
        integrity: sha512-anP1Z8qwhkbmu7MFP5iTt+wQKXgwzf7zTyGlcdzabySa9vd0Xt392U0rVmz9poOaBj0uHJKyyo9/upk0HrEQew==,
      }

  picomatch@2.3.1:
    resolution:
      {
        integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==,
      }
    engines: { node: ">=8.6" }

  pidtree@0.3.1:
    resolution:
      {
        integrity: sha512-qQbW94hLHEqCg7nhby4yRC7G2+jYHY4Rguc2bjw7Uug4GIJuu1tvf2uHaZv5Q8zdt+WKJ6qK1FOI6amaWUo5FA==,
      }
    engines: { node: ">=0.10" }
    hasBin: true

  pidtree@0.6.0:
    resolution:
      {
        integrity: sha512-eG2dWTVw5bzqGRztnHExczNxt5VGsE6OwTeCG3fdUf9KBsZzO3R5OIIIzWR+iZA0NtZ+RDVdaoE2dK1cn6jH4g==,
      }
    engines: { node: ">=0.10" }
    hasBin: true

  pify@2.3.0:
    resolution:
      {
        integrity: sha512-udgsAY+fTnvv7kI7aaxbqwWNb0AHiB0qBO89PZKPkoTmGOgdbrHDKD+0B2X4uTfJ/FT1R09r9gTsjUjNJotuog==,
      }
    engines: { node: ">=0.10.0" }

  pify@3.0.0:
    resolution:
      {
        integrity: sha512-C3FsVNH1udSEX48gGX1xfvwTWfsYWj5U+8/uK15BGzIGrKoUpghX8hWZwa/OFnakBiiVNmBvemTJR5mcy7iPcg==,
      }
    engines: { node: ">=4" }

  pirates@4.0.6:
    resolution:
      {
        integrity: sha512-saLsH7WeYYPiD25LDuLRRY/i+6HaPYr6G1OUlN39otzkSTxKnubR9RTxS3/Kk50s1g2JTgFwWQDQyplC5/SHZg==,
      }
    engines: { node: ">= 6" }

  pkg-types@1.1.1:
    resolution:
      {
        integrity: sha512-ko14TjmDuQJ14zsotODv7dBlwxKhUKQEhuhmbqo1uCi9BB0Z2alo/wAXg6q1dTR5TyuqYyWhjtfe/Tsh+X28jQ==,
      }

  playwright-core@1.44.0:
    resolution:
      {
        integrity: sha512-ZTbkNpFfYcGWohvTTl+xewITm7EOuqIqex0c7dNZ+aXsbrLj0qI8XlGKfPpipjm0Wny/4Lt4CJsWJk1stVS5qQ==,
      }
    engines: { node: ">=16" }
    hasBin: true

  playwright@1.44.0:
    resolution:
      {
        integrity: sha512-F9b3GUCLQ3Nffrfb6dunPOkE5Mh68tR7zN32L4jCk4FjQamgesGay7/dAAe1WaMEGV04DkdJfcJzjoCKygUaRQ==,
      }
    engines: { node: ">=16" }
    hasBin: true

  possible-typed-array-names@1.0.0:
    resolution:
      {
        integrity: sha512-d7Uw+eZoloe0EHDIYoe+bQ5WXnGMOpmiZFTuMWCwpjzzkL2nTjcKiAk4hh8TjnGye2TwWOk3UXucZ+3rbmBa8Q==,
      }
    engines: { node: ">= 0.4" }

  postcss-import@15.1.0:
    resolution:
      {
        integrity: sha512-hpr+J05B2FVYUAXHeK1YyI267J/dDDhMU6B6civm8hSY1jYJnBXxzKDKDswzJmtLHryrjhnDjqqp/49t8FALew==,
      }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      postcss: ^8.0.0

  postcss-js@4.0.1:
    resolution:
      {
        integrity: sha512-dDLF8pEO191hJMtlHFPRa8xsizHaM82MLfNkUHdUtVEV3tgTp5oj+8qbEqYM57SLfc74KSbw//4SeJma2LRVIw==,
      }
    engines: { node: ^12 || ^14 || >= 16 }
    peerDependencies:
      postcss: ^8.4.21

  postcss-load-config@4.0.2:
    resolution:
      {
        integrity: sha512-bSVhyJGL00wMVoPUzAVAnbEoWyqRxkjv64tUl427SKnPrENtq6hJwUojroMz2VB+Q1edmi4IfrAPpami5VVgMQ==,
      }
    engines: { node: ">= 14" }
    peerDependencies:
      postcss: ">=8.0.9"
      ts-node: ">=9.0.0"
    peerDependenciesMeta:
      postcss:
        optional: true
      ts-node:
        optional: true

  postcss-nested@6.0.1:
    resolution:
      {
        integrity: sha512-mEp4xPMi5bSWiMbsgoPfcP74lsWLHkQbZc3sY+jWYd65CUwXrUaTp0fmNpa01ZcETKlIgUdFN/MpS2xZtqL9dQ==,
      }
    engines: { node: ">=12.0" }
    peerDependencies:
      postcss: ^8.2.14

  postcss-selector-parser@6.0.16:
    resolution:
      {
        integrity: sha512-A0RVJrX+IUkVZbW3ClroRWurercFhieevHB38sr2+l9eUClMqome3LmEmnhlNy+5Mr2EYN6B2Kaw9wYdd+VHiw==,
      }
    engines: { node: ">=4" }

  postcss-value-parser@4.2.0:
    resolution:
      {
        integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==,
      }

  postcss@8.4.38:
    resolution:
      {
        integrity: sha512-Wglpdk03BSfXkHoQa3b/oulrotAkwrlLDRSOb9D0bN86FdRyE9lppSp33aHNPgBa0JKCoB+drFLZkQoRRYae5A==,
      }
    engines: { node: ^10 || ^12 || >=14 }

  prelude-ls@1.2.1:
    resolution:
      {
        integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==,
      }
    engines: { node: ">= 0.8.0" }

  prettier-eslint@15.0.1:
    resolution:
      {
        integrity: sha512-mGOWVHixSvpZWARqSDXbdtTL54mMBxc5oQYQ6RAqy8jecuNJBgN3t9E5a81G66F8x8fsKNiR1HWaBV66MJDOpg==,
      }
    engines: { node: ">=10.0.0" }

  prettier-linter-helpers@1.0.0:
    resolution:
      {
        integrity: sha512-GbK2cP9nraSSUF9N2XwUwqfzlAFlMNYYl+ShE/V+H8a9uNl/oUqB1w2EL54Jh0OlyRSd8RfWYJ3coVS4TROP2w==,
      }
    engines: { node: ">=6.0.0" }

  prettier@2.8.7:
    resolution:
      {
        integrity: sha512-yPngTo3aXUUmyuTjeTUT75txrf+aMh9FiD7q9ZE/i6r0bPb22g4FsE6Y338PQX1bmfy08i9QQCB7/rcUAVntfw==,
      }
    engines: { node: ">=10.13.0" }
    hasBin: true

  pretty-format@23.6.0:
    resolution:
      {
        integrity: sha512-zf9NV1NSlDLDjycnwm6hpFATCGl/K1lt0R/GdkAK2O5LN/rwJoB+Mh93gGJjut4YbmecbfgLWVGSTCr0Ewvvbw==,
      }

  pretty-format@24.9.0:
    resolution:
      {
        integrity: sha512-00ZMZUiHaJrNfk33guavqgvfJS30sLYf0f8+Srklv0AMPodGGHcoHgksZ3OThYnIvOd+8yMCn0YiEOogjlgsnA==,
      }
    engines: { node: ">= 6" }

  pretty-format@27.5.1:
    resolution:
      {
        integrity: sha512-Qb1gy5OrP5+zDf2Bvnzdl3jsTf1qXVMazbvCoKhtKqVs4/YK4ozX4gKQJJVyNe+cajNPn0KoC0MC3FUmaHWEmQ==,
      }
    engines: { node: ^10.13.0 || ^12.13.0 || ^14.15.0 || >=15.0.0 }

  prop-types@15.8.1:
    resolution:
      {
        integrity: sha512-oj87CgZICdulUohogVAR7AjlC0327U4el4L6eAvOqCeudMDVU0NThNaV+b9Df4dXgSP1gXMTnPdhfe/2qDH5cg==,
      }

  property-expr@2.0.6:
    resolution:
      {
        integrity: sha512-SVtmxhRE/CGkn3eZY1T6pC8Nln6Fr/lu1mKSgRud0eC73whjGfoAogbn78LkD8aFL0zz3bAFerKSnOl7NlErBA==,
      }

  proxy-from-env@1.1.0:
    resolution:
      {
        integrity: sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg==,
      }

  psl@1.9.0:
    resolution:
      {
        integrity: sha512-E/ZsdU4HLs/68gYzgGTkMicWTLPdAftJLfJFlLUAAKZGkStNU72sZjT66SnMDVOfOWY/YAoiD7Jxa9iHvngcag==,
      }

  punycode@2.3.1:
    resolution:
      {
        integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==,
      }
    engines: { node: ">=6" }

  querystringify@2.2.0:
    resolution:
      {
        integrity: sha512-FIqgj2EUvTa7R50u0rGsyTftzjYmv/a3hO345bZNrqabNqjtgiDMgmo4mkUjd+nzU5oF3dClKqFIPUKybUyqoQ==,
      }

  queue-microtask@1.2.3:
    resolution:
      {
        integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==,
      }

  quick-lru@4.0.1:
    resolution:
      {
        integrity: sha512-ARhCpm70fzdcvNQfPoy49IaanKkTlRWF2JMzqhcJbhSFRZv7nPTvZJdcY7301IPmvW+/p0RgIWnQDLJxifsQ7g==,
      }
    engines: { node: ">=8" }

  react-dom@18.3.1:
    resolution:
      {
        integrity: sha512-5m4nQKp+rZRb09LNH59GM4BxTh9251/ylbKIbpe7TpGxfJ+9kv6BLkLBXIjjspbgbnIBNqlI23tRnTWT0snUIw==,
      }
    peerDependencies:
      react: ^18.3.1

  react-fast-compare@2.0.4:
    resolution:
      {
        integrity: sha512-suNP+J1VU1MWFKcyt7RtjiSWUjvidmQSlqu+eHslq+342xCbGTYmC0mEhPCOHxlW0CywylOC1u2DFAT+bv4dBw==,
      }

  react-hot-toast@2.4.1:
    resolution:
      {
        integrity: sha512-j8z+cQbWIM5LY37pR6uZR6D4LfseplqnuAO4co4u8917hBUvXlEqyP1ZzqVLcqoyUesZZv/ImreoCeHVDpE5pQ==,
      }
    engines: { node: ">=10" }
    peerDependencies:
      react: ">=16"
      react-dom: ">=16"

  react-i18next@12.3.1:
    resolution:
      {
        integrity: sha512-5v8E2XjZDFzK7K87eSwC7AJcAkcLt5xYZ4+yTPDAW1i7C93oOY1dnr4BaQM7un4Hm+GmghuiPvevWwlca5PwDA==,
      }
    peerDependencies:
      i18next: ">= 19.0.0"
      react: ">= 16.8.0"
      react-dom: "*"
      react-native: "*"
    peerDependenciesMeta:
      react-dom:
        optional: true
      react-native:
        optional: true

  react-is@16.13.1:
    resolution:
      {
        integrity: sha512-24e6ynE2H+OKt4kqsOvNd8kBpV65zoxbA4BVsEOB3ARVWQki/DHzaUoC5KuON/BiccDaCCTZBuOcfZs70kR8bQ==,
      }

  react-is@17.0.2:
    resolution:
      {
        integrity: sha512-w2GsyukL62IJnlaff/nRegPQR94C/XXamvMWmSHRJ4y7Ts/4ocGRmTHvOs8PSE6pB3dWOrD/nueuU5sduBsQ4w==,
      }

  react-is@18.3.1:
    resolution:
      {
        integrity: sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==,
      }

  react-redux@9.1.2:
    resolution:
      {
        integrity: sha512-0OA4dhM1W48l3uzmv6B7TXPCGmokUU4p1M44DGN2/D9a1FjVPukVjER1PcPX97jIg6aUeLq1XJo1IpfbgULn0w==,
      }
    peerDependencies:
      "@types/react": ^18.2.25
      react: ^18.0
      redux: ^5.0.0
    peerDependenciesMeta:
      "@types/react":
        optional: true
      redux:
        optional: true

  react-refresh@0.14.2:
    resolution:
      {
        integrity: sha512-jCvmsr+1IUSMUyzOkRcvnVbX3ZYC6g9TDrDbFuFmRDq7PD4yaGbLKNQL6k2jnArV8hjYxh7hVhAZB6s9HDGpZA==,
      }
    engines: { node: ">=0.10.0" }

  react-router-dom@6.23.1:
    resolution:
      {
        integrity: sha512-utP+K+aSTtEdbWpC+4gxhdlPFwuEfDKq8ZrPFU65bbRJY+l706qjR7yaidBpo3MSeA/fzwbXWbKBI6ftOnP3OQ==,
      }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      react: ">=16.8"
      react-dom: ">=16.8"

  react-router@6.23.1:
    resolution:
      {
        integrity: sha512-fzcOaRF69uvqbbM7OhvQyBTFDVrrGlsFdS3AL+1KfIBtGETibHzi3FkoTRyiDJnWNc2VxrfvR+657ROHjaNjqQ==,
      }
    engines: { node: ">=14.0.0" }
    peerDependencies:
      react: ">=16.8"

  react-spinners@0.13.8:
    resolution:
      {
        integrity: sha512-3e+k56lUkPj0vb5NDXPVFAOkPC//XyhKPJjvcGjyMNPWsBKpplfeyialP74G7H7+It7KzhtET+MvGqbKgAqpZA==,
      }
    peerDependencies:
      react: ^16.0.0 || ^17.0.0 || ^18.0.0
      react-dom: ^16.0.0 || ^17.0.0 || ^18.0.0

  react-transition-group@4.4.5:
    resolution:
      {
        integrity: sha512-pZcd1MCJoiKiBR2NRxeCRg13uCXbydPnmB4EOeRrY7480qNWO8IIgQG6zlDkm6uRMsURXPuKq0GWtiM59a5Q6g==,
      }
    peerDependencies:
      react: ">=16.6.0"
      react-dom: ">=16.6.0"

  react-universal-interface@0.6.2:
    resolution:
      {
        integrity: sha512-dg8yXdcQmvgR13RIlZbTRQOoUrDciFVoSBZILwjE2LFISxZZ8loVJKAkuzswl5js8BHda79bIb2b84ehU8IjXw==,
      }
    peerDependencies:
      react: "*"
      tslib: "*"

  react-use@17.5.0:
    resolution:
      {
        integrity: sha512-PbfwSPMwp/hoL847rLnm/qkjg3sTRCvn6YhUZiHaUa3FA6/aNoFX79ul5Xt70O1rK+9GxSVqkY0eTwMdsR/bWg==,
      }
    peerDependencies:
      react: "*"
      react-dom: "*"

  react@18.3.1:
    resolution:
      {
        integrity: sha512-wS+hAgJShR0KhEvPJArfuPVN1+Hz1t0Y6n5jLrGQbkb4urgPE/0Rve+1kMB1v/oWgHgm4WIcV+i7F2pTVj+2iQ==,
      }
    engines: { node: ">=0.10.0" }

  read-cache@1.0.0:
    resolution:
      {
        integrity: sha512-Owdv/Ft7IjOgm/i0xvNDZ1LrRANRfew4b2prF3OWMQLxLfu3bS8FVhCsrSCMK4lR56Y9ya+AThoTpDCTxCmpRA==,
      }

  read-pkg-up@7.0.1:
    resolution:
      {
        integrity: sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==,
      }
    engines: { node: ">=8" }

  read-pkg@3.0.0:
    resolution:
      {
        integrity: sha512-BLq/cCO9two+lBgiTYNqD6GdtK8s4NpaWrl6/rCO9w0TUS8oJl7cmToOZfRYllKTISY6nt1U7jQ53brmKqY6BA==,
      }
    engines: { node: ">=4" }

  read-pkg@5.2.0:
    resolution:
      {
        integrity: sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==,
      }
    engines: { node: ">=8" }

  readable-stream@3.6.2:
    resolution:
      {
        integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==,
      }
    engines: { node: ">= 6" }

  readdirp@3.6.0:
    resolution:
      {
        integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==,
      }
    engines: { node: ">=8.10.0" }

  redent@3.0.0:
    resolution:
      {
        integrity: sha512-6tDA8g98We0zd0GvVeMT9arEOnTw9qM03L9cJXaCjrip1OO764RDBLBfrB4cwzNGDj5OA5ioymC9GkizgWJDUg==,
      }
    engines: { node: ">=8" }

  redux-thunk@3.1.0:
    resolution:
      {
        integrity: sha512-NW2r5T6ksUKXCabzhL9z+h206HQw/NJkcLm1GPImRQ8IzfXwRGqjVhKJGauHirT0DAuyy6hjdnMZaRoAcy0Klw==,
      }
    peerDependencies:
      redux: ^5.0.0

  redux@5.0.1:
    resolution:
      {
        integrity: sha512-M9/ELqF6fy8FwmkpnF0S3YKOqMyoWJ4+CS5Efg2ct3oY9daQvd/Pc71FpGZsVsbl3Cpb+IIcjBDUnnyBdQbq4w==,
      }

  reflect.getprototypeof@1.0.6:
    resolution:
      {
        integrity: sha512-fmfw4XgoDke3kdI6h4xcUz1dG8uaiv5q9gcEwLS4Pnth2kxT+GZ7YehS1JTMGBQmtV7Y4GFGbs2re2NqhdozUg==,
      }
    engines: { node: ">= 0.4" }

  regenerate-unicode-properties@10.1.1:
    resolution:
      {
        integrity: sha512-X007RyZLsCJVVrjgEFVpLUTZwyOZk3oiL75ZcuYjlIWd6rNJtOjkBwQc5AsRrpbKVkxN6sklw/k/9m2jJYOf8Q==,
      }
    engines: { node: ">=4" }

  regenerate@1.4.2:
    resolution:
      {
        integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==,
      }

  regenerator-runtime@0.14.1:
    resolution:
      {
        integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==,
      }

  regenerator-transform@0.15.2:
    resolution:
      {
        integrity: sha512-hfMp2BoF0qOk3uc5V20ALGDS2ddjQaLrdl7xrGXvAIow7qeWRM2VA2HuCHkUKk9slq3VwEwLNK3DFBqDfPGYtg==,
      }

  regexp.prototype.flags@1.5.2:
    resolution:
      {
        integrity: sha512-NcDiDkTLuPR+++OCKB0nWafEmhg/Da8aUPLPMQbK+bxKKCm1/S5he+AqYa4PlMCVBalb4/yxIRub6qkEx5yJbw==,
      }
    engines: { node: ">= 0.4" }

  regexpp@3.2.0:
    resolution:
      {
        integrity: sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==,
      }
    engines: { node: ">=8" }

  regexpu-core@5.3.2:
    resolution:
      {
        integrity: sha512-RAM5FlZz+Lhmo7db9L298p2vHP5ZywrVXmVXpmAD9GuL5MPH6t9ROw1iA/wfHkQ76Qe7AaPF0nGuim96/IrQMQ==,
      }
    engines: { node: ">=4" }

  regjsparser@0.9.1:
    resolution:
      {
        integrity: sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==,
      }
    hasBin: true

  remove-accents@0.5.0:
    resolution:
      {
        integrity: sha512-8g3/Otx1eJaVD12e31UbJj1YzdtVvzH85HV7t+9MJYk/u3XmkOUJ5Ys9wQrf9PCPK8+xn4ymzqYCiZl6QWKn+A==,
      }

  require-directory@2.1.1:
    resolution:
      {
        integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==,
      }
    engines: { node: ">=0.10.0" }

  require-from-string@2.0.2:
    resolution:
      {
        integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==,
      }
    engines: { node: ">=0.10.0" }

  require-relative@0.8.7:
    resolution:
      {
        integrity: sha512-AKGr4qvHiryxRb19m3PsLRGuKVAbJLUD7E6eOaHkfKhwc+vSgVOCY5xNvm9EkolBKTOf0GrQAZKLimOCz81Khg==,
      }

  requires-port@1.0.0:
    resolution:
      {
        integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==,
      }

  reselect@4.1.8:
    resolution:
      {
        integrity: sha512-ab9EmR80F/zQTMNeneUr4cv+jSwPJgIlvEmVwLerwrWVbpLlBuls9XHzIeTFy4cegU2NHBp3va0LKOzU5qFEYQ==,
      }

  reselect@5.1.1:
    resolution:
      {
        integrity: sha512-K/BG6eIky/SBpzfHZv/dd+9JBFiS4SWV7FIujVyJRux6e45+73RaUHXLmIR1f7WOMaQ0U1km6qwklRQxpJJY0w==,
      }

  resize-observer-polyfill@1.5.1:
    resolution:
      {
        integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==,
      }

  resolve-from@4.0.0:
    resolution:
      {
        integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==,
      }
    engines: { node: ">=4" }

  resolve-from@5.0.0:
    resolution:
      {
        integrity: sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==,
      }
    engines: { node: ">=8" }

  resolve-global@1.0.0:
    resolution:
      {
        integrity: sha512-zFa12V4OLtT5XUX/Q4VLvTfBf+Ok0SPc1FNGM/z9ctUdiU618qwKpWnd0CHs3+RqROfyEg/DhuHbMWYqcgljEw==,
      }
    engines: { node: ">=8" }

  resolve-url@0.2.1:
    resolution:
      {
        integrity: sha512-ZuF55hVUQaaczgOIwqWzkEcEidmlD/xl44x1UZnhOXcYuFN2S6+rcxpG+C1N3So0wvNI3DmJICUFfu2SxhBmvg==,
      }
    deprecated: https://github.com/lydell/resolve-url#deprecated

  resolve@1.22.8:
    resolution:
      {
        integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==,
      }
    hasBin: true

  resolve@2.0.0-next.5:
    resolution:
      {
        integrity: sha512-U7WjGVG9sH8tvjW5SmGbQuui75FiyjAX72HX15DwBBwF9dNiQZRQAg9nnPhYy+TUnE0+VcrttuvNI8oSxZcocA==,
      }
    hasBin: true

  restore-cursor@4.0.0:
    resolution:
      {
        integrity: sha512-I9fPXU9geO9bHOt9pHHOhOkYerIMsmVaWB0rA2AI9ERh/+x/i7MV5HKBNrg+ljO5eoPVgCcnFuRjJ9uH6I/3eg==,
      }
    engines: { node: ^12.20.0 || ^14.13.1 || >=16.0.0 }

  reusify@1.0.4:
    resolution:
      {
        integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==,
      }
    engines: { iojs: ">=1.0.0", node: ">=0.10.0" }

  rfdc@1.3.1:
    resolution:
      {
        integrity: sha512-r5a3l5HzYlIC68TpmYKlxWjmOP6wiPJ1vWv2HeLhNsRZMrCkxeqxiHlQ21oXmQ4F3SiryXBHhAD7JZqvOJjFmg==,
      }

  rimraf@3.0.2:
    resolution:
      {
        integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==,
      }
    deprecated: Rimraf versions prior to v4 are no longer supported
    hasBin: true

  rimraf@5.0.7:
    resolution:
      {
        integrity: sha512-nV6YcJo5wbLW77m+8KjH8aB/7/rxQy9SZ0HY5shnwULfS+9nmTtVXAJET5NdZmCzA4fPI/Hm1wo/Po/4mopOdg==,
      }
    engines: { node: ">=14.18" }
    hasBin: true

  rollup@3.29.4:
    resolution:
      {
        integrity: sha512-oWzmBZwvYrU0iJHtDmhsm662rC15FRXmcjCk1xD771dFDx5jJ02ufAQQTn0etB2emNk4J9EZg/yWKpsn9BWGRw==,
      }
    engines: { node: ">=14.18.0", npm: ">=8.0.0" }
    hasBin: true

  rrweb-cssom@0.6.0:
    resolution:
      {
        integrity: sha512-APM0Gt1KoXBz0iIkkdB/kfvGOwC4UuJFeG/c+yV7wSc7q96cG/kJ0HiYCnzivD9SB53cLV1MlHFNfOuPaadYSw==,
      }

  rtl-css-js@1.16.1:
    resolution:
      {
        integrity: sha512-lRQgou1mu19e+Ya0LsTvKrVJ5TYUbqCVPAiImX3UfLTenarvPUl1QFdvu5Z3PYmHT9RCcwIfbjRQBntExyj3Zg==,
      }

  run-parallel@1.2.0:
    resolution:
      {
        integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==,
      }

  safe-array-concat@1.1.2:
    resolution:
      {
        integrity: sha512-vj6RsCsWBCf19jIeHEfkRMw8DPiBb+DMXklQ/1SGDHOMlHdPUkZXFQ2YdplS23zESTijAcurb1aSgJA3AgMu1Q==,
      }
    engines: { node: ">=0.4" }

  safe-buffer@5.2.1:
    resolution:
      {
        integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==,
      }

  safe-regex-test@1.0.3:
    resolution:
      {
        integrity: sha512-CdASjNJPvRa7roO6Ra/gLYBTzYzzPyyBXxIMdGW3USQLyjWEls2RgW5UBTXaQVp+OrpeCK3bLem8smtmheoRuw==,
      }
    engines: { node: ">= 0.4" }

  safer-buffer@2.1.2:
    resolution:
      {
        integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==,
      }

  sass@1.77.1:
    resolution:
      {
        integrity: sha512-OMEyfirt9XEfyvocduUIOlUSkWOXS/LAt6oblR/ISXCTukyavjex+zQNm51pPCOiFKY1QpWvEH1EeCkgyV3I6w==,
      }
    engines: { node: ">=14.0.0" }
    hasBin: true

  saxes@6.0.0:
    resolution:
      {
        integrity: sha512-xAg7SOnEhrm5zI3puOOKyy1OMcMlIJZYNJY7xLBwSze0UjhPLnWfj2GF2EpT0jmzaJKIWKHLsaSSajf35bcYnA==,
      }
    engines: { node: ">=v12.22.7" }

  scheduler@0.23.2:
    resolution:
      {
        integrity: sha512-UOShsPwz7NrMUqhR6t0hWjFduvOzbtv7toDH1/hIrfRNIDBnnBWd0CwJTGvTpngVlmwGCdP9/Zl/tVrDqcuYzQ==,
      }

  screenfull@5.2.0:
    resolution:
      {
        integrity: sha512-9BakfsO2aUQN2K9Fdbj87RJIEZ82Q9IGim7FqM5OsebfoFC6ZHXgDq/KvniuLTPdeM8wY2o6Dj3WQ7KeQCj3cA==,
      }
    engines: { node: ">=0.10.0" }

  semver@5.7.2:
    resolution:
      {
        integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==,
      }
    hasBin: true

  semver@6.3.1:
    resolution:
      {
        integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==,
      }
    hasBin: true

  semver@7.5.4:
    resolution:
      {
        integrity: sha512-1bCSESV6Pv+i21Hvpxp3Dx+pSD8lIPt8uVjRrxAUt/nbswYc+tK6Y2btiULjd4+fnq15PX+nqQDC7Oft7WkwcA==,
      }
    engines: { node: ">=10" }
    hasBin: true

  semver@7.6.2:
    resolution:
      {
        integrity: sha512-FNAIBWCx9qcRhoHcgcJ0gvU7SN1lYU2ZXuSfl04bSC5OpvDHFyJCjdNHomPXxjQlCBU67YW64PzY7/VIEH7F2w==,
      }
    engines: { node: ">=10" }
    hasBin: true

  set-function-length@1.2.2:
    resolution:
      {
        integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==,
      }
    engines: { node: ">= 0.4" }

  set-function-name@2.0.2:
    resolution:
      {
        integrity: sha512-7PGFlmtwsEADb0WYyvCMa1t+yke6daIG4Wirafur5kcf+MhUnPms1UeR0CKQdTZD81yESwMHbtn+TR+dMviakQ==,
      }
    engines: { node: ">= 0.4" }

  set-harmonic-interval@1.0.1:
    resolution:
      {
        integrity: sha512-AhICkFV84tBP1aWqPwLZqFvAwqEoVA9kxNMniGEUvzOlm4vLmOFLiTT3UZ6bziJTy4bOVpzWGTfSCbmaayGx8g==,
      }
    engines: { node: ">=6.9" }

  shebang-command@1.2.0:
    resolution:
      {
        integrity: sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg==,
      }
    engines: { node: ">=0.10.0" }

  shebang-command@2.0.0:
    resolution:
      {
        integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==,
      }
    engines: { node: ">=8" }

  shebang-regex@1.0.0:
    resolution:
      {
        integrity: sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==,
      }
    engines: { node: ">=0.10.0" }

  shebang-regex@3.0.0:
    resolution:
      {
        integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==,
      }
    engines: { node: ">=8" }

  shell-quote@1.8.1:
    resolution:
      {
        integrity: sha512-6j1W9l1iAs/4xYBI1SYOVZyFcCis9b4KCLQ8fgAGG07QvzaRLVVRQvAy85yNmmZSjYjg4MWh4gNvlPujU/5LpA==,
      }

  side-channel@1.0.6:
    resolution:
      {
        integrity: sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==,
      }
    engines: { node: ">= 0.4" }

  siginfo@2.0.0:
    resolution:
      {
        integrity: sha512-ybx0WO1/8bSBLEWXZvEd7gMW3Sn3JFlW3TvX1nREbDLRNQNaeNN8WK0meBwPdAaOI7TtRRRJn/Es1zhrrCHu7g==,
      }

  signal-exit@3.0.7:
    resolution:
      {
        integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==,
      }

  signal-exit@4.1.0:
    resolution:
      {
        integrity: sha512-bzyZ1e88w9O1iNJbKnOlvYTrWPDl46O1bG0D3XInv+9tkPrxrN8jUUTiFlDkkmKWgn1M6CfIA13SuGqOa9Korw==,
      }
    engines: { node: ">=14" }

  sirv@2.0.4:
    resolution:
      {
        integrity: sha512-94Bdh3cC2PKrbgSOUqTiGPWVZeSiXfKOVZNJniWoqrWrRkB1CJzBU3NEbiTsPcYy1lDsANA/THzS+9WBiy5nfQ==,
      }
    engines: { node: ">= 10" }

  slash@3.0.0:
    resolution:
      {
        integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==,
      }
    engines: { node: ">=8" }

  slice-ansi@5.0.0:
    resolution:
      {
        integrity: sha512-FC+lgizVPfie0kkhqUScwRu1O/lF6NOgJmlCgK+/LYxDCTk8sGelYaHDhFcDN+Sn3Cv+3VSa4Byeo+IMCzpMgQ==,
      }
    engines: { node: ">=12" }

  sort-by@1.2.0:
    resolution:
      {
        integrity: sha512-aRyW65r3xMnf4nxJRluCg0H/woJpksU1dQxRtXYzau30sNBOmf5HACpDd9MZDhKh7ALQ5FgSOfMPwZEtUmMqcg==,
      }

  source-map-js@1.2.0:
    resolution:
      {
        integrity: sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==,
      }
    engines: { node: ">=0.10.0" }

  source-map-resolve@0.5.3:
    resolution:
      {
        integrity: sha512-Htz+RnsXWk5+P2slx5Jh3Q66vhQj1Cllm0zvnaY98+NFx+Dv2CF/f5O/t8x+KaNdrdIAsruNzoh/KpialbqAnw==,
      }
    deprecated: See https://github.com/lydell/source-map-resolve#deprecated

  source-map-url@0.4.1:
    resolution:
      {
        integrity: sha512-cPiFOTLUKvJFIg4SKVScy4ilPPW6rFgMgfuZJPNoDuMs3nC1HbMUycBoJw77xFIp6z1UJQJOfx6C9GMH80DiTw==,
      }
    deprecated: See https://github.com/lydell/source-map-url#deprecated

  source-map@0.5.6:
    resolution:
      {
        integrity: sha512-MjZkVp0NHr5+TPihLcadqnlVoGIoWo4IBHptutGh9wI3ttUYvCG26HkSuDi+K6lsZ25syXJXcctwgyVCt//xqA==,
      }
    engines: { node: ">=0.10.0" }

  source-map@0.5.7:
    resolution:
      {
        integrity: sha512-LbrmJOMUSdEVxIKvdcJzQC+nQhe8FUZQTXQy6+I75skNgn3OoQ0DZA8YnFa7gp8tqtL3KPf1kmo0R5DoApeSGQ==,
      }
    engines: { node: ">=0.10.0" }

  source-map@0.6.1:
    resolution:
      {
        integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==,
      }
    engines: { node: ">=0.10.0" }

  spdx-correct@3.2.0:
    resolution:
      {
        integrity: sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==,
      }

  spdx-exceptions@2.5.0:
    resolution:
      {
        integrity: sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==,
      }

  spdx-expression-parse@3.0.1:
    resolution:
      {
        integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==,
      }

  spdx-license-ids@3.0.17:
    resolution:
      {
        integrity: sha512-sh8PWc/ftMqAAdFiBu6Fy6JUOYjqDJBJvIhpfDMyHrr0Rbp5liZqd4TjtQ/RgfLjKFZb+LMx5hpml5qOWy0qvg==,
      }

  split2@3.2.2:
    resolution:
      {
        integrity: sha512-9NThjpgZnifTkJpzTZ7Eue85S49QwpNhZTq6GRJwObb6jnLFNGB7Qm73V5HewTROPyxD0C29xqmaI68bQtV+hg==,
      }

  stack-generator@2.0.10:
    resolution:
      {
        integrity: sha512-mwnua/hkqM6pF4k8SnmZ2zfETsRUpWXREfA/goT8SLCV4iOFa4bzOX2nDipWAZFPTjLvQB82f5yaodMVhK0yJQ==,
      }

  stackback@0.0.2:
    resolution:
      {
        integrity: sha512-1XMJE5fQo1jGH6Y/7ebnwPOBEkIEnT4QF32d5R1+VXdXveM0IBMJt8zfaxX1P3QhVwrYe+576+jkANtSS2mBbw==,
      }

  stackframe@1.3.4:
    resolution:
      {
        integrity: sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==,
      }

  stacktrace-gps@3.1.2:
    resolution:
      {
        integrity: sha512-GcUgbO4Jsqqg6RxfyTHFiPxdPqF+3LFmQhm7MgCuYQOYuWyqxo5pwRPz5d/u6/WYJdEnWfK4r+jGbyD8TSggXQ==,
      }

  stacktrace-js@2.0.2:
    resolution:
      {
        integrity: sha512-Je5vBeY4S1r/RnLydLl0TBTi3F2qdfWmYsGvtfZgEI+SCprPppaIhQf5nGcal4gI4cGpCV/duLcAzT1np6sQqg==,
      }

  std-env@3.7.0:
    resolution:
      {
        integrity: sha512-JPbdCEQLj1w5GilpiHAx3qJvFndqybBysA3qUOnznweH4QbNYUsW/ea8QzSrnh0vNsezMMw5bcVool8lM0gwzg==,
      }

  stop-iteration-iterator@1.0.0:
    resolution:
      {
        integrity: sha512-iCGQj+0l0HOdZ2AEeBADlsRC+vsnDsZsbdSiH1yNSjcfKM7fdpCMfqAL/dwF5BLiw/XhRft/Wax6zQbhq2BcjQ==,
      }
    engines: { node: ">= 0.4" }

  string-argv@0.3.2:
    resolution:
      {
        integrity: sha512-aqD2Q0144Z+/RqG52NeHEkZauTAUWJO8c6yTftGJKO3Tja5tUgIfmIl6kExvhtxSDP7fXB6DvzkfMpCd/F3G+Q==,
      }
    engines: { node: ">=0.6.19" }

  string-width@4.2.3:
    resolution:
      {
        integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==,
      }
    engines: { node: ">=8" }

  string-width@5.1.2:
    resolution:
      {
        integrity: sha512-HnLOCR3vjcY8beoNLtcjZ5/nxn2afmME6lhrDrebokqMap+XbeW8n9TXpPDOqdGK5qcI3oT0GKTW6wC7EMiVqA==,
      }
    engines: { node: ">=12" }

  string.prototype.matchall@4.0.11:
    resolution:
      {
        integrity: sha512-NUdh0aDavY2og7IbBPenWqR9exH+E26Sv8e0/eTe1tltDGZL+GtBkDAnnyBtmekfK6/Dq3MkcGtzXFEd1LQrtg==,
      }
    engines: { node: ">= 0.4" }

  string.prototype.padend@3.1.6:
    resolution:
      {
        integrity: sha512-XZpspuSB7vJWhvJc9DLSlrXl1mcA2BdoY5jjnS135ydXqLoqhs96JjDtCkjJEQHvfqZIp9hBuBMgI589peyx9Q==,
      }
    engines: { node: ">= 0.4" }

  string.prototype.trim@1.2.9:
    resolution:
      {
        integrity: sha512-klHuCNxiMZ8MlsOihJhJEBJAiMVqU3Z2nEXWfWnIqjN0gEFS9J9+IxKozWWtQGcgoa1WUZzLjKPTr4ZHNFTFxw==,
      }
    engines: { node: ">= 0.4" }

  string.prototype.trimend@1.0.8:
    resolution:
      {
        integrity: sha512-p73uL5VCHCO2BZZ6krwwQE3kCzM7NKmis8S//xEC6fQonchbum4eP6kR4DLEjQFO3Wnj3Fuo8NM0kOSjVdHjZQ==,
      }

  string.prototype.trimstart@1.0.8:
    resolution:
      {
        integrity: sha512-UXSH262CSZY1tfu3G3Secr6uGLCFVPMhIqHjlgCUtCCcgihYc/xKs9djMTMUOb2j1mVSeU8EU6NWc/iQKU6Gfg==,
      }
    engines: { node: ">= 0.4" }

  string_decoder@1.3.0:
    resolution:
      {
        integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==,
      }

  strip-ansi@3.0.1:
    resolution:
      {
        integrity: sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==,
      }
    engines: { node: ">=0.10.0" }

  strip-ansi@6.0.1:
    resolution:
      {
        integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==,
      }
    engines: { node: ">=8" }

  strip-ansi@7.1.0:
    resolution:
      {
        integrity: sha512-iq6eVVI64nQQTRYq2KtEg2d2uU7LElhTJwsH4YzIHZshxlgZms/wIc4VoDQTlG/IvVIrBKG06CrZnp0qv7hkcQ==,
      }
    engines: { node: ">=12" }

  strip-bom@3.0.0:
    resolution:
      {
        integrity: sha512-vavAMRXOgBVNF6nyEEmL3DBK19iRpDcoIwW+swQ+CbGiu7lju6t+JklA1MHweoWtadgt4ISVUsXLyDq34ddcwA==,
      }
    engines: { node: ">=4" }

  strip-final-newline@2.0.0:
    resolution:
      {
        integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==,
      }
    engines: { node: ">=6" }

  strip-final-newline@3.0.0:
    resolution:
      {
        integrity: sha512-dOESqjYr96iWYylGObzd39EuNTa5VJxyvVAEm5Jnh7KGo75V43Hk1odPQkNDyXNmUR6k+gEiDVXnjB8HJ3crXw==,
      }
    engines: { node: ">=12" }

  strip-indent@3.0.0:
    resolution:
      {
        integrity: sha512-laJTa3Jb+VQpaC6DseHhF7dXVqHTfJPCRDaEbid/drOhgitgYku/letMUqOXFoWV0zIIUbjpdH2t+tYj4bQMRQ==,
      }
    engines: { node: ">=8" }

  strip-json-comments@3.1.1:
    resolution:
      {
        integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==,
      }
    engines: { node: ">=8" }

  strip-literal@1.3.0:
    resolution:
      {
        integrity: sha512-PugKzOsyXpArk0yWmUwqOZecSO0GH0bPoctLcqNDH9J04pVW3lflYE0ujElBGTloevcxF5MofAOZ7C5l2b+wLg==,
      }

  stylis@4.2.0:
    resolution:
      {
        integrity: sha512-Orov6g6BB1sDfYgzWfTHDOxamtX1bE/zo104Dh9e6fqJ3PooipYyfJ0pUmrZO2wAvO8YbEyeFrkV91XTsGMSrw==,
      }

  stylis@4.3.2:
    resolution:
      {
        integrity: sha512-bhtUjWd/z6ltJiQwg0dUfxEJ+W+jdqQd8TbWLWyeIJHlnsqmGLRFFd8e5mA0AZi/zx90smXRlN66YMTcaSFifg==,
      }

  sucrase@3.35.0:
    resolution:
      {
        integrity: sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==,
      }
    engines: { node: ">=16 || 14 >=14.17" }
    hasBin: true

  supports-color@2.0.0:
    resolution:
      {
        integrity: sha512-KKNVtd6pCYgPIKU4cp2733HWYCpplQhddZLBUryaAHou723x+FRzQ5Df824Fj+IyyuiQTRoub4SnIFfIcrp70g==,
      }
    engines: { node: ">=0.8.0" }

  supports-color@5.5.0:
    resolution:
      {
        integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==,
      }
    engines: { node: ">=4" }

  supports-color@7.2.0:
    resolution:
      {
        integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==,
      }
    engines: { node: ">=8" }

  supports-preserve-symlinks-flag@1.0.0:
    resolution:
      {
        integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==,
      }
    engines: { node: ">= 0.4" }

  svg-parser@2.0.4:
    resolution:
      {
        integrity: sha512-e4hG1hRwoOdRb37cIMSgzNsxyzKfayW6VOflrwvR+/bzrkyxY/31WkbgnQpgtrNp1SdpJvpUAGTa/ZoiPNDuRQ==,
      }

  svgo@3.3.2:
    resolution:
      {
        integrity: sha512-OoohrmuUlBs8B8o6MB2Aevn+pRIH9zDALSR+6hhqVfa6fRwG/Qw9VUMSMW9VNg2CFc/MTIfabtdOVl9ODIJjpw==,
      }
    engines: { node: ">=14.0.0" }
    hasBin: true

  symbol-tree@3.2.4:
    resolution:
      {
        integrity: sha512-9QNk5KwDF+Bvz+PyObkmSYjI5ksVUYtjW7AU22r2NKcfLJcXp96hkDWU3+XndOsUb+AQ9QhfzfCT2O+CNWT5Tw==,
      }

  tailwindcss@3.4.3:
    resolution:
      {
        integrity: sha512-U7sxQk/n397Bmx4JHbJx/iSOOv5G+II3f1kpLpY2QeUv5DcPdcTsYLlusZfq1NthHS1c1cZoyFmmkex1rzke0A==,
      }
    engines: { node: ">=14.0.0" }
    hasBin: true

  test-exclude@6.0.0:
    resolution:
      {
        integrity: sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==,
      }
    engines: { node: ">=8" }

  text-extensions@1.9.0:
    resolution:
      {
        integrity: sha512-wiBrwC1EhBelW12Zy26JeOUkQ5mRu+5o8rpsJk5+2t+Y5vE7e842qtZDQ2g1NpX/29HdyFeJ4nSIhI47ENSxlQ==,
      }
    engines: { node: ">=0.10" }

  text-table@0.2.0:
    resolution:
      {
        integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==,
      }

  thenify-all@1.6.0:
    resolution:
      {
        integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==,
      }
    engines: { node: ">=0.8" }

  thenify@3.3.1:
    resolution:
      {
        integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==,
      }

  throttle-debounce@3.0.1:
    resolution:
      {
        integrity: sha512-dTEWWNu6JmeVXY0ZYoPuH5cRIwc0MeGbJwah9KUNYSJwommQpCzTySTpEe8Gs1J23aeWEuAobe4Ag7EHVt/LOg==,
      }
    engines: { node: ">=10" }

  through2@4.0.2:
    resolution:
      {
        integrity: sha512-iOqSav00cVxEEICeD7TjLB1sueEL+81Wpzp2bY17uZjZN0pWZPuo4suZ/61VujxmqSGFfgOcNuTZ85QJwNZQpw==,
      }

  through@2.3.8:
    resolution:
      {
        integrity: sha512-w89qg7PI8wAdvX60bMDP+bFoD5Dvhm9oLheFp5O4a2QF0cSBGsBX4qZmadPMvVqlLJBBci+WqGGOAPvcDeNSVg==,
      }

  tiny-case@1.0.3:
    resolution:
      {
        integrity: sha512-Eet/eeMhkO6TX8mnUteS9zgPbUMQa4I6Kkp5ORiBD5476/m+PIRiumP5tmh5ioJpH7k51Kehawy2UDfsnxxY8Q==,
      }

  tiny-invariant@1.3.3:
    resolution:
      {
        integrity: sha512-+FbBPE1o9QAYvviau/qC5SE3caw21q3xkvWKBtja5vgqOWIHHJ3ioaq1VPfn/Szqctz2bU/oYeKd9/z5BL+PVg==,
      }

  tiny-warning@1.0.3:
    resolution:
      {
        integrity: sha512-lBN9zLN/oAf68o3zNXYrdCt1kP8WsiGW8Oo2ka41b2IM5JL/S1CTyX1rW0mb/zSuJun0ZUrDxx4sqvYS2FWzPA==,
      }

  tinybench@2.8.0:
    resolution:
      {
        integrity: sha512-1/eK7zUnIklz4JUUlL+658n58XO2hHLQfSk1Zf2LKieUjxidN16eKFEoDEfjHc3ohofSSqK3X5yO6VGb6iW8Lw==,
      }

  tinypool@0.4.0:
    resolution:
      {
        integrity: sha512-2ksntHOKf893wSAH4z/+JbPpi92esw8Gn9N2deXX+B0EO92hexAVI9GIZZPx7P5aYo5KULfeOSt3kMOmSOy6uA==,
      }
    engines: { node: ">=14.0.0" }

  tinyspy@1.1.1:
    resolution:
      {
        integrity: sha512-UVq5AXt/gQlti7oxoIg5oi/9r0WpF7DGEVwXgqWSMmyN16+e3tl5lIvTaOpJ3TAtu5xFzWccFRM4R5NaWHF+4g==,
      }
    engines: { node: ">=14.0.0" }

  to-fast-properties@2.0.0:
    resolution:
      {
        integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==,
      }
    engines: { node: ">=4" }

  to-regex-range@5.0.1:
    resolution:
      {
        integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==,
      }
    engines: { node: ">=8.0" }

  toggle-selection@1.0.6:
    resolution:
      {
        integrity: sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ==,
      }

  toposort@2.0.2:
    resolution:
      {
        integrity: sha512-0a5EOkAUp8D4moMi2W8ZF8jcga7BgZd91O/yabJCFY8az+XSzeGyTKs0Aoo897iV1Nj6guFq8orWDS96z91oGg==,
      }

  totalist@3.0.1:
    resolution:
      {
        integrity: sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==,
      }
    engines: { node: ">=6" }

  tough-cookie@4.1.4:
    resolution:
      {
        integrity: sha512-Loo5UUvLD9ScZ6jh8beX1T6sO1w2/MpCRpEP7V280GKMVUQ0Jzar2U3UJPsrdbziLEMMhu3Ujnq//rhiFuIeag==,
      }
    engines: { node: ">=6" }

  tr46@0.0.3:
    resolution:
      {
        integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==,
      }

  tr46@4.1.1:
    resolution:
      {
        integrity: sha512-2lv/66T7e5yNyhAAC4NaKe5nVavzuGJQVVtRYLyQ2OI8tsJ61PMLlelehb0wi2Hx6+hT/OJUWZcw8MjlSRnxvw==,
      }
    engines: { node: ">=14" }

  trim-newlines@3.0.1:
    resolution:
      {
        integrity: sha512-c1PTsA3tYrIsLGkJkzHF+w9F2EyxfXGo4UyJc4pFL++FMjnq0HJS69T3M7d//gKrFKwy429bouPescbjecU+Zw==,
      }
    engines: { node: ">=8" }

  ts-easing@0.2.0:
    resolution:
      {
        integrity: sha512-Z86EW+fFFh/IFB1fqQ3/+7Zpf9t2ebOAxNI/V6Wo7r5gqiqtxmgTlQ1qbqQcjLKYeSHPTsEmvlJUDg/EuL0uHQ==,
      }

  ts-interface-checker@0.1.13:
    resolution:
      {
        integrity: sha512-Y/arvbn+rrz3JCKl9C4kVNfTfSm2/mEp5FSz5EsZSANGPSlQrpRI5M4PKF+mJnE52jOO90PnPSc3Ur3bTQw0gA==,
      }

  ts-node@10.9.2:
    resolution:
      {
        integrity: sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==,
      }
    hasBin: true
    peerDependencies:
      "@swc/core": ">=1.2.50"
      "@swc/wasm": ">=1.2.50"
      "@types/node": "*"
      typescript: ">=2.7"
    peerDependenciesMeta:
      "@swc/core":
        optional: true
      "@swc/wasm":
        optional: true

  tsconfig-paths@3.15.0:
    resolution:
      {
        integrity: sha512-2Ac2RgzDe/cn48GvOe3M+o82pEFewD3UPbyoUHHdKasHwJKjds4fLXWf/Ux5kATBKN20oaFGu+jbElp1pos0mg==,
      }

  tslib@1.14.1:
    resolution:
      {
        integrity: sha512-Xni35NKzjgMrwevysHTCArtLDpPvye8zV/0E4EyYn43P7/7qvQwPh9BGkHewbMulVntbigmcT7rdX3BNo9wRJg==,
      }

  tslib@2.6.2:
    resolution:
      {
        integrity: sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==,
      }

  tsutils@3.21.0:
    resolution:
      {
        integrity: sha512-mHKK3iUXL+3UF6xL5k0PEhKRUBKPBCv/+RkEOpjRWxxx27KKRBmmA60A9pgOUvMi8GKhRMPEmjBRPzs2W7O1OA==,
      }
    engines: { node: ">= 6" }
    peerDependencies:
      typescript: ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"

  twin.macro@3.4.1:
    resolution:
      {
        integrity: sha512-bxGKTV4u/iGcQqHIugPaW5YSLJ5rIr56ay4Pjcr2Mbb037k341bQ+eWT8z3F7r8ZGTXjTD3uiuxos+qQRy4VjQ==,
      }
    engines: { node: ">=16.14.0" }
    peerDependencies:
      tailwindcss: ">=3.3.1"

  type-check@0.4.0:
    resolution:
      {
        integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==,
      }
    engines: { node: ">= 0.8.0" }

  type-detect@4.0.8:
    resolution:
      {
        integrity: sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==,
      }
    engines: { node: ">=4" }

  type-fest@0.18.1:
    resolution:
      {
        integrity: sha512-OIAYXk8+ISY+qTOwkHtKqzAuxchoMiD9Udx+FSGQDuiRR+PJKJHc2NJAXlbhkGwTt/4/nKZxELY1w3ReWOL8mw==,
      }
    engines: { node: ">=10" }

  type-fest@0.20.2:
    resolution:
      {
        integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==,
      }
    engines: { node: ">=10" }

  type-fest@0.21.3:
    resolution:
      {
        integrity: sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==,
      }
    engines: { node: ">=10" }

  type-fest@0.6.0:
    resolution:
      {
        integrity: sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==,
      }
    engines: { node: ">=8" }

  type-fest@0.8.1:
    resolution:
      {
        integrity: sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==,
      }
    engines: { node: ">=8" }

  type-fest@1.4.0:
    resolution:
      {
        integrity: sha512-yGSza74xk0UG8k+pLh5oeoYirvIiWo5t0/o3zHHAO2tRDiZcxWP7fywNlXhqb6/r6sWvwi+RsyQMWhVLe4BVuA==,
      }
    engines: { node: ">=10" }

  type-fest@2.19.0:
    resolution:
      {
        integrity: sha512-RAH822pAdBgcNMAfWnCBU3CFZcfZ/i1eZjwFU/dsLKumyuuP3niueg2UAukXYF0E2AAoc82ZSSf9J0WQBinzHA==,
      }
    engines: { node: ">=12.20" }

  typed-array-buffer@1.0.2:
    resolution:
      {
        integrity: sha512-gEymJYKZtKXzzBzM4jqa9w6Q1Jjm7x2d+sh19AdsD4wqnMPDYyvwpsIc2Q/835kHuo3BEQ7CjelGhfTsoBb2MQ==,
      }
    engines: { node: ">= 0.4" }

  typed-array-byte-length@1.0.1:
    resolution:
      {
        integrity: sha512-3iMJ9q0ao7WE9tWcaYKIptkNBuOIcZCCT0d4MRvuuH88fEoEH62IuQe0OtraD3ebQEoTRk8XCBoknUNc1Y67pw==,
      }
    engines: { node: ">= 0.4" }

  typed-array-byte-offset@1.0.2:
    resolution:
      {
        integrity: sha512-Ous0vodHa56FviZucS2E63zkgtgrACj7omjwd/8lTEMEPFFyjfixMZ1ZXenpgCFBBt4EC1J2XsyVS2gkG0eTFA==,
      }
    engines: { node: ">= 0.4" }

  typed-array-length@1.0.6:
    resolution:
      {
        integrity: sha512-/OxDN6OtAk5KBpGb28T+HZc2M+ADtvRxXrKKbUwtsLgdoxgX13hyy7ek6bFRl5+aBs2yZzB0c4CnQfAtVypW/g==,
      }
    engines: { node: ">= 0.4" }

  typescript@4.9.5:
    resolution:
      {
        integrity: sha512-1FXk9E2Hm+QzZQ7z+McJiHL4NW1F2EzMu9Nq9i3zAaGqibafqYwCVU6WyWAuyQRRzOlxou8xZSyXLEN8oKj24g==,
      }
    engines: { node: ">=4.2.0" }
    hasBin: true

  ufo@1.5.3:
    resolution:
      {
        integrity: sha512-Y7HYmWaFwPUmkoQCUIAYpKqkOf+SbVj/2fJJZ4RJMCfZp0rTGwRbzQD+HghfnhKOjL9E01okqz+ncJskGYfBNw==,
      }

  unbox-primitive@1.0.2:
    resolution:
      {
        integrity: sha512-61pPlCD9h51VoreyJ0BReideM3MDKMKnh6+V9L08331ipq6Q8OFXZYiqP6n/tbHx4s5I9uRhcye6BrbkizkBDw==,
      }

  undici-types@5.26.5:
    resolution:
      {
        integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==,
      }

  unicode-canonical-property-names-ecmascript@2.0.0:
    resolution:
      {
        integrity: sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ==,
      }
    engines: { node: ">=4" }

  unicode-match-property-ecmascript@2.0.0:
    resolution:
      {
        integrity: sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==,
      }
    engines: { node: ">=4" }

  unicode-match-property-value-ecmascript@2.1.0:
    resolution:
      {
        integrity: sha512-qxkjQt6qjg/mYscYMC0XKRn3Rh0wFPlfxB0xkt9CfyTvpX1Ra0+rAmdX2QyAobptSEvuy4RtpPRui6XkV+8wjA==,
      }
    engines: { node: ">=4" }

  unicode-property-aliases-ecmascript@2.1.0:
    resolution:
      {
        integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==,
      }
    engines: { node: ">=4" }

  universalify@0.2.0:
    resolution:
      {
        integrity: sha512-CJ1QgKmNg3CwvAv/kOFmtnEN05f0D/cn9QntgNOQlQF9dgvVTHj3t+8JPdjqawCHk7V/KA+fbUqzZ9XWhcqPUg==,
      }
    engines: { node: ">= 4.0.0" }

  universalify@2.0.1:
    resolution:
      {
        integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==,
      }
    engines: { node: ">= 10.0.0" }

  update-browserslist-db@1.0.16:
    resolution:
      {
        integrity: sha512-KVbTxlBYlckhF5wgfyZXTWnMn7MMZjMu9XG8bPlliUOP9ThaF4QnhP8qrjrH7DRzHfSk0oQv1wToW+iA5GajEQ==,
      }
    hasBin: true
    peerDependencies:
      browserslist: ">= 4.21.0"

  uri-js@4.4.1:
    resolution:
      {
        integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==,
      }

  urix@0.1.0:
    resolution:
      {
        integrity: sha512-Am1ousAhSLBeB9cG/7k7r2R0zj50uDRlZHPGbazid5s9rlF1F/QKYObEKSIunSjIOkJZqwRRLpvewjEkM7pSqg==,
      }
    deprecated: Please see https://github.com/lydell/urix#deprecated

  url-parse@1.5.10:
    resolution:
      {
        integrity: sha512-WypcfiRhfeUP9vvF0j6rw0J3hrWrw6iZv3+22h6iRMJ/8z1Tj6XfLP4DsUix5MhMPnXpiHDoKyoZ/bdCkwBCiQ==,
      }

  use-debounce@10.0.0:
    resolution:
      {
        integrity: sha512-XRjvlvCB46bah9IBXVnq/ACP2lxqXyZj0D9hj4K5OzNroMDpTEBg8Anuh1/UfRTRs7pLhQ+RiNxxwZu9+MVl1A==,
      }
    engines: { node: ">= 16.0.0" }
    peerDependencies:
      react: ">=16.8.0"

  use-sync-external-store@1.2.2:
    resolution:
      {
        integrity: sha512-PElTlVMwpblvbNqQ82d2n6RjStvdSoNe9FG28kNfz3WiXilJm4DdNkEzRhCZuIDwY8U08WVihhGR5iRqAwfDiw==,
      }
    peerDependencies:
      react: ^16.8.0 || ^17.0.0 || ^18.0.0

  util-deprecate@1.0.2:
    resolution:
      {
        integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==,
      }

  uuid@9.0.1:
    resolution:
      {
        integrity: sha512-b+1eJOlsR9K8HJpow9Ok3fiWOWSIcIzXodvv0rQjVoOVNpWMpxf1wZNpt4y9h10odCNrqnYp1OBzRktckBe3sA==,
      }
    hasBin: true

  v8-compile-cache-lib@3.0.1:
    resolution:
      {
        integrity: sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==,
      }

  v8-to-istanbul@9.2.0:
    resolution:
      {
        integrity: sha512-/EH/sDgxU2eGxajKdwLCDmQ4FWq+kpi3uCmBGpw1xJtnAxEjlD8j8PEiGWpCIMIs3ciNAgH0d3TTJiUkYzyZjA==,
      }
    engines: { node: ">=10.12.0" }

  validate-npm-package-license@3.0.4:
    resolution:
      {
        integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==,
      }

  vite-node@0.29.8:
    resolution:
      {
        integrity: sha512-b6OtCXfk65L6SElVM20q5G546yu10/kNrhg08afEoWlFRJXFq9/6glsvSVY+aI6YeC1tu2TtAqI2jHEQmOmsFw==,
      }
    engines: { node: ">=v14.16.0" }
    hasBin: true

  vite-plugin-checker@0.5.6:
    resolution:
      {
        integrity: sha512-ftRyON0gORUHDxcDt2BErmsikKSkfvl1i2DoP6Jt2zDO9InfvM6tqO1RkXhSjkaXEhKPea6YOnhFaZxW3BzudQ==,
      }
    engines: { node: ">=14.16" }
    peerDependencies:
      eslint: ">=7"
      meow: ^9.0.0
      optionator: ^0.9.1
      stylelint: ">=13"
      typescript: "*"
      vite: ">=2.0.0"
      vls: "*"
      vti: "*"
      vue-tsc: "*"
    peerDependenciesMeta:
      eslint:
        optional: true
      meow:
        optional: true
      optionator:
        optional: true
      stylelint:
        optional: true
      typescript:
        optional: true
      vls:
        optional: true
      vti:
        optional: true
      vue-tsc:
        optional: true

  vite@4.5.14:
    resolution:
      {
        integrity: sha512-+v57oAaoYNnO3hIu5Z/tJRZjq5aHM2zDve9YZ8HngVHbhk66RStobhb1sqPMIPEleV6cNKYK4eGrAbE9Ulbl2g==,
      }
    engines: { node: ^14.18.0 || >=16.0.0 }
    hasBin: true
    peerDependencies:
      "@types/node": ">= 14"
      less: "*"
      lightningcss: ^1.21.0
      sass: "*"
      stylus: "*"
      sugarss: "*"
      terser: ^5.4.0
    peerDependenciesMeta:
      "@types/node":
        optional: true
      less:
        optional: true
      lightningcss:
        optional: true
      sass:
        optional: true
      stylus:
        optional: true
      sugarss:
        optional: true
      terser:
        optional: true

  vitest@0.29.8:
    resolution:
      {
        integrity: sha512-JIAVi2GK5cvA6awGpH0HvH/gEG9PZ0a/WoxdiV3PmqK+3CjQMf8c+J/Vhv4mdZ2nRyXFw66sAg6qz7VNkaHfDQ==,
      }
    engines: { node: ">=v14.16.0" }
    hasBin: true
    peerDependencies:
      "@edge-runtime/vm": "*"
      "@vitest/browser": "*"
      "@vitest/ui": "*"
      happy-dom: "*"
      jsdom: "*"
      playwright: "*"
      safaridriver: "*"
      webdriverio: "*"
    peerDependenciesMeta:
      "@edge-runtime/vm":
        optional: true
      "@vitest/browser":
        optional: true
      "@vitest/ui":
        optional: true
      happy-dom:
        optional: true
      jsdom:
        optional: true
      playwright:
        optional: true
      safaridriver:
        optional: true
      webdriverio:
        optional: true

  void-elements@3.1.0:
    resolution:
      {
        integrity: sha512-Dhxzh5HZuiHQhbvTW9AMetFfBHDMYpo23Uo9btPXgdYP+3T5S+p+jgNy7spra+veYhBP2dCSgxR/i2Y02h5/6w==,
      }
    engines: { node: ">=0.10.0" }

  vscode-jsonrpc@6.0.0:
    resolution:
      {
        integrity: sha512-wnJA4BnEjOSyFMvjZdpiOwhSq9uDoK8e/kpRJDTaMYzwlkrhG1fwDIZI94CLsLzlCK5cIbMMtFlJlfR57Lavmg==,
      }
    engines: { node: ">=8.0.0 || >=10.0.0" }

  vscode-languageclient@7.0.0:
    resolution:
      {
        integrity: sha512-P9AXdAPlsCgslpP9pRxYPqkNYV7Xq8300/aZDpO35j1fJm/ncize8iGswzYlcvFw5DQUx4eVk+KvfXdL0rehNg==,
      }
    engines: { vscode: ^1.52.0 }

  vscode-languageserver-protocol@3.16.0:
    resolution:
      {
        integrity: sha512-sdeUoAawceQdgIfTI+sdcwkiK2KU+2cbEYA0agzM2uqaUy2UpnnGHtWTHVEtS0ES4zHU0eMFRGN+oQgDxlD66A==,
      }

  vscode-languageserver-textdocument@1.0.11:
    resolution:
      {
        integrity: sha512-X+8T3GoiwTVlJbicx/sIAF+yuJAqz8VvwJyoMVhwEMoEKE/fkDmrqUgDMyBECcM2A2frVZIUj5HI/ErRXCfOeA==,
      }

  vscode-languageserver-types@3.16.0:
    resolution:
      {
        integrity: sha512-k8luDIWJWyenLc5ToFQQMaSrqCHiLwyKPHKPQZ5zz21vM+vIVUSvsRpcbiECH4WR88K2XZqc4ScRcZ7nk/jbeA==,
      }

  vscode-languageserver@7.0.0:
    resolution:
      {
        integrity: sha512-60HTx5ID+fLRcgdHfmz0LDZAXYEV68fzwG0JWwEPBode9NuMYTIxuYXPg4ngO8i8+Ou0lM7y6GzaYWbiDL0drw==,
      }
    hasBin: true

  vscode-uri@3.0.8:
    resolution:
      {
        integrity: sha512-AyFQ0EVmsOZOlAnxoFOGOq1SQDWAB7C6aqMGS23svWAllfOaxbuFvcT8D1i8z3Gyn8fraVeZNNmN6e9bxxXkKw==,
      }

  vue-eslint-parser@8.3.0:
    resolution:
      {
        integrity: sha512-dzHGG3+sYwSf6zFBa0Gi9ZDshD7+ad14DGOdTLjruRVgZXe2J+DcZ9iUhyR48z5g1PqRa20yt3Njna/veLJL/g==,
      }
    engines: { node: ^12.22.0 || ^14.17.0 || >=16.0.0 }
    peerDependencies:
      eslint: ">=6.0.0"

  w3c-xmlserializer@4.0.0:
    resolution:
      {
        integrity: sha512-d+BFHzbiCx6zGfz0HyQ6Rg69w9k19nviJspaj4yNscGjrHu94sVP+aRm75yEbCh+r2/yR+7q6hux9LVtbuTGBw==,
      }
    engines: { node: ">=14" }

  webidl-conversions@3.0.1:
    resolution:
      {
        integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==,
      }

  webidl-conversions@7.0.0:
    resolution:
      {
        integrity: sha512-VwddBukDzu71offAQR975unBIGqfKZpM+8ZX6ySk8nYhVoo5CYaZyzt3YBvYtRtO+aoGlqxPg/B87NGVZ/fu6g==,
      }
    engines: { node: ">=12" }

  whatwg-encoding@2.0.0:
    resolution:
      {
        integrity: sha512-p41ogyeMUrw3jWclHWTQg1k05DSVXPLcVxRTYsXUk+ZooOCZLcoYgPZ/HL/D/N+uQPOtcp1me1WhBEaX02mhWg==,
      }
    engines: { node: ">=12" }

  whatwg-mimetype@3.0.0:
    resolution:
      {
        integrity: sha512-nt+N2dzIutVRxARx1nghPKGv1xHikU7HKdfafKkLNLindmPU/ch3U31NOCGGA/dmPcmb1VlofO0vnKAcsm0o/Q==,
      }
    engines: { node: ">=12" }

  whatwg-url@12.0.1:
    resolution:
      {
        integrity: sha512-Ed/LrqB8EPlGxjS+TrsXcpUond1mhccS3pchLhzSgPCnTimUCKj3IZE75pAs5m6heB2U2TMerKFUXheyHY+VDQ==,
      }
    engines: { node: ">=14" }

  whatwg-url@5.0.0:
    resolution:
      {
        integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==,
      }

  which-boxed-primitive@1.0.2:
    resolution:
      {
        integrity: sha512-bwZdv0AKLpplFY2KZRX6TvyuN7ojjr7lwkg6ml0roIy9YeuSr7JS372qlNW18UQYzgYK9ziGcerWqZOmEn9VNg==,
      }

  which-builtin-type@1.1.3:
    resolution:
      {
        integrity: sha512-YmjsSMDBYsM1CaFiayOVT06+KJeXf0o5M/CAd4o1lTadFAtacTUM49zoYxr/oroopFDfhvN6iEcBxUyc3gvKmw==,
      }
    engines: { node: ">= 0.4" }

  which-collection@1.0.2:
    resolution:
      {
        integrity: sha512-K4jVyjnBdgvc86Y6BkaLZEN933SwYOuBFkdmBu9ZfkcAbdVbpITnDmjvZ/aQjRXQrv5EPkTnD1s39GiiqbngCw==,
      }
    engines: { node: ">= 0.4" }

  which-typed-array@1.1.15:
    resolution:
      {
        integrity: sha512-oV0jmFtUky6CXfkqehVvBP/LSWJ2sy4vWMioiENyJLePrBO/yKyV9OyJySfAKosh+RYkIl5zJCNZ8/4JncrpdA==,
      }
    engines: { node: ">= 0.4" }

  which@1.3.1:
    resolution:
      {
        integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==,
      }
    hasBin: true

  which@2.0.2:
    resolution:
      {
        integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==,
      }
    engines: { node: ">= 8" }
    hasBin: true

  why-is-node-running@2.2.2:
    resolution:
      {
        integrity: sha512-6tSwToZxTOcotxHeA+qGCq1mVzKR3CwcJGmVcY+QE8SHy6TnpFnh8PAvPNHYr7EcuVeG0QSMxtYCuO1ta/G/oA==,
      }
    engines: { node: ">=8" }
    hasBin: true

  word-wrap@1.2.5:
    resolution:
      {
        integrity: sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA==,
      }
    engines: { node: ">=0.10.0" }

  wrap-ansi@7.0.0:
    resolution:
      {
        integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==,
      }
    engines: { node: ">=10" }

  wrap-ansi@8.1.0:
    resolution:
      {
        integrity: sha512-si7QWI6zUMq56bESFvagtmzMdGOtoxfR+Sez11Mobfc7tm+VkUckk9bW2UeffTGVUbOksxmSw0AA2gs8g71NCQ==,
      }
    engines: { node: ">=12" }

  wrappy@1.0.2:
    resolution:
      {
        integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==,
      }

  ws@8.17.0:
    resolution:
      {
        integrity: sha512-uJq6108EgZMAl20KagGkzCKfMEjxmKvZHG7Tlq0Z6nOky7YF7aq4mOx6xK8TJ/i1LeK4Qus7INktacctDgY8Ow==,
      }
    engines: { node: ">=10.0.0" }
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ">=5.0.2"
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true

  xlsx@file:vendor/xlsx-0.20.3.tgz:
    resolution:
      {
        integrity: sha512-oLDq3jw7AcLqKWH2AhCpVTZl8mf6X2YReP+Neh0SJUzV/BdZYjth94tG5toiMB1PPrYtxOCfaoUCkvtuH+3AJA==,
        tarball: file:vendor/xlsx-0.20.3.tgz,
      }
    version: 0.20.3
    engines: { node: ">=0.8" }
    hasBin: true

  xml-name-validator@4.0.0:
    resolution:
      {
        integrity: sha512-ICP2e+jsHvAj2E2lIHxa5tjXRlKDJo4IdvPvCXbXQGdzSfmSpNVyIKMvoZHjDY9DP0zV17iI85o90vRFXNccRw==,
      }
    engines: { node: ">=12" }

  xmlchars@2.2.0:
    resolution:
      {
        integrity: sha512-JZnDKK8B0RCDw84FNdDAIpZK+JuJw+s7Lz8nksI7SIuU3UXJJslUthsi+uWBUYOwPFwW7W7PRLRfUKpxjtjFCw==,
      }

  y18n@5.0.8:
    resolution:
      {
        integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==,
      }
    engines: { node: ">=10" }

  yallist@3.1.1:
    resolution:
      {
        integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==,
      }

  yallist@4.0.0:
    resolution:
      {
        integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==,
      }

  yaml@1.10.2:
    resolution:
      {
        integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==,
      }
    engines: { node: ">= 6" }

  yaml@2.3.1:
    resolution:
      {
        integrity: sha512-2eHWfjaoXgTBC2jNM1LRef62VQa0umtvRiDSk6HSzW7RvS5YtkabJrwYLLEKWBc8a5U2PTSCs+dJjUTJdlHsWQ==,
      }
    engines: { node: ">= 14" }

  yaml@2.4.2:
    resolution:
      {
        integrity: sha512-B3VqDZ+JAg1nZpaEmWtTXUlBneoGx6CPM9b0TENK6aoSu5t73dItudwdgmi6tHlIZZId4dZ9skcAQ2UbcyAeVA==,
      }
    engines: { node: ">= 14" }
    hasBin: true

  yargs-parser@20.2.9:
    resolution:
      {
        integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==,
      }
    engines: { node: ">=10" }

  yargs-parser@21.1.1:
    resolution:
      {
        integrity: sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==,
      }
    engines: { node: ">=12" }

  yargs@16.2.0:
    resolution:
      {
        integrity: sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==,
      }
    engines: { node: ">=10" }

  yargs@17.7.2:
    resolution:
      {
        integrity: sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==,
      }
    engines: { node: ">=12" }

  yn@3.1.1:
    resolution:
      {
        integrity: sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==,
      }
    engines: { node: ">=6" }

  yocto-queue@0.1.0:
    resolution:
      {
        integrity: sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==,
      }
    engines: { node: ">=10" }

  yocto-queue@1.0.0:
    resolution:
      {
        integrity: sha512-9bnSc/HEW2uRy67wc+T8UwauLuPJVn28jb+GtJY16iiKWyvmYJRXVT4UamsAEGQfPohgr2q4Tq0sQbQlxTfi1g==,
      }
    engines: { node: ">=12.20" }

  yoctodelay@1.2.0:
    resolution:
      {
        integrity: sha512-12y/P9MSig9/5BEhBgylss+fkHiCRZCvYR81eH35NW9uw801cvJt31EAV+WOLcwZRZbLiIQl/hxcdXXXFmGvXg==,
      }
    engines: { node: ">=4" }

  yup@1.4.0:
    resolution:
      {
        integrity: sha512-wPbgkJRCqIf+OHyiTBQoJiP5PFuAXaWiJK6AmYkzQAh5/c2K9hzSApBZG5wV9KoKSePF7sAxmNSvh/13YHkFDg==,
      }

snapshots:
  "@alloc/quick-lru@5.2.0": {}

  "@ampproject/remapping@2.3.0":
    dependencies:
      "@jridgewell/gen-mapping": 0.3.5
      "@jridgewell/trace-mapping": 0.3.25

  "@babel/code-frame@7.24.2":
    dependencies:
      "@babel/highlight": 7.24.5
      picocolors: 1.0.1

  "@babel/compat-data@7.24.4": {}

  "@babel/core@7.24.5":
    dependencies:
      "@ampproject/remapping": 2.3.0
      "@babel/code-frame": 7.24.2
      "@babel/generator": 7.24.5
      "@babel/helper-compilation-targets": 7.23.6
      "@babel/helper-module-transforms": 7.24.5(@babel/core@7.24.5)
      "@babel/helpers": 7.24.5
      "@babel/parser": 7.24.5
      "@babel/template": 7.24.0
      "@babel/traverse": 7.24.5
      "@babel/types": 7.24.5
      convert-source-map: 2.0.0
      debug: 4.3.4
      gensync: 1.0.0-beta.2
      json5: 2.2.3
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  "@babel/generator@7.24.5":
    dependencies:
      "@babel/types": 7.24.5
      "@jridgewell/gen-mapping": 0.3.5
      "@jridgewell/trace-mapping": 0.3.25
      jsesc: 2.5.2

  "@babel/helper-annotate-as-pure@7.22.5":
    dependencies:
      "@babel/types": 7.24.5

  "@babel/helper-builder-binary-assignment-operator-visitor@7.22.15":
    dependencies:
      "@babel/types": 7.24.5

  "@babel/helper-compilation-targets@7.23.6":
    dependencies:
      "@babel/compat-data": 7.24.4
      "@babel/helper-validator-option": 7.23.5
      browserslist: 4.23.0
      lru-cache: 5.1.1
      semver: 6.3.1

  "@babel/helper-create-class-features-plugin@7.24.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-annotate-as-pure": 7.22.5
      "@babel/helper-environment-visitor": 7.22.20
      "@babel/helper-function-name": 7.23.0
      "@babel/helper-member-expression-to-functions": 7.24.5
      "@babel/helper-optimise-call-expression": 7.22.5
      "@babel/helper-replace-supers": 7.24.1(@babel/core@7.24.5)
      "@babel/helper-skip-transparent-expression-wrappers": 7.22.5
      "@babel/helper-split-export-declaration": 7.24.5
      semver: 6.3.1

  "@babel/helper-create-regexp-features-plugin@7.22.15(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-annotate-as-pure": 7.22.5
      regexpu-core: 5.3.2
      semver: 6.3.1

  "@babel/helper-define-polyfill-provider@0.6.2(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-compilation-targets": 7.23.6
      "@babel/helper-plugin-utils": 7.24.5
      debug: 4.3.4
      lodash.debounce: 4.0.8
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color

  "@babel/helper-environment-visitor@7.22.20": {}

  "@babel/helper-function-name@7.23.0":
    dependencies:
      "@babel/template": 7.24.0
      "@babel/types": 7.24.5

  "@babel/helper-hoist-variables@7.22.5":
    dependencies:
      "@babel/types": 7.24.5

  "@babel/helper-member-expression-to-functions@7.24.5":
    dependencies:
      "@babel/types": 7.24.5

  "@babel/helper-module-imports@7.24.3":
    dependencies:
      "@babel/types": 7.24.5

  "@babel/helper-module-transforms@7.24.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-environment-visitor": 7.22.20
      "@babel/helper-module-imports": 7.24.3
      "@babel/helper-simple-access": 7.24.5
      "@babel/helper-split-export-declaration": 7.24.5
      "@babel/helper-validator-identifier": 7.24.5

  "@babel/helper-optimise-call-expression@7.22.5":
    dependencies:
      "@babel/types": 7.24.5

  "@babel/helper-plugin-utils@7.24.5": {}

  "@babel/helper-remap-async-to-generator@7.22.20(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-annotate-as-pure": 7.22.5
      "@babel/helper-environment-visitor": 7.22.20
      "@babel/helper-wrap-function": 7.24.5

  "@babel/helper-replace-supers@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-environment-visitor": 7.22.20
      "@babel/helper-member-expression-to-functions": 7.24.5
      "@babel/helper-optimise-call-expression": 7.22.5

  "@babel/helper-simple-access@7.24.5":
    dependencies:
      "@babel/types": 7.24.5

  "@babel/helper-skip-transparent-expression-wrappers@7.22.5":
    dependencies:
      "@babel/types": 7.24.5

  "@babel/helper-split-export-declaration@7.24.5":
    dependencies:
      "@babel/types": 7.24.5

  "@babel/helper-string-parser@7.24.1": {}

  "@babel/helper-validator-identifier@7.24.5": {}

  "@babel/helper-validator-option@7.23.5": {}

  "@babel/helper-wrap-function@7.24.5":
    dependencies:
      "@babel/helper-function-name": 7.23.0
      "@babel/template": 7.24.0
      "@babel/types": 7.24.5

  "@babel/helpers@7.24.5":
    dependencies:
      "@babel/template": 7.24.0
      "@babel/traverse": 7.24.5
      "@babel/types": 7.24.5
    transitivePeerDependencies:
      - supports-color

  "@babel/highlight@7.24.5":
    dependencies:
      "@babel/helper-validator-identifier": 7.24.5
      chalk: 2.4.2
      js-tokens: 4.0.0
      picocolors: 1.0.1

  "@babel/parser@7.24.5":
    dependencies:
      "@babel/types": 7.24.5

  "@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.24.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-environment-visitor": 7.22.20
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/helper-skip-transparent-expression-wrappers": 7.22.5
      "@babel/plugin-transform-optional-chaining": 7.24.5(@babel/core@7.24.5)

  "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-environment-visitor": 7.22.20
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5

  "@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-export-namespace-from@7.8.3(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-import-assertions@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-import-attributes@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-jsx@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-typescript@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-create-regexp-features-plugin": 7.22.15(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-arrow-functions@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-async-generator-functions@7.24.3(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-environment-visitor": 7.22.20
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/helper-remap-async-to-generator": 7.22.20(@babel/core@7.24.5)
      "@babel/plugin-syntax-async-generators": 7.8.4(@babel/core@7.24.5)

  "@babel/plugin-transform-async-to-generator@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-module-imports": 7.24.3
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/helper-remap-async-to-generator": 7.22.20(@babel/core@7.24.5)

  "@babel/plugin-transform-block-scoped-functions@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-block-scoping@7.24.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-class-properties@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-create-class-features-plugin": 7.24.5(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-class-static-block@7.24.4(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-create-class-features-plugin": 7.24.5(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/plugin-syntax-class-static-block": 7.14.5(@babel/core@7.24.5)

  "@babel/plugin-transform-classes@7.24.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-annotate-as-pure": 7.22.5
      "@babel/helper-compilation-targets": 7.23.6
      "@babel/helper-environment-visitor": 7.22.20
      "@babel/helper-function-name": 7.23.0
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/helper-replace-supers": 7.24.1(@babel/core@7.24.5)
      "@babel/helper-split-export-declaration": 7.24.5
      globals: 11.12.0

  "@babel/plugin-transform-computed-properties@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/template": 7.24.0

  "@babel/plugin-transform-destructuring@7.24.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-dotall-regex@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-create-regexp-features-plugin": 7.22.15(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-duplicate-keys@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-dynamic-import@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/plugin-syntax-dynamic-import": 7.8.3(@babel/core@7.24.5)

  "@babel/plugin-transform-exponentiation-operator@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-builder-binary-assignment-operator-visitor": 7.22.15
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-export-namespace-from@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/plugin-syntax-export-namespace-from": 7.8.3(@babel/core@7.24.5)

  "@babel/plugin-transform-for-of@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/helper-skip-transparent-expression-wrappers": 7.22.5

  "@babel/plugin-transform-function-name@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-compilation-targets": 7.23.6
      "@babel/helper-function-name": 7.23.0
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-json-strings@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/plugin-syntax-json-strings": 7.8.3(@babel/core@7.24.5)

  "@babel/plugin-transform-literals@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-logical-assignment-operators@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/plugin-syntax-logical-assignment-operators": 7.10.4(@babel/core@7.24.5)

  "@babel/plugin-transform-member-expression-literals@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-modules-amd@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-module-transforms": 7.24.5(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-modules-commonjs@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-module-transforms": 7.24.5(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/helper-simple-access": 7.24.5

  "@babel/plugin-transform-modules-systemjs@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-hoist-variables": 7.22.5
      "@babel/helper-module-transforms": 7.24.5(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/helper-validator-identifier": 7.24.5

  "@babel/plugin-transform-modules-umd@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-module-transforms": 7.24.5(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-named-capturing-groups-regex@7.22.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-create-regexp-features-plugin": 7.22.15(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-new-target@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-nullish-coalescing-operator@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/plugin-syntax-nullish-coalescing-operator": 7.8.3(@babel/core@7.24.5)

  "@babel/plugin-transform-numeric-separator@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/plugin-syntax-numeric-separator": 7.10.4(@babel/core@7.24.5)

  "@babel/plugin-transform-object-rest-spread@7.24.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-compilation-targets": 7.23.6
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/plugin-syntax-object-rest-spread": 7.8.3(@babel/core@7.24.5)
      "@babel/plugin-transform-parameters": 7.24.5(@babel/core@7.24.5)

  "@babel/plugin-transform-object-super@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/helper-replace-supers": 7.24.1(@babel/core@7.24.5)

  "@babel/plugin-transform-optional-catch-binding@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/plugin-syntax-optional-catch-binding": 7.8.3(@babel/core@7.24.5)

  "@babel/plugin-transform-optional-chaining@7.24.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/helper-skip-transparent-expression-wrappers": 7.22.5
      "@babel/plugin-syntax-optional-chaining": 7.8.3(@babel/core@7.24.5)

  "@babel/plugin-transform-parameters@7.24.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-private-methods@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-create-class-features-plugin": 7.24.5(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-private-property-in-object@7.24.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-annotate-as-pure": 7.22.5
      "@babel/helper-create-class-features-plugin": 7.24.5(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/plugin-syntax-private-property-in-object": 7.14.5(@babel/core@7.24.5)

  "@babel/plugin-transform-property-literals@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-react-constant-elements@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-react-display-name@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-react-jsx-development@7.22.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/plugin-transform-react-jsx": 7.23.4(@babel/core@7.24.5)

  "@babel/plugin-transform-react-jsx-self@7.24.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-react-jsx-source@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-react-jsx@7.23.4(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-annotate-as-pure": 7.22.5
      "@babel/helper-module-imports": 7.24.3
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/plugin-syntax-jsx": 7.24.1(@babel/core@7.24.5)
      "@babel/types": 7.24.5

  "@babel/plugin-transform-react-pure-annotations@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-annotate-as-pure": 7.22.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-regenerator@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      regenerator-transform: 0.15.2

  "@babel/plugin-transform-reserved-words@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-shorthand-properties@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-spread@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/helper-skip-transparent-expression-wrappers": 7.22.5

  "@babel/plugin-transform-sticky-regex@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-template-literals@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-typeof-symbol@7.24.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-typescript@7.24.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-annotate-as-pure": 7.22.5
      "@babel/helper-create-class-features-plugin": 7.24.5(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/plugin-syntax-typescript": 7.24.1(@babel/core@7.24.5)

  "@babel/plugin-transform-unicode-escapes@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-unicode-property-regex@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-create-regexp-features-plugin": 7.22.15(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-unicode-regex@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-create-regexp-features-plugin": 7.22.15(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/plugin-transform-unicode-sets-regex@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-create-regexp-features-plugin": 7.22.15(@babel/core@7.24.5)
      "@babel/helper-plugin-utils": 7.24.5

  "@babel/preset-env@7.24.5(@babel/core@7.24.5)":
    dependencies:
      "@babel/compat-data": 7.24.4
      "@babel/core": 7.24.5
      "@babel/helper-compilation-targets": 7.23.6
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/helper-validator-option": 7.23.5
      "@babel/plugin-bugfix-firefox-class-in-computed-class-key": 7.24.5(@babel/core@7.24.5)
      "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-proposal-private-property-in-object": 7.21.0-placeholder-for-preset-env.2(@babel/core@7.24.5)
      "@babel/plugin-syntax-async-generators": 7.8.4(@babel/core@7.24.5)
      "@babel/plugin-syntax-class-properties": 7.12.13(@babel/core@7.24.5)
      "@babel/plugin-syntax-class-static-block": 7.14.5(@babel/core@7.24.5)
      "@babel/plugin-syntax-dynamic-import": 7.8.3(@babel/core@7.24.5)
      "@babel/plugin-syntax-export-namespace-from": 7.8.3(@babel/core@7.24.5)
      "@babel/plugin-syntax-import-assertions": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-syntax-import-attributes": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-syntax-import-meta": 7.10.4(@babel/core@7.24.5)
      "@babel/plugin-syntax-json-strings": 7.8.3(@babel/core@7.24.5)
      "@babel/plugin-syntax-logical-assignment-operators": 7.10.4(@babel/core@7.24.5)
      "@babel/plugin-syntax-nullish-coalescing-operator": 7.8.3(@babel/core@7.24.5)
      "@babel/plugin-syntax-numeric-separator": 7.10.4(@babel/core@7.24.5)
      "@babel/plugin-syntax-object-rest-spread": 7.8.3(@babel/core@7.24.5)
      "@babel/plugin-syntax-optional-catch-binding": 7.8.3(@babel/core@7.24.5)
      "@babel/plugin-syntax-optional-chaining": 7.8.3(@babel/core@7.24.5)
      "@babel/plugin-syntax-private-property-in-object": 7.14.5(@babel/core@7.24.5)
      "@babel/plugin-syntax-top-level-await": 7.14.5(@babel/core@7.24.5)
      "@babel/plugin-syntax-unicode-sets-regex": 7.18.6(@babel/core@7.24.5)
      "@babel/plugin-transform-arrow-functions": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-async-generator-functions": 7.24.3(@babel/core@7.24.5)
      "@babel/plugin-transform-async-to-generator": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-block-scoped-functions": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-block-scoping": 7.24.5(@babel/core@7.24.5)
      "@babel/plugin-transform-class-properties": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-class-static-block": 7.24.4(@babel/core@7.24.5)
      "@babel/plugin-transform-classes": 7.24.5(@babel/core@7.24.5)
      "@babel/plugin-transform-computed-properties": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-destructuring": 7.24.5(@babel/core@7.24.5)
      "@babel/plugin-transform-dotall-regex": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-duplicate-keys": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-dynamic-import": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-exponentiation-operator": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-export-namespace-from": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-for-of": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-function-name": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-json-strings": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-literals": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-logical-assignment-operators": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-member-expression-literals": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-modules-amd": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-modules-commonjs": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-modules-systemjs": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-modules-umd": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-named-capturing-groups-regex": 7.22.5(@babel/core@7.24.5)
      "@babel/plugin-transform-new-target": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-nullish-coalescing-operator": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-numeric-separator": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-object-rest-spread": 7.24.5(@babel/core@7.24.5)
      "@babel/plugin-transform-object-super": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-optional-catch-binding": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-optional-chaining": 7.24.5(@babel/core@7.24.5)
      "@babel/plugin-transform-parameters": 7.24.5(@babel/core@7.24.5)
      "@babel/plugin-transform-private-methods": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-private-property-in-object": 7.24.5(@babel/core@7.24.5)
      "@babel/plugin-transform-property-literals": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-regenerator": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-reserved-words": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-shorthand-properties": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-spread": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-sticky-regex": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-template-literals": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-typeof-symbol": 7.24.5(@babel/core@7.24.5)
      "@babel/plugin-transform-unicode-escapes": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-unicode-property-regex": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-unicode-regex": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-unicode-sets-regex": 7.24.1(@babel/core@7.24.5)
      "@babel/preset-modules": 0.1.6-no-external-plugins(@babel/core@7.24.5)
      babel-plugin-polyfill-corejs2: 0.4.11(@babel/core@7.24.5)
      babel-plugin-polyfill-corejs3: 0.10.4(@babel/core@7.24.5)
      babel-plugin-polyfill-regenerator: 0.6.2(@babel/core@7.24.5)
      core-js-compat: 3.37.1
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  "@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/types": 7.24.5
      esutils: 2.0.3

  "@babel/preset-react@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/helper-validator-option": 7.23.5
      "@babel/plugin-transform-react-display-name": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-react-jsx": 7.23.4(@babel/core@7.24.5)
      "@babel/plugin-transform-react-jsx-development": 7.22.5(@babel/core@7.24.5)
      "@babel/plugin-transform-react-pure-annotations": 7.24.1(@babel/core@7.24.5)

  "@babel/preset-typescript@7.24.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-plugin-utils": 7.24.5
      "@babel/helper-validator-option": 7.23.5
      "@babel/plugin-syntax-jsx": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-modules-commonjs": 7.24.1(@babel/core@7.24.5)
      "@babel/plugin-transform-typescript": 7.24.5(@babel/core@7.24.5)

  "@babel/regjsgen@0.8.0": {}

  "@babel/runtime@7.24.5":
    dependencies:
      regenerator-runtime: 0.14.1

  "@babel/runtime@7.25.0":
    dependencies:
      regenerator-runtime: 0.14.1

  "@babel/template@7.24.0":
    dependencies:
      "@babel/code-frame": 7.24.2
      "@babel/parser": 7.24.5
      "@babel/types": 7.24.5

  "@babel/traverse@7.24.5":
    dependencies:
      "@babel/code-frame": 7.24.2
      "@babel/generator": 7.24.5
      "@babel/helper-environment-visitor": 7.22.20
      "@babel/helper-function-name": 7.23.0
      "@babel/helper-hoist-variables": 7.22.5
      "@babel/helper-split-export-declaration": 7.24.5
      "@babel/parser": 7.24.5
      "@babel/types": 7.24.5
      debug: 4.3.4
      globals: 11.12.0
    transitivePeerDependencies:
      - supports-color

  "@babel/types@7.24.5":
    dependencies:
      "@babel/helper-string-parser": 7.24.1
      "@babel/helper-validator-identifier": 7.24.5
      to-fast-properties: 2.0.0

  "@bcoe/v8-coverage@0.2.3": {}

  "@commitlint/cli@17.8.1":
    dependencies:
      "@commitlint/format": 17.8.1
      "@commitlint/lint": 17.8.1
      "@commitlint/load": 17.8.1
      "@commitlint/read": 17.8.1
      "@commitlint/types": 17.8.1
      execa: 5.1.1
      lodash.isfunction: 3.0.9
      resolve-from: 5.0.0
      resolve-global: 1.0.0
      yargs: 17.7.2
    transitivePeerDependencies:
      - "@swc/core"
      - "@swc/wasm"

  "@commitlint/config-conventional@17.8.1":
    dependencies:
      conventional-changelog-conventionalcommits: 6.1.0

  "@commitlint/config-validator@17.8.1":
    dependencies:
      "@commitlint/types": 17.8.1
      ajv: 8.13.0

  "@commitlint/ensure@17.8.1":
    dependencies:
      "@commitlint/types": 17.8.1
      lodash.camelcase: 4.3.0
      lodash.kebabcase: 4.1.1
      lodash.snakecase: 4.1.1
      lodash.startcase: 4.4.0
      lodash.upperfirst: 4.3.1

  "@commitlint/execute-rule@17.8.1": {}

  "@commitlint/format@17.8.1":
    dependencies:
      "@commitlint/types": 17.8.1
      chalk: 4.1.2

  "@commitlint/is-ignored@17.8.1":
    dependencies:
      "@commitlint/types": 17.8.1
      semver: 7.5.4

  "@commitlint/lint@17.8.1":
    dependencies:
      "@commitlint/is-ignored": 17.8.1
      "@commitlint/parse": 17.8.1
      "@commitlint/rules": 17.8.1
      "@commitlint/types": 17.8.1

  "@commitlint/load@17.8.1":
    dependencies:
      "@commitlint/config-validator": 17.8.1
      "@commitlint/execute-rule": 17.8.1
      "@commitlint/resolve-extends": 17.8.1
      "@commitlint/types": 17.8.1
      "@types/node": 20.5.1
      chalk: 4.1.2
      cosmiconfig: 8.3.6(typescript@4.9.5)
      cosmiconfig-typescript-loader: 4.4.0(@types/node@20.5.1)(cosmiconfig@8.3.6(typescript@4.9.5))(ts-node@10.9.2(@types/node@20.5.1)(typescript@4.9.5))(typescript@4.9.5)
      lodash.isplainobject: 4.0.6
      lodash.merge: 4.6.2
      lodash.uniq: 4.5.0
      resolve-from: 5.0.0
      ts-node: 10.9.2(@types/node@20.5.1)(typescript@4.9.5)
      typescript: 4.9.5
    transitivePeerDependencies:
      - "@swc/core"
      - "@swc/wasm"

  "@commitlint/message@17.8.1": {}

  "@commitlint/parse@17.8.1":
    dependencies:
      "@commitlint/types": 17.8.1
      conventional-changelog-angular: 6.0.0
      conventional-commits-parser: 4.0.0

  "@commitlint/read@17.8.1":
    dependencies:
      "@commitlint/top-level": 17.8.1
      "@commitlint/types": 17.8.1
      fs-extra: 11.2.0
      git-raw-commits: 2.0.11
      minimist: 1.2.8

  "@commitlint/resolve-extends@17.8.1":
    dependencies:
      "@commitlint/config-validator": 17.8.1
      "@commitlint/types": 17.8.1
      import-fresh: 3.3.0
      lodash.mergewith: 4.6.2
      resolve-from: 5.0.0
      resolve-global: 1.0.0

  "@commitlint/rules@17.8.1":
    dependencies:
      "@commitlint/ensure": 17.8.1
      "@commitlint/message": 17.8.1
      "@commitlint/to-lines": 17.8.1
      "@commitlint/types": 17.8.1
      execa: 5.1.1

  "@commitlint/to-lines@17.8.1": {}

  "@commitlint/top-level@17.8.1":
    dependencies:
      find-up: 5.0.0

  "@commitlint/types@17.8.1":
    dependencies:
      chalk: 4.1.2

  "@cspotcode/source-map-support@0.8.1":
    dependencies:
      "@jridgewell/trace-mapping": 0.3.9

  "@emotion/babel-plugin-jsx-pragmatic@0.2.1(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/plugin-syntax-jsx": 7.24.1(@babel/core@7.24.5)

  "@emotion/babel-plugin@11.11.0":
    dependencies:
      "@babel/helper-module-imports": 7.24.3
      "@babel/runtime": 7.25.0
      "@emotion/hash": 0.9.1
      "@emotion/memoize": 0.8.1
      "@emotion/serialize": 1.1.4
      babel-plugin-macros: 3.1.0
      convert-source-map: 1.9.0
      escape-string-regexp: 4.0.0
      find-root: 1.1.0
      source-map: 0.5.7
      stylis: 4.2.0

  "@emotion/cache@11.11.0":
    dependencies:
      "@emotion/memoize": 0.8.1
      "@emotion/sheet": 1.2.2
      "@emotion/utils": 1.2.1
      "@emotion/weak-memoize": 0.3.1
      stylis: 4.2.0

  "@emotion/hash@0.9.1": {}

  "@emotion/is-prop-valid@1.2.2":
    dependencies:
      "@emotion/memoize": 0.8.1

  "@emotion/memoize@0.8.1": {}

  "@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1)":
    dependencies:
      "@babel/runtime": 7.25.0
      "@emotion/babel-plugin": 11.11.0
      "@emotion/cache": 11.11.0
      "@emotion/serialize": 1.1.4
      "@emotion/use-insertion-effect-with-fallbacks": 1.0.1(react@18.3.1)
      "@emotion/utils": 1.2.1
      "@emotion/weak-memoize": 0.3.1
      hoist-non-react-statics: 3.3.2
      react: 18.3.1
    optionalDependencies:
      "@types/react": 18.3.2

  "@emotion/serialize@1.1.4":
    dependencies:
      "@emotion/hash": 0.9.1
      "@emotion/memoize": 0.8.1
      "@emotion/unitless": 0.8.1
      "@emotion/utils": 1.2.1
      csstype: 3.1.3

  "@emotion/sheet@1.2.2": {}

  "@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1)":
    dependencies:
      "@babel/runtime": 7.25.0
      "@emotion/babel-plugin": 11.11.0
      "@emotion/is-prop-valid": 1.2.2
      "@emotion/react": 11.11.4(@types/react@18.3.2)(react@18.3.1)
      "@emotion/serialize": 1.1.4
      "@emotion/use-insertion-effect-with-fallbacks": 1.0.1(react@18.3.1)
      "@emotion/utils": 1.2.1
      react: 18.3.1
    optionalDependencies:
      "@types/react": 18.3.2

  "@emotion/unitless@0.8.1": {}

  "@emotion/use-insertion-effect-with-fallbacks@1.0.1(react@18.3.1)":
    dependencies:
      react: 18.3.1

  "@emotion/utils@1.2.1": {}

  "@emotion/weak-memoize@0.3.1": {}

  "@esbuild/aix-ppc64@0.25.9":
    optional: true

  "@esbuild/android-arm64@0.18.20":
    optional: true

  "@esbuild/android-arm64@0.25.9":
    optional: true

  "@esbuild/android-arm@0.18.20":
    optional: true

  "@esbuild/android-arm@0.25.9":
    optional: true

  "@esbuild/android-x64@0.18.20":
    optional: true

  "@esbuild/android-x64@0.25.9":
    optional: true

  "@esbuild/darwin-arm64@0.18.20":
    optional: true

  "@esbuild/darwin-arm64@0.25.9":
    optional: true

  "@esbuild/darwin-x64@0.18.20":
    optional: true

  "@esbuild/darwin-x64@0.25.9":
    optional: true

  "@esbuild/freebsd-arm64@0.18.20":
    optional: true

  "@esbuild/freebsd-arm64@0.25.9":
    optional: true

  "@esbuild/freebsd-x64@0.18.20":
    optional: true

  "@esbuild/freebsd-x64@0.25.9":
    optional: true

  "@esbuild/linux-arm64@0.18.20":
    optional: true

  "@esbuild/linux-arm64@0.25.9":
    optional: true

  "@esbuild/linux-arm@0.18.20":
    optional: true

  "@esbuild/linux-arm@0.25.9":
    optional: true

  "@esbuild/linux-ia32@0.18.20":
    optional: true

  "@esbuild/linux-ia32@0.25.9":
    optional: true

  "@esbuild/linux-loong64@0.18.20":
    optional: true

  "@esbuild/linux-loong64@0.25.9":
    optional: true

  "@esbuild/linux-mips64el@0.18.20":
    optional: true

  "@esbuild/linux-mips64el@0.25.9":
    optional: true

  "@esbuild/linux-ppc64@0.18.20":
    optional: true

  "@esbuild/linux-ppc64@0.25.9":
    optional: true

  "@esbuild/linux-riscv64@0.18.20":
    optional: true

  "@esbuild/linux-riscv64@0.25.9":
    optional: true

  "@esbuild/linux-s390x@0.18.20":
    optional: true

  "@esbuild/linux-s390x@0.25.9":
    optional: true

  "@esbuild/linux-x64@0.18.20":
    optional: true

  "@esbuild/linux-x64@0.25.9":
    optional: true

  "@esbuild/netbsd-arm64@0.25.9":
    optional: true

  "@esbuild/netbsd-x64@0.18.20":
    optional: true

  "@esbuild/netbsd-x64@0.25.9":
    optional: true

  "@esbuild/openbsd-arm64@0.25.9":
    optional: true

  "@esbuild/openbsd-x64@0.18.20":
    optional: true

  "@esbuild/openbsd-x64@0.25.9":
    optional: true

  "@esbuild/openharmony-arm64@0.25.9":
    optional: true

  "@esbuild/sunos-x64@0.18.20":
    optional: true

  "@esbuild/sunos-x64@0.25.9":
    optional: true

  "@esbuild/win32-arm64@0.18.20":
    optional: true

  "@esbuild/win32-arm64@0.25.9":
    optional: true

  "@esbuild/win32-ia32@0.18.20":
    optional: true

  "@esbuild/win32-ia32@0.25.9":
    optional: true

  "@esbuild/win32-x64@0.18.20":
    optional: true

  "@esbuild/win32-x64@0.25.9":
    optional: true

  "@eslint-community/eslint-utils@4.4.0(eslint@8.57.0)":
    dependencies:
      eslint: 8.57.0
      eslint-visitor-keys: 3.4.3

  "@eslint-community/regexpp@4.10.0": {}

  "@eslint/eslintrc@2.1.4":
    dependencies:
      ajv: 6.12.6
      debug: 4.3.4
      espree: 9.6.1
      globals: 13.24.0
      ignore: 5.3.1
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      minimatch: 3.1.2
      strip-json-comments: 3.1.1
    transitivePeerDependencies:
      - supports-color

  "@eslint/js@8.57.0": {}

  "@humanwhocodes/config-array@0.11.14":
    dependencies:
      "@humanwhocodes/object-schema": 2.0.3
      debug: 4.3.4
      minimatch: 3.1.2
    transitivePeerDependencies:
      - supports-color

  "@humanwhocodes/module-importer@1.0.1": {}

  "@humanwhocodes/object-schema@2.0.3": {}

  "@isaacs/cliui@8.0.2":
    dependencies:
      string-width: 5.1.2
      string-width-cjs: string-width@4.2.3
      strip-ansi: 7.1.0
      strip-ansi-cjs: strip-ansi@6.0.1
      wrap-ansi: 8.1.0
      wrap-ansi-cjs: wrap-ansi@7.0.0

  "@istanbuljs/schema@0.1.3": {}

  "@jest/types@24.9.0":
    dependencies:
      "@types/istanbul-lib-coverage": 2.0.6
      "@types/istanbul-reports": 1.1.2
      "@types/yargs": 13.0.12

  "@jridgewell/gen-mapping@0.3.5":
    dependencies:
      "@jridgewell/set-array": 1.2.1
      "@jridgewell/sourcemap-codec": 1.4.15
      "@jridgewell/trace-mapping": 0.3.25

  "@jridgewell/resolve-uri@3.1.2": {}

  "@jridgewell/set-array@1.2.1": {}

  "@jridgewell/sourcemap-codec@1.4.15": {}

  "@jridgewell/trace-mapping@0.3.25":
    dependencies:
      "@jridgewell/resolve-uri": 3.1.2
      "@jridgewell/sourcemap-codec": 1.4.15

  "@jridgewell/trace-mapping@0.3.9":
    dependencies:
      "@jridgewell/resolve-uri": 3.1.2
      "@jridgewell/sourcemap-codec": 1.4.15

  "@loadable/component@5.16.4(react@18.3.1)":
    dependencies:
      "@babel/runtime": 7.25.0
      hoist-non-react-statics: 3.3.2
      react: 18.3.1
      react-is: 16.13.1

  "@mui/core-downloads-tracker@5.16.7": {}

  "@mui/icons-material@5.16.7(@mui/material@5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@types/react@18.3.2)(react@18.3.1)":
    dependencies:
      "@babel/runtime": 7.25.0
      "@mui/material": 5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
    optionalDependencies:
      "@types/react": 18.3.2

  "@mui/material@5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
    dependencies:
      "@babel/runtime": 7.25.0
      "@mui/core-downloads-tracker": 5.16.7
      "@mui/system": 5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1)
      "@mui/types": 7.2.15(@types/react@18.3.2)
      "@mui/utils": 5.16.6(@types/react@18.3.2)(react@18.3.1)
      "@popperjs/core": 2.11.8
      "@types/react-transition-group": 4.4.11
      clsx: 2.1.1
      csstype: 3.1.3
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-is: 18.3.1
      react-transition-group: 4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
    optionalDependencies:
      "@emotion/react": 11.11.4(@types/react@18.3.2)(react@18.3.1)
      "@emotion/styled": 11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1)
      "@types/react": 18.3.2

  "@mui/private-theming@5.16.6(@types/react@18.3.2)(react@18.3.1)":
    dependencies:
      "@babel/runtime": 7.25.0
      "@mui/utils": 5.16.6(@types/react@18.3.2)(react@18.3.1)
      prop-types: 15.8.1
      react: 18.3.1
    optionalDependencies:
      "@types/react": 18.3.2

  "@mui/styled-engine@5.16.6(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(react@18.3.1)":
    dependencies:
      "@babel/runtime": 7.25.0
      "@emotion/cache": 11.11.0
      csstype: 3.1.3
      prop-types: 15.8.1
      react: 18.3.1
    optionalDependencies:
      "@emotion/react": 11.11.4(@types/react@18.3.2)(react@18.3.1)
      "@emotion/styled": 11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1)

  "@mui/system@5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1)":
    dependencies:
      "@babel/runtime": 7.25.0
      "@mui/private-theming": 5.16.6(@types/react@18.3.2)(react@18.3.1)
      "@mui/styled-engine": 5.16.6(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(react@18.3.1)
      "@mui/types": 7.2.15(@types/react@18.3.2)
      "@mui/utils": 5.16.6(@types/react@18.3.2)(react@18.3.1)
      clsx: 2.1.1
      csstype: 3.1.3
      prop-types: 15.8.1
      react: 18.3.1
    optionalDependencies:
      "@emotion/react": 11.11.4(@types/react@18.3.2)(react@18.3.1)
      "@emotion/styled": 11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1)
      "@types/react": 18.3.2

  "@mui/types@7.2.15(@types/react@18.3.2)":
    optionalDependencies:
      "@types/react": 18.3.2

  "@mui/utils@5.16.6(@types/react@18.3.2)(react@18.3.1)":
    dependencies:
      "@babel/runtime": 7.25.0
      "@mui/types": 7.2.15(@types/react@18.3.2)
      "@types/prop-types": 15.7.12
      clsx: 2.1.1
      prop-types: 15.8.1
      react: 18.3.1
      react-is: 18.3.1
    optionalDependencies:
      "@types/react": 18.3.2

  "@mui/x-data-grid@7.14.0(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@mui/material@5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@types/react@18.3.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
    dependencies:
      "@babel/runtime": 7.25.0
      "@mui/material": 5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      "@mui/system": 5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1)
      "@mui/utils": 5.16.6(@types/react@18.3.2)(react@18.3.1)
      "@mui/x-internals": 7.14.0(@types/react@18.3.2)(react@18.3.1)
      clsx: 2.1.1
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      reselect: 4.1.8
    optionalDependencies:
      "@emotion/react": 11.11.4(@types/react@18.3.2)(react@18.3.1)
      "@emotion/styled": 11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1)
    transitivePeerDependencies:
      - "@types/react"

  "@mui/x-date-pickers@7.14.0(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@mui/material@5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1))(@types/react@18.3.2)(dayjs@1.11.13)(moment@2.30.1)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
    dependencies:
      "@babel/runtime": 7.25.0
      "@mui/material": 5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      "@mui/system": 5.16.7(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@emotion/styled@11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1)
      "@mui/utils": 5.16.6(@types/react@18.3.2)(react@18.3.1)
      "@types/react-transition-group": 4.4.11
      clsx: 2.1.1
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-transition-group: 4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
    optionalDependencies:
      "@emotion/react": 11.11.4(@types/react@18.3.2)(react@18.3.1)
      "@emotion/styled": 11.11.5(@emotion/react@11.11.4(@types/react@18.3.2)(react@18.3.1))(@types/react@18.3.2)(react@18.3.1)
      dayjs: 1.11.13
      moment: 2.30.1
    transitivePeerDependencies:
      - "@types/react"

  "@mui/x-internals@7.14.0(@types/react@18.3.2)(react@18.3.1)":
    dependencies:
      "@babel/runtime": 7.25.0
      "@mui/utils": 5.16.6(@types/react@18.3.2)(react@18.3.1)
      react: 18.3.1
    transitivePeerDependencies:
      - "@types/react"

  "@nodelib/fs.scandir@2.1.5":
    dependencies:
      "@nodelib/fs.stat": 2.0.5
      run-parallel: 1.2.0

  "@nodelib/fs.stat@2.0.5": {}

  "@nodelib/fs.walk@1.2.8":
    dependencies:
      "@nodelib/fs.scandir": 2.1.5
      fastq: 1.17.1

  "@pkgjs/parseargs@0.11.0":
    optional: true

  "@playwright/test@1.44.0":
    dependencies:
      playwright: 1.44.0

  "@polka/url@1.0.0-next.25": {}

  "@popperjs/core@2.11.8": {}

  "@reduxjs/toolkit@2.2.7(react-redux@9.1.2(@types/react@18.3.2)(react@18.3.1)(redux@5.0.1))(react@18.3.1)":
    dependencies:
      immer: 10.1.1
      redux: 5.0.1
      redux-thunk: 3.1.0(redux@5.0.1)
      reselect: 5.1.1
    optionalDependencies:
      react: 18.3.1
      react-redux: 9.1.2(@types/react@18.3.2)(react@18.3.1)(redux@5.0.1)

  "@remix-run/router@1.16.1": {}

  "@rollup/pluginutils@5.1.0(rollup@3.29.4)":
    dependencies:
      "@types/estree": 1.0.5
      estree-walker: 2.0.2
      picomatch: 2.3.1
    optionalDependencies:
      rollup: 3.29.4

  "@svgr/babel-plugin-add-jsx-attribute@7.0.0(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5

  "@svgr/babel-plugin-remove-jsx-attribute@7.0.0(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5

  "@svgr/babel-plugin-remove-jsx-empty-expression@7.0.0(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5

  "@svgr/babel-plugin-replace-jsx-attribute-value@7.0.0(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5

  "@svgr/babel-plugin-svg-dynamic-title@7.0.0(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5

  "@svgr/babel-plugin-svg-em-dimensions@7.0.0(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5

  "@svgr/babel-plugin-transform-react-native-svg@7.0.0(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5

  "@svgr/babel-plugin-transform-svg-component@7.0.0(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5

  "@svgr/babel-preset@7.0.0(@babel/core@7.24.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@svgr/babel-plugin-add-jsx-attribute": 7.0.0(@babel/core@7.24.5)
      "@svgr/babel-plugin-remove-jsx-attribute": 7.0.0(@babel/core@7.24.5)
      "@svgr/babel-plugin-remove-jsx-empty-expression": 7.0.0(@babel/core@7.24.5)
      "@svgr/babel-plugin-replace-jsx-attribute-value": 7.0.0(@babel/core@7.24.5)
      "@svgr/babel-plugin-svg-dynamic-title": 7.0.0(@babel/core@7.24.5)
      "@svgr/babel-plugin-svg-em-dimensions": 7.0.0(@babel/core@7.24.5)
      "@svgr/babel-plugin-transform-react-native-svg": 7.0.0(@babel/core@7.24.5)
      "@svgr/babel-plugin-transform-svg-component": 7.0.0(@babel/core@7.24.5)

  "@svgr/core@7.0.0(typescript@4.9.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@svgr/babel-preset": 7.0.0(@babel/core@7.24.5)
      camelcase: 6.3.0
      cosmiconfig: 8.3.6(typescript@4.9.5)
    transitivePeerDependencies:
      - supports-color
      - typescript

  "@svgr/hast-util-to-babel-ast@7.0.0":
    dependencies:
      "@babel/types": 7.24.5
      entities: 4.5.0

  "@svgr/plugin-jsx@7.0.0":
    dependencies:
      "@babel/core": 7.24.5
      "@svgr/babel-preset": 7.0.0(@babel/core@7.24.5)
      "@svgr/hast-util-to-babel-ast": 7.0.0
      svg-parser: 2.0.4
    transitivePeerDependencies:
      - supports-color

  "@svgr/plugin-svgo@7.0.0(@svgr/core@7.0.0(typescript@4.9.5))(typescript@4.9.5)":
    dependencies:
      "@svgr/core": 7.0.0(typescript@4.9.5)
      cosmiconfig: 8.3.6(typescript@4.9.5)
      deepmerge: 4.3.1
      svgo: 3.3.2
    transitivePeerDependencies:
      - typescript

  "@svgr/rollup@7.0.0(rollup@3.29.4)(typescript@4.9.5)":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/plugin-transform-react-constant-elements": 7.24.1(@babel/core@7.24.5)
      "@babel/preset-env": 7.24.5(@babel/core@7.24.5)
      "@babel/preset-react": 7.24.1(@babel/core@7.24.5)
      "@babel/preset-typescript": 7.24.1(@babel/core@7.24.5)
      "@rollup/pluginutils": 5.1.0(rollup@3.29.4)
      "@svgr/core": 7.0.0(typescript@4.9.5)
      "@svgr/plugin-jsx": 7.0.0
      "@svgr/plugin-svgo": 7.0.0(@svgr/core@7.0.0(typescript@4.9.5))(typescript@4.9.5)
    transitivePeerDependencies:
      - rollup
      - supports-color
      - typescript

  "@testing-library/dom@9.3.4":
    dependencies:
      "@babel/code-frame": 7.24.2
      "@babel/runtime": 7.25.0
      "@types/aria-query": 5.0.4
      aria-query: 5.1.3
      chalk: 4.1.2
      dom-accessibility-api: 0.5.16
      lz-string: 1.5.0
      pretty-format: 27.5.1

  "@testing-library/jest-dom@4.2.4":
    dependencies:
      "@babel/runtime": 7.24.5
      chalk: 2.4.2
      css: 2.2.4
      css.escape: 1.5.1
      jest-diff: 24.9.0
      jest-matcher-utils: 24.9.0
      lodash: 4.17.21
      pretty-format: 24.9.0
      redent: 3.0.0

  "@testing-library/react@14.3.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)":
    dependencies:
      "@babel/runtime": 7.25.0
      "@testing-library/dom": 9.3.4
      "@types/react-dom": 18.3.0
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  "@tootallnate/once@2.0.0": {}

  "@trysound/sax@0.2.0": {}

  "@tsconfig/node10@1.0.11": {}

  "@tsconfig/node12@1.0.11": {}

  "@tsconfig/node14@1.0.3": {}

  "@tsconfig/node16@1.0.4": {}

  "@types/aria-query@5.0.4": {}

  "@types/chai-subset@1.3.5":
    dependencies:
      "@types/chai": 4.3.16

  "@types/chai@4.3.16": {}

  "@types/chroma-js@2.4.4": {}

  "@types/eslint@8.56.10":
    dependencies:
      "@types/estree": 1.0.5
      "@types/json-schema": 7.0.15

  "@types/estree@1.0.5": {}

  "@types/hoist-non-react-statics@3.3.5":
    dependencies:
      "@types/react": 18.3.2
      hoist-non-react-statics: 3.3.2

  "@types/istanbul-lib-coverage@2.0.6": {}

  "@types/istanbul-lib-report@3.0.3":
    dependencies:
      "@types/istanbul-lib-coverage": 2.0.6

  "@types/istanbul-reports@1.1.2":
    dependencies:
      "@types/istanbul-lib-coverage": 2.0.6
      "@types/istanbul-lib-report": 3.0.3

  "@types/js-cookie@2.2.7": {}

  "@types/json-schema@7.0.15": {}

  "@types/json5@0.0.29": {}

  "@types/loadable__component@5.13.9":
    dependencies:
      "@types/react": 18.3.2

  "@types/lodash@4.17.1": {}

  "@types/minimist@1.2.5": {}

  "@types/node@18.19.33":
    dependencies:
      undici-types: 5.26.5

  "@types/node@20.5.1": {}

  "@types/normalize-package-data@2.4.4": {}

  "@types/papaparse@5.3.16":
    dependencies:
      "@types/node": 18.19.33

  "@types/parse-json@4.0.2": {}

  "@types/prettier@2.7.3": {}

  "@types/prop-types@15.7.12": {}

  "@types/react-dom@18.3.0":
    dependencies:
      "@types/react": 18.3.2

  "@types/react-transition-group@4.4.11":
    dependencies:
      "@types/react": 18.3.2

  "@types/react@18.3.2":
    dependencies:
      "@types/prop-types": 15.7.12
      csstype: 3.1.3

  "@types/semver@7.5.8": {}

  "@types/use-sync-external-store@0.0.3": {}

  "@types/uuid@9.0.8": {}

  "@types/yargs-parser@21.0.3": {}

  "@types/yargs@13.0.12":
    dependencies:
      "@types/yargs-parser": 21.0.3

  "@typescript-eslint/eslint-plugin@5.62.0(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@4.9.5))(eslint@8.57.0)(typescript@4.9.5)":
    dependencies:
      "@eslint-community/regexpp": 4.10.0
      "@typescript-eslint/parser": 5.62.0(eslint@8.57.0)(typescript@4.9.5)
      "@typescript-eslint/scope-manager": 5.62.0
      "@typescript-eslint/type-utils": 5.62.0(eslint@8.57.0)(typescript@4.9.5)
      "@typescript-eslint/utils": 5.62.0(eslint@8.57.0)(typescript@4.9.5)
      debug: 4.3.4
      eslint: 8.57.0
      graphemer: 1.4.0
      ignore: 5.3.1
      natural-compare-lite: 1.4.0
      semver: 7.6.2
      tsutils: 3.21.0(typescript@4.9.5)
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  "@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@4.9.5)":
    dependencies:
      "@typescript-eslint/scope-manager": 5.62.0
      "@typescript-eslint/types": 5.62.0
      "@typescript-eslint/typescript-estree": 5.62.0(typescript@4.9.5)
      debug: 4.3.4
      eslint: 8.57.0
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  "@typescript-eslint/scope-manager@5.62.0":
    dependencies:
      "@typescript-eslint/types": 5.62.0
      "@typescript-eslint/visitor-keys": 5.62.0

  "@typescript-eslint/type-utils@5.62.0(eslint@8.57.0)(typescript@4.9.5)":
    dependencies:
      "@typescript-eslint/typescript-estree": 5.62.0(typescript@4.9.5)
      "@typescript-eslint/utils": 5.62.0(eslint@8.57.0)(typescript@4.9.5)
      debug: 4.3.4
      eslint: 8.57.0
      tsutils: 3.21.0(typescript@4.9.5)
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  "@typescript-eslint/types@5.62.0": {}

  "@typescript-eslint/typescript-estree@5.62.0(typescript@4.9.5)":
    dependencies:
      "@typescript-eslint/types": 5.62.0
      "@typescript-eslint/visitor-keys": 5.62.0
      debug: 4.3.4
      globby: 11.1.0
      is-glob: 4.0.3
      semver: 7.6.2
      tsutils: 3.21.0(typescript@4.9.5)
    optionalDependencies:
      typescript: 4.9.5
    transitivePeerDependencies:
      - supports-color

  "@typescript-eslint/utils@5.62.0(eslint@8.57.0)(typescript@4.9.5)":
    dependencies:
      "@eslint-community/eslint-utils": 4.4.0(eslint@8.57.0)
      "@types/json-schema": 7.0.15
      "@types/semver": 7.5.8
      "@typescript-eslint/scope-manager": 5.62.0
      "@typescript-eslint/types": 5.62.0
      "@typescript-eslint/typescript-estree": 5.62.0(typescript@4.9.5)
      eslint: 8.57.0
      eslint-scope: 5.1.1
      semver: 7.6.2
    transitivePeerDependencies:
      - supports-color
      - typescript

  "@typescript-eslint/visitor-keys@5.62.0":
    dependencies:
      "@typescript-eslint/types": 5.62.0
      eslint-visitor-keys: 3.4.3

  "@ungap/structured-clone@1.2.0": {}

  "@vitejs/plugin-react@3.1.0(vite@4.5.14(@types/node@18.19.33)(sass@1.77.1))":
    dependencies:
      "@babel/core": 7.24.5
      "@babel/plugin-transform-react-jsx-self": 7.24.5(@babel/core@7.24.5)
      "@babel/plugin-transform-react-jsx-source": 7.24.1(@babel/core@7.24.5)
      magic-string: 0.27.0
      react-refresh: 0.14.2
      vite: 4.5.14(@types/node@18.19.33)(sass@1.77.1)
    transitivePeerDependencies:
      - supports-color

  "@vitest/coverage-c8@0.29.8(vitest@0.29.8(@vitest/ui@0.29.8)(happy-dom@8.9.0)(jsdom@22.1.0)(playwright@1.44.0)(sass@1.77.1))":
    dependencies:
      c8: 7.14.0
      picocolors: 1.0.1
      std-env: 3.7.0
      vitest: 0.29.8(@vitest/ui@0.29.8)(happy-dom@8.9.0)(jsdom@22.1.0)(playwright@1.44.0)(sass@1.77.1)

  "@vitest/expect@0.29.8":
    dependencies:
      "@vitest/spy": 0.29.8
      "@vitest/utils": 0.29.8
      chai: 4.4.1

  "@vitest/runner@0.29.8":
    dependencies:
      "@vitest/utils": 0.29.8
      p-limit: 4.0.0
      pathe: 1.1.2

  "@vitest/spy@0.29.8":
    dependencies:
      tinyspy: 1.1.1

  "@vitest/ui@0.29.8":
    dependencies:
      fast-glob: 3.3.2
      flatted: 3.3.1
      pathe: 1.1.2
      picocolors: 1.0.1
      sirv: 2.0.4

  "@vitest/utils@0.29.8":
    dependencies:
      cli-truncate: 3.1.0
      diff: 5.2.0
      loupe: 2.3.7
      pretty-format: 27.5.1

  "@xobotyi/scrollbar-width@1.9.5": {}

  JSONStream@1.3.5:
    dependencies:
      jsonparse: 1.3.1
      through: 2.3.8

  abab@2.0.6: {}

  acorn-jsx@5.3.2(acorn@8.11.3):
    dependencies:
      acorn: 8.11.3

  acorn-walk@8.3.2: {}

  acorn@8.11.3: {}

  agent-base@6.0.2:
    dependencies:
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  ajv@6.12.6:
    dependencies:
      fast-deep-equal: 3.1.3
      fast-json-stable-stringify: 2.1.0
      json-schema-traverse: 0.4.1
      uri-js: 4.4.1

  ajv@8.13.0:
    dependencies:
      fast-deep-equal: 3.1.3
      json-schema-traverse: 1.0.0
      require-from-string: 2.0.2
      uri-js: 4.4.1

  ansi-escapes@4.3.2:
    dependencies:
      type-fest: 0.21.3

  ansi-escapes@5.0.0:
    dependencies:
      type-fest: 1.4.0

  ansi-regex@2.1.1: {}

  ansi-regex@3.0.1: {}

  ansi-regex@4.1.1: {}

  ansi-regex@5.0.1: {}

  ansi-regex@6.0.1: {}

  ansi-styles@2.2.1: {}

  ansi-styles@3.2.1:
    dependencies:
      color-convert: 1.9.3

  ansi-styles@4.3.0:
    dependencies:
      color-convert: 2.0.1

  ansi-styles@5.2.0: {}

  ansi-styles@6.2.1: {}

  any-promise@1.3.0: {}

  anymatch@3.1.3:
    dependencies:
      normalize-path: 3.0.0
      picomatch: 2.3.1

  arg@4.1.3: {}

  arg@5.0.2: {}

  argparse@2.0.1: {}

  aria-query@5.1.3:
    dependencies:
      deep-equal: 2.2.3

  array-buffer-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      is-array-buffer: 3.0.4

  array-ify@1.0.0: {}

  array-includes@3.1.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0
      get-intrinsic: 1.2.4
      is-string: 1.0.7

  array-union@2.1.0: {}

  array.prototype.findlast@1.2.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-shim-unscopables: 1.0.2

  array.prototype.findlastindex@1.2.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-shim-unscopables: 1.0.2

  array.prototype.flat@1.3.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-shim-unscopables: 1.0.2

  array.prototype.flatmap@1.3.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-shim-unscopables: 1.0.2

  array.prototype.toreversed@1.1.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-shim-unscopables: 1.0.2

  array.prototype.tosorted@1.1.3:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-shim-unscopables: 1.0.2

  arraybuffer.prototype.slice@1.0.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      is-array-buffer: 3.0.4
      is-shared-array-buffer: 1.0.3

  arrify@1.0.1: {}

  assertion-error@1.1.0: {}

  asynckit@0.4.0: {}

  atob@2.1.2: {}

  autoprefixer@10.4.19(postcss@8.4.38):
    dependencies:
      browserslist: 4.23.0
      caniuse-lite: 1.0.30001718
      fraction.js: 4.3.7
      normalize-range: 0.1.2
      picocolors: 1.0.1
      postcss: 8.4.38
      postcss-value-parser: 4.2.0

  available-typed-arrays@1.0.7:
    dependencies:
      possible-typed-array-names: 1.0.0

  axios@1.7.4:
    dependencies:
      follow-redirects: 1.15.6
      form-data: 4.0.0
      proxy-from-env: 1.1.0
    transitivePeerDependencies:
      - debug

  babel-plugin-import@1.13.8:
    dependencies:
      "@babel/helper-module-imports": 7.24.3

  babel-plugin-macros@3.1.0:
    dependencies:
      "@babel/runtime": 7.24.5
      cosmiconfig: 7.1.0
      resolve: 1.22.8

  babel-plugin-polyfill-corejs2@0.4.11(@babel/core@7.24.5):
    dependencies:
      "@babel/compat-data": 7.24.4
      "@babel/core": 7.24.5
      "@babel/helper-define-polyfill-provider": 0.6.2(@babel/core@7.24.5)
      semver: 6.3.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-corejs3@0.10.4(@babel/core@7.24.5):
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-define-polyfill-provider": 0.6.2(@babel/core@7.24.5)
      core-js-compat: 3.37.1
    transitivePeerDependencies:
      - supports-color

  babel-plugin-polyfill-regenerator@0.6.2(@babel/core@7.24.5):
    dependencies:
      "@babel/core": 7.24.5
      "@babel/helper-define-polyfill-provider": 0.6.2(@babel/core@7.24.5)
    transitivePeerDependencies:
      - supports-color

  balanced-match@1.0.2: {}

  binary-extensions@2.3.0: {}

  boolbase@1.0.0: {}

  brace-expansion@1.1.11:
    dependencies:
      balanced-match: 1.0.2
      concat-map: 0.0.1

  brace-expansion@2.0.1:
    dependencies:
      balanced-match: 1.0.2

  braces@3.0.2:
    dependencies:
      fill-range: 7.0.1

  browserslist@4.23.0:
    dependencies:
      caniuse-lite: 1.0.30001718
      electron-to-chromium: 1.4.769
      node-releases: 2.0.14
      update-browserslist-db: 1.0.16(browserslist@4.23.0)

  builtins@5.1.0:
    dependencies:
      semver: 7.6.2

  c8@7.14.0:
    dependencies:
      "@bcoe/v8-coverage": 0.2.3
      "@istanbuljs/schema": 0.1.3
      find-up: 5.0.0
      foreground-child: 2.0.0
      istanbul-lib-coverage: 3.2.2
      istanbul-lib-report: 3.0.1
      istanbul-reports: 3.1.7
      rimraf: 3.0.2
      test-exclude: 6.0.0
      v8-to-istanbul: 9.2.0
      yargs: 16.2.0
      yargs-parser: 20.2.9

  cac@6.7.14: {}

  call-bind@1.0.7:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      set-function-length: 1.2.2

  callsites@3.1.0: {}

  camelcase-css@2.0.1: {}

  camelcase-keys@6.2.2:
    dependencies:
      camelcase: 5.3.1
      map-obj: 4.3.0
      quick-lru: 4.0.1

  camelcase@5.3.1: {}

  camelcase@6.3.0: {}

  caniuse-lite@1.0.30001718: {}

  chai@4.4.1:
    dependencies:
      assertion-error: 1.1.0
      check-error: 1.0.3
      deep-eql: 4.1.3
      get-func-name: 2.0.2
      loupe: 2.3.7
      pathval: 1.1.1
      type-detect: 4.0.8

  chalk@1.1.3:
    dependencies:
      ansi-styles: 2.2.1
      escape-string-regexp: 1.0.5
      has-ansi: 2.0.0
      strip-ansi: 3.0.1
      supports-color: 2.0.0

  chalk@2.4.2:
    dependencies:
      ansi-styles: 3.2.1
      escape-string-regexp: 1.0.5
      supports-color: 5.5.0

  chalk@4.1.2:
    dependencies:
      ansi-styles: 4.3.0
      supports-color: 7.2.0

  chalk@5.3.0: {}

  check-error@1.0.3:
    dependencies:
      get-func-name: 2.0.2

  chokidar@3.6.0:
    dependencies:
      anymatch: 3.1.3
      braces: 3.0.2
      glob-parent: 5.1.2
      is-binary-path: 2.1.0
      is-glob: 4.0.3
      normalize-path: 3.0.0
      readdirp: 3.6.0
    optionalDependencies:
      fsevents: 2.3.3

  chroma-js@2.4.2: {}

  cli-cursor@4.0.0:
    dependencies:
      restore-cursor: 4.0.0

  cli-truncate@3.1.0:
    dependencies:
      slice-ansi: 5.0.0
      string-width: 5.1.2

  cliui@7.0.4:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  cliui@8.0.1:
    dependencies:
      string-width: 4.2.3
      strip-ansi: 6.0.1
      wrap-ansi: 7.0.0

  clsx@1.2.1: {}

  clsx@2.1.1: {}

  color-convert@1.9.3:
    dependencies:
      color-name: 1.1.3

  color-convert@2.0.1:
    dependencies:
      color-name: 1.1.4

  color-name@1.1.3: {}

  color-name@1.1.4: {}

  colorette@2.0.20: {}

  combined-stream@1.0.8:
    dependencies:
      delayed-stream: 1.0.0

  commander@11.0.0: {}

  commander@4.1.1: {}

  commander@7.2.0: {}

  commander@8.3.0: {}

  common-tags@1.8.2: {}

  compare-func@2.0.0:
    dependencies:
      array-ify: 1.0.0
      dot-prop: 5.3.0

  concat-map@0.0.1: {}

  confbox@0.1.7: {}

  conventional-changelog-angular@6.0.0:
    dependencies:
      compare-func: 2.0.0

  conventional-changelog-conventionalcommits@6.1.0:
    dependencies:
      compare-func: 2.0.0

  conventional-commits-parser@4.0.0:
    dependencies:
      JSONStream: 1.3.5
      is-text-path: 1.0.1
      meow: 8.1.2
      split2: 3.2.2

  convert-source-map@1.9.0: {}

  convert-source-map@2.0.0: {}

  copy-to-clipboard@3.3.3:
    dependencies:
      toggle-selection: 1.0.6

  core-js-compat@3.37.1:
    dependencies:
      browserslist: 4.23.0

  cosmiconfig-typescript-loader@4.4.0(@types/node@20.5.1)(cosmiconfig@8.3.6(typescript@4.9.5))(ts-node@10.9.2(@types/node@20.5.1)(typescript@4.9.5))(typescript@4.9.5):
    dependencies:
      "@types/node": 20.5.1
      cosmiconfig: 8.3.6(typescript@4.9.5)
      ts-node: 10.9.2(@types/node@20.5.1)(typescript@4.9.5)
      typescript: 4.9.5

  cosmiconfig@7.1.0:
    dependencies:
      "@types/parse-json": 4.0.2
      import-fresh: 3.3.0
      parse-json: 5.2.0
      path-type: 4.0.0
      yaml: 1.10.2

  cosmiconfig@8.3.6(typescript@4.9.5):
    dependencies:
      import-fresh: 3.3.0
      js-yaml: 4.1.0
      parse-json: 5.2.0
      path-type: 4.0.0
    optionalDependencies:
      typescript: 4.9.5

  create-require@1.1.1: {}

  cross-spawn@6.0.5:
    dependencies:
      nice-try: 1.0.5
      path-key: 2.0.1
      semver: 5.7.2
      shebang-command: 1.2.0
      which: 1.3.1

  cross-spawn@7.0.3:
    dependencies:
      path-key: 3.1.1
      shebang-command: 2.0.0
      which: 2.0.2

  css-in-js-utils@3.1.0:
    dependencies:
      hyphenate-style-name: 1.0.5

  css-select@5.1.0:
    dependencies:
      boolbase: 1.0.0
      css-what: 6.1.0
      domhandler: 5.0.3
      domutils: 3.1.0
      nth-check: 2.1.1

  css-tree@1.1.3:
    dependencies:
      mdn-data: 2.0.14
      source-map: 0.6.1

  css-tree@2.2.1:
    dependencies:
      mdn-data: 2.0.28
      source-map-js: 1.2.0

  css-tree@2.3.1:
    dependencies:
      mdn-data: 2.0.30
      source-map-js: 1.2.0

  css-what@6.1.0: {}

  css.escape@1.5.1: {}

  css@2.2.4:
    dependencies:
      inherits: 2.0.4
      source-map: 0.6.1
      source-map-resolve: 0.5.3
      urix: 0.1.0

  cssesc@3.0.0: {}

  csso@5.0.5:
    dependencies:
      css-tree: 2.2.1

  cssstyle@3.0.0:
    dependencies:
      rrweb-cssom: 0.6.0

  csstype@3.1.3: {}

  dargs@7.0.0: {}

  data-urls@4.0.0:
    dependencies:
      abab: 2.0.6
      whatwg-mimetype: 3.0.0
      whatwg-url: 12.0.1

  data-view-buffer@1.0.1:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  data-view-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  data-view-byte-offset@1.0.0:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-data-view: 1.0.1

  dayjs@1.11.13: {}

  debug@3.2.7:
    dependencies:
      ms: 2.1.3

  debug@4.3.4:
    dependencies:
      ms: 2.1.2

  decamelize-keys@1.1.1:
    dependencies:
      decamelize: 1.2.0
      map-obj: 1.0.1

  decamelize@1.2.0: {}

  decimal.js@10.4.3: {}

  decode-uri-component@0.2.2: {}

  deep-eql@4.1.3:
    dependencies:
      type-detect: 4.0.8

  deep-equal@2.2.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      call-bind: 1.0.7
      es-get-iterator: 1.1.3
      get-intrinsic: 1.2.4
      is-arguments: 1.1.1
      is-array-buffer: 3.0.4
      is-date-object: 1.0.5
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.3
      isarray: 2.0.5
      object-is: 1.1.6
      object-keys: 1.1.1
      object.assign: 4.1.5
      regexp.prototype.flags: 1.5.2
      side-channel: 1.0.6
      which-boxed-primitive: 1.0.2
      which-collection: 1.0.2
      which-typed-array: 1.1.15

  deep-is@0.1.4: {}

  deepmerge@2.2.1: {}

  deepmerge@4.3.1: {}

  define-data-property@1.1.4:
    dependencies:
      es-define-property: 1.0.0
      es-errors: 1.3.0
      gopd: 1.0.1

  define-properties@1.2.1:
    dependencies:
      define-data-property: 1.1.4
      has-property-descriptors: 1.0.2
      object-keys: 1.1.1

  delayed-stream@1.0.0: {}

  didyoumean@1.2.2: {}

  diff-sequences@24.9.0: {}

  diff@4.0.2: {}

  diff@5.2.0: {}

  dir-glob@3.0.1:
    dependencies:
      path-type: 4.0.0

  dlv@1.1.3: {}

  doctrine@2.1.0:
    dependencies:
      esutils: 2.0.3

  doctrine@3.0.0:
    dependencies:
      esutils: 2.0.3

  dom-accessibility-api@0.5.16: {}

  dom-helpers@5.2.1:
    dependencies:
      "@babel/runtime": 7.25.0
      csstype: 3.1.3

  dom-serializer@2.0.0:
    dependencies:
      domelementtype: 2.3.0
      domhandler: 5.0.3
      entities: 4.5.0

  domelementtype@2.3.0: {}

  domexception@4.0.0:
    dependencies:
      webidl-conversions: 7.0.0

  domhandler@5.0.3:
    dependencies:
      domelementtype: 2.3.0

  domutils@3.1.0:
    dependencies:
      dom-serializer: 2.0.0
      domelementtype: 2.3.0
      domhandler: 5.0.3

  dot-prop@5.3.0:
    dependencies:
      is-obj: 2.0.0

  dotenv@16.4.5: {}

  eastasianwidth@0.2.0: {}

  electron-to-chromium@1.4.769: {}

  emoji-regex@8.0.0: {}

  emoji-regex@9.2.2: {}

  entities@4.5.0: {}

  env-cmd@10.1.0:
    dependencies:
      commander: 4.1.1
      cross-spawn: 7.0.3

  error-ex@1.3.2:
    dependencies:
      is-arrayish: 0.2.1

  error-stack-parser@2.1.4:
    dependencies:
      stackframe: 1.3.4

  es-abstract@1.23.3:
    dependencies:
      array-buffer-byte-length: 1.0.1
      arraybuffer.prototype.slice: 1.0.3
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      data-view-buffer: 1.0.1
      data-view-byte-length: 1.0.1
      data-view-byte-offset: 1.0.0
      es-define-property: 1.0.0
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      es-set-tostringtag: 2.0.3
      es-to-primitive: 1.2.1
      function.prototype.name: 1.1.6
      get-intrinsic: 1.2.4
      get-symbol-description: 1.0.2
      globalthis: 1.0.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2
      internal-slot: 1.0.7
      is-array-buffer: 3.0.4
      is-callable: 1.2.7
      is-data-view: 1.0.1
      is-negative-zero: 2.0.3
      is-regex: 1.1.4
      is-shared-array-buffer: 1.0.3
      is-string: 1.0.7
      is-typed-array: 1.1.13
      is-weakref: 1.0.2
      object-inspect: 1.13.1
      object-keys: 1.1.1
      object.assign: 4.1.5
      regexp.prototype.flags: 1.5.2
      safe-array-concat: 1.1.2
      safe-regex-test: 1.0.3
      string.prototype.trim: 1.2.9
      string.prototype.trimend: 1.0.8
      string.prototype.trimstart: 1.0.8
      typed-array-buffer: 1.0.2
      typed-array-byte-length: 1.0.1
      typed-array-byte-offset: 1.0.2
      typed-array-length: 1.0.6
      unbox-primitive: 1.0.2
      which-typed-array: 1.1.15

  es-define-property@1.0.0:
    dependencies:
      get-intrinsic: 1.2.4

  es-errors@1.3.0: {}

  es-get-iterator@1.1.3:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      is-arguments: 1.1.1
      is-map: 2.0.3
      is-set: 2.0.3
      is-string: 1.0.7
      isarray: 2.0.5
      stop-iteration-iterator: 1.0.0

  es-iterator-helpers@1.0.19:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-set-tostringtag: 2.0.3
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      globalthis: 1.0.4
      has-property-descriptors: 1.0.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      internal-slot: 1.0.7
      iterator.prototype: 1.1.2
      safe-array-concat: 1.1.2

  es-object-atoms@1.0.0:
    dependencies:
      es-errors: 1.3.0

  es-set-tostringtag@2.0.3:
    dependencies:
      get-intrinsic: 1.2.4
      has-tostringtag: 1.0.2
      hasown: 2.0.2

  es-shim-unscopables@1.0.2:
    dependencies:
      hasown: 2.0.2

  es-to-primitive@1.2.1:
    dependencies:
      is-callable: 1.2.7
      is-date-object: 1.0.5
      is-symbol: 1.0.4

  esbuild@0.18.20:
    optionalDependencies:
      "@esbuild/android-arm": 0.18.20
      "@esbuild/android-arm64": 0.18.20
      "@esbuild/android-x64": 0.18.20
      "@esbuild/darwin-arm64": 0.18.20
      "@esbuild/darwin-x64": 0.18.20
      "@esbuild/freebsd-arm64": 0.18.20
      "@esbuild/freebsd-x64": 0.18.20
      "@esbuild/linux-arm": 0.18.20
      "@esbuild/linux-arm64": 0.18.20
      "@esbuild/linux-ia32": 0.18.20
      "@esbuild/linux-loong64": 0.18.20
      "@esbuild/linux-mips64el": 0.18.20
      "@esbuild/linux-ppc64": 0.18.20
      "@esbuild/linux-riscv64": 0.18.20
      "@esbuild/linux-s390x": 0.18.20
      "@esbuild/linux-x64": 0.18.20
      "@esbuild/netbsd-x64": 0.18.20
      "@esbuild/openbsd-x64": 0.18.20
      "@esbuild/sunos-x64": 0.18.20
      "@esbuild/win32-arm64": 0.18.20
      "@esbuild/win32-ia32": 0.18.20
      "@esbuild/win32-x64": 0.18.20

  esbuild@0.25.9:
    optionalDependencies:
      "@esbuild/aix-ppc64": 0.25.9
      "@esbuild/android-arm": 0.25.9
      "@esbuild/android-arm64": 0.25.9
      "@esbuild/android-x64": 0.25.9
      "@esbuild/darwin-arm64": 0.25.9
      "@esbuild/darwin-x64": 0.25.9
      "@esbuild/freebsd-arm64": 0.25.9
      "@esbuild/freebsd-x64": 0.25.9
      "@esbuild/linux-arm": 0.25.9
      "@esbuild/linux-arm64": 0.25.9
      "@esbuild/linux-ia32": 0.25.9
      "@esbuild/linux-loong64": 0.25.9
      "@esbuild/linux-mips64el": 0.25.9
      "@esbuild/linux-ppc64": 0.25.9
      "@esbuild/linux-riscv64": 0.25.9
      "@esbuild/linux-s390x": 0.25.9
      "@esbuild/linux-x64": 0.25.9
      "@esbuild/netbsd-arm64": 0.25.9
      "@esbuild/netbsd-x64": 0.25.9
      "@esbuild/openbsd-arm64": 0.25.9
      "@esbuild/openbsd-x64": 0.25.9
      "@esbuild/openharmony-arm64": 0.25.9
      "@esbuild/sunos-x64": 0.25.9
      "@esbuild/win32-arm64": 0.25.9
      "@esbuild/win32-ia32": 0.25.9
      "@esbuild/win32-x64": 0.25.9

  escalade@3.1.2: {}

  escape-string-regexp@1.0.5: {}

  escape-string-regexp@4.0.0: {}

  eslint-config-prettier@8.10.0(eslint@8.57.0):
    dependencies:
      eslint: 8.57.0

  eslint-config-standard@17.1.0(eslint-plugin-import@2.29.1(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@4.9.5))(eslint@8.57.0))(eslint-plugin-n@15.7.0(eslint@8.57.0))(eslint-plugin-promise@6.1.1(eslint@8.57.0))(eslint@8.57.0):
    dependencies:
      eslint: 8.57.0
      eslint-plugin-import: 2.29.1(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@4.9.5))(eslint@8.57.0)
      eslint-plugin-n: 15.7.0(eslint@8.57.0)
      eslint-plugin-promise: 6.1.1(eslint@8.57.0)

  eslint-import-resolver-node@0.3.9:
    dependencies:
      debug: 3.2.7
      is-core-module: 2.13.1
      resolve: 1.22.8
    transitivePeerDependencies:
      - supports-color

  eslint-module-utils@2.8.1(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@4.9.5))(eslint-import-resolver-node@0.3.9)(eslint@8.57.0):
    dependencies:
      debug: 3.2.7
    optionalDependencies:
      "@typescript-eslint/parser": 5.62.0(eslint@8.57.0)(typescript@4.9.5)
      eslint: 8.57.0
      eslint-import-resolver-node: 0.3.9
    transitivePeerDependencies:
      - supports-color

  eslint-plugin-es@4.1.0(eslint@8.57.0):
    dependencies:
      eslint: 8.57.0
      eslint-utils: 2.1.0
      regexpp: 3.2.0

  eslint-plugin-import@2.29.1(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@4.9.5))(eslint@8.57.0):
    dependencies:
      array-includes: 3.1.8
      array.prototype.findlastindex: 1.2.5
      array.prototype.flat: 1.3.2
      array.prototype.flatmap: 1.3.2
      debug: 3.2.7
      doctrine: 2.1.0
      eslint: 8.57.0
      eslint-import-resolver-node: 0.3.9
      eslint-module-utils: 2.8.1(@typescript-eslint/parser@5.62.0(eslint@8.57.0)(typescript@4.9.5))(eslint-import-resolver-node@0.3.9)(eslint@8.57.0)
      hasown: 2.0.2
      is-core-module: 2.13.1
      is-glob: 4.0.3
      minimatch: 3.1.2
      object.fromentries: 2.0.8
      object.groupby: 1.0.3
      object.values: 1.2.0
      semver: 6.3.1
      tsconfig-paths: 3.15.0
    optionalDependencies:
      "@typescript-eslint/parser": 5.62.0(eslint@8.57.0)(typescript@4.9.5)
    transitivePeerDependencies:
      - eslint-import-resolver-typescript
      - eslint-import-resolver-webpack
      - supports-color

  eslint-plugin-n@15.7.0(eslint@8.57.0):
    dependencies:
      builtins: 5.1.0
      eslint: 8.57.0
      eslint-plugin-es: 4.1.0(eslint@8.57.0)
      eslint-utils: 3.0.0(eslint@8.57.0)
      ignore: 5.3.1
      is-core-module: 2.13.1
      minimatch: 3.1.2
      resolve: 1.22.8
      semver: 7.6.2

  eslint-plugin-prettier@4.2.1(eslint-config-prettier@8.10.0(eslint@8.57.0))(eslint@8.57.0)(prettier@2.8.7):
    dependencies:
      eslint: 8.57.0
      prettier: 2.8.7
      prettier-linter-helpers: 1.0.0
    optionalDependencies:
      eslint-config-prettier: 8.10.0(eslint@8.57.0)

  eslint-plugin-promise@6.1.1(eslint@8.57.0):
    dependencies:
      eslint: 8.57.0

  eslint-plugin-react@7.34.1(eslint@8.57.0):
    dependencies:
      array-includes: 3.1.8
      array.prototype.findlast: 1.2.5
      array.prototype.flatmap: 1.3.2
      array.prototype.toreversed: 1.1.2
      array.prototype.tosorted: 1.1.3
      doctrine: 2.1.0
      es-iterator-helpers: 1.0.19
      eslint: 8.57.0
      estraverse: 5.3.0
      jsx-ast-utils: 3.3.5
      minimatch: 3.1.2
      object.entries: 1.1.8
      object.fromentries: 2.0.8
      object.hasown: 1.1.4
      object.values: 1.2.0
      prop-types: 15.8.1
      resolve: 2.0.0-next.5
      semver: 6.3.1
      string.prototype.matchall: 4.0.11

  eslint-plugin-simple-import-sort@10.0.0(eslint@8.57.0):
    dependencies:
      eslint: 8.57.0

  eslint-scope@5.1.1:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 4.3.0

  eslint-scope@7.2.2:
    dependencies:
      esrecurse: 4.3.0
      estraverse: 5.3.0

  eslint-utils@2.1.0:
    dependencies:
      eslint-visitor-keys: 1.3.0

  eslint-utils@3.0.0(eslint@8.57.0):
    dependencies:
      eslint: 8.57.0
      eslint-visitor-keys: 2.1.0

  eslint-visitor-keys@1.3.0: {}

  eslint-visitor-keys@2.1.0: {}

  eslint-visitor-keys@3.4.3: {}

  eslint@8.57.0:
    dependencies:
      "@eslint-community/eslint-utils": 4.4.0(eslint@8.57.0)
      "@eslint-community/regexpp": 4.10.0
      "@eslint/eslintrc": 2.1.4
      "@eslint/js": 8.57.0
      "@humanwhocodes/config-array": 0.11.14
      "@humanwhocodes/module-importer": 1.0.1
      "@nodelib/fs.walk": 1.2.8
      "@ungap/structured-clone": 1.2.0
      ajv: 6.12.6
      chalk: 4.1.2
      cross-spawn: 7.0.3
      debug: 4.3.4
      doctrine: 3.0.0
      escape-string-regexp: 4.0.0
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.5.0
      esutils: 2.0.3
      fast-deep-equal: 3.1.3
      file-entry-cache: 6.0.1
      find-up: 5.0.0
      glob-parent: 6.0.2
      globals: 13.24.0
      graphemer: 1.4.0
      ignore: 5.3.1
      imurmurhash: 0.1.4
      is-glob: 4.0.3
      is-path-inside: 3.0.3
      js-yaml: 4.1.0
      json-stable-stringify-without-jsonify: 1.0.1
      levn: 0.4.1
      lodash.merge: 4.6.2
      minimatch: 3.1.2
      natural-compare: 1.4.0
      optionator: 0.9.4
      strip-ansi: 6.0.1
      text-table: 0.2.0
    transitivePeerDependencies:
      - supports-color

  espree@9.6.1:
    dependencies:
      acorn: 8.11.3
      acorn-jsx: 5.3.2(acorn@8.11.3)
      eslint-visitor-keys: 3.4.3

  esquery@1.5.0:
    dependencies:
      estraverse: 5.3.0

  esrecurse@4.3.0:
    dependencies:
      estraverse: 5.3.0

  estraverse@4.3.0: {}

  estraverse@5.3.0: {}

  estree-walker@2.0.2: {}

  esutils@2.0.3: {}

  eventemitter3@5.0.1: {}

  execa@5.1.1:
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 2.1.0
      is-stream: 2.0.1
      merge-stream: 2.0.0
      npm-run-path: 4.0.1
      onetime: 5.1.2
      signal-exit: 3.0.7
      strip-final-newline: 2.0.0

  execa@7.2.0:
    dependencies:
      cross-spawn: 7.0.3
      get-stream: 6.0.1
      human-signals: 4.3.1
      is-stream: 3.0.0
      merge-stream: 2.0.0
      npm-run-path: 5.3.0
      onetime: 6.0.0
      signal-exit: 3.0.7
      strip-final-newline: 3.0.0

  fast-deep-equal@3.1.3: {}

  fast-diff@1.3.0: {}

  fast-glob@3.3.2:
    dependencies:
      "@nodelib/fs.stat": 2.0.5
      "@nodelib/fs.walk": 1.2.8
      glob-parent: 5.1.2
      merge2: 1.4.1
      micromatch: 4.0.5

  fast-json-stable-stringify@2.1.0: {}

  fast-levenshtein@2.0.6: {}

  fast-loops@1.1.3: {}

  fast-shallow-equal@1.0.0: {}

  fastest-stable-stringify@2.0.2: {}

  fastq@1.17.1:
    dependencies:
      reusify: 1.0.4

  file-entry-cache@6.0.1:
    dependencies:
      flat-cache: 3.2.0

  fill-range@7.0.1:
    dependencies:
      to-regex-range: 5.0.1

  find-root@1.1.0: {}

  find-up@4.1.0:
    dependencies:
      locate-path: 5.0.0
      path-exists: 4.0.0

  find-up@5.0.0:
    dependencies:
      locate-path: 6.0.0
      path-exists: 4.0.0

  flat-cache@3.2.0:
    dependencies:
      flatted: 3.3.1
      keyv: 4.5.4
      rimraf: 3.0.2

  flatted@3.3.1: {}

  follow-redirects@1.15.6: {}

  for-each@0.3.3:
    dependencies:
      is-callable: 1.2.7

  foreground-child@2.0.0:
    dependencies:
      cross-spawn: 7.0.3
      signal-exit: 3.0.7

  foreground-child@3.1.1:
    dependencies:
      cross-spawn: 7.0.3
      signal-exit: 4.1.0

  form-data@4.0.0:
    dependencies:
      asynckit: 0.4.0
      combined-stream: 1.0.8
      mime-types: 2.1.35

  formik@2.4.6(react@18.3.1):
    dependencies:
      "@types/hoist-non-react-statics": 3.3.5
      deepmerge: 2.2.1
      hoist-non-react-statics: 3.3.2
      lodash: 4.17.21
      lodash-es: 4.17.21
      react: 18.3.1
      react-fast-compare: 2.0.4
      tiny-warning: 1.0.3
      tslib: 2.6.2

  fraction.js@4.3.7: {}

  fs-extra@11.2.0:
    dependencies:
      graceful-fs: 4.2.11
      jsonfile: 6.1.0
      universalify: 2.0.1

  fs.realpath@1.0.0: {}

  fsevents@2.3.2:
    optional: true

  fsevents@2.3.3:
    optional: true

  function-bind@1.1.2: {}

  function.prototype.name@1.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      functions-have-names: 1.2.3

  functions-have-names@1.2.3: {}

  gensync@1.0.0-beta.2: {}

  get-caller-file@2.0.5: {}

  get-func-name@2.0.2: {}

  get-intrinsic@1.2.4:
    dependencies:
      es-errors: 1.3.0
      function-bind: 1.1.2
      has-proto: 1.0.3
      has-symbols: 1.0.3
      hasown: 2.0.2

  get-stream@6.0.1: {}

  get-symbol-description@1.0.2:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4

  git-raw-commits@2.0.11:
    dependencies:
      dargs: 7.0.0
      lodash: 4.17.21
      meow: 8.1.2
      split2: 3.2.2
      through2: 4.0.2

  glob-parent@5.1.2:
    dependencies:
      is-glob: 4.0.3

  glob-parent@6.0.2:
    dependencies:
      is-glob: 4.0.3

  glob@10.3.15:
    dependencies:
      foreground-child: 3.1.1
      jackspeak: 2.3.6
      minimatch: 9.0.4
      minipass: 7.1.1
      path-scurry: 1.11.1

  glob@7.2.3:
    dependencies:
      fs.realpath: 1.0.0
      inflight: 1.0.6
      inherits: 2.0.4
      minimatch: 3.1.2
      once: 1.4.0
      path-is-absolute: 1.0.1

  global-dirs@0.1.1:
    dependencies:
      ini: 1.3.8

  globals@11.12.0: {}

  globals@13.24.0:
    dependencies:
      type-fest: 0.20.2

  globalthis@1.0.4:
    dependencies:
      define-properties: 1.2.1
      gopd: 1.0.1

  globby@11.1.0:
    dependencies:
      array-union: 2.1.0
      dir-glob: 3.0.1
      fast-glob: 3.3.2
      ignore: 5.3.1
      merge2: 1.4.1
      slash: 3.0.0

  goober@2.1.14(csstype@3.1.3):
    dependencies:
      csstype: 3.1.3

  gopd@1.0.1:
    dependencies:
      get-intrinsic: 1.2.4

  graceful-fs@4.2.11: {}

  graphemer@1.4.0: {}

  happy-dom@8.9.0:
    dependencies:
      css.escape: 1.5.1
      he: 1.2.0
      iconv-lite: 0.6.3
      node-fetch: 2.7.0
      webidl-conversions: 7.0.0
      whatwg-encoding: 2.0.0
      whatwg-mimetype: 3.0.0
    transitivePeerDependencies:
      - encoding

  hard-rejection@2.1.0: {}

  has-ansi@2.0.0:
    dependencies:
      ansi-regex: 2.1.1

  has-bigints@1.0.2: {}

  has-flag@3.0.0: {}

  has-flag@4.0.0: {}

  has-property-descriptors@1.0.2:
    dependencies:
      es-define-property: 1.0.0

  has-proto@1.0.3: {}

  has-symbols@1.0.3: {}

  has-tostringtag@1.0.2:
    dependencies:
      has-symbols: 1.0.3

  hasown@2.0.2:
    dependencies:
      function-bind: 1.1.2

  he@1.2.0: {}

  hoist-non-react-statics@3.3.2:
    dependencies:
      react-is: 16.13.1

  hosted-git-info@2.8.9: {}

  hosted-git-info@4.1.0:
    dependencies:
      lru-cache: 6.0.0

  html-encoding-sniffer@3.0.0:
    dependencies:
      whatwg-encoding: 2.0.0

  html-escaper@2.0.2: {}

  html-parse-stringify@3.0.1:
    dependencies:
      void-elements: 3.1.0

  http-proxy-agent@5.0.0:
    dependencies:
      "@tootallnate/once": 2.0.0
      agent-base: 6.0.2
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  https-proxy-agent@5.0.1:
    dependencies:
      agent-base: 6.0.2
      debug: 4.3.4
    transitivePeerDependencies:
      - supports-color

  human-signals@2.1.0: {}

  human-signals@4.3.1: {}

  husky@8.0.3: {}

  hyphenate-style-name@1.0.5: {}

  i18next@23.11.4:
    dependencies:
      "@babel/runtime": 7.24.5

  iconv-lite@0.6.3:
    dependencies:
      safer-buffer: 2.1.2

  ignore@5.3.1: {}

  immediate@3.0.6: {}

  immer@10.1.1: {}

  immutable@4.3.6: {}

  import-fresh@3.3.0:
    dependencies:
      parent-module: 1.0.1
      resolve-from: 4.0.0

  imurmurhash@0.1.4: {}

  indent-string@4.0.0: {}

  inflight@1.0.6:
    dependencies:
      once: 1.4.0
      wrappy: 1.0.2

  inherits@2.0.4: {}

  ini@1.3.8: {}

  inline-style-prefixer@7.0.0:
    dependencies:
      css-in-js-utils: 3.1.0
      fast-loops: 1.1.3

  internal-slot@1.0.7:
    dependencies:
      es-errors: 1.3.0
      hasown: 2.0.2
      side-channel: 1.0.6

  is-arguments@1.1.1:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-array-buffer@3.0.4:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4

  is-arrayish@0.2.1: {}

  is-async-function@2.0.0:
    dependencies:
      has-tostringtag: 1.0.2

  is-bigint@1.0.4:
    dependencies:
      has-bigints: 1.0.2

  is-binary-path@2.1.0:
    dependencies:
      binary-extensions: 2.3.0

  is-boolean-object@1.1.2:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-callable@1.2.7: {}

  is-core-module@2.13.1:
    dependencies:
      hasown: 2.0.2

  is-data-view@1.0.1:
    dependencies:
      is-typed-array: 1.1.13

  is-date-object@1.0.5:
    dependencies:
      has-tostringtag: 1.0.2

  is-extglob@2.1.1: {}

  is-finalizationregistry@1.0.2:
    dependencies:
      call-bind: 1.0.7

  is-fullwidth-code-point@3.0.0: {}

  is-fullwidth-code-point@4.0.0: {}

  is-generator-function@1.0.10:
    dependencies:
      has-tostringtag: 1.0.2

  is-glob@4.0.3:
    dependencies:
      is-extglob: 2.1.1

  is-map@2.0.3: {}

  is-negative-zero@2.0.3: {}

  is-number-object@1.0.7:
    dependencies:
      has-tostringtag: 1.0.2

  is-number@7.0.0: {}

  is-obj@2.0.0: {}

  is-path-inside@3.0.3: {}

  is-plain-obj@1.1.0: {}

  is-potential-custom-element-name@1.0.1: {}

  is-regex@1.1.4:
    dependencies:
      call-bind: 1.0.7
      has-tostringtag: 1.0.2

  is-set@2.0.3: {}

  is-shared-array-buffer@1.0.3:
    dependencies:
      call-bind: 1.0.7

  is-stream@2.0.1: {}

  is-stream@3.0.0: {}

  is-string@1.0.7:
    dependencies:
      has-tostringtag: 1.0.2

  is-symbol@1.0.4:
    dependencies:
      has-symbols: 1.0.3

  is-text-path@1.0.1:
    dependencies:
      text-extensions: 1.9.0

  is-typed-array@1.1.13:
    dependencies:
      which-typed-array: 1.1.15

  is-weakmap@2.0.2: {}

  is-weakref@1.0.2:
    dependencies:
      call-bind: 1.0.7

  is-weakset@2.0.3:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4

  isarray@2.0.5: {}

  isexe@2.0.0: {}

  istanbul-lib-coverage@3.2.2: {}

  istanbul-lib-report@3.0.1:
    dependencies:
      istanbul-lib-coverage: 3.2.2
      make-dir: 4.0.0
      supports-color: 7.2.0

  istanbul-reports@3.1.7:
    dependencies:
      html-escaper: 2.0.2
      istanbul-lib-report: 3.0.1

  iterator.prototype@1.1.2:
    dependencies:
      define-properties: 1.2.1
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      reflect.getprototypeof: 1.0.6
      set-function-name: 2.0.2

  jackspeak@2.3.6:
    dependencies:
      "@isaacs/cliui": 8.0.2
    optionalDependencies:
      "@pkgjs/parseargs": 0.11.0

  jest-diff@24.9.0:
    dependencies:
      chalk: 2.4.2
      diff-sequences: 24.9.0
      jest-get-type: 24.9.0
      pretty-format: 24.9.0

  jest-get-type@24.9.0: {}

  jest-matcher-utils@24.9.0:
    dependencies:
      chalk: 2.4.2
      jest-diff: 24.9.0
      jest-get-type: 24.9.0
      pretty-format: 24.9.0

  jiti@1.21.0: {}

  jotai@2.8.0(@types/react@18.3.2)(react@18.3.1):
    optionalDependencies:
      "@types/react": 18.3.2
      react: 18.3.1

  js-cookie@2.2.1: {}

  js-tokens@4.0.0: {}

  js-yaml@4.1.0:
    dependencies:
      argparse: 2.0.1

  jsdom@22.1.0:
    dependencies:
      abab: 2.0.6
      cssstyle: 3.0.0
      data-urls: 4.0.0
      decimal.js: 10.4.3
      domexception: 4.0.0
      form-data: 4.0.0
      html-encoding-sniffer: 3.0.0
      http-proxy-agent: 5.0.0
      https-proxy-agent: 5.0.1
      is-potential-custom-element-name: 1.0.1
      nwsapi: 2.2.10
      parse5: 7.1.2
      rrweb-cssom: 0.6.0
      saxes: 6.0.0
      symbol-tree: 3.2.4
      tough-cookie: 4.1.4
      w3c-xmlserializer: 4.0.0
      webidl-conversions: 7.0.0
      whatwg-encoding: 2.0.0
      whatwg-mimetype: 3.0.0
      whatwg-url: 12.0.1
      ws: 8.17.0
      xml-name-validator: 4.0.0
    transitivePeerDependencies:
      - bufferutil
      - supports-color
      - utf-8-validate

  jsesc@0.5.0: {}

  jsesc@2.5.2: {}

  json-buffer@3.0.1: {}

  json-parse-better-errors@1.0.2: {}

  json-parse-even-better-errors@2.3.1: {}

  json-schema-traverse@0.4.1: {}

  json-schema-traverse@1.0.0: {}

  json-stable-stringify-without-jsonify@1.0.1: {}

  json-to-csv-export@3.0.1: {}

  json5@1.0.2:
    dependencies:
      minimist: 1.2.8

  json5@2.2.3: {}

  jsonfile@6.1.0:
    dependencies:
      universalify: 2.0.1
    optionalDependencies:
      graceful-fs: 4.2.11

  jsonparse@1.3.1: {}

  jsx-ast-utils@3.3.5:
    dependencies:
      array-includes: 3.1.8
      array.prototype.flat: 1.3.2
      object.assign: 4.1.5
      object.values: 1.2.0

  jwt-decode@4.0.0: {}

  keyv@4.5.4:
    dependencies:
      json-buffer: 3.0.1

  kind-of@6.0.3: {}

  levn@0.4.1:
    dependencies:
      prelude-ls: 1.2.1
      type-check: 0.4.0

  lie@3.1.1:
    dependencies:
      immediate: 3.0.6

  lilconfig@2.1.0: {}

  lilconfig@3.1.1: {}

  lines-and-columns@1.2.4: {}

  lint-staged@13.3.0:
    dependencies:
      chalk: 5.3.0
      commander: 11.0.0
      debug: 4.3.4
      execa: 7.2.0
      lilconfig: 2.1.0
      listr2: 6.6.1
      micromatch: 4.0.5
      pidtree: 0.6.0
      string-argv: 0.3.2
      yaml: 2.3.1
    transitivePeerDependencies:
      - enquirer
      - supports-color

  listr2@6.6.1:
    dependencies:
      cli-truncate: 3.1.0
      colorette: 2.0.20
      eventemitter3: 5.0.1
      log-update: 5.0.1
      rfdc: 1.3.1
      wrap-ansi: 8.1.0

  load-json-file@4.0.0:
    dependencies:
      graceful-fs: 4.2.11
      parse-json: 4.0.0
      pify: 3.0.0
      strip-bom: 3.0.0

  local-pkg@0.4.3: {}

  localforage@1.10.0:
    dependencies:
      lie: 3.1.1

  locate-path@5.0.0:
    dependencies:
      p-locate: 4.1.0

  locate-path@6.0.0:
    dependencies:
      p-locate: 5.0.0

  lodash-es@4.17.21: {}

  lodash.camelcase@4.3.0: {}

  lodash.debounce@4.0.8: {}

  lodash.get@4.4.2: {}

  lodash.isfunction@3.0.9: {}

  lodash.isplainobject@4.0.6: {}

  lodash.kebabcase@4.1.1: {}

  lodash.merge@4.6.2: {}

  lodash.mergewith@4.6.2: {}

  lodash.pick@4.4.0: {}

  lodash.snakecase@4.1.1: {}

  lodash.startcase@4.4.0: {}

  lodash.uniq@4.5.0: {}

  lodash.upperfirst@4.3.1: {}

  lodash@4.17.21: {}

  log-update@5.0.1:
    dependencies:
      ansi-escapes: 5.0.0
      cli-cursor: 4.0.0
      slice-ansi: 5.0.0
      strip-ansi: 7.1.0
      wrap-ansi: 8.1.0

  loglevel-colored-level-prefix@1.0.0:
    dependencies:
      chalk: 1.1.3
      loglevel: 1.9.1

  loglevel@1.9.1: {}

  loose-envify@1.4.0:
    dependencies:
      js-tokens: 4.0.0

  loupe@2.3.7:
    dependencies:
      get-func-name: 2.0.2

  lru-cache@10.2.2: {}

  lru-cache@5.1.1:
    dependencies:
      yallist: 3.1.1

  lru-cache@6.0.0:
    dependencies:
      yallist: 4.0.0

  lz-string@1.5.0: {}

  magic-string@0.27.0:
    dependencies:
      "@jridgewell/sourcemap-codec": 1.4.15

  make-dir@4.0.0:
    dependencies:
      semver: 7.6.2

  make-error@1.3.6: {}

  map-obj@1.0.1: {}

  map-obj@4.3.0: {}

  match-sorter@6.3.4:
    dependencies:
      "@babel/runtime": 7.25.0
      remove-accents: 0.5.0

  mdn-data@2.0.14: {}

  mdn-data@2.0.28: {}

  mdn-data@2.0.30: {}

  memorystream@0.3.1: {}

  meow@8.1.2:
    dependencies:
      "@types/minimist": 1.2.5
      camelcase-keys: 6.2.2
      decamelize-keys: 1.1.1
      hard-rejection: 2.1.0
      minimist-options: 4.1.0
      normalize-package-data: 3.0.3
      read-pkg-up: 7.0.1
      redent: 3.0.0
      trim-newlines: 3.0.1
      type-fest: 0.18.1
      yargs-parser: 20.2.9

  merge-stream@2.0.0: {}

  merge2@1.4.1: {}

  micromatch@4.0.5:
    dependencies:
      braces: 3.0.2
      picomatch: 2.3.1

  mime-db@1.52.0: {}

  mime-types@2.1.35:
    dependencies:
      mime-db: 1.52.0

  mimic-fn@2.1.0: {}

  mimic-fn@4.0.0: {}

  min-indent@1.0.1: {}

  minimatch@3.1.2:
    dependencies:
      brace-expansion: 1.1.11

  minimatch@9.0.4:
    dependencies:
      brace-expansion: 2.0.1

  minimist-options@4.1.0:
    dependencies:
      arrify: 1.0.1
      is-plain-obj: 1.1.0
      kind-of: 6.0.3

  minimist@1.2.8: {}

  minipass@7.1.1: {}

  mlly@1.7.0:
    dependencies:
      acorn: 8.11.3
      pathe: 1.1.2
      pkg-types: 1.1.1
      ufo: 1.5.3

  moment@2.30.1:
    optional: true

  mrmime@2.0.0: {}

  ms@2.1.2: {}

  ms@2.1.3: {}

  mz@2.7.0:
    dependencies:
      any-promise: 1.3.0
      object-assign: 4.1.1
      thenify-all: 1.6.0

  nano-css@5.6.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      "@jridgewell/sourcemap-codec": 1.4.15
      css-tree: 1.1.3
      csstype: 3.1.3
      fastest-stable-stringify: 2.0.2
      inline-style-prefixer: 7.0.0
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      rtl-css-js: 1.16.1
      stacktrace-js: 2.0.2
      stylis: 4.3.2

  nanoid@3.3.7: {}

  natural-compare-lite@1.4.0: {}

  natural-compare@1.4.0: {}

  nice-try@1.0.5: {}

  node-fetch@2.7.0:
    dependencies:
      whatwg-url: 5.0.0

  node-releases@2.0.14: {}

  normalize-package-data@2.5.0:
    dependencies:
      hosted-git-info: 2.8.9
      resolve: 1.22.8
      semver: 5.7.2
      validate-npm-package-license: 3.0.4

  normalize-package-data@3.0.3:
    dependencies:
      hosted-git-info: 4.1.0
      is-core-module: 2.13.1
      semver: 7.6.2
      validate-npm-package-license: 3.0.4

  normalize-path@3.0.0: {}

  normalize-range@0.1.2: {}

  npm-run-all@4.1.5:
    dependencies:
      ansi-styles: 3.2.1
      chalk: 2.4.2
      cross-spawn: 6.0.5
      memorystream: 0.3.1
      minimatch: 3.1.2
      pidtree: 0.3.1
      read-pkg: 3.0.0
      shell-quote: 1.8.1
      string.prototype.padend: 3.1.6

  npm-run-path@4.0.1:
    dependencies:
      path-key: 3.1.1

  npm-run-path@5.3.0:
    dependencies:
      path-key: 4.0.0

  nth-check@2.1.1:
    dependencies:
      boolbase: 1.0.0

  nwsapi@2.2.10: {}

  object-assign@4.1.1: {}

  object-hash@3.0.0: {}

  object-inspect@1.13.1: {}

  object-is@1.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1

  object-keys@1.1.1: {}

  object-path@0.6.0: {}

  object.assign@4.1.5:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      has-symbols: 1.0.3
      object-keys: 1.1.1

  object.entries@1.1.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  object.fromentries@2.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  object.groupby@1.0.3:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3

  object.hasown@1.1.4:
    dependencies:
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  object.values@1.2.0:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  once@1.4.0:
    dependencies:
      wrappy: 1.0.2

  onetime@5.1.2:
    dependencies:
      mimic-fn: 2.1.0

  onetime@6.0.0:
    dependencies:
      mimic-fn: 4.0.0

  optionator@0.9.4:
    dependencies:
      deep-is: 0.1.4
      fast-levenshtein: 2.0.6
      levn: 0.4.1
      prelude-ls: 1.2.1
      type-check: 0.4.0
      word-wrap: 1.2.5

  p-limit@2.3.0:
    dependencies:
      p-try: 2.2.0

  p-limit@3.1.0:
    dependencies:
      yocto-queue: 0.1.0

  p-limit@4.0.0:
    dependencies:
      yocto-queue: 1.0.0

  p-locate@4.1.0:
    dependencies:
      p-limit: 2.3.0

  p-locate@5.0.0:
    dependencies:
      p-limit: 3.1.0

  p-min-delay@4.0.2:
    dependencies:
      yoctodelay: 1.2.0

  p-try@2.2.0: {}

  papaparse@5.5.3: {}

  parent-module@1.0.1:
    dependencies:
      callsites: 3.1.0

  parse-json@4.0.0:
    dependencies:
      error-ex: 1.3.2
      json-parse-better-errors: 1.0.2

  parse-json@5.2.0:
    dependencies:
      "@babel/code-frame": 7.24.2
      error-ex: 1.3.2
      json-parse-even-better-errors: 2.3.1
      lines-and-columns: 1.2.4

  parse5@7.1.2:
    dependencies:
      entities: 4.5.0

  path-exists@4.0.0: {}

  path-is-absolute@1.0.1: {}

  path-key@2.0.1: {}

  path-key@3.1.1: {}

  path-key@4.0.0: {}

  path-parse@1.0.7: {}

  path-scurry@1.11.1:
    dependencies:
      lru-cache: 10.2.2
      minipass: 7.1.1

  path-type@3.0.0:
    dependencies:
      pify: 3.0.0

  path-type@4.0.0: {}

  pathe@1.1.2: {}

  pathval@1.1.1: {}

  picocolors@1.0.1: {}

  picomatch@2.3.1: {}

  pidtree@0.3.1: {}

  pidtree@0.6.0: {}

  pify@2.3.0: {}

  pify@3.0.0: {}

  pirates@4.0.6: {}

  pkg-types@1.1.1:
    dependencies:
      confbox: 0.1.7
      mlly: 1.7.0
      pathe: 1.1.2

  playwright-core@1.44.0: {}

  playwright@1.44.0:
    dependencies:
      playwright-core: 1.44.0
    optionalDependencies:
      fsevents: 2.3.2

  possible-typed-array-names@1.0.0: {}

  postcss-import@15.1.0(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-value-parser: 4.2.0
      read-cache: 1.0.0
      resolve: 1.22.8

  postcss-js@4.0.1(postcss@8.4.38):
    dependencies:
      camelcase-css: 2.0.1
      postcss: 8.4.38

  postcss-load-config@4.0.2(postcss@8.4.38)(ts-node@10.9.2(@types/node@18.19.33)(typescript@4.9.5)):
    dependencies:
      lilconfig: 3.1.1
      yaml: 2.4.2
    optionalDependencies:
      postcss: 8.4.38
      ts-node: 10.9.2(@types/node@20.5.1)(typescript@4.9.5)

  postcss-nested@6.0.1(postcss@8.4.38):
    dependencies:
      postcss: 8.4.38
      postcss-selector-parser: 6.0.16

  postcss-selector-parser@6.0.16:
    dependencies:
      cssesc: 3.0.0
      util-deprecate: 1.0.2

  postcss-value-parser@4.2.0: {}

  postcss@8.4.38:
    dependencies:
      nanoid: 3.3.7
      picocolors: 1.0.1
      source-map-js: 1.2.0

  prelude-ls@1.2.1: {}

  prettier-eslint@15.0.1:
    dependencies:
      "@types/eslint": 8.56.10
      "@types/prettier": 2.7.3
      "@typescript-eslint/parser": 5.62.0(eslint@8.57.0)(typescript@4.9.5)
      common-tags: 1.8.2
      dlv: 1.1.3
      eslint: 8.57.0
      indent-string: 4.0.0
      lodash.merge: 4.6.2
      loglevel-colored-level-prefix: 1.0.0
      prettier: 2.8.7
      pretty-format: 23.6.0
      require-relative: 0.8.7
      typescript: 4.9.5
      vue-eslint-parser: 8.3.0(eslint@8.57.0)
    transitivePeerDependencies:
      - supports-color

  prettier-linter-helpers@1.0.0:
    dependencies:
      fast-diff: 1.3.0

  prettier@2.8.7: {}

  pretty-format@23.6.0:
    dependencies:
      ansi-regex: 3.0.1
      ansi-styles: 3.2.1

  pretty-format@24.9.0:
    dependencies:
      "@jest/types": 24.9.0
      ansi-regex: 4.1.1
      ansi-styles: 3.2.1
      react-is: 16.13.1

  pretty-format@27.5.1:
    dependencies:
      ansi-regex: 5.0.1
      ansi-styles: 5.2.0
      react-is: 17.0.2

  prop-types@15.8.1:
    dependencies:
      loose-envify: 1.4.0
      object-assign: 4.1.1
      react-is: 16.13.1

  property-expr@2.0.6: {}

  proxy-from-env@1.1.0: {}

  psl@1.9.0: {}

  punycode@2.3.1: {}

  querystringify@2.2.0: {}

  queue-microtask@1.2.3: {}

  quick-lru@4.0.1: {}

  react-dom@18.3.1(react@18.3.1):
    dependencies:
      loose-envify: 1.4.0
      react: 18.3.1
      scheduler: 0.23.2

  react-fast-compare@2.0.4: {}

  react-hot-toast@2.4.1(csstype@3.1.3)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      goober: 2.1.14(csstype@3.1.3)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
    transitivePeerDependencies:
      - csstype

  react-i18next@12.3.1(i18next@23.11.4)(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      "@babel/runtime": 7.25.0
      html-parse-stringify: 3.0.1
      i18next: 23.11.4
      react: 18.3.1
    optionalDependencies:
      react-dom: 18.3.1(react@18.3.1)

  react-is@16.13.1: {}

  react-is@17.0.2: {}

  react-is@18.3.1: {}

  react-redux@9.1.2(@types/react@18.3.2)(react@18.3.1)(redux@5.0.1):
    dependencies:
      "@types/use-sync-external-store": 0.0.3
      react: 18.3.1
      use-sync-external-store: 1.2.2(react@18.3.1)
    optionalDependencies:
      "@types/react": 18.3.2
      redux: 5.0.1

  react-refresh@0.14.2: {}

  react-router-dom@6.23.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      "@remix-run/router": 1.16.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-router: 6.23.1(react@18.3.1)

  react-router@6.23.1(react@18.3.1):
    dependencies:
      "@remix-run/router": 1.16.1
      react: 18.3.1

  react-spinners@0.13.8(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  react-transition-group@4.4.5(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      "@babel/runtime": 7.25.0
      dom-helpers: 5.2.1
      loose-envify: 1.4.0
      prop-types: 15.8.1
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)

  react-universal-interface@0.6.2(react@18.3.1)(tslib@2.6.2):
    dependencies:
      react: 18.3.1
      tslib: 2.6.2

  react-use@17.5.0(react-dom@18.3.1(react@18.3.1))(react@18.3.1):
    dependencies:
      "@types/js-cookie": 2.2.7
      "@xobotyi/scrollbar-width": 1.9.5
      copy-to-clipboard: 3.3.3
      fast-deep-equal: 3.1.3
      fast-shallow-equal: 1.0.0
      js-cookie: 2.2.1
      nano-css: 5.6.1(react-dom@18.3.1(react@18.3.1))(react@18.3.1)
      react: 18.3.1
      react-dom: 18.3.1(react@18.3.1)
      react-universal-interface: 0.6.2(react@18.3.1)(tslib@2.6.2)
      resize-observer-polyfill: 1.5.1
      screenfull: 5.2.0
      set-harmonic-interval: 1.0.1
      throttle-debounce: 3.0.1
      ts-easing: 0.2.0
      tslib: 2.6.2

  react@18.3.1:
    dependencies:
      loose-envify: 1.4.0

  read-cache@1.0.0:
    dependencies:
      pify: 2.3.0

  read-pkg-up@7.0.1:
    dependencies:
      find-up: 4.1.0
      read-pkg: 5.2.0
      type-fest: 0.8.1

  read-pkg@3.0.0:
    dependencies:
      load-json-file: 4.0.0
      normalize-package-data: 2.5.0
      path-type: 3.0.0

  read-pkg@5.2.0:
    dependencies:
      "@types/normalize-package-data": 2.4.4
      normalize-package-data: 2.5.0
      parse-json: 5.2.0
      type-fest: 0.6.0

  readable-stream@3.6.2:
    dependencies:
      inherits: 2.0.4
      string_decoder: 1.3.0
      util-deprecate: 1.0.2

  readdirp@3.6.0:
    dependencies:
      picomatch: 2.3.1

  redent@3.0.0:
    dependencies:
      indent-string: 4.0.0
      strip-indent: 3.0.0

  redux-thunk@3.1.0(redux@5.0.1):
    dependencies:
      redux: 5.0.1

  redux@5.0.1: {}

  reflect.getprototypeof@1.0.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      globalthis: 1.0.4
      which-builtin-type: 1.1.3

  regenerate-unicode-properties@10.1.1:
    dependencies:
      regenerate: 1.4.2

  regenerate@1.4.2: {}

  regenerator-runtime@0.14.1: {}

  regenerator-transform@0.15.2:
    dependencies:
      "@babel/runtime": 7.25.0

  regexp.prototype.flags@1.5.2:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-errors: 1.3.0
      set-function-name: 2.0.2

  regexpp@3.2.0: {}

  regexpu-core@5.3.2:
    dependencies:
      "@babel/regjsgen": 0.8.0
      regenerate: 1.4.2
      regenerate-unicode-properties: 10.1.1
      regjsparser: 0.9.1
      unicode-match-property-ecmascript: 2.0.0
      unicode-match-property-value-ecmascript: 2.1.0

  regjsparser@0.9.1:
    dependencies:
      jsesc: 0.5.0

  remove-accents@0.5.0: {}

  require-directory@2.1.1: {}

  require-from-string@2.0.2: {}

  require-relative@0.8.7: {}

  requires-port@1.0.0: {}

  reselect@4.1.8: {}

  reselect@5.1.1: {}

  resize-observer-polyfill@1.5.1: {}

  resolve-from@4.0.0: {}

  resolve-from@5.0.0: {}

  resolve-global@1.0.0:
    dependencies:
      global-dirs: 0.1.1

  resolve-url@0.2.1: {}

  resolve@1.22.8:
    dependencies:
      is-core-module: 2.13.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  resolve@2.0.0-next.5:
    dependencies:
      is-core-module: 2.13.1
      path-parse: 1.0.7
      supports-preserve-symlinks-flag: 1.0.0

  restore-cursor@4.0.0:
    dependencies:
      onetime: 5.1.2
      signal-exit: 3.0.7

  reusify@1.0.4: {}

  rfdc@1.3.1: {}

  rimraf@3.0.2:
    dependencies:
      glob: 7.2.3

  rimraf@5.0.7:
    dependencies:
      glob: 10.3.15

  rollup@3.29.4:
    optionalDependencies:
      fsevents: 2.3.3

  rrweb-cssom@0.6.0: {}

  rtl-css-js@1.16.1:
    dependencies:
      "@babel/runtime": 7.25.0

  run-parallel@1.2.0:
    dependencies:
      queue-microtask: 1.2.3

  safe-array-concat@1.1.2:
    dependencies:
      call-bind: 1.0.7
      get-intrinsic: 1.2.4
      has-symbols: 1.0.3
      isarray: 2.0.5

  safe-buffer@5.2.1: {}

  safe-regex-test@1.0.3:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-regex: 1.1.4

  safer-buffer@2.1.2: {}

  sass@1.77.1:
    dependencies:
      chokidar: 3.6.0
      immutable: 4.3.6
      source-map-js: 1.2.0

  saxes@6.0.0:
    dependencies:
      xmlchars: 2.2.0

  scheduler@0.23.2:
    dependencies:
      loose-envify: 1.4.0

  screenfull@5.2.0: {}

  semver@5.7.2: {}

  semver@6.3.1: {}

  semver@7.5.4:
    dependencies:
      lru-cache: 6.0.0

  semver@7.6.2: {}

  set-function-length@1.2.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      function-bind: 1.1.2
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-property-descriptors: 1.0.2

  set-function-name@2.0.2:
    dependencies:
      define-data-property: 1.1.4
      es-errors: 1.3.0
      functions-have-names: 1.2.3
      has-property-descriptors: 1.0.2

  set-harmonic-interval@1.0.1: {}

  shebang-command@1.2.0:
    dependencies:
      shebang-regex: 1.0.0

  shebang-command@2.0.0:
    dependencies:
      shebang-regex: 3.0.0

  shebang-regex@1.0.0: {}

  shebang-regex@3.0.0: {}

  shell-quote@1.8.1: {}

  side-channel@1.0.6:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      get-intrinsic: 1.2.4
      object-inspect: 1.13.1

  siginfo@2.0.0: {}

  signal-exit@3.0.7: {}

  signal-exit@4.1.0: {}

  sirv@2.0.4:
    dependencies:
      "@polka/url": 1.0.0-next.25
      mrmime: 2.0.0
      totalist: 3.0.1

  slash@3.0.0: {}

  slice-ansi@5.0.0:
    dependencies:
      ansi-styles: 6.2.1
      is-fullwidth-code-point: 4.0.0

  sort-by@1.2.0:
    dependencies:
      object-path: 0.6.0

  source-map-js@1.2.0: {}

  source-map-resolve@0.5.3:
    dependencies:
      atob: 2.1.2
      decode-uri-component: 0.2.2
      resolve-url: 0.2.1
      source-map-url: 0.4.1
      urix: 0.1.0

  source-map-url@0.4.1: {}

  source-map@0.5.6: {}

  source-map@0.5.7: {}

  source-map@0.6.1: {}

  spdx-correct@3.2.0:
    dependencies:
      spdx-expression-parse: 3.0.1
      spdx-license-ids: 3.0.17

  spdx-exceptions@2.5.0: {}

  spdx-expression-parse@3.0.1:
    dependencies:
      spdx-exceptions: 2.5.0
      spdx-license-ids: 3.0.17

  spdx-license-ids@3.0.17: {}

  split2@3.2.2:
    dependencies:
      readable-stream: 3.6.2

  stack-generator@2.0.10:
    dependencies:
      stackframe: 1.3.4

  stackback@0.0.2: {}

  stackframe@1.3.4: {}

  stacktrace-gps@3.1.2:
    dependencies:
      source-map: 0.5.6
      stackframe: 1.3.4

  stacktrace-js@2.0.2:
    dependencies:
      error-stack-parser: 2.1.4
      stack-generator: 2.0.10
      stacktrace-gps: 3.1.2

  std-env@3.7.0: {}

  stop-iteration-iterator@1.0.0:
    dependencies:
      internal-slot: 1.0.7

  string-argv@0.3.2: {}

  string-width@4.2.3:
    dependencies:
      emoji-regex: 8.0.0
      is-fullwidth-code-point: 3.0.0
      strip-ansi: 6.0.1

  string-width@5.1.2:
    dependencies:
      eastasianwidth: 0.2.0
      emoji-regex: 9.2.2
      strip-ansi: 7.1.0

  string.prototype.matchall@4.0.11:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-errors: 1.3.0
      es-object-atoms: 1.0.0
      get-intrinsic: 1.2.4
      gopd: 1.0.1
      has-symbols: 1.0.3
      internal-slot: 1.0.7
      regexp.prototype.flags: 1.5.2
      set-function-name: 2.0.2
      side-channel: 1.0.6

  string.prototype.padend@3.1.6:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  string.prototype.trim@1.2.9:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-abstract: 1.23.3
      es-object-atoms: 1.0.0

  string.prototype.trimend@1.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  string.prototype.trimstart@1.0.8:
    dependencies:
      call-bind: 1.0.7
      define-properties: 1.2.1
      es-object-atoms: 1.0.0

  string_decoder@1.3.0:
    dependencies:
      safe-buffer: 5.2.1

  strip-ansi@3.0.1:
    dependencies:
      ansi-regex: 2.1.1

  strip-ansi@6.0.1:
    dependencies:
      ansi-regex: 5.0.1

  strip-ansi@7.1.0:
    dependencies:
      ansi-regex: 6.0.1

  strip-bom@3.0.0: {}

  strip-final-newline@2.0.0: {}

  strip-final-newline@3.0.0: {}

  strip-indent@3.0.0:
    dependencies:
      min-indent: 1.0.1

  strip-json-comments@3.1.1: {}

  strip-literal@1.3.0:
    dependencies:
      acorn: 8.11.3

  stylis@4.2.0: {}

  stylis@4.3.2: {}

  sucrase@3.35.0:
    dependencies:
      "@jridgewell/gen-mapping": 0.3.5
      commander: 4.1.1
      glob: 10.3.15
      lines-and-columns: 1.2.4
      mz: 2.7.0
      pirates: 4.0.6
      ts-interface-checker: 0.1.13

  supports-color@2.0.0: {}

  supports-color@5.5.0:
    dependencies:
      has-flag: 3.0.0

  supports-color@7.2.0:
    dependencies:
      has-flag: 4.0.0

  supports-preserve-symlinks-flag@1.0.0: {}

  svg-parser@2.0.4: {}

  svgo@3.3.2:
    dependencies:
      "@trysound/sax": 0.2.0
      commander: 7.2.0
      css-select: 5.1.0
      css-tree: 2.3.1
      css-what: 6.1.0
      csso: 5.0.5
      picocolors: 1.0.1

  symbol-tree@3.2.4: {}

  tailwindcss@3.4.3(ts-node@10.9.2(@types/node@18.19.33)(typescript@4.9.5)):
    dependencies:
      "@alloc/quick-lru": 5.2.0
      arg: 5.0.2
      chokidar: 3.6.0
      didyoumean: 1.2.2
      dlv: 1.1.3
      fast-glob: 3.3.2
      glob-parent: 6.0.2
      is-glob: 4.0.3
      jiti: 1.21.0
      lilconfig: 2.1.0
      micromatch: 4.0.5
      normalize-path: 3.0.0
      object-hash: 3.0.0
      picocolors: 1.0.1
      postcss: 8.4.38
      postcss-import: 15.1.0(postcss@8.4.38)
      postcss-js: 4.0.1(postcss@8.4.38)
      postcss-load-config: 4.0.2(postcss@8.4.38)(ts-node@10.9.2(@types/node@18.19.33)(typescript@4.9.5))
      postcss-nested: 6.0.1(postcss@8.4.38)
      postcss-selector-parser: 6.0.16
      resolve: 1.22.8
      sucrase: 3.35.0
    transitivePeerDependencies:
      - ts-node

  test-exclude@6.0.0:
    dependencies:
      "@istanbuljs/schema": 0.1.3
      glob: 7.2.3
      minimatch: 3.1.2

  text-extensions@1.9.0: {}

  text-table@0.2.0: {}

  thenify-all@1.6.0:
    dependencies:
      thenify: 3.3.1

  thenify@3.3.1:
    dependencies:
      any-promise: 1.3.0

  throttle-debounce@3.0.1: {}

  through2@4.0.2:
    dependencies:
      readable-stream: 3.6.2

  through@2.3.8: {}

  tiny-case@1.0.3: {}

  tiny-invariant@1.3.3: {}

  tiny-warning@1.0.3: {}

  tinybench@2.8.0: {}

  tinypool@0.4.0: {}

  tinyspy@1.1.1: {}

  to-fast-properties@2.0.0: {}

  to-regex-range@5.0.1:
    dependencies:
      is-number: 7.0.0

  toggle-selection@1.0.6: {}

  toposort@2.0.2: {}

  totalist@3.0.1: {}

  tough-cookie@4.1.4:
    dependencies:
      psl: 1.9.0
      punycode: 2.3.1
      universalify: 0.2.0
      url-parse: 1.5.10

  tr46@0.0.3: {}

  tr46@4.1.1:
    dependencies:
      punycode: 2.3.1

  trim-newlines@3.0.1: {}

  ts-easing@0.2.0: {}

  ts-interface-checker@0.1.13: {}

  ts-node@10.9.2(@types/node@20.5.1)(typescript@4.9.5):
    dependencies:
      "@cspotcode/source-map-support": 0.8.1
      "@tsconfig/node10": 1.0.11
      "@tsconfig/node12": 1.0.11
      "@tsconfig/node14": 1.0.3
      "@tsconfig/node16": 1.0.4
      "@types/node": 20.5.1
      acorn: 8.11.3
      acorn-walk: 8.3.2
      arg: 4.1.3
      create-require: 1.1.1
      diff: 4.0.2
      make-error: 1.3.6
      typescript: 4.9.5
      v8-compile-cache-lib: 3.0.1
      yn: 3.1.1

  tsconfig-paths@3.15.0:
    dependencies:
      "@types/json5": 0.0.29
      json5: 1.0.2
      minimist: 1.2.8
      strip-bom: 3.0.0

  tslib@1.14.1: {}

  tslib@2.6.2: {}

  tsutils@3.21.0(typescript@4.9.5):
    dependencies:
      tslib: 1.14.1
      typescript: 4.9.5

  twin.macro@3.4.1(tailwindcss@3.4.3(ts-node@10.9.2(@types/node@18.19.33)(typescript@4.9.5))):
    dependencies:
      "@babel/template": 7.24.0
      babel-plugin-macros: 3.1.0
      chalk: 4.1.2
      lodash.get: 4.4.2
      lodash.merge: 4.6.2
      postcss-selector-parser: 6.0.16
      tailwindcss: 3.4.3(ts-node@10.9.2(@types/node@18.19.33)(typescript@4.9.5))

  type-check@0.4.0:
    dependencies:
      prelude-ls: 1.2.1

  type-detect@4.0.8: {}

  type-fest@0.18.1: {}

  type-fest@0.20.2: {}

  type-fest@0.21.3: {}

  type-fest@0.6.0: {}

  type-fest@0.8.1: {}

  type-fest@1.4.0: {}

  type-fest@2.19.0: {}

  typed-array-buffer@1.0.2:
    dependencies:
      call-bind: 1.0.7
      es-errors: 1.3.0
      is-typed-array: 1.1.13

  typed-array-byte-length@1.0.1:
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13

  typed-array-byte-offset@1.0.2:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13

  typed-array-length@1.0.6:
    dependencies:
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-proto: 1.0.3
      is-typed-array: 1.1.13
      possible-typed-array-names: 1.0.0

  typescript@4.9.5: {}

  ufo@1.5.3: {}

  unbox-primitive@1.0.2:
    dependencies:
      call-bind: 1.0.7
      has-bigints: 1.0.2
      has-symbols: 1.0.3
      which-boxed-primitive: 1.0.2

  undici-types@5.26.5: {}

  unicode-canonical-property-names-ecmascript@2.0.0: {}

  unicode-match-property-ecmascript@2.0.0:
    dependencies:
      unicode-canonical-property-names-ecmascript: 2.0.0
      unicode-property-aliases-ecmascript: 2.1.0

  unicode-match-property-value-ecmascript@2.1.0: {}

  unicode-property-aliases-ecmascript@2.1.0: {}

  universalify@0.2.0: {}

  universalify@2.0.1: {}

  update-browserslist-db@1.0.16(browserslist@4.23.0):
    dependencies:
      browserslist: 4.23.0
      escalade: 3.1.2
      picocolors: 1.0.1

  uri-js@4.4.1:
    dependencies:
      punycode: 2.3.1

  urix@0.1.0: {}

  url-parse@1.5.10:
    dependencies:
      querystringify: 2.2.0
      requires-port: 1.0.0

  use-debounce@10.0.0(react@18.3.1):
    dependencies:
      react: 18.3.1

  use-sync-external-store@1.2.2(react@18.3.1):
    dependencies:
      react: 18.3.1

  util-deprecate@1.0.2: {}

  uuid@9.0.1: {}

  v8-compile-cache-lib@3.0.1: {}

  v8-to-istanbul@9.2.0:
    dependencies:
      "@jridgewell/trace-mapping": 0.3.25
      "@types/istanbul-lib-coverage": 2.0.6
      convert-source-map: 2.0.0

  validate-npm-package-license@3.0.4:
    dependencies:
      spdx-correct: 3.2.0
      spdx-expression-parse: 3.0.1

  vite-node@0.29.8(@types/node@18.19.33)(sass@1.77.1):
    dependencies:
      cac: 6.7.14
      debug: 4.3.4
      mlly: 1.7.0
      pathe: 1.1.2
      picocolors: 1.0.1
      vite: 4.5.14(@types/node@18.19.33)(sass@1.77.1)
    transitivePeerDependencies:
      - "@types/node"
      - less
      - lightningcss
      - sass
      - stylus
      - sugarss
      - supports-color
      - terser

  vite-plugin-checker@0.5.6(eslint@8.57.0)(optionator@0.9.4)(typescript@4.9.5)(vite@4.5.14(@types/node@18.19.33)(sass@1.77.1)):
    dependencies:
      "@babel/code-frame": 7.24.2
      ansi-escapes: 4.3.2
      chalk: 4.1.2
      chokidar: 3.6.0
      commander: 8.3.0
      fast-glob: 3.3.2
      fs-extra: 11.2.0
      lodash.debounce: 4.0.8
      lodash.pick: 4.4.0
      npm-run-path: 4.0.1
      strip-ansi: 6.0.1
      tiny-invariant: 1.3.3
      vite: 4.5.14(@types/node@18.19.33)(sass@1.77.1)
      vscode-languageclient: 7.0.0
      vscode-languageserver: 7.0.0
      vscode-languageserver-textdocument: 1.0.11
      vscode-uri: 3.0.8
    optionalDependencies:
      eslint: 8.57.0
      optionator: 0.9.4
      typescript: 4.9.5

  vite@4.5.14(@types/node@18.19.33)(sass@1.77.1):
    dependencies:
      esbuild: 0.18.20
      postcss: 8.4.38
      rollup: 3.29.4
    optionalDependencies:
      "@types/node": 18.19.33
      fsevents: 2.3.3
      sass: 1.77.1

  vitest@0.29.8(@vitest/ui@0.29.8)(happy-dom@8.9.0)(jsdom@22.1.0)(playwright@1.44.0)(sass@1.77.1):
    dependencies:
      "@types/chai": 4.3.16
      "@types/chai-subset": 1.3.5
      "@types/node": 18.19.33
      "@vitest/expect": 0.29.8
      "@vitest/runner": 0.29.8
      "@vitest/spy": 0.29.8
      "@vitest/utils": 0.29.8
      acorn: 8.11.3
      acorn-walk: 8.3.2
      cac: 6.7.14
      chai: 4.4.1
      debug: 4.3.4
      local-pkg: 0.4.3
      pathe: 1.1.2
      picocolors: 1.0.1
      source-map: 0.6.1
      std-env: 3.7.0
      strip-literal: 1.3.0
      tinybench: 2.8.0
      tinypool: 0.4.0
      tinyspy: 1.1.1
      vite: 4.5.14(@types/node@18.19.33)(sass@1.77.1)
      vite-node: 0.29.8(@types/node@18.19.33)(sass@1.77.1)
      why-is-node-running: 2.2.2
    optionalDependencies:
      "@vitest/ui": 0.29.8
      happy-dom: 8.9.0
      jsdom: 22.1.0
      playwright: 1.44.0
    transitivePeerDependencies:
      - less
      - lightningcss
      - sass
      - stylus
      - sugarss
      - supports-color
      - terser

  void-elements@3.1.0: {}

  vscode-jsonrpc@6.0.0: {}

  vscode-languageclient@7.0.0:
    dependencies:
      minimatch: 3.1.2
      semver: 7.6.2
      vscode-languageserver-protocol: 3.16.0

  vscode-languageserver-protocol@3.16.0:
    dependencies:
      vscode-jsonrpc: 6.0.0
      vscode-languageserver-types: 3.16.0

  vscode-languageserver-textdocument@1.0.11: {}

  vscode-languageserver-types@3.16.0: {}

  vscode-languageserver@7.0.0:
    dependencies:
      vscode-languageserver-protocol: 3.16.0

  vscode-uri@3.0.8: {}

  vue-eslint-parser@8.3.0(eslint@8.57.0):
    dependencies:
      debug: 4.3.4
      eslint: 8.57.0
      eslint-scope: 7.2.2
      eslint-visitor-keys: 3.4.3
      espree: 9.6.1
      esquery: 1.5.0
      lodash: 4.17.21
      semver: 7.6.2
    transitivePeerDependencies:
      - supports-color

  w3c-xmlserializer@4.0.0:
    dependencies:
      xml-name-validator: 4.0.0

  webidl-conversions@3.0.1: {}

  webidl-conversions@7.0.0: {}

  whatwg-encoding@2.0.0:
    dependencies:
      iconv-lite: 0.6.3

  whatwg-mimetype@3.0.0: {}

  whatwg-url@12.0.1:
    dependencies:
      tr46: 4.1.1
      webidl-conversions: 7.0.0

  whatwg-url@5.0.0:
    dependencies:
      tr46: 0.0.3
      webidl-conversions: 3.0.1

  which-boxed-primitive@1.0.2:
    dependencies:
      is-bigint: 1.0.4
      is-boolean-object: 1.1.2
      is-number-object: 1.0.7
      is-string: 1.0.7
      is-symbol: 1.0.4

  which-builtin-type@1.1.3:
    dependencies:
      function.prototype.name: 1.1.6
      has-tostringtag: 1.0.2
      is-async-function: 2.0.0
      is-date-object: 1.0.5
      is-finalizationregistry: 1.0.2
      is-generator-function: 1.0.10
      is-regex: 1.1.4
      is-weakref: 1.0.2
      isarray: 2.0.5
      which-boxed-primitive: 1.0.2
      which-collection: 1.0.2
      which-typed-array: 1.1.15

  which-collection@1.0.2:
    dependencies:
      is-map: 2.0.3
      is-set: 2.0.3
      is-weakmap: 2.0.2
      is-weakset: 2.0.3

  which-typed-array@1.1.15:
    dependencies:
      available-typed-arrays: 1.0.7
      call-bind: 1.0.7
      for-each: 0.3.3
      gopd: 1.0.1
      has-tostringtag: 1.0.2

  which@1.3.1:
    dependencies:
      isexe: 2.0.0

  which@2.0.2:
    dependencies:
      isexe: 2.0.0

  why-is-node-running@2.2.2:
    dependencies:
      siginfo: 2.0.0
      stackback: 0.0.2

  word-wrap@1.2.5: {}

  wrap-ansi@7.0.0:
    dependencies:
      ansi-styles: 4.3.0
      string-width: 4.2.3
      strip-ansi: 6.0.1

  wrap-ansi@8.1.0:
    dependencies:
      ansi-styles: 6.2.1
      string-width: 5.1.2
      strip-ansi: 7.1.0

  wrappy@1.0.2: {}

  ws@8.17.0: {}

  xlsx@file:vendor/xlsx-0.20.3.tgz: {}

  xml-name-validator@4.0.0: {}

  xmlchars@2.2.0: {}

  y18n@5.0.8: {}

  yallist@3.1.1: {}

  yallist@4.0.0: {}

  yaml@1.10.2: {}

  yaml@2.3.1: {}

  yaml@2.4.2: {}

  yargs-parser@20.2.9: {}

  yargs-parser@21.1.1: {}

  yargs@16.2.0:
    dependencies:
      cliui: 7.0.4
      escalade: 3.1.2
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 20.2.9

  yargs@17.7.2:
    dependencies:
      cliui: 8.0.1
      escalade: 3.1.2
      get-caller-file: 2.0.5
      require-directory: 2.1.1
      string-width: 4.2.3
      y18n: 5.0.8
      yargs-parser: 21.1.1

  yn@3.1.1: {}

  yocto-queue@0.1.0: {}

  yocto-queue@1.0.0: {}

  yoctodelay@1.2.0: {}

  yup@1.4.0:
    dependencies:
      property-expr: 2.0.6
      tiny-case: 1.0.3
      toposort: 2.0.2
      type-fest: 2.19.0
