name: CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      #
      - uses: actions/checkout@v3
      #
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      #
      - name: Cache node modules
        id: cache-npm
        uses: actions/cache@v3
        env:
          cache-name: cache-node-modules
        with:
          # npm cache files are stored in `~/.npm` on Linux/macOS
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-
      - if: ${{ steps.cache-npm.outputs.cache-hit != 'true' }}
        name: List the state of node modules
        continue-on-error: true
        run: npm list
      # Clean Install
      - name: Install dependencies
        run: npm ci
      # Typescript checks
      - name: Type check
        run: npm run type-check
      # Format and lint
      - name: Format and lint
        run: npm run lf
      # Run unit tests
      - name: Run unit tests
        run: npm run test:unit
      # Build for production (required for e2e test)
      - name: Build for production
        run: npm run build
      # Install playwright
      - name: Install Playwright
        run: npx playwright install --with-deps
      # Install msedge
      - name: Install Playwright
        run: npx playwright install msedge
      # Run e2e tests
      - name: Run e2e tests
        uses: coactions/setup-xvfb@v1
        with:
          run: npm run test:e2e:ci
      # Upload test report
      - name: Upload test report
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 1
