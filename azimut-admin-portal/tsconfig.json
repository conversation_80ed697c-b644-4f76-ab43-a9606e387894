{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "jsxImportSource": "@emotion/react", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@img/*": ["./src/assets/img"]}}, "types": ["vitest/globals", "@testing-library/jest-dom"], "include": ["src", "types", "config"], "references": [{"path": "./tsconfig.node.json"}]}