{"name": "vite-react", "private": true, "version": "0.1.0", "type": "module", "babelMacros": {"twin": {"preset": "emotion", "config": "tailwind.config.mjs"}}, "scripts": {"dev": "vite --open", "dev:force": "run-s clear \"dev -- --force\"", "build-only": "vite build", "build": "run-p type-check build-only", "build:staging": "run-p type-check && \"build-only -- --mode staging\"", "preview": "vite preview", "type-check": "tsc --noEmit", "lint": "eslint . --ext .js,.cjs,.mjs,.ts,.tsx --fix", "format": "prettier --write . -l -u --no-error-on-unmatched-pattern", "lf": "run-p lint format", "test:unit": "run-s \"vitest -- run\"", "test:unit:watch": "run-s \"vitest -- --ui\"", "test:e2e": "env-cmd -f .env.development --no-override playwright test", "test:e2e:watch": "env-cmd -f .env.development --no-override playwright test --ui", "test:e2e:ci": "env-cmd -f .env.staging --no-override playwright test", "clear": "<PERSON><PERSON><PERSON> test-results playwright-report dist", "prepare": "husky install", "vitest": "vitest --coverage"}, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@loadable/component": "^5.15.3", "@mui/icons-material": "^5.16.6", "@mui/material": "^5.16.6", "@mui/utils": "^5.16.6", "@mui/x-data-grid": "^7.14.0", "@mui/x-date-pickers": "^7.14.0", "@reduxjs/toolkit": "^2.2.7", "axios": "^1.7.4", "chroma-js": "^2.4.2", "clsx": "^1.2.1", "dayjs": "^1.11.13", "formik": "^2.2.9", "i18next": "^23.11.4", "jotai": "^2.0.3", "json-to-csv-export": "^3.0.1", "jwt-decode": "^4.0.0", "localforage": "^1.10.0", "lodash": "^4.17.21", "match-sorter": "^6.3.1", "papaparse": "^5.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-i18next": "^12.2.0", "react-redux": "^9.1.2", "react-router-dom": "^6.23.1", "react-spinners": "^0.13.8", "react-use": "^17.4.0", "sort-by": "^1.2.0", "twin.macro": "^3.3.1", "use-debounce": "^10.0.0", "uuid": "^9.0.0", "xlsx": "file:vendor/xlsx-0.20.3.tgz", "yup": "^1.1.0"}, "devDependencies": {"@babel/plugin-transform-react-jsx": "^7.21.0", "@commitlint/cli": "^17.5.1", "@commitlint/config-conventional": "^17.4.4", "@emotion/babel-plugin-jsx-pragmatic": "^0.2.0", "@playwright/test": "^1.33.0", "@svgr/rollup": "^7.0.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^14.0.0", "@types/chroma-js": "^2.4.0", "@types/loadable__component": "^5.13.4", "@types/lodash": "^4.14.194", "@types/node": "^18.15.10", "@types/papaparse": "^5.3.16", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^5.57.0", "@typescript-eslint/parser": "^5.57.0", "@vitejs/plugin-react": "^3.1.0", "@vitest/coverage-c8": "^0.29.8", "@vitest/ui": "^0.29.8", "autoprefixer": "^10.4.14", "babel-plugin-import": "^1.13.6", "babel-plugin-macros": "^3.1.0", "dotenv": "^16.0.3", "env-cmd": "^10.1.0", "esbuild": "^0.25.9", "eslint": "^8.36.0", "eslint-config-prettier": "^8.8.0", "eslint-config-standard": "^17.0.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-n": "^15.6.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-simple-import-sort": "^10.0.0", "happy-dom": "^8.9.0", "husky": "^8.0.3", "jsdom": "^22.0.0", "lint-staged": "^13.2.0", "npm-run-all": "^4.1.5", "p-min-delay": "^4.0.2", "postcss": "^8.4.21", "prettier": "2.8.7", "prettier-eslint": "^15.0.1", "rimraf": "^5.0.1", "sass": "^1.60.0", "svgo": "^3.0.2", "tailwindcss": "^3.3.0", "typescript": "^4.9.3", "vite": "^4.5.14", "vite-plugin-checker": "^0.5.6", "vitest": "^0.29.8"}}